{"name": "saber-admin", "version": "3.0.1", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "analyz": "npm_config_report=true npm run build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e"}, "dependencies": {"@map-library/core": "file:C:/myDriver/work/myDriver/work-file/map-package/unified-map-monorepo/packages/core", "@map-library/maymap": "file:C:/myDriver/work/myDriver/work-file/map-package/unified-map-monorepo/packages/maymap", "@amap/amap-jsapi-loader": "^1.0.1", "@femessage/element-ui": "^2.22.0", "@liveqing/liveplayer": "^2.7.32", "@riophae/vue-treeselect": "^0.1.0", "@wchbrad/vue-easy-tree": "^1.0.6", "animejs": "^3.2.1", "avue-plugin-ueditor": "^0.1.4", "axios": "^0.18.0", "babel-polyfill": "^6.26.0", "cesium": "^1.74.0", "classlist-polyfill": "^1.2.0", "compression-webpack-plugin": "^5.0.1", "copy-webpack-plugin": "^4.6.0", "core-js": "^3.31.0", "crypto-js": "^4.0.0", "dayjs": "^1.11.8", "echarts": "^5.5.0", "element-resize-detector": "^1.2.3", "file-saver": "^2.0.5", "flv.js": "^1.6.1", "github-markdown-css": "^5.6.1", "html-loader": "^2.0.0", "jquery": "^3.6.0", "js-base64": "^2.5.1", "js-cookie": "^2.2.0", "js-md5": "^0.7.3", "json-bigint": "^1.0.0", "jszip": "^3.10.1", "less": "^4.1.1", "less-loader": "^7.3.0", "lodash": "^4.17.21", "markdown-loader": "^4.0.0", "marked": "^5.0.0", "moment": "^2.29.1", "node-gyp": "^5.0.6", "nprogress": "^0.2.0", "portfinder": "^1.0.23", "recorder-core": "^1.1.21080800", "script-loader": "^0.7.2", "svg-sprite-loader": "^4.1.3", "three": "^0.148.0", "umy-ui": "^1.1.6", "vue-axios": "^2.1.2", "vue-dompurify-html": "^2.6.0", "vue-i18n": "^8.7.0", "webrtc-adapter": "^8.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/plugin-proposal-optional-chaining": "^7.21.0", "@open-wc/webpack-import-meta-loader": "^0.4.7", "@types/three": "^0.163.0", "@vue/babel-preset-app": "^5.0.8", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "^3.1.5", "@vue/cli-service": "^3.1.4", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "babel-plugin-transform-remove-console": "^6.9.4", "chai": "^4.1.2", "eslint": "^4.19.1", "eslint-plugin-vue": "^6.2.2", "node-sass": "npm:sass@^1.66.1", "sass-loader": "^10.0.5", "style-resources-loader": "^1.4.1", "vue-cli-plugin-style-resources-loader": "^0.1.3", "vue": "2.7.14", "vue-template-compiler": "2.7.14", "webpack-bundle-analyzer": "^3.0.3"}, "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}}