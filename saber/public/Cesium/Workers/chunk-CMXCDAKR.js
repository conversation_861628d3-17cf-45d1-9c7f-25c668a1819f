/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.118.1
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as _,b as Pe,c as Oe,d as Ie,e as $,f as un}from"./chunk-LJCGAQ64.js";import{a as I}from"./chunk-JFG572S7.js";import{a as Se}from"./chunk-IRDBGNMC.js";import{a as O}from"./chunk-42NIXFVW.js";import{a as A,b as s}from"./chunk-5YVCOCPP.js";import{a as Sn,c as En,d as Ze,e as p}from"./chunk-U73D6PDD.js";var Pt=En((en,nn)=>{/*! https://mths.be/punycode v1.4.0 by @mathias */(function(e){var n=typeof en=="object"&&en&&!en.nodeType&&en,t=typeof nn=="object"&&nn&&!nn.nodeType&&nn,o=typeof global=="object"&&global;(o.global===o||o.window===o||o.self===o)&&(e=o);var i,r=**********,a=36,u=1,d=26,m=38,l=700,w=72,T=128,R="-",P=/^xn--/,C=/[^\x20-\x7E]/,q=/[\x2E\u3002\uFF0E\uFF61]/g,k={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},L=a-u,F=Math.floor,x=String.fromCharCode,W;function Q(b){throw new RangeError(k[b])}function ee(b,z){for(var N=b.length,V=[];N--;)V[N]=z(b[N]);return V}function re(b,z){var N=b.split("@"),V="";N.length>1&&(V=N[0]+"@",b=N[1]),b=b.replace(q,".");var ie=b.split("."),ae=ee(ie,z).join(".");return V+ae}function Z(b){for(var z=[],N=0,V=b.length,ie,ae;N<V;)ie=b.charCodeAt(N++),ie>=55296&&ie<=56319&&N<V?(ae=b.charCodeAt(N++),(ae&64512)==56320?z.push(((ie&1023)<<10)+(ae&1023)+65536):(z.push(ie),N--)):z.push(ie);return z}function oe(b){return ee(b,function(z){var N="";return z>65535&&(z-=65536,N+=x(z>>>10&1023|55296),z=56320|z&1023),N+=x(z),N}).join("")}function J(b){return b-48<10?b-22:b-65<26?b-65:b-97<26?b-97:a}function c(b,z){return b+22+75*(b<26)-((z!=0)<<5)}function f(b,z,N){var V=0;for(b=N?F(b/l):b>>1,b+=F(b/z);b>L*d>>1;V+=a)b=F(b/L);return F(V+(L+1)*b/(b+m))}function h(b){var z=[],N=b.length,V,ie=0,ae=T,ne=w,ue,he,ye,de,pe,Y,_e,Re,je;for(ue=b.lastIndexOf(R),ue<0&&(ue=0),he=0;he<ue;++he)b.charCodeAt(he)>=128&&Q("not-basic"),z.push(b.charCodeAt(he));for(ye=ue>0?ue+1:0;ye<N;){for(de=ie,pe=1,Y=a;ye>=N&&Q("invalid-input"),_e=J(b.charCodeAt(ye++)),(_e>=a||_e>F((r-ie)/pe))&&Q("overflow"),ie+=_e*pe,Re=Y<=ne?u:Y>=ne+d?d:Y-ne,!(_e<Re);Y+=a)je=a-Re,pe>F(r/je)&&Q("overflow"),pe*=je;V=z.length+1,ne=f(ie-de,V,de==0),F(ie/V)>r-ae&&Q("overflow"),ae+=F(ie/V),ie%=V,z.splice(ie++,0,ae)}return oe(z)}function y(b){var z,N,V,ie,ae,ne,ue,he,ye,de,pe,Y=[],_e,Re,je,fn;for(b=Z(b),_e=b.length,z=T,N=0,ae=w,ne=0;ne<_e;++ne)pe=b[ne],pe<128&&Y.push(x(pe));for(V=ie=Y.length,ie&&Y.push(R);V<_e;){for(ue=r,ne=0;ne<_e;++ne)pe=b[ne],pe>=z&&pe<ue&&(ue=pe);for(Re=V+1,ue-z>F((r-N)/Re)&&Q("overflow"),N+=(ue-z)*Re,z=ue,ne=0;ne<_e;++ne)if(pe=b[ne],pe<z&&++N>r&&Q("overflow"),pe==z){for(he=N,ye=a;de=ye<=ae?u:ye>=ae+d?d:ye-ae,!(he<de);ye+=a)fn=he-de,je=a-de,Y.push(x(c(de+fn%je,0))),he=F(fn/je);Y.push(x(c(he,0))),ae=f(N,Re,V==ie),N=0,++V}++N,++z}return Y.join("")}function g(b){return re(b,function(z){return P.test(z)?h(z.slice(4).toLowerCase()):z})}function v(b){return re(b,function(z){return C.test(z)?"xn--"+y(z):z})}if(i={version:"1.3.2",ucs2:{decode:Z,encode:oe},decode:h,encode:y,toASCII:v,toUnicode:g},typeof define=="function"&&typeof define.amd=="object"&&define.amd)define("punycode",function(){return i});else if(n&&t)if(nn.exports==n)t.exports=i;else for(W in i)i.hasOwnProperty(W)&&(n[W]=i[W]);else e.punycode=i})(en)});var Ut=En((Mt,Pn)=>{/*!
 * URI.js - Mutating URLs
 * IPv6 Support
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,n){"use strict";typeof Pn=="object"&&Pn.exports?Pn.exports=n():typeof define=="function"&&define.amd?define(n):e.IPv6=n(e)})(Mt,function(e){"use strict";var n=e&&e.IPv6;function t(i){var r=i.toLowerCase(),a=r.split(":"),u=a.length,d=8;a[0]===""&&a[1]===""&&a[2]===""?(a.shift(),a.shift()):a[0]===""&&a[1]===""?a.shift():a[u-1]===""&&a[u-2]===""&&a.pop(),u=a.length,a[u-1].indexOf(".")!==-1&&(d=7);var m;for(m=0;m<u&&a[m]!=="";m++);if(m<d)for(a.splice(m,1,"0000");a.length<d;)a.splice(m,0,"0000");for(var l,w=0;w<d;w++){l=a[w].split("");for(var T=0;T<3&&(l[0]==="0"&&l.length>1);T++)l.splice(0,1);a[w]=l.join("")}var R=-1,P=0,C=0,q=-1,k=!1;for(w=0;w<d;w++)k?a[w]==="0"?C+=1:(k=!1,C>P&&(R=q,P=C)):a[w]==="0"&&(k=!0,q=w,C=1);C>P&&(R=q,P=C),P>1&&a.splice(R,P,""),u=a.length;var L="";for(a[0]===""&&(L=":"),w=0;w<u&&(L+=a[w],w!==u-1);w++)L+=":";return a[u-1]===""&&(L+=":"),L}function o(){return e.IPv6===this&&(e.IPv6=n),this}return{best:t,noConflict:o}})});var It=En((zt,Mn)=>{/*!
 * URI.js - Mutating URLs
 * Second Level Domain (SLD) Support
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,n){"use strict";typeof Mn=="object"&&Mn.exports?Mn.exports=n():typeof define=="function"&&define.amd?define(n):e.SecondLevelDomains=n(e)})(zt,function(e){"use strict";var n=e&&e.SecondLevelDomains,t={list:{ac:" com gov mil net org ",ae:" ac co gov mil name net org pro sch ",af:" com edu gov net org ",al:" com edu gov mil net org ",ao:" co ed gv it og pb ",ar:" com edu gob gov int mil net org tur ",at:" ac co gv or ",au:" asn com csiro edu gov id net org ",ba:" co com edu gov mil net org rs unbi unmo unsa untz unze ",bb:" biz co com edu gov info net org store tv ",bh:" biz cc com edu gov info net org ",bn:" com edu gov net org ",bo:" com edu gob gov int mil net org tv ",br:" adm adv agr am arq art ato b bio blog bmd cim cng cnt com coop ecn edu eng esp etc eti far flog fm fnd fot fst g12 ggf gov imb ind inf jor jus lel mat med mil mus net nom not ntr odo org ppg pro psc psi qsl rec slg srv tmp trd tur tv vet vlog wiki zlg ",bs:" com edu gov net org ",bz:" du et om ov rg ",ca:" ab bc mb nb nf nl ns nt nu on pe qc sk yk ",ck:" biz co edu gen gov info net org ",cn:" ac ah bj com cq edu fj gd gov gs gx gz ha hb he hi hl hn jl js jx ln mil net nm nx org qh sc sd sh sn sx tj tw xj xz yn zj ",co:" com edu gov mil net nom org ",cr:" ac c co ed fi go or sa ",cy:" ac biz com ekloges gov ltd name net org parliament press pro tm ",do:" art com edu gob gov mil net org sld web ",dz:" art asso com edu gov net org pol ",ec:" com edu fin gov info med mil net org pro ",eg:" com edu eun gov mil name net org sci ",er:" com edu gov ind mil net org rochest w ",es:" com edu gob nom org ",et:" biz com edu gov info name net org ",fj:" ac biz com info mil name net org pro ",fk:" ac co gov net nom org ",fr:" asso com f gouv nom prd presse tm ",gg:" co net org ",gh:" com edu gov mil org ",gn:" ac com gov net org ",gr:" com edu gov mil net org ",gt:" com edu gob ind mil net org ",gu:" com edu gov net org ",hk:" com edu gov idv net org ",hu:" 2000 agrar bolt casino city co erotica erotika film forum games hotel info ingatlan jogasz konyvelo lakas media news org priv reklam sex shop sport suli szex tm tozsde utazas video ",id:" ac co go mil net or sch web ",il:" ac co gov idf k12 muni net org ",in:" ac co edu ernet firm gen gov i ind mil net nic org res ",iq:" com edu gov i mil net org ",ir:" ac co dnssec gov i id net org sch ",it:" edu gov ",je:" co net org ",jo:" com edu gov mil name net org sch ",jp:" ac ad co ed go gr lg ne or ",ke:" ac co go info me mobi ne or sc ",kh:" com edu gov mil net org per ",ki:" biz com de edu gov info mob net org tel ",km:" asso com coop edu gouv k medecin mil nom notaires pharmaciens presse tm veterinaire ",kn:" edu gov net org ",kr:" ac busan chungbuk chungnam co daegu daejeon es gangwon go gwangju gyeongbuk gyeonggi gyeongnam hs incheon jeju jeonbuk jeonnam k kg mil ms ne or pe re sc seoul ulsan ",kw:" com edu gov net org ",ky:" com edu gov net org ",kz:" com edu gov mil net org ",lb:" com edu gov net org ",lk:" assn com edu gov grp hotel int ltd net ngo org sch soc web ",lr:" com edu gov net org ",lv:" asn com conf edu gov id mil net org ",ly:" com edu gov id med net org plc sch ",ma:" ac co gov m net org press ",mc:" asso tm ",me:" ac co edu gov its net org priv ",mg:" com edu gov mil nom org prd tm ",mk:" com edu gov inf name net org pro ",ml:" com edu gov net org presse ",mn:" edu gov org ",mo:" com edu gov net org ",mt:" com edu gov net org ",mv:" aero biz com coop edu gov info int mil museum name net org pro ",mw:" ac co com coop edu gov int museum net org ",mx:" com edu gob net org ",my:" com edu gov mil name net org sch ",nf:" arts com firm info net other per rec store web ",ng:" biz com edu gov mil mobi name net org sch ",ni:" ac co com edu gob mil net nom org ",np:" com edu gov mil net org ",nr:" biz com edu gov info net org ",om:" ac biz co com edu gov med mil museum net org pro sch ",pe:" com edu gob mil net nom org sld ",ph:" com edu gov i mil net ngo org ",pk:" biz com edu fam gob gok gon gop gos gov net org web ",pl:" art bialystok biz com edu gda gdansk gorzow gov info katowice krakow lodz lublin mil net ngo olsztyn org poznan pwr radom slupsk szczecin torun warszawa waw wroc wroclaw zgora ",pr:" ac biz com edu est gov info isla name net org pro prof ",ps:" com edu gov net org plo sec ",pw:" belau co ed go ne or ",ro:" arts com firm info nom nt org rec store tm www ",rs:" ac co edu gov in org ",sb:" com edu gov net org ",sc:" com edu gov net org ",sh:" co com edu gov net nom org ",sl:" com edu gov net org ",st:" co com consulado edu embaixada gov mil net org principe saotome store ",sv:" com edu gob org red ",sz:" ac co org ",tr:" av bbs bel biz com dr edu gen gov info k12 name net org pol tel tsk tv web ",tt:" aero biz cat co com coop edu gov info int jobs mil mobi museum name net org pro tel travel ",tw:" club com ebiz edu game gov idv mil net org ",mu:" ac co com gov net or org ",mz:" ac co edu gov org ",na:" co com ",nz:" ac co cri geek gen govt health iwi maori mil net org parliament school ",pa:" abo ac com edu gob ing med net nom org sld ",pt:" com edu gov int net nome org publ ",py:" com edu gov mil net org ",qa:" com edu gov mil net org ",re:" asso com nom ",ru:" ac adygeya altai amur arkhangelsk astrakhan bashkiria belgorod bir bryansk buryatia cbg chel chelyabinsk chita chukotka chuvashia com dagestan e-burg edu gov grozny int irkutsk ivanovo izhevsk jar joshkar-ola kalmykia kaluga kamchatka karelia kazan kchr kemerovo khabarovsk khakassia khv kirov koenig komi kostroma kranoyarsk kuban kurgan kursk lipetsk magadan mari mari-el marine mil mordovia mosreg msk murmansk nalchik net nnov nov novosibirsk nsk omsk orenburg org oryol penza perm pp pskov ptz rnd ryazan sakhalin samara saratov simbirsk smolensk spb stavropol stv surgut tambov tatarstan tom tomsk tsaritsyn tsk tula tuva tver tyumen udm udmurtia ulan-ude vladikavkaz vladimir vladivostok volgograd vologda voronezh vrn vyatka yakutia yamal yekaterinburg yuzhno-sakhalinsk ",rw:" ac co com edu gouv gov int mil net ",sa:" com edu gov med net org pub sch ",sd:" com edu gov info med net org tv ",se:" a ac b bd c d e f g h i k l m n o org p parti pp press r s t tm u w x y z ",sg:" com edu gov idn net org per ",sn:" art com edu gouv org perso univ ",sy:" com edu gov mil net news org ",th:" ac co go in mi net or ",tj:" ac biz co com edu go gov info int mil name net nic org test web ",tn:" agrinet com defense edunet ens fin gov ind info intl mincom nat net org perso rnrt rns rnu tourism ",tz:" ac co go ne or ",ua:" biz cherkassy chernigov chernovtsy ck cn co com crimea cv dn dnepropetrovsk donetsk dp edu gov if in ivano-frankivsk kh kharkov kherson khmelnitskiy kiev kirovograd km kr ks kv lg lugansk lutsk lviv me mk net nikolaev od odessa org pl poltava pp rovno rv sebastopol sumy te ternopil uzhgorod vinnica vn zaporizhzhe zhitomir zp zt ",ug:" ac co go ne or org sc ",uk:" ac bl british-library co cym gov govt icnet jet lea ltd me mil mod national-library-scotland nel net nhs nic nls org orgn parliament plc police sch scot soc ",us:" dni fed isa kids nsn ",uy:" com edu gub mil net org ",ve:" co com edu gob info mil net org web ",vi:" co com k12 net org ",vn:" ac biz com edu gov health info int name net org pro ",ye:" co com gov ltd me net org plc ",yu:" ac co edu gov org ",za:" ac agric alt bourse city co cybernet db edu gov grondar iaccess imt inca landesign law mil net ngo nis nom olivetti org pix school tm web ",zm:" ac co com edu gov net org sch ",com:"ar br cn de eu gb gr hu jpn kr no qc ru sa se uk us uy za ",net:"gb jp se uk ",org:"ae",de:"com "},has:function(o){var i=o.lastIndexOf(".");if(i<=0||i>=o.length-1)return!1;var r=o.lastIndexOf(".",i-1);if(r<=0||r>=i-1)return!1;var a=t.list[o.slice(i+1)];return a?a.indexOf(" "+o.slice(r+1,i)+" ")>=0:!1},is:function(o){var i=o.lastIndexOf(".");if(i<=0||i>=o.length-1)return!1;var r=o.lastIndexOf(".",i-1);if(r>=0)return!1;var a=t.list[o.slice(i+1)];return a?a.indexOf(" "+o.slice(0,i)+" ")>=0:!1},get:function(o){var i=o.lastIndexOf(".");if(i<=0||i>=o.length-1)return null;var r=o.lastIndexOf(".",i-1);if(r<=0||r>=i-1)return null;var a=t.list[o.slice(i+1)];return!a||a.indexOf(" "+o.slice(r+1,i)+" ")<0?null:o.slice(r+1)},noConflict:function(){return e.SecondLevelDomains===this&&(e.SecondLevelDomains=n),this}};return t})});var We=En((qt,Un)=>{/*!
 * URI.js - Mutating URLs
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,n){"use strict";typeof Un=="object"&&Un.exports?Un.exports=n(Pt(),Ut(),It()):typeof define=="function"&&define.amd?define(["./punycode","./IPv6","./SecondLevelDomains"],n):e.URI=n(e.punycode,e.IPv6,e.SecondLevelDomains,e)})(qt,function(e,n,t,o){"use strict";var i=o&&o.URI;function r(c,f){var h=arguments.length>=1,y=arguments.length>=2;if(!(this instanceof r))return h?y?new r(c,f):new r(c):new r;if(c===void 0){if(h)throw new TypeError("undefined is not a valid argument for URI");typeof location<"u"?c=location.href+"":c=""}if(c===null&&h)throw new TypeError("null is not a valid argument for URI");return this.href(c),f!==void 0?this.absoluteTo(f):this}function a(c){return/^[0-9]+$/.test(c)}r.version="1.19.11";var u=r.prototype,d=Object.prototype.hasOwnProperty;function m(c){return c.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function l(c){return c===void 0?"Undefined":String(Object.prototype.toString.call(c)).slice(8,-1)}function w(c){return l(c)==="Array"}function T(c,f){var h={},y,g;if(l(f)==="RegExp")h=null;else if(w(f))for(y=0,g=f.length;y<g;y++)h[f[y]]=!0;else h[f]=!0;for(y=0,g=c.length;y<g;y++){var v=h&&h[c[y]]!==void 0||!h&&f.test(c[y]);v&&(c.splice(y,1),g--,y--)}return c}function R(c,f){var h,y;if(w(f)){for(h=0,y=f.length;h<y;h++)if(!R(c,f[h]))return!1;return!0}var g=l(f);for(h=0,y=c.length;h<y;h++)if(g==="RegExp"){if(typeof c[h]=="string"&&c[h].match(f))return!0}else if(c[h]===f)return!0;return!1}function P(c,f){if(!w(c)||!w(f)||c.length!==f.length)return!1;c.sort(),f.sort();for(var h=0,y=c.length;h<y;h++)if(c[h]!==f[h])return!1;return!0}function C(c){var f=/^\/+|\/+$/g;return c.replace(f,"")}r._parts=function(){return{protocol:null,username:null,password:null,hostname:null,urn:null,port:null,path:null,query:null,fragment:null,preventInvalidHostname:r.preventInvalidHostname,duplicateQueryParameters:r.duplicateQueryParameters,escapeQuerySpace:r.escapeQuerySpace}},r.preventInvalidHostname=!1,r.duplicateQueryParameters=!1,r.escapeQuerySpace=!0,r.protocol_expression=/^[a-z][a-z0-9.+-]*$/i,r.idn_expression=/[^a-z0-9\._-]/i,r.punycode_expression=/(xn--)/i,r.ip4_expression=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,r.ip6_expression=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,r.find_uri_expression=/\b((?:[a-z][\w-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))/ig,r.findUri={start:/\b(?:([a-z][a-z0-9.+-]*:\/\/)|www\.)/gi,end:/[\s\r\n]|$/,trim:/[`!()\[\]{};:'".,<>?«»“”„‘’]+$/,parens:/(\([^\)]*\)|\[[^\]]*\]|\{[^}]*\}|<[^>]*>)/g},r.leading_whitespace_expression=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,r.ascii_tab_whitespace=/[\u0009\u000A\u000D]+/g,r.defaultPorts={http:"80",https:"443",ftp:"21",gopher:"70",ws:"80",wss:"443"},r.hostProtocols=["http","https"],r.invalid_hostname_characters=/[^a-zA-Z0-9\.\-:_]/,r.domAttributes={a:"href",blockquote:"cite",link:"href",base:"href",script:"src",form:"action",img:"src",area:"href",iframe:"src",embed:"src",source:"src",track:"src",input:"src",audio:"src",video:"src"},r.getDomAttribute=function(c){if(!(!c||!c.nodeName)){var f=c.nodeName.toLowerCase();if(!(f==="input"&&c.type!=="image"))return r.domAttributes[f]}};function q(c){return escape(c)}function k(c){return encodeURIComponent(c).replace(/[!'()*]/g,q).replace(/\*/g,"%2A")}r.encode=k,r.decode=decodeURIComponent,r.iso8859=function(){r.encode=escape,r.decode=unescape},r.unicode=function(){r.encode=k,r.decode=decodeURIComponent},r.characters={pathname:{encode:{expression:/%(24|26|2B|2C|3B|3D|3A|40)/ig,map:{"%24":"$","%26":"&","%2B":"+","%2C":",","%3B":";","%3D":"=","%3A":":","%40":"@"}},decode:{expression:/[\/\?#]/g,map:{"/":"%2F","?":"%3F","#":"%23"}}},reserved:{encode:{expression:/%(21|23|24|26|27|28|29|2A|2B|2C|2F|3A|3B|3D|3F|40|5B|5D)/ig,map:{"%3A":":","%2F":"/","%3F":"?","%23":"#","%5B":"[","%5D":"]","%40":"@","%21":"!","%24":"$","%26":"&","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"="}}},urnpath:{encode:{expression:/%(21|24|27|28|29|2A|2B|2C|3B|3D|40)/ig,map:{"%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"=","%40":"@"}},decode:{expression:/[\/\?#:]/g,map:{"/":"%2F","?":"%3F","#":"%23",":":"%3A"}}}},r.encodeQuery=function(c,f){var h=r.encode(c+"");return f===void 0&&(f=r.escapeQuerySpace),f?h.replace(/%20/g,"+"):h},r.decodeQuery=function(c,f){c+="",f===void 0&&(f=r.escapeQuerySpace);try{return r.decode(f?c.replace(/\+/g,"%20"):c)}catch{return c}};var L={encode:"encode",decode:"decode"},F,x=function(c,f){return function(h){try{return r[f](h+"").replace(r.characters[c][f].expression,function(y){return r.characters[c][f].map[y]})}catch{return h}}};for(F in L)r[F+"PathSegment"]=x("pathname",L[F]),r[F+"UrnPathSegment"]=x("urnpath",L[F]);var W=function(c,f,h){return function(y){var g;h?g=function(N){return r[f](r[h](N))}:g=r[f];for(var v=(y+"").split(c),b=0,z=v.length;b<z;b++)v[b]=g(v[b]);return v.join(c)}};r.decodePath=W("/","decodePathSegment"),r.decodeUrnPath=W(":","decodeUrnPathSegment"),r.recodePath=W("/","encodePathSegment","decode"),r.recodeUrnPath=W(":","encodeUrnPathSegment","decode"),r.encodeReserved=x("reserved","encode"),r.parse=function(c,f){var h;return f||(f={preventInvalidHostname:r.preventInvalidHostname}),c=c.replace(r.leading_whitespace_expression,""),c=c.replace(r.ascii_tab_whitespace,""),h=c.indexOf("#"),h>-1&&(f.fragment=c.substring(h+1)||null,c=c.substring(0,h)),h=c.indexOf("?"),h>-1&&(f.query=c.substring(h+1)||null,c=c.substring(0,h)),c=c.replace(/^(https?|ftp|wss?)?:+[/\\]*/i,"$1://"),c=c.replace(/^[/\\]{2,}/i,"//"),c.substring(0,2)==="//"?(f.protocol=null,c=c.substring(2),c=r.parseAuthority(c,f)):(h=c.indexOf(":"),h>-1&&(f.protocol=c.substring(0,h)||null,f.protocol&&!f.protocol.match(r.protocol_expression)?f.protocol=void 0:c.substring(h+1,h+3).replace(/\\/g,"/")==="//"?(c=c.substring(h+3),c=r.parseAuthority(c,f)):(c=c.substring(h+1),f.urn=!0))),f.path=c,f},r.parseHost=function(c,f){c||(c=""),c=c.replace(/\\/g,"/");var h=c.indexOf("/"),y,g;if(h===-1&&(h=c.length),c.charAt(0)==="[")y=c.indexOf("]"),f.hostname=c.substring(1,y)||null,f.port=c.substring(y+2,h)||null,f.port==="/"&&(f.port=null);else{var v=c.indexOf(":"),b=c.indexOf("/"),z=c.indexOf(":",v+1);z!==-1&&(b===-1||z<b)?(f.hostname=c.substring(0,h)||null,f.port=null):(g=c.substring(0,h).split(":"),f.hostname=g[0]||null,f.port=g[1]||null)}return f.hostname&&c.substring(h).charAt(0)!=="/"&&(h++,c="/"+c),f.preventInvalidHostname&&r.ensureValidHostname(f.hostname,f.protocol),f.port&&r.ensureValidPort(f.port),c.substring(h)||"/"},r.parseAuthority=function(c,f){return c=r.parseUserinfo(c,f),r.parseHost(c,f)},r.parseUserinfo=function(c,f){var h=c,y=c.indexOf("\\");y!==-1&&(c=c.replace(/\\/g,"/"));var g=c.indexOf("/"),v=c.lastIndexOf("@",g>-1?g:c.length-1),b;return v>-1&&(g===-1||v<g)?(b=c.substring(0,v).split(":"),f.username=b[0]?r.decode(b[0]):null,b.shift(),f.password=b[0]?r.decode(b.join(":")):null,c=h.substring(v+1)):(f.username=null,f.password=null),c},r.parseQuery=function(c,f){if(!c)return{};if(c=c.replace(/&+/g,"&").replace(/^\?*&*|&+$/g,""),!c)return{};for(var h={},y=c.split("&"),g=y.length,v,b,z,N=0;N<g;N++)v=y[N].split("="),b=r.decodeQuery(v.shift(),f),z=v.length?r.decodeQuery(v.join("="),f):null,b!=="__proto__"&&(d.call(h,b)?((typeof h[b]=="string"||h[b]===null)&&(h[b]=[h[b]]),h[b].push(z)):h[b]=z);return h},r.build=function(c){var f="",h=!1;return c.protocol&&(f+=c.protocol+":"),!c.urn&&(f||c.hostname)&&(f+="//",h=!0),f+=r.buildAuthority(c)||"",typeof c.path=="string"&&(c.path.charAt(0)!=="/"&&h&&(f+="/"),f+=c.path),typeof c.query=="string"&&c.query&&(f+="?"+c.query),typeof c.fragment=="string"&&c.fragment&&(f+="#"+c.fragment),f},r.buildHost=function(c){var f="";if(c.hostname)r.ip6_expression.test(c.hostname)?f+="["+c.hostname+"]":f+=c.hostname;else return"";return c.port&&(f+=":"+c.port),f},r.buildAuthority=function(c){return r.buildUserinfo(c)+r.buildHost(c)},r.buildUserinfo=function(c){var f="";return c.username&&(f+=r.encode(c.username)),c.password&&(f+=":"+r.encode(c.password)),f&&(f+="@"),f},r.buildQuery=function(c,f,h){var y="",g,v,b,z;for(v in c)if(v!=="__proto__"&&d.call(c,v))if(w(c[v]))for(g={},b=0,z=c[v].length;b<z;b++)c[v][b]!==void 0&&g[c[v][b]+""]===void 0&&(y+="&"+r.buildQueryParameter(v,c[v][b],h),f!==!0&&(g[c[v][b]+""]=!0));else c[v]!==void 0&&(y+="&"+r.buildQueryParameter(v,c[v],h));return y.substring(1)},r.buildQueryParameter=function(c,f,h){return r.encodeQuery(c,h)+(f!==null?"="+r.encodeQuery(f,h):"")},r.addQuery=function(c,f,h){if(typeof f=="object")for(var y in f)d.call(f,y)&&r.addQuery(c,y,f[y]);else if(typeof f=="string"){if(c[f]===void 0){c[f]=h;return}else typeof c[f]=="string"&&(c[f]=[c[f]]);w(h)||(h=[h]),c[f]=(c[f]||[]).concat(h)}else throw new TypeError("URI.addQuery() accepts an object, string as the name parameter")},r.setQuery=function(c,f,h){if(typeof f=="object")for(var y in f)d.call(f,y)&&r.setQuery(c,y,f[y]);else if(typeof f=="string")c[f]=h===void 0?null:h;else throw new TypeError("URI.setQuery() accepts an object, string as the name parameter")},r.removeQuery=function(c,f,h){var y,g,v;if(w(f))for(y=0,g=f.length;y<g;y++)c[f[y]]=void 0;else if(l(f)==="RegExp")for(v in c)f.test(v)&&(c[v]=void 0);else if(typeof f=="object")for(v in f)d.call(f,v)&&r.removeQuery(c,v,f[v]);else if(typeof f=="string")h!==void 0?l(h)==="RegExp"?!w(c[f])&&h.test(c[f])?c[f]=void 0:c[f]=T(c[f],h):c[f]===String(h)&&(!w(h)||h.length===1)?c[f]=void 0:w(c[f])&&(c[f]=T(c[f],h)):c[f]=void 0;else throw new TypeError("URI.removeQuery() accepts an object, string, RegExp as the first parameter")},r.hasQuery=function(c,f,h,y){switch(l(f)){case"String":break;case"RegExp":for(var g in c)if(d.call(c,g)&&f.test(g)&&(h===void 0||r.hasQuery(c,g,h)))return!0;return!1;case"Object":for(var v in f)if(d.call(f,v)&&!r.hasQuery(c,v,f[v]))return!1;return!0;default:throw new TypeError("URI.hasQuery() accepts a string, regular expression or object as the name parameter")}switch(l(h)){case"Undefined":return f in c;case"Boolean":var b=!!(w(c[f])?c[f].length:c[f]);return h===b;case"Function":return!!h(c[f],f,c);case"Array":if(!w(c[f]))return!1;var z=y?R:P;return z(c[f],h);case"RegExp":return w(c[f])?y?R(c[f],h):!1:!!(c[f]&&c[f].match(h));case"Number":h=String(h);case"String":return w(c[f])?y?R(c[f],h):!1:c[f]===h;default:throw new TypeError("URI.hasQuery() accepts undefined, boolean, string, number, RegExp, Function as the value parameter")}},r.joinPaths=function(){for(var c=[],f=[],h=0,y=0;y<arguments.length;y++){var g=new r(arguments[y]);c.push(g);for(var v=g.segment(),b=0;b<v.length;b++)typeof v[b]=="string"&&f.push(v[b]),v[b]&&h++}if(!f.length||!h)return new r("");var z=new r("").segment(f);return(c[0].path()===""||c[0].path().slice(0,1)==="/")&&z.path("/"+z.path()),z.normalize()},r.commonPath=function(c,f){var h=Math.min(c.length,f.length),y;for(y=0;y<h;y++)if(c.charAt(y)!==f.charAt(y)){y--;break}return y<1?c.charAt(0)===f.charAt(0)&&c.charAt(0)==="/"?"/":"":((c.charAt(y)!=="/"||f.charAt(y)!=="/")&&(y=c.substring(0,y).lastIndexOf("/")),c.substring(0,y+1))},r.withinString=function(c,f,h){h||(h={});var y=h.start||r.findUri.start,g=h.end||r.findUri.end,v=h.trim||r.findUri.trim,b=h.parens||r.findUri.parens,z=/[a-z0-9-]=["']?$/i;for(y.lastIndex=0;;){var N=y.exec(c);if(!N)break;var V=N.index;if(h.ignoreHtml){var ie=c.slice(Math.max(V-3,0),V);if(ie&&z.test(ie))continue}for(var ae=V+c.slice(V).search(g),ne=c.slice(V,ae),ue=-1;;){var he=b.exec(ne);if(!he)break;var ye=he.index+he[0].length;ue=Math.max(ue,ye)}if(ue>-1?ne=ne.slice(0,ue)+ne.slice(ue).replace(v,""):ne=ne.replace(v,""),!(ne.length<=N[0].length)&&!(h.ignore&&h.ignore.test(ne))){ae=V+ne.length;var de=f(ne,V,ae,c);if(de===void 0){y.lastIndex=ae;continue}de=String(de),c=c.slice(0,V)+de+c.slice(ae),y.lastIndex=V+de.length}}return y.lastIndex=0,c},r.ensureValidHostname=function(c,f){var h=!!c,y=!!f,g=!1;if(y&&(g=R(r.hostProtocols,f)),g&&!h)throw new TypeError("Hostname cannot be empty, if protocol is "+f);if(c&&c.match(r.invalid_hostname_characters)){if(!e)throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-:_] and Punycode.js is not available');if(e.toASCII(c).match(r.invalid_hostname_characters))throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-:_]')}},r.ensureValidPort=function(c){if(c){var f=Number(c);if(!(a(f)&&f>0&&f<65536))throw new TypeError('Port "'+c+'" is not a valid port')}},r.noConflict=function(c){if(c){var f={URI:this.noConflict()};return o.URITemplate&&typeof o.URITemplate.noConflict=="function"&&(f.URITemplate=o.URITemplate.noConflict()),o.IPv6&&typeof o.IPv6.noConflict=="function"&&(f.IPv6=o.IPv6.noConflict()),o.SecondLevelDomains&&typeof o.SecondLevelDomains.noConflict=="function"&&(f.SecondLevelDomains=o.SecondLevelDomains.noConflict()),f}else o.URI===this&&(o.URI=i);return this},u.build=function(c){return c===!0?this._deferred_build=!0:(c===void 0||this._deferred_build)&&(this._string=r.build(this._parts),this._deferred_build=!1),this},u.clone=function(){return new r(this)},u.valueOf=u.toString=function(){return this.build(!1)._string};function Q(c){return function(f,h){return f===void 0?this._parts[c]||"":(this._parts[c]=f||null,this.build(!h),this)}}function ee(c,f){return function(h,y){return h===void 0?this._parts[c]||"":(h!==null&&(h=h+"",h.charAt(0)===f&&(h=h.substring(1))),this._parts[c]=h,this.build(!y),this)}}u.protocol=Q("protocol"),u.username=Q("username"),u.password=Q("password"),u.hostname=Q("hostname"),u.port=Q("port"),u.query=ee("query","?"),u.fragment=ee("fragment","#"),u.search=function(c,f){var h=this.query(c,f);return typeof h=="string"&&h.length?"?"+h:h},u.hash=function(c,f){var h=this.fragment(c,f);return typeof h=="string"&&h.length?"#"+h:h},u.pathname=function(c,f){if(c===void 0||c===!0){var h=this._parts.path||(this._parts.hostname?"/":"");return c?(this._parts.urn?r.decodeUrnPath:r.decodePath)(h):h}else return this._parts.urn?this._parts.path=c?r.recodeUrnPath(c):"":this._parts.path=c?r.recodePath(c):"/",this.build(!f),this},u.path=u.pathname,u.href=function(c,f){var h;if(c===void 0)return this.toString();this._string="",this._parts=r._parts();var y=c instanceof r,g=typeof c=="object"&&(c.hostname||c.path||c.pathname);if(c.nodeName){var v=r.getDomAttribute(c);c=c[v]||"",g=!1}if(!y&&g&&c.pathname!==void 0&&(c=c.toString()),typeof c=="string"||c instanceof String)this._parts=r.parse(String(c),this._parts);else if(y||g){var b=y?c._parts:c;for(h in b)h!=="query"&&d.call(this._parts,h)&&(this._parts[h]=b[h]);b.query&&this.query(b.query,!1)}else throw new TypeError("invalid input");return this.build(!f),this},u.is=function(c){var f=!1,h=!1,y=!1,g=!1,v=!1,b=!1,z=!1,N=!this._parts.urn;switch(this._parts.hostname&&(N=!1,h=r.ip4_expression.test(this._parts.hostname),y=r.ip6_expression.test(this._parts.hostname),f=h||y,g=!f,v=g&&t&&t.has(this._parts.hostname),b=g&&r.idn_expression.test(this._parts.hostname),z=g&&r.punycode_expression.test(this._parts.hostname)),c.toLowerCase()){case"relative":return N;case"absolute":return!N;case"domain":case"name":return g;case"sld":return v;case"ip":return f;case"ip4":case"ipv4":case"inet4":return h;case"ip6":case"ipv6":case"inet6":return y;case"idn":return b;case"url":return!this._parts.urn;case"urn":return!!this._parts.urn;case"punycode":return z}return null};var re=u.protocol,Z=u.port,oe=u.hostname;u.protocol=function(c,f){if(c&&(c=c.replace(/:(\/\/)?$/,""),!c.match(r.protocol_expression)))throw new TypeError('Protocol "'+c+`" contains characters other than [A-Z0-9.+-] or doesn't start with [A-Z]`);return re.call(this,c,f)},u.scheme=u.protocol,u.port=function(c,f){return this._parts.urn?c===void 0?"":this:(c!==void 0&&(c===0&&(c=null),c&&(c+="",c.charAt(0)===":"&&(c=c.substring(1)),r.ensureValidPort(c))),Z.call(this,c,f))},u.hostname=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c!==void 0){var h={preventInvalidHostname:this._parts.preventInvalidHostname},y=r.parseHost(c,h);if(y!=="/")throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-]');c=h.hostname,this._parts.preventInvalidHostname&&r.ensureValidHostname(c,this._parts.protocol)}return oe.call(this,c,f)},u.origin=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0){var h=this.protocol(),y=this.authority();return y?(h?h+"://":"")+this.authority():""}else{var g=r(c);return this.protocol(g.protocol()).authority(g.authority()).build(!f),this}},u.host=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0)return this._parts.hostname?r.buildHost(this._parts):"";var h=r.parseHost(c,this._parts);if(h!=="/")throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-]');return this.build(!f),this},u.authority=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0)return this._parts.hostname?r.buildAuthority(this._parts):"";var h=r.parseAuthority(c,this._parts);if(h!=="/")throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-]');return this.build(!f),this},u.userinfo=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0){var h=r.buildUserinfo(this._parts);return h&&h.substring(0,h.length-1)}else return c[c.length-1]!=="@"&&(c+="@"),r.parseUserinfo(c,this._parts),this.build(!f),this},u.resource=function(c,f){var h;return c===void 0?this.path()+this.search()+this.hash():(h=r.parse(c),this._parts.path=h.path,this._parts.query=h.query,this._parts.fragment=h.fragment,this.build(!f),this)},u.subdomain=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0){if(!this._parts.hostname||this.is("IP"))return"";var h=this._parts.hostname.length-this.domain().length-1;return this._parts.hostname.substring(0,h)||""}else{var y=this._parts.hostname.length-this.domain().length,g=this._parts.hostname.substring(0,y),v=new RegExp("^"+m(g));if(c&&c.charAt(c.length-1)!=="."&&(c+="."),c.indexOf(":")!==-1)throw new TypeError("Domains cannot contain colons");return c&&r.ensureValidHostname(c,this._parts.protocol),this._parts.hostname=this._parts.hostname.replace(v,c),this.build(!f),this}},u.domain=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(typeof c=="boolean"&&(f=c,c=void 0),c===void 0){if(!this._parts.hostname||this.is("IP"))return"";var h=this._parts.hostname.match(/\./g);if(h&&h.length<2)return this._parts.hostname;var y=this._parts.hostname.length-this.tld(f).length-1;return y=this._parts.hostname.lastIndexOf(".",y-1)+1,this._parts.hostname.substring(y)||""}else{if(!c)throw new TypeError("cannot set domain empty");if(c.indexOf(":")!==-1)throw new TypeError("Domains cannot contain colons");if(r.ensureValidHostname(c,this._parts.protocol),!this._parts.hostname||this.is("IP"))this._parts.hostname=c;else{var g=new RegExp(m(this.domain())+"$");this._parts.hostname=this._parts.hostname.replace(g,c)}return this.build(!f),this}},u.tld=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(typeof c=="boolean"&&(f=c,c=void 0),c===void 0){if(!this._parts.hostname||this.is("IP"))return"";var h=this._parts.hostname.lastIndexOf("."),y=this._parts.hostname.substring(h+1);return f!==!0&&t&&t.list[y.toLowerCase()]&&t.get(this._parts.hostname)||y}else{var g;if(c)if(c.match(/[^a-zA-Z0-9-]/))if(t&&t.is(c))g=new RegExp(m(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(g,c);else throw new TypeError('TLD "'+c+'" contains characters other than [A-Z0-9]');else{if(!this._parts.hostname||this.is("IP"))throw new ReferenceError("cannot set TLD on non-domain host");g=new RegExp(m(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(g,c)}else throw new TypeError("cannot set TLD empty");return this.build(!f),this}},u.directory=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0||c===!0){if(!this._parts.path&&!this._parts.hostname)return"";if(this._parts.path==="/")return"/";var h=this._parts.path.length-this.filename().length-1,y=this._parts.path.substring(0,h)||(this._parts.hostname?"/":"");return c?r.decodePath(y):y}else{var g=this._parts.path.length-this.filename().length,v=this._parts.path.substring(0,g),b=new RegExp("^"+m(v));return this.is("relative")||(c||(c="/"),c.charAt(0)!=="/"&&(c="/"+c)),c&&c.charAt(c.length-1)!=="/"&&(c+="/"),c=r.recodePath(c),this._parts.path=this._parts.path.replace(b,c),this.build(!f),this}},u.filename=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(typeof c!="string"){if(!this._parts.path||this._parts.path==="/")return"";var h=this._parts.path.lastIndexOf("/"),y=this._parts.path.substring(h+1);return c?r.decodePathSegment(y):y}else{var g=!1;c.charAt(0)==="/"&&(c=c.substring(1)),c.match(/\.?\//)&&(g=!0);var v=new RegExp(m(this.filename())+"$");return c=r.recodePath(c),this._parts.path=this._parts.path.replace(v,c),g?this.normalizePath(f):this.build(!f),this}},u.suffix=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0||c===!0){if(!this._parts.path||this._parts.path==="/")return"";var h=this.filename(),y=h.lastIndexOf("."),g,v;return y===-1?"":(g=h.substring(y+1),v=/^[a-z0-9%]+$/i.test(g)?g:"",c?r.decodePathSegment(v):v)}else{c.charAt(0)==="."&&(c=c.substring(1));var b=this.suffix(),z;if(b)c?z=new RegExp(m(b)+"$"):z=new RegExp(m("."+b)+"$");else{if(!c)return this;this._parts.path+="."+r.recodePath(c)}return z&&(c=r.recodePath(c),this._parts.path=this._parts.path.replace(z,c)),this.build(!f),this}},u.segment=function(c,f,h){var y=this._parts.urn?":":"/",g=this.path(),v=g.substring(0,1)==="/",b=g.split(y);if(c!==void 0&&typeof c!="number"&&(h=f,f=c,c=void 0),c!==void 0&&typeof c!="number")throw new Error('Bad segment "'+c+'", must be 0-based integer');if(v&&b.shift(),c<0&&(c=Math.max(b.length+c,0)),f===void 0)return c===void 0?b:b[c];if(c===null||b[c]===void 0)if(w(f)){b=[];for(var z=0,N=f.length;z<N;z++)!f[z].length&&(!b.length||!b[b.length-1].length)||(b.length&&!b[b.length-1].length&&b.pop(),b.push(C(f[z])))}else(f||typeof f=="string")&&(f=C(f),b[b.length-1]===""?b[b.length-1]=f:b.push(f));else f?b[c]=C(f):b.splice(c,1);return v&&b.unshift(""),this.path(b.join(y),h)},u.segmentCoded=function(c,f,h){var y,g,v;if(typeof c!="number"&&(h=f,f=c,c=void 0),f===void 0){if(y=this.segment(c,f,h),!w(y))y=y!==void 0?r.decode(y):void 0;else for(g=0,v=y.length;g<v;g++)y[g]=r.decode(y[g]);return y}if(!w(f))f=typeof f=="string"||f instanceof String?r.encode(f):f;else for(g=0,v=f.length;g<v;g++)f[g]=r.encode(f[g]);return this.segment(c,f,h)};var J=u.query;return u.query=function(c,f){if(c===!0)return r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if(typeof c=="function"){var h=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace),y=c.call(this,h);return this._parts.query=r.buildQuery(y||h,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!f),this}else return c!==void 0&&typeof c!="string"?(this._parts.query=r.buildQuery(c,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!f),this):J.call(this,c,f)},u.setQuery=function(c,f,h){var y=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if(typeof c=="string"||c instanceof String)y[c]=f!==void 0?f:null;else if(typeof c=="object")for(var g in c)d.call(c,g)&&(y[g]=c[g]);else throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");return this._parts.query=r.buildQuery(y,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof c!="string"&&(h=f),this.build(!h),this},u.addQuery=function(c,f,h){var y=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return r.addQuery(y,c,f===void 0?null:f),this._parts.query=r.buildQuery(y,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof c!="string"&&(h=f),this.build(!h),this},u.removeQuery=function(c,f,h){var y=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return r.removeQuery(y,c,f),this._parts.query=r.buildQuery(y,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof c!="string"&&(h=f),this.build(!h),this},u.hasQuery=function(c,f,h){var y=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return r.hasQuery(y,c,f,h)},u.setSearch=u.setQuery,u.addSearch=u.addQuery,u.removeSearch=u.removeQuery,u.hasSearch=u.hasQuery,u.normalize=function(){return this._parts.urn?this.normalizeProtocol(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build():this.normalizeProtocol(!1).normalizeHostname(!1).normalizePort(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build()},u.normalizeProtocol=function(c){return typeof this._parts.protocol=="string"&&(this._parts.protocol=this._parts.protocol.toLowerCase(),this.build(!c)),this},u.normalizeHostname=function(c){return this._parts.hostname&&(this.is("IDN")&&e?this._parts.hostname=e.toASCII(this._parts.hostname):this.is("IPv6")&&n&&(this._parts.hostname=n.best(this._parts.hostname)),this._parts.hostname=this._parts.hostname.toLowerCase(),this.build(!c)),this},u.normalizePort=function(c){return typeof this._parts.protocol=="string"&&this._parts.port===r.defaultPorts[this._parts.protocol]&&(this._parts.port=null,this.build(!c)),this},u.normalizePath=function(c){var f=this._parts.path;if(!f)return this;if(this._parts.urn)return this._parts.path=r.recodeUrnPath(this._parts.path),this.build(!c),this;if(this._parts.path==="/")return this;f=r.recodePath(f);var h,y="",g,v;for(f.charAt(0)!=="/"&&(h=!0,f="/"+f),(f.slice(-3)==="/.."||f.slice(-2)==="/.")&&(f+="/"),f=f.replace(/(\/(\.\/)+)|(\/\.$)/g,"/").replace(/\/{2,}/g,"/"),h&&(y=f.substring(1).match(/^(\.\.\/)+/)||"",y&&(y=y[0]));g=f.search(/\/\.\.(\/|$)/),g!==-1;){if(g===0){f=f.substring(3);continue}v=f.substring(0,g).lastIndexOf("/"),v===-1&&(v=g),f=f.substring(0,v)+f.substring(g+3)}return h&&this.is("relative")&&(f=y+f.substring(1)),this._parts.path=f,this.build(!c),this},u.normalizePathname=u.normalizePath,u.normalizeQuery=function(c){return typeof this._parts.query=="string"&&(this._parts.query.length?this.query(r.parseQuery(this._parts.query,this._parts.escapeQuerySpace)):this._parts.query=null,this.build(!c)),this},u.normalizeFragment=function(c){return this._parts.fragment||(this._parts.fragment=null,this.build(!c)),this},u.normalizeSearch=u.normalizeQuery,u.normalizeHash=u.normalizeFragment,u.iso8859=function(){var c=r.encode,f=r.decode;r.encode=escape,r.decode=decodeURIComponent;try{this.normalize()}finally{r.encode=c,r.decode=f}return this},u.unicode=function(){var c=r.encode,f=r.decode;r.encode=k,r.decode=unescape;try{this.normalize()}finally{r.encode=c,r.decode=f}return this},u.readable=function(){var c=this.clone();c.username("").password("").normalize();var f="";if(c._parts.protocol&&(f+=c._parts.protocol+"://"),c._parts.hostname&&(c.is("punycode")&&e?(f+=e.toUnicode(c._parts.hostname),c._parts.port&&(f+=":"+c._parts.port)):f+=c.host()),c._parts.hostname&&c._parts.path&&c._parts.path.charAt(0)!=="/"&&(f+="/"),f+=c.path(!0),c._parts.query){for(var h="",y=0,g=c._parts.query.split("&"),v=g.length;y<v;y++){var b=(g[y]||"").split("=");h+="&"+r.decodeQuery(b[0],this._parts.escapeQuerySpace).replace(/&/g,"%26"),b[1]!==void 0&&(h+="="+r.decodeQuery(b[1],this._parts.escapeQuerySpace).replace(/&/g,"%26"))}f+="?"+h.substring(1)}return f+=r.decodeQuery(c.hash(),!0),f},u.absoluteTo=function(c){var f=this.clone(),h=["protocol","username","password","hostname","port"],y,g,v;if(this._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(c instanceof r||(c=new r(c)),f._parts.protocol||(f._parts.protocol=c._parts.protocol,this._parts.hostname))return f;for(g=0;v=h[g];g++)f._parts[v]=c._parts[v];return f._parts.path?(f._parts.path.substring(-2)===".."&&(f._parts.path+="/"),f.path().charAt(0)!=="/"&&(y=c.directory(),y=y||(c.path().indexOf("/")===0?"/":""),f._parts.path=(y?y+"/":"")+f._parts.path,f.normalizePath())):(f._parts.path=c._parts.path,f._parts.query||(f._parts.query=c._parts.query)),f.build(),f},u.relativeTo=function(c){var f=this.clone().normalize(),h,y,g,v,b;if(f._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(c=new r(c).normalize(),h=f._parts,y=c._parts,v=f.path(),b=c.path(),v.charAt(0)!=="/")throw new Error("URI is already relative");if(b.charAt(0)!=="/")throw new Error("Cannot calculate a URI relative to another relative URI");if(h.protocol===y.protocol&&(h.protocol=null),h.username!==y.username||h.password!==y.password||h.protocol!==null||h.username!==null||h.password!==null)return f.build();if(h.hostname===y.hostname&&h.port===y.port)h.hostname=null,h.port=null;else return f.build();if(v===b)return h.path="",f.build();if(g=r.commonPath(v,b),!g)return f.build();var z=y.path.substring(g.length).replace(/[^\/]*$/,"").replace(/.*?\//g,"../");return h.path=z+h.path.substring(g.length)||"./",f.build()},u.equals=function(c){var f=this.clone(),h=new r(c),y={},g={},v={},b,z,N;if(f.normalize(),h.normalize(),f.toString()===h.toString())return!0;if(b=f.query(),z=h.query(),f.query(""),h.query(""),f.toString()!==h.toString()||b.length!==z.length)return!1;y=r.parseQuery(b,this._parts.escapeQuerySpace),g=r.parseQuery(z,this._parts.escapeQuerySpace);for(N in y)if(d.call(y,N)){if(w(y[N])){if(!P(y[N],g[N]))return!1}else if(y[N]!==g[N])return!1;v[N]=!0}for(N in g)if(d.call(g,N)&&!v[N])return!1;return!0},u.preventInvalidHostname=function(c){return this._parts.preventInvalidHostname=!!c,this},u.duplicateQueryParameters=function(c){return this._parts.duplicateQueryParameters=!!c,this},u.escapeQuerySpace=function(c){return this._parts.escapeQuerySpace=!!c,this},r})});function j(e,n,t,o){this.x=O(e,0),this.y=O(n,0),this.z=O(t,0),this.w=O(o,0)}j.fromElements=function(e,n,t,o,i){return p(i)?(i.x=e,i.y=n,i.z=t,i.w=o,i):new j(e,n,t,o)};j.fromColor=function(e,n){return s.typeOf.object("color",e),p(n)?(n.x=e.red,n.y=e.green,n.z=e.blue,n.w=e.alpha,n):new j(e.red,e.green,e.blue,e.alpha)};j.clone=function(e,n){if(p(e))return p(n)?(n.x=e.x,n.y=e.y,n.z=e.z,n.w=e.w,n):new j(e.x,e.y,e.z,e.w)};j.packedLength=4;j.pack=function(e,n,t){return s.typeOf.object("value",e),s.defined("array",n),t=O(t,0),n[t++]=e.x,n[t++]=e.y,n[t++]=e.z,n[t]=e.w,n};j.unpack=function(e,n,t){return s.defined("array",e),n=O(n,0),p(t)||(t=new j),t.x=e[n++],t.y=e[n++],t.z=e[n++],t.w=e[n],t};j.packArray=function(e,n){s.defined("array",e);let t=e.length,o=t*4;if(!p(n))n=new Array(o);else{if(!Array.isArray(n)&&n.length!==o)throw new A("If result is a typed array, it must have exactly array.length * 4 elements");n.length!==o&&(n.length=o)}for(let i=0;i<t;++i)j.pack(e[i],n,i*4);return n};j.unpackArray=function(e,n){if(s.defined("array",e),s.typeOf.number.greaterThanOrEquals("array.length",e.length,4),e.length%4!==0)throw new A("array length must be a multiple of 4.");let t=e.length;p(n)?n.length=t/4:n=new Array(t/4);for(let o=0;o<t;o+=4){let i=o/4;n[i]=j.unpack(e,o,n[i])}return n};j.fromArray=j.unpack;j.maximumComponent=function(e){return s.typeOf.object("cartesian",e),Math.max(e.x,e.y,e.z,e.w)};j.minimumComponent=function(e){return s.typeOf.object("cartesian",e),Math.min(e.x,e.y,e.z,e.w)};j.minimumByComponent=function(e,n,t){return s.typeOf.object("first",e),s.typeOf.object("second",n),s.typeOf.object("result",t),t.x=Math.min(e.x,n.x),t.y=Math.min(e.y,n.y),t.z=Math.min(e.z,n.z),t.w=Math.min(e.w,n.w),t};j.maximumByComponent=function(e,n,t){return s.typeOf.object("first",e),s.typeOf.object("second",n),s.typeOf.object("result",t),t.x=Math.max(e.x,n.x),t.y=Math.max(e.y,n.y),t.z=Math.max(e.z,n.z),t.w=Math.max(e.w,n.w),t};j.clamp=function(e,n,t,o){s.typeOf.object("value",e),s.typeOf.object("min",n),s.typeOf.object("max",t),s.typeOf.object("result",o);let i=I.clamp(e.x,n.x,t.x),r=I.clamp(e.y,n.y,t.y),a=I.clamp(e.z,n.z,t.z),u=I.clamp(e.w,n.w,t.w);return o.x=i,o.y=r,o.z=a,o.w=u,o};j.magnitudeSquared=function(e){return s.typeOf.object("cartesian",e),e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w};j.magnitude=function(e){return Math.sqrt(j.magnitudeSquared(e))};var Tn=new j;j.distance=function(e,n){return s.typeOf.object("left",e),s.typeOf.object("right",n),j.subtract(e,n,Tn),j.magnitude(Tn)};j.distanceSquared=function(e,n){return s.typeOf.object("left",e),s.typeOf.object("right",n),j.subtract(e,n,Tn),j.magnitudeSquared(Tn)};j.normalize=function(e,n){s.typeOf.object("cartesian",e),s.typeOf.object("result",n);let t=j.magnitude(e);if(n.x=e.x/t,n.y=e.y/t,n.z=e.z/t,n.w=e.w/t,isNaN(n.x)||isNaN(n.y)||isNaN(n.z)||isNaN(n.w))throw new A("normalized result is not a number");return n};j.dot=function(e,n){return s.typeOf.object("left",e),s.typeOf.object("right",n),e.x*n.x+e.y*n.y+e.z*n.z+e.w*n.w};j.multiplyComponents=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t.x=e.x*n.x,t.y=e.y*n.y,t.z=e.z*n.z,t.w=e.w*n.w,t};j.divideComponents=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t.x=e.x/n.x,t.y=e.y/n.y,t.z=e.z/n.z,t.w=e.w/n.w,t};j.add=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t.x=e.x+n.x,t.y=e.y+n.y,t.z=e.z+n.z,t.w=e.w+n.w,t};j.subtract=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t.x=e.x-n.x,t.y=e.y-n.y,t.z=e.z-n.z,t.w=e.w-n.w,t};j.multiplyByScalar=function(e,n,t){return s.typeOf.object("cartesian",e),s.typeOf.number("scalar",n),s.typeOf.object("result",t),t.x=e.x*n,t.y=e.y*n,t.z=e.z*n,t.w=e.w*n,t};j.divideByScalar=function(e,n,t){return s.typeOf.object("cartesian",e),s.typeOf.number("scalar",n),s.typeOf.object("result",t),t.x=e.x/n,t.y=e.y/n,t.z=e.z/n,t.w=e.w/n,t};j.negate=function(e,n){return s.typeOf.object("cartesian",e),s.typeOf.object("result",n),n.x=-e.x,n.y=-e.y,n.z=-e.z,n.w=-e.w,n};j.abs=function(e,n){return s.typeOf.object("cartesian",e),s.typeOf.object("result",n),n.x=Math.abs(e.x),n.y=Math.abs(e.y),n.z=Math.abs(e.z),n.w=Math.abs(e.w),n};var Et=new j;j.lerp=function(e,n,t,o){return s.typeOf.object("start",e),s.typeOf.object("end",n),s.typeOf.number("t",t),s.typeOf.object("result",o),j.multiplyByScalar(n,t,Et),o=j.multiplyByScalar(e,1-t,o),j.add(Et,o,o)};var Uo=new j;j.mostOrthogonalAxis=function(e,n){s.typeOf.object("cartesian",e),s.typeOf.object("result",n);let t=j.normalize(e,Uo);return j.abs(t,t),t.x<=t.y?t.x<=t.z?t.x<=t.w?n=j.clone(j.UNIT_X,n):n=j.clone(j.UNIT_W,n):t.z<=t.w?n=j.clone(j.UNIT_Z,n):n=j.clone(j.UNIT_W,n):t.y<=t.z?t.y<=t.w?n=j.clone(j.UNIT_Y,n):n=j.clone(j.UNIT_W,n):t.z<=t.w?n=j.clone(j.UNIT_Z,n):n=j.clone(j.UNIT_W,n),n};j.equals=function(e,n){return e===n||p(e)&&p(n)&&e.x===n.x&&e.y===n.y&&e.z===n.z&&e.w===n.w};j.equalsArray=function(e,n,t){return e.x===n[t]&&e.y===n[t+1]&&e.z===n[t+2]&&e.w===n[t+3]};j.equalsEpsilon=function(e,n,t,o){return e===n||p(e)&&p(n)&&I.equalsEpsilon(e.x,n.x,t,o)&&I.equalsEpsilon(e.y,n.y,t,o)&&I.equalsEpsilon(e.z,n.z,t,o)&&I.equalsEpsilon(e.w,n.w,t,o)};j.ZERO=Object.freeze(new j(0,0,0,0));j.ONE=Object.freeze(new j(1,1,1,1));j.UNIT_X=Object.freeze(new j(1,0,0,0));j.UNIT_Y=Object.freeze(new j(0,1,0,0));j.UNIT_Z=Object.freeze(new j(0,0,1,0));j.UNIT_W=Object.freeze(new j(0,0,0,1));j.prototype.clone=function(e){return j.clone(this,e)};j.prototype.equals=function(e){return j.equals(this,e)};j.prototype.equalsEpsilon=function(e,n,t){return j.equalsEpsilon(this,e,n,t)};j.prototype.toString=function(){return`(${this.x}, ${this.y}, ${this.z}, ${this.w})`};var Xn=new Float32Array(1),le=new Uint8Array(Xn.buffer),zo=new Uint32Array([287454020]),Io=new Uint8Array(zo.buffer),Tt=Io[0]===68;j.packFloat=function(e,n){return s.typeOf.number("value",e),p(n)||(n=new j),Xn[0]=e,Tt?(n.x=le[0],n.y=le[1],n.z=le[2],n.w=le[3]):(n.x=le[3],n.y=le[2],n.z=le[1],n.w=le[0]),n};j.unpackFloat=function(e){return s.typeOf.object("packedFloat",e),Tt?(le[0]=e.x,le[1]=e.y,le[2]=e.z,le[3]=e.w):(le[0]=e.w,le[1]=e.z,le[2]=e.y,le[3]=e.x),Xn[0]};var qe=j;function S(e,n,t,o,i,r,a,u,d,m,l,w,T,R,P,C){this[0]=O(e,0),this[1]=O(i,0),this[2]=O(d,0),this[3]=O(T,0),this[4]=O(n,0),this[5]=O(r,0),this[6]=O(m,0),this[7]=O(R,0),this[8]=O(t,0),this[9]=O(a,0),this[10]=O(l,0),this[11]=O(P,0),this[12]=O(o,0),this[13]=O(u,0),this[14]=O(w,0),this[15]=O(C,0)}S.packedLength=16;S.pack=function(e,n,t){return s.typeOf.object("value",e),s.defined("array",n),t=O(t,0),n[t++]=e[0],n[t++]=e[1],n[t++]=e[2],n[t++]=e[3],n[t++]=e[4],n[t++]=e[5],n[t++]=e[6],n[t++]=e[7],n[t++]=e[8],n[t++]=e[9],n[t++]=e[10],n[t++]=e[11],n[t++]=e[12],n[t++]=e[13],n[t++]=e[14],n[t]=e[15],n};S.unpack=function(e,n,t){return s.defined("array",e),n=O(n,0),p(t)||(t=new S),t[0]=e[n++],t[1]=e[n++],t[2]=e[n++],t[3]=e[n++],t[4]=e[n++],t[5]=e[n++],t[6]=e[n++],t[7]=e[n++],t[8]=e[n++],t[9]=e[n++],t[10]=e[n++],t[11]=e[n++],t[12]=e[n++],t[13]=e[n++],t[14]=e[n++],t[15]=e[n],t};S.packArray=function(e,n){s.defined("array",e);let t=e.length,o=t*16;if(!p(n))n=new Array(o);else{if(!Array.isArray(n)&&n.length!==o)throw new A("If result is a typed array, it must have exactly array.length * 16 elements");n.length!==o&&(n.length=o)}for(let i=0;i<t;++i)S.pack(e[i],n,i*16);return n};S.unpackArray=function(e,n){if(s.defined("array",e),s.typeOf.number.greaterThanOrEquals("array.length",e.length,16),e.length%16!==0)throw new A("array length must be a multiple of 16.");let t=e.length;p(n)?n.length=t/16:n=new Array(t/16);for(let o=0;o<t;o+=16){let i=o/16;n[i]=S.unpack(e,o,n[i])}return n};S.clone=function(e,n){if(p(e))return p(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n[9]=e[9],n[10]=e[10],n[11]=e[11],n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15],n):new S(e[0],e[4],e[8],e[12],e[1],e[5],e[9],e[13],e[2],e[6],e[10],e[14],e[3],e[7],e[11],e[15])};S.fromArray=S.unpack;S.fromColumnMajorArray=function(e,n){return s.defined("values",e),S.clone(e,n)};S.fromRowMajorArray=function(e,n){return s.defined("values",e),p(n)?(n[0]=e[0],n[1]=e[4],n[2]=e[8],n[3]=e[12],n[4]=e[1],n[5]=e[5],n[6]=e[9],n[7]=e[13],n[8]=e[2],n[9]=e[6],n[10]=e[10],n[11]=e[14],n[12]=e[3],n[13]=e[7],n[14]=e[11],n[15]=e[15],n):new S(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15])};S.fromRotationTranslation=function(e,n,t){return s.typeOf.object("rotation",e),n=O(n,_.ZERO),p(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=0,t[4]=e[3],t[5]=e[4],t[6]=e[5],t[7]=0,t[8]=e[6],t[9]=e[7],t[10]=e[8],t[11]=0,t[12]=n.x,t[13]=n.y,t[14]=n.z,t[15]=1,t):new S(e[0],e[3],e[6],n.x,e[1],e[4],e[7],n.y,e[2],e[5],e[8],n.z,0,0,0,1)};S.fromTranslationQuaternionRotationScale=function(e,n,t,o){s.typeOf.object("translation",e),s.typeOf.object("rotation",n),s.typeOf.object("scale",t),p(o)||(o=new S);let i=t.x,r=t.y,a=t.z,u=n.x*n.x,d=n.x*n.y,m=n.x*n.z,l=n.x*n.w,w=n.y*n.y,T=n.y*n.z,R=n.y*n.w,P=n.z*n.z,C=n.z*n.w,q=n.w*n.w,k=u-w-P+q,L=2*(d-C),F=2*(m+R),x=2*(d+C),W=-u+w-P+q,Q=2*(T-l),ee=2*(m-R),re=2*(T+l),Z=-u-w+P+q;return o[0]=k*i,o[1]=x*i,o[2]=ee*i,o[3]=0,o[4]=L*r,o[5]=W*r,o[6]=re*r,o[7]=0,o[8]=F*a,o[9]=Q*a,o[10]=Z*a,o[11]=0,o[12]=e.x,o[13]=e.y,o[14]=e.z,o[15]=1,o};S.fromTranslationRotationScale=function(e,n){return s.typeOf.object("translationRotationScale",e),S.fromTranslationQuaternionRotationScale(e.translation,e.rotation,e.scale,n)};S.fromTranslation=function(e,n){return s.typeOf.object("translation",e),S.fromRotationTranslation($.IDENTITY,e,n)};S.fromScale=function(e,n){return s.typeOf.object("scale",e),p(n)?(n[0]=e.x,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=e.y,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=e.z,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n):new S(e.x,0,0,0,0,e.y,0,0,0,0,e.z,0,0,0,0,1)};S.fromUniformScale=function(e,n){return s.typeOf.number("scale",e),p(n)?(n[0]=e,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=e,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=e,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n):new S(e,0,0,0,0,e,0,0,0,0,e,0,0,0,0,1)};S.fromRotation=function(e,n){return s.typeOf.object("rotation",e),p(n)||(n=new S),n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=0,n[4]=e[3],n[5]=e[4],n[6]=e[5],n[7]=0,n[8]=e[6],n[9]=e[7],n[10]=e[8],n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n};var Je=new _,Ge=new _,pn=new _;S.fromCamera=function(e,n){s.typeOf.object("camera",e);let t=e.position,o=e.direction,i=e.up;s.typeOf.object("camera.position",t),s.typeOf.object("camera.direction",o),s.typeOf.object("camera.up",i),_.normalize(o,Je),_.normalize(_.cross(Je,i,Ge),Ge),_.normalize(_.cross(Ge,Je,pn),pn);let r=Ge.x,a=Ge.y,u=Ge.z,d=Je.x,m=Je.y,l=Je.z,w=pn.x,T=pn.y,R=pn.z,P=t.x,C=t.y,q=t.z,k=r*-P+a*-C+u*-q,L=w*-P+T*-C+R*-q,F=d*P+m*C+l*q;return p(n)?(n[0]=r,n[1]=w,n[2]=-d,n[3]=0,n[4]=a,n[5]=T,n[6]=-m,n[7]=0,n[8]=u,n[9]=R,n[10]=-l,n[11]=0,n[12]=k,n[13]=L,n[14]=F,n[15]=1,n):new S(r,a,u,k,w,T,R,L,-d,-m,-l,F,0,0,0,1)};S.computePerspectiveFieldOfView=function(e,n,t,o,i){s.typeOf.number.greaterThan("fovY",e,0),s.typeOf.number.lessThan("fovY",e,Math.PI),s.typeOf.number.greaterThan("near",t,0),s.typeOf.number.greaterThan("far",o,0),s.typeOf.object("result",i);let a=1/Math.tan(e*.5),u=a/n,d=(o+t)/(t-o),m=2*o*t/(t-o);return i[0]=u,i[1]=0,i[2]=0,i[3]=0,i[4]=0,i[5]=a,i[6]=0,i[7]=0,i[8]=0,i[9]=0,i[10]=d,i[11]=-1,i[12]=0,i[13]=0,i[14]=m,i[15]=0,i};S.computeOrthographicOffCenter=function(e,n,t,o,i,r,a){s.typeOf.number("left",e),s.typeOf.number("right",n),s.typeOf.number("bottom",t),s.typeOf.number("top",o),s.typeOf.number("near",i),s.typeOf.number("far",r),s.typeOf.object("result",a);let u=1/(n-e),d=1/(o-t),m=1/(r-i),l=-(n+e)*u,w=-(o+t)*d,T=-(r+i)*m;return u*=2,d*=2,m*=-2,a[0]=u,a[1]=0,a[2]=0,a[3]=0,a[4]=0,a[5]=d,a[6]=0,a[7]=0,a[8]=0,a[9]=0,a[10]=m,a[11]=0,a[12]=l,a[13]=w,a[14]=T,a[15]=1,a};S.computePerspectiveOffCenter=function(e,n,t,o,i,r,a){s.typeOf.number("left",e),s.typeOf.number("right",n),s.typeOf.number("bottom",t),s.typeOf.number("top",o),s.typeOf.number("near",i),s.typeOf.number("far",r),s.typeOf.object("result",a);let u=2*i/(n-e),d=2*i/(o-t),m=(n+e)/(n-e),l=(o+t)/(o-t),w=-(r+i)/(r-i),T=-1,R=-2*r*i/(r-i);return a[0]=u,a[1]=0,a[2]=0,a[3]=0,a[4]=0,a[5]=d,a[6]=0,a[7]=0,a[8]=m,a[9]=l,a[10]=w,a[11]=T,a[12]=0,a[13]=0,a[14]=R,a[15]=0,a};S.computeInfinitePerspectiveOffCenter=function(e,n,t,o,i,r){s.typeOf.number("left",e),s.typeOf.number("right",n),s.typeOf.number("bottom",t),s.typeOf.number("top",o),s.typeOf.number("near",i),s.typeOf.object("result",r);let a=2*i/(n-e),u=2*i/(o-t),d=(n+e)/(n-e),m=(o+t)/(o-t),l=-1,w=-1,T=-2*i;return r[0]=a,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=u,r[6]=0,r[7]=0,r[8]=d,r[9]=m,r[10]=l,r[11]=w,r[12]=0,r[13]=0,r[14]=T,r[15]=0,r};S.computeViewportTransformation=function(e,n,t,o){p(o)||(o=new S),e=O(e,O.EMPTY_OBJECT);let i=O(e.x,0),r=O(e.y,0),a=O(e.width,0),u=O(e.height,0);n=O(n,0),t=O(t,1);let d=a*.5,m=u*.5,l=(t-n)*.5,w=d,T=m,R=l,P=i+d,C=r+m,q=n+l,k=1;return o[0]=w,o[1]=0,o[2]=0,o[3]=0,o[4]=0,o[5]=T,o[6]=0,o[7]=0,o[8]=0,o[9]=0,o[10]=R,o[11]=0,o[12]=P,o[13]=C,o[14]=q,o[15]=k,o};S.computeView=function(e,n,t,o,i){return s.typeOf.object("position",e),s.typeOf.object("direction",n),s.typeOf.object("up",t),s.typeOf.object("right",o),s.typeOf.object("result",i),i[0]=o.x,i[1]=t.x,i[2]=-n.x,i[3]=0,i[4]=o.y,i[5]=t.y,i[6]=-n.y,i[7]=0,i[8]=o.z,i[9]=t.z,i[10]=-n.z,i[11]=0,i[12]=-_.dot(o,e),i[13]=-_.dot(t,e),i[14]=_.dot(n,e),i[15]=1,i};S.toArray=function(e,n){return s.typeOf.object("matrix",e),p(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n[9]=e[9],n[10]=e[10],n[11]=e[11],n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15],n):[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15]]};S.getElementIndex=function(e,n){return s.typeOf.number.greaterThanOrEquals("row",n,0),s.typeOf.number.lessThanOrEquals("row",n,3),s.typeOf.number.greaterThanOrEquals("column",e,0),s.typeOf.number.lessThanOrEquals("column",e,3),e*4+n};S.getColumn=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,3),s.typeOf.object("result",t);let o=n*4,i=e[o],r=e[o+1],a=e[o+2],u=e[o+3];return t.x=i,t.y=r,t.z=a,t.w=u,t};S.setColumn=function(e,n,t,o){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,3),s.typeOf.object("cartesian",t),s.typeOf.object("result",o),o=S.clone(e,o);let i=n*4;return o[i]=t.x,o[i+1]=t.y,o[i+2]=t.z,o[i+3]=t.w,o};S.getRow=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,3),s.typeOf.object("result",t);let o=e[n],i=e[n+4],r=e[n+8],a=e[n+12];return t.x=o,t.y=i,t.z=r,t.w=a,t};S.setRow=function(e,n,t,o){return s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,3),s.typeOf.object("cartesian",t),s.typeOf.object("result",o),o=S.clone(e,o),o[n]=t.x,o[n+4]=t.y,o[n+8]=t.z,o[n+12]=t.w,o};S.setTranslation=function(e,n,t){return s.typeOf.object("matrix",e),s.typeOf.object("translation",n),s.typeOf.object("result",t),t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=n.x,t[13]=n.y,t[14]=n.z,t[15]=e[15],t};var qo=new _;S.setScale=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("scale",n),s.typeOf.object("result",t);let o=S.getScale(e,qo),i=n.x/o.x,r=n.y/o.y,a=n.z/o.z;return t[0]=e[0]*i,t[1]=e[1]*i,t[2]=e[2]*i,t[3]=e[3],t[4]=e[4]*r,t[5]=e[5]*r,t[6]=e[6]*r,t[7]=e[7],t[8]=e[8]*a,t[9]=e[9]*a,t[10]=e[10]*a,t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t};var Do=new _;S.setUniformScale=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.number("scale",n),s.typeOf.object("result",t);let o=S.getScale(e,Do),i=n/o.x,r=n/o.y,a=n/o.z;return t[0]=e[0]*i,t[1]=e[1]*i,t[2]=e[2]*i,t[3]=e[3],t[4]=e[4]*r,t[5]=e[5]*r,t[6]=e[6]*r,t[7]=e[7],t[8]=e[8]*a,t[9]=e[9]*a,t[10]=e[10]*a,t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t};var Zn=new _;S.getScale=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n.x=_.magnitude(_.fromElements(e[0],e[1],e[2],Zn)),n.y=_.magnitude(_.fromElements(e[4],e[5],e[6],Zn)),n.z=_.magnitude(_.fromElements(e[8],e[9],e[10],Zn)),n};var Rt=new _;S.getMaximumScale=function(e){return S.getScale(e,Rt),_.maximumComponent(Rt)};var No=new _;S.setRotation=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("result",t);let o=S.getScale(e,No);return t[0]=n[0]*o.x,t[1]=n[1]*o.x,t[2]=n[2]*o.x,t[3]=e[3],t[4]=n[3]*o.y,t[5]=n[4]*o.y,t[6]=n[5]*o.y,t[7]=e[7],t[8]=n[6]*o.z,t[9]=n[7]*o.z,t[10]=n[8]*o.z,t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t};var ko=new _;S.getRotation=function(e,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let t=S.getScale(e,ko);return n[0]=e[0]/t.x,n[1]=e[1]/t.x,n[2]=e[2]/t.x,n[3]=e[4]/t.y,n[4]=e[5]/t.y,n[5]=e[6]/t.y,n[6]=e[8]/t.z,n[7]=e[9]/t.z,n[8]=e[10]/t.z,n};S.multiply=function(e,n,t){s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t);let o=e[0],i=e[1],r=e[2],a=e[3],u=e[4],d=e[5],m=e[6],l=e[7],w=e[8],T=e[9],R=e[10],P=e[11],C=e[12],q=e[13],k=e[14],L=e[15],F=n[0],x=n[1],W=n[2],Q=n[3],ee=n[4],re=n[5],Z=n[6],oe=n[7],J=n[8],c=n[9],f=n[10],h=n[11],y=n[12],g=n[13],v=n[14],b=n[15],z=o*F+u*x+w*W+C*Q,N=i*F+d*x+T*W+q*Q,V=r*F+m*x+R*W+k*Q,ie=a*F+l*x+P*W+L*Q,ae=o*ee+u*re+w*Z+C*oe,ne=i*ee+d*re+T*Z+q*oe,ue=r*ee+m*re+R*Z+k*oe,he=a*ee+l*re+P*Z+L*oe,ye=o*J+u*c+w*f+C*h,de=i*J+d*c+T*f+q*h,pe=r*J+m*c+R*f+k*h,Y=a*J+l*c+P*f+L*h,_e=o*y+u*g+w*v+C*b,Re=i*y+d*g+T*v+q*b,je=r*y+m*g+R*v+k*b,fn=a*y+l*g+P*v+L*b;return t[0]=z,t[1]=N,t[2]=V,t[3]=ie,t[4]=ae,t[5]=ne,t[6]=ue,t[7]=he,t[8]=ye,t[9]=de,t[10]=pe,t[11]=Y,t[12]=_e,t[13]=Re,t[14]=je,t[15]=fn,t};S.add=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t[0]=e[0]+n[0],t[1]=e[1]+n[1],t[2]=e[2]+n[2],t[3]=e[3]+n[3],t[4]=e[4]+n[4],t[5]=e[5]+n[5],t[6]=e[6]+n[6],t[7]=e[7]+n[7],t[8]=e[8]+n[8],t[9]=e[9]+n[9],t[10]=e[10]+n[10],t[11]=e[11]+n[11],t[12]=e[12]+n[12],t[13]=e[13]+n[13],t[14]=e[14]+n[14],t[15]=e[15]+n[15],t};S.subtract=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t[0]=e[0]-n[0],t[1]=e[1]-n[1],t[2]=e[2]-n[2],t[3]=e[3]-n[3],t[4]=e[4]-n[4],t[5]=e[5]-n[5],t[6]=e[6]-n[6],t[7]=e[7]-n[7],t[8]=e[8]-n[8],t[9]=e[9]-n[9],t[10]=e[10]-n[10],t[11]=e[11]-n[11],t[12]=e[12]-n[12],t[13]=e[13]-n[13],t[14]=e[14]-n[14],t[15]=e[15]-n[15],t};S.multiplyTransformation=function(e,n,t){s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t);let o=e[0],i=e[1],r=e[2],a=e[4],u=e[5],d=e[6],m=e[8],l=e[9],w=e[10],T=e[12],R=e[13],P=e[14],C=n[0],q=n[1],k=n[2],L=n[4],F=n[5],x=n[6],W=n[8],Q=n[9],ee=n[10],re=n[12],Z=n[13],oe=n[14],J=o*C+a*q+m*k,c=i*C+u*q+l*k,f=r*C+d*q+w*k,h=o*L+a*F+m*x,y=i*L+u*F+l*x,g=r*L+d*F+w*x,v=o*W+a*Q+m*ee,b=i*W+u*Q+l*ee,z=r*W+d*Q+w*ee,N=o*re+a*Z+m*oe+T,V=i*re+u*Z+l*oe+R,ie=r*re+d*Z+w*oe+P;return t[0]=J,t[1]=c,t[2]=f,t[3]=0,t[4]=h,t[5]=y,t[6]=g,t[7]=0,t[8]=v,t[9]=b,t[10]=z,t[11]=0,t[12]=N,t[13]=V,t[14]=ie,t[15]=1,t};S.multiplyByMatrix3=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("rotation",n),s.typeOf.object("result",t);let o=e[0],i=e[1],r=e[2],a=e[4],u=e[5],d=e[6],m=e[8],l=e[9],w=e[10],T=n[0],R=n[1],P=n[2],C=n[3],q=n[4],k=n[5],L=n[6],F=n[7],x=n[8],W=o*T+a*R+m*P,Q=i*T+u*R+l*P,ee=r*T+d*R+w*P,re=o*C+a*q+m*k,Z=i*C+u*q+l*k,oe=r*C+d*q+w*k,J=o*L+a*F+m*x,c=i*L+u*F+l*x,f=r*L+d*F+w*x;return t[0]=W,t[1]=Q,t[2]=ee,t[3]=0,t[4]=re,t[5]=Z,t[6]=oe,t[7]=0,t[8]=J,t[9]=c,t[10]=f,t[11]=0,t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t};S.multiplyByTranslation=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("translation",n),s.typeOf.object("result",t);let o=n.x,i=n.y,r=n.z,a=o*e[0]+i*e[4]+r*e[8]+e[12],u=o*e[1]+i*e[5]+r*e[9]+e[13],d=o*e[2]+i*e[6]+r*e[10]+e[14];return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=a,t[13]=u,t[14]=d,t[15]=e[15],t};S.multiplyByScale=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("scale",n),s.typeOf.object("result",t);let o=n.x,i=n.y,r=n.z;return o===1&&i===1&&r===1?S.clone(e,t):(t[0]=o*e[0],t[1]=o*e[1],t[2]=o*e[2],t[3]=e[3],t[4]=i*e[4],t[5]=i*e[5],t[6]=i*e[6],t[7]=e[7],t[8]=r*e[8],t[9]=r*e[9],t[10]=r*e[10],t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t)};S.multiplyByUniformScale=function(e,n,t){return s.typeOf.object("matrix",e),s.typeOf.number("scale",n),s.typeOf.object("result",t),t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3],t[4]=e[4]*n,t[5]=e[5]*n,t[6]=e[6]*n,t[7]=e[7],t[8]=e[8]*n,t[9]=e[9]*n,t[10]=e[10]*n,t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t};S.multiplyByVector=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("cartesian",n),s.typeOf.object("result",t);let o=n.x,i=n.y,r=n.z,a=n.w,u=e[0]*o+e[4]*i+e[8]*r+e[12]*a,d=e[1]*o+e[5]*i+e[9]*r+e[13]*a,m=e[2]*o+e[6]*i+e[10]*r+e[14]*a,l=e[3]*o+e[7]*i+e[11]*r+e[15]*a;return t.x=u,t.y=d,t.z=m,t.w=l,t};S.multiplyByPointAsVector=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("cartesian",n),s.typeOf.object("result",t);let o=n.x,i=n.y,r=n.z,a=e[0]*o+e[4]*i+e[8]*r,u=e[1]*o+e[5]*i+e[9]*r,d=e[2]*o+e[6]*i+e[10]*r;return t.x=a,t.y=u,t.z=d,t};S.multiplyByPoint=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("cartesian",n),s.typeOf.object("result",t);let o=n.x,i=n.y,r=n.z,a=e[0]*o+e[4]*i+e[8]*r+e[12],u=e[1]*o+e[5]*i+e[9]*r+e[13],d=e[2]*o+e[6]*i+e[10]*r+e[14];return t.x=a,t.y=u,t.z=d,t};S.multiplyByScalar=function(e,n,t){return s.typeOf.object("matrix",e),s.typeOf.number("scalar",n),s.typeOf.object("result",t),t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3]*n,t[4]=e[4]*n,t[5]=e[5]*n,t[6]=e[6]*n,t[7]=e[7]*n,t[8]=e[8]*n,t[9]=e[9]*n,t[10]=e[10]*n,t[11]=e[11]*n,t[12]=e[12]*n,t[13]=e[13]*n,t[14]=e[14]*n,t[15]=e[15]*n,t};S.negate=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n[0]=-e[0],n[1]=-e[1],n[2]=-e[2],n[3]=-e[3],n[4]=-e[4],n[5]=-e[5],n[6]=-e[6],n[7]=-e[7],n[8]=-e[8],n[9]=-e[9],n[10]=-e[10],n[11]=-e[11],n[12]=-e[12],n[13]=-e[13],n[14]=-e[14],n[15]=-e[15],n};S.transpose=function(e,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let t=e[1],o=e[2],i=e[3],r=e[6],a=e[7],u=e[11];return n[0]=e[0],n[1]=e[4],n[2]=e[8],n[3]=e[12],n[4]=t,n[5]=e[5],n[6]=e[9],n[7]=e[13],n[8]=o,n[9]=r,n[10]=e[10],n[11]=e[14],n[12]=i,n[13]=a,n[14]=u,n[15]=e[15],n};S.abs=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n[0]=Math.abs(e[0]),n[1]=Math.abs(e[1]),n[2]=Math.abs(e[2]),n[3]=Math.abs(e[3]),n[4]=Math.abs(e[4]),n[5]=Math.abs(e[5]),n[6]=Math.abs(e[6]),n[7]=Math.abs(e[7]),n[8]=Math.abs(e[8]),n[9]=Math.abs(e[9]),n[10]=Math.abs(e[10]),n[11]=Math.abs(e[11]),n[12]=Math.abs(e[12]),n[13]=Math.abs(e[13]),n[14]=Math.abs(e[14]),n[15]=Math.abs(e[15]),n};S.equals=function(e,n){return e===n||p(e)&&p(n)&&e[12]===n[12]&&e[13]===n[13]&&e[14]===n[14]&&e[0]===n[0]&&e[1]===n[1]&&e[2]===n[2]&&e[4]===n[4]&&e[5]===n[5]&&e[6]===n[6]&&e[8]===n[8]&&e[9]===n[9]&&e[10]===n[10]&&e[3]===n[3]&&e[7]===n[7]&&e[11]===n[11]&&e[15]===n[15]};S.equalsEpsilon=function(e,n,t){return t=O(t,0),e===n||p(e)&&p(n)&&Math.abs(e[0]-n[0])<=t&&Math.abs(e[1]-n[1])<=t&&Math.abs(e[2]-n[2])<=t&&Math.abs(e[3]-n[3])<=t&&Math.abs(e[4]-n[4])<=t&&Math.abs(e[5]-n[5])<=t&&Math.abs(e[6]-n[6])<=t&&Math.abs(e[7]-n[7])<=t&&Math.abs(e[8]-n[8])<=t&&Math.abs(e[9]-n[9])<=t&&Math.abs(e[10]-n[10])<=t&&Math.abs(e[11]-n[11])<=t&&Math.abs(e[12]-n[12])<=t&&Math.abs(e[13]-n[13])<=t&&Math.abs(e[14]-n[14])<=t&&Math.abs(e[15]-n[15])<=t};S.getTranslation=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n.x=e[12],n.y=e[13],n.z=e[14],n};S.getMatrix3=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[4],n[4]=e[5],n[5]=e[6],n[6]=e[8],n[7]=e[9],n[8]=e[10],n};var Fo=new $,Lo=new $,Bo=new qe,xo=new qe(0,0,0,1);S.inverse=function(e,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let t=e[0],o=e[4],i=e[8],r=e[12],a=e[1],u=e[5],d=e[9],m=e[13],l=e[2],w=e[6],T=e[10],R=e[14],P=e[3],C=e[7],q=e[11],k=e[15],L=T*k,F=R*q,x=w*k,W=R*C,Q=w*q,ee=T*C,re=l*k,Z=R*P,oe=l*q,J=T*P,c=l*C,f=w*P,h=L*u+W*d+Q*m-(F*u+x*d+ee*m),y=F*a+re*d+J*m-(L*a+Z*d+oe*m),g=x*a+Z*u+c*m-(W*a+re*u+f*m),v=ee*a+oe*u+f*d-(Q*a+J*u+c*d),b=F*o+x*i+ee*r-(L*o+W*i+Q*r),z=L*t+Z*i+oe*r-(F*t+re*i+J*r),N=W*t+re*o+f*r-(x*t+Z*o+c*r),V=Q*t+J*o+c*i-(ee*t+oe*o+f*i);L=i*m,F=r*d,x=o*m,W=r*u,Q=o*d,ee=i*u,re=t*m,Z=r*a,oe=t*d,J=i*a,c=t*u,f=o*a;let ie=L*C+W*q+Q*k-(F*C+x*q+ee*k),ae=F*P+re*q+J*k-(L*P+Z*q+oe*k),ne=x*P+Z*C+c*k-(W*P+re*C+f*k),ue=ee*P+oe*C+f*q-(Q*P+J*C+c*q),he=x*T+ee*R+F*w-(Q*R+L*w+W*T),ye=oe*R+L*l+Z*T-(re*T+J*R+F*l),de=re*w+f*R+W*l-(c*R+x*l+Z*w),pe=c*T+Q*l+J*w-(oe*w+f*T+ee*l),Y=t*h+o*y+i*g+r*v;if(Math.abs(Y)<I.EPSILON21){if($.equalsEpsilon(S.getMatrix3(e,Fo),Lo,I.EPSILON7)&&qe.equals(S.getRow(e,3,Bo),xo))return n[0]=0,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=0,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=0,n[11]=0,n[12]=-e[12],n[13]=-e[13],n[14]=-e[14],n[15]=1,n;throw new Se("matrix is not invertible because its determinate is zero.")}return Y=1/Y,n[0]=h*Y,n[1]=y*Y,n[2]=g*Y,n[3]=v*Y,n[4]=b*Y,n[5]=z*Y,n[6]=N*Y,n[7]=V*Y,n[8]=ie*Y,n[9]=ae*Y,n[10]=ne*Y,n[11]=ue*Y,n[12]=he*Y,n[13]=ye*Y,n[14]=de*Y,n[15]=pe*Y,n};S.inverseTransformation=function(e,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let t=e[0],o=e[1],i=e[2],r=e[4],a=e[5],u=e[6],d=e[8],m=e[9],l=e[10],w=e[12],T=e[13],R=e[14],P=-t*w-o*T-i*R,C=-r*w-a*T-u*R,q=-d*w-m*T-l*R;return n[0]=t,n[1]=r,n[2]=d,n[3]=0,n[4]=o,n[5]=a,n[6]=m,n[7]=0,n[8]=i,n[9]=u,n[10]=l,n[11]=0,n[12]=P,n[13]=C,n[14]=q,n[15]=1,n};var Wo=new S;S.inverseTranspose=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),S.inverse(S.transpose(e,Wo),n)};S.IDENTITY=Object.freeze(new S(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1));S.ZERO=Object.freeze(new S(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0));S.COLUMN0ROW0=0;S.COLUMN0ROW1=1;S.COLUMN0ROW2=2;S.COLUMN0ROW3=3;S.COLUMN1ROW0=4;S.COLUMN1ROW1=5;S.COLUMN1ROW2=6;S.COLUMN1ROW3=7;S.COLUMN2ROW0=8;S.COLUMN2ROW1=9;S.COLUMN2ROW2=10;S.COLUMN2ROW3=11;S.COLUMN3ROW0=12;S.COLUMN3ROW1=13;S.COLUMN3ROW2=14;S.COLUMN3ROW3=15;Object.defineProperties(S.prototype,{length:{get:function(){return S.packedLength}}});S.prototype.clone=function(e){return S.clone(this,e)};S.prototype.equals=function(e){return S.equals(this,e)};S.equalsArray=function(e,n,t){return e[0]===n[t]&&e[1]===n[t+1]&&e[2]===n[t+2]&&e[3]===n[t+3]&&e[4]===n[t+4]&&e[5]===n[t+5]&&e[6]===n[t+6]&&e[7]===n[t+7]&&e[8]===n[t+8]&&e[9]===n[t+9]&&e[10]===n[t+10]&&e[11]===n[t+11]&&e[12]===n[t+12]&&e[13]===n[t+13]&&e[14]===n[t+14]&&e[15]===n[t+15]};S.prototype.equalsEpsilon=function(e,n){return S.equalsEpsilon(this,e,n)};S.prototype.toString=function(){return`(${this[0]}, ${this[4]}, ${this[8]}, ${this[12]})
(${this[1]}, ${this[5]}, ${this[9]}, ${this[13]})
(${this[2]}, ${this[6]}, ${this[10]}, ${this[14]})
(${this[3]}, ${this[7]}, ${this[11]}, ${this[15]})`};var G=S;function vt(e,n,t){t=O(t,!1);let o={},i=p(e),r=p(n),a,u,d;if(i)for(a in e)e.hasOwnProperty(a)&&(u=e[a],r&&t&&typeof u=="object"&&n.hasOwnProperty(a)?(d=n[a],typeof d=="object"?o[a]=vt(u,d,t):o[a]=u):o[a]=u);if(r)for(a in n)n.hasOwnProperty(a)&&!o.hasOwnProperty(a)&&(d=n[a],o[a]=d);return o}var De=vt;function Qo(e,n,t){s.defined("array",e),s.defined("itemToFind",n),s.defined("comparator",t);let o=0,i=e.length-1,r,a;for(;o<=i;){if(r=~~((o+i)/2),a=t(e[r],n),a<0){o=r+1;continue}if(a>0){i=r-1;continue}return r}return~(i+1)}var xe=Qo;function Ho(e,n,t,o,i){this.xPoleWander=e,this.yPoleWander=n,this.xPoleOffset=t,this.yPoleOffset=o,this.ut1MinusUtc=i}var hn=Ho;function $o(e){if(e===null||isNaN(e))throw new A("year is required and must be a number.");return e%4===0&&e%100!==0||e%400===0}var dn=$o;var Ct=[31,28,31,30,31,30,31,31,30,31,30,31];function Vo(e,n,t,o,i,r,a,u){e=O(e,1),n=O(n,1),t=O(t,1),o=O(o,0),i=O(i,0),r=O(r,0),a=O(a,0),u=O(u,!1),C(),q(),this.year=e,this.month=n,this.day=t,this.hour=o,this.minute=i,this.second=r,this.millisecond=a,this.isLeapSecond=u;function C(){s.typeOf.number.greaterThanOrEquals("Year",e,1),s.typeOf.number.lessThanOrEquals("Year",e,9999),s.typeOf.number.greaterThanOrEquals("Month",n,1),s.typeOf.number.lessThanOrEquals("Month",n,12),s.typeOf.number.greaterThanOrEquals("Day",t,1),s.typeOf.number.lessThanOrEquals("Day",t,31),s.typeOf.number.greaterThanOrEquals("Hour",o,0),s.typeOf.number.lessThanOrEquals("Hour",o,23),s.typeOf.number.greaterThanOrEquals("Minute",i,0),s.typeOf.number.lessThanOrEquals("Minute",i,59),s.typeOf.bool("IsLeapSecond",u),s.typeOf.number.greaterThanOrEquals("Second",r,0),s.typeOf.number.lessThanOrEquals("Second",r,u?60:59),s.typeOf.number.greaterThanOrEquals("Millisecond",a,0),s.typeOf.number.lessThan("Millisecond",a,1e3)}function q(){let k=n===2&&dn(e)?Ct[n-1]+1:Ct[n-1];if(t>k)throw new A("Month and Day represents invalid date")}}var Rn=Vo;function Yo(e,n){this.julianDate=e,this.offset=n}var te=Yo;var Xo={SECONDS_PER_MILLISECOND:.001,SECONDS_PER_MINUTE:60,MINUTES_PER_HOUR:60,HOURS_PER_DAY:24,SECONDS_PER_HOUR:3600,MINUTES_PER_DAY:1440,SECONDS_PER_DAY:86400,DAYS_PER_JULIAN_CENTURY:36525,PICOSECOND:1e-9,MODIFIED_JULIAN_DATE_DIFFERENCE:24000005e-1},ce=Object.freeze(Xo);var Zo={UTC:0,TAI:1},H=Object.freeze(Zo);var jt=new Rn,vn=[31,28,31,30,31,30,31,31,30,31,30,31],Cn=29;function Jn(e,n){return U.compare(e.julianDate,n.julianDate)}var Ke=new te;function jn(e){Ke.julianDate=e;let n=U.leapSeconds,t=xe(n,Ke,Jn);t<0&&(t=~t),t>=n.length&&(t=n.length-1);let o=n[t].offset;t>0&&U.secondsDifference(n[t].julianDate,e)>o&&(t--,o=n[t].offset),U.addSeconds(e,o,e)}function At(e,n){Ke.julianDate=e;let t=U.leapSeconds,o=xe(t,Ke,Jn);if(o<0&&(o=~o),o===0)return U.addSeconds(e,-t[0].offset,n);if(o>=t.length)return U.addSeconds(e,-t[o-1].offset,n);let i=U.secondsDifference(t[o].julianDate,e);if(i===0)return U.addSeconds(e,-t[o].offset,n);if(!(i<=1))return U.addSeconds(e,-t[--o].offset,n)}function Ne(e,n,t){let o=n/ce.SECONDS_PER_DAY|0;return e+=o,n-=ce.SECONDS_PER_DAY*o,n<0&&(e--,n+=ce.SECONDS_PER_DAY),t.dayNumber=e,t.secondsOfDay=n,t}function Gn(e,n,t,o,i,r,a){let u=(n-14)/12|0,d=e+4800+u,m=(1461*d/4|0)+(367*(n-2-12*u)/12|0)-(3*((d+100)/100|0)/4|0)+t-32075;o=o-12,o<0&&(o+=24);let l=r+(o*ce.SECONDS_PER_HOUR+i*ce.SECONDS_PER_MINUTE+a*ce.SECONDS_PER_MILLISECOND);return l>=43200&&(m-=1),[m,l]}var Jo=/^(\d{4})$/,Go=/^(\d{4})-(\d{2})$/,Ko=/^(\d{4})-?(\d{3})$/,er=/^(\d{4})-?W(\d{2})-?(\d{1})?$/,nr=/^(\d{4})-?(\d{2})-?(\d{2})$/,Kn=/([Z+\-])?(\d{2})?:?(\d{2})?$/,tr=/^(\d{2})(\.\d+)?/.source+Kn.source,or=/^(\d{2}):?(\d{2})(\.\d+)?/.source+Kn.source,rr=/^(\d{2}):?(\d{2}):?(\d{2})(\.\d+)?/.source+Kn.source,Ee="Invalid ISO 8601 date.";function U(e,n,t){this.dayNumber=void 0,this.secondsOfDay=void 0,e=O(e,0),n=O(n,0),t=O(t,H.UTC);let o=e|0;n=n+(e-o)*ce.SECONDS_PER_DAY,Ne(o,n,this),t===H.UTC&&jn(this)}U.fromGregorianDate=function(e,n){if(!(e instanceof Rn))throw new A("date must be a valid GregorianDate.");let t=Gn(e.year,e.month,e.day,e.hour,e.minute,e.second,e.millisecond);return p(n)?(Ne(t[0],t[1],n),jn(n),n):new U(t[0],t[1],H.UTC)};U.fromDate=function(e,n){if(!(e instanceof Date)||isNaN(e.getTime()))throw new A("date must be a valid JavaScript Date.");let t=Gn(e.getUTCFullYear(),e.getUTCMonth()+1,e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds());return p(n)?(Ne(t[0],t[1],n),jn(n),n):new U(t[0],t[1],H.UTC)};U.fromIso8601=function(e,n){if(typeof e!="string")throw new A(Ee);e=e.replace(",",".");let t=e.split("T"),o,i=1,r=1,a=0,u=0,d=0,m=0,l=t[0],w=t[1],T,R;if(!p(l))throw new A(Ee);let P;if(t=l.match(nr),t!==null){if(P=l.split("-").length-1,P>0&&P!==2)throw new A(Ee);o=+t[1],i=+t[2],r=+t[3]}else if(t=l.match(Go),t!==null)o=+t[1],i=+t[2];else if(t=l.match(Jo),t!==null)o=+t[1];else{let L;if(t=l.match(Ko),t!==null){if(o=+t[1],L=+t[2],R=dn(o),L<1||R&&L>366||!R&&L>365)throw new A(Ee)}else if(t=l.match(er),t!==null){o=+t[1];let F=+t[2],x=+t[3]||0;if(P=l.split("-").length-1,P>0&&(!p(t[3])&&P!==1||p(t[3])&&P!==2))throw new A(Ee);let W=new Date(Date.UTC(o,0,4));L=F*7+x-W.getUTCDay()-3}else throw new A(Ee);T=new Date(Date.UTC(o,0,1)),T.setUTCDate(L),i=T.getUTCMonth()+1,r=T.getUTCDate()}if(R=dn(o),i<1||i>12||r<1||(i!==2||!R)&&r>vn[i-1]||R&&i===2&&r>Cn)throw new A(Ee);let C;if(p(w)){if(t=w.match(rr),t!==null){if(P=w.split(":").length-1,P>0&&P!==2&&P!==3)throw new A(Ee);a=+t[1],u=+t[2],d=+t[3],m=+(t[4]||0)*1e3,C=5}else if(t=w.match(or),t!==null){if(P=w.split(":").length-1,P>2)throw new A(Ee);a=+t[1],u=+t[2],d=+(t[3]||0)*60,C=4}else if(t=w.match(tr),t!==null)a=+t[1],u=+(t[2]||0)*60,C=3;else throw new A(Ee);if(u>=60||d>=61||a>24||a===24&&(u>0||d>0||m>0))throw new A(Ee);let L=t[C],F=+t[C+1],x=+(t[C+2]||0);switch(L){case"+":a=a-F,u=u-x;break;case"-":a=a+F,u=u+x;break;case"Z":break;default:u=u+new Date(Date.UTC(o,i-1,r,a,u)).getTimezoneOffset();break}}let q=d===60;for(q&&d--;u>=60;)u-=60,a++;for(;a>=24;)a-=24,r++;for(T=R&&i===2?Cn:vn[i-1];r>T;)r-=T,i++,i>12&&(i-=12,o++),T=R&&i===2?Cn:vn[i-1];for(;u<0;)u+=60,a--;for(;a<0;)a+=24,r--;for(;r<1;)i--,i<1&&(i+=12,o--),T=R&&i===2?Cn:vn[i-1],r+=T;let k=Gn(o,i,r,a,u,d,m);return p(n)?(Ne(k[0],k[1],n),jn(n)):n=new U(k[0],k[1],H.UTC),q&&U.addSeconds(n,1,n),n};U.now=function(e){return U.fromDate(new Date,e)};var An=new U(0,0,H.TAI);U.toGregorianDate=function(e,n){if(!p(e))throw new A("julianDate is required.");let t=!1,o=At(e,An);p(o)||(U.addSeconds(e,-1,An),o=At(An,An),t=!0);let i=o.dayNumber,r=o.secondsOfDay;r>=43200&&(i+=1);let a=i+68569|0,u=4*a/146097|0;a=a-((146097*u+3)/4|0)|0;let d=4e3*(a+1)/1461001|0;a=a-(1461*d/4|0)+31|0;let m=80*a/2447|0,l=a-(2447*m/80|0)|0;a=m/11|0;let w=m+2-12*a|0,T=100*(u-49)+d+a|0,R=r/ce.SECONDS_PER_HOUR|0,P=r-R*ce.SECONDS_PER_HOUR,C=P/ce.SECONDS_PER_MINUTE|0;P=P-C*ce.SECONDS_PER_MINUTE;let q=P|0,k=(P-q)/ce.SECONDS_PER_MILLISECOND;return R+=12,R>23&&(R-=24),t&&(q+=1),p(n)?(n.year=T,n.month=w,n.day=l,n.hour=R,n.minute=C,n.second=q,n.millisecond=k,n.isLeapSecond=t,n):new Rn(T,w,l,R,C,q,k,t)};U.toDate=function(e){if(!p(e))throw new A("julianDate is required.");let n=U.toGregorianDate(e,jt),t=n.second;return n.isLeapSecond&&(t-=1),new Date(Date.UTC(n.year,n.month-1,n.day,n.hour,n.minute,t,n.millisecond))};U.toIso8601=function(e,n){if(!p(e))throw new A("julianDate is required.");let t=U.toGregorianDate(e,jt),o=t.year,i=t.month,r=t.day,a=t.hour,u=t.minute,d=t.second,m=t.millisecond;o===1e4&&i===1&&r===1&&a===0&&u===0&&d===0&&m===0&&(o=9999,i=12,r=31,a=24);let l;return!p(n)&&m!==0?(l=(m*.01).toString().replace(".",""),`${o.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${r.toString().padStart(2,"0")}T${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${d.toString().padStart(2,"0")}.${l}Z`):!p(n)||n===0?`${o.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${r.toString().padStart(2,"0")}T${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${d.toString().padStart(2,"0")}Z`:(l=(m*.01).toFixed(n).replace(".","").slice(0,n),`${o.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${r.toString().padStart(2,"0")}T${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${d.toString().padStart(2,"0")}.${l}Z`)};U.clone=function(e,n){if(p(e))return p(n)?(n.dayNumber=e.dayNumber,n.secondsOfDay=e.secondsOfDay,n):new U(e.dayNumber,e.secondsOfDay,H.TAI)};U.compare=function(e,n){if(!p(e))throw new A("left is required.");if(!p(n))throw new A("right is required.");let t=e.dayNumber-n.dayNumber;return t!==0?t:e.secondsOfDay-n.secondsOfDay};U.equals=function(e,n){return e===n||p(e)&&p(n)&&e.dayNumber===n.dayNumber&&e.secondsOfDay===n.secondsOfDay};U.equalsEpsilon=function(e,n,t){return t=O(t,0),e===n||p(e)&&p(n)&&Math.abs(U.secondsDifference(e,n))<=t};U.totalDays=function(e){if(!p(e))throw new A("julianDate is required.");return e.dayNumber+e.secondsOfDay/ce.SECONDS_PER_DAY};U.secondsDifference=function(e,n){if(!p(e))throw new A("left is required.");if(!p(n))throw new A("right is required.");return(e.dayNumber-n.dayNumber)*ce.SECONDS_PER_DAY+(e.secondsOfDay-n.secondsOfDay)};U.daysDifference=function(e,n){if(!p(e))throw new A("left is required.");if(!p(n))throw new A("right is required.");let t=e.dayNumber-n.dayNumber,o=(e.secondsOfDay-n.secondsOfDay)/ce.SECONDS_PER_DAY;return t+o};U.computeTaiMinusUtc=function(e){Ke.julianDate=e;let n=U.leapSeconds,t=xe(n,Ke,Jn);return t<0&&(t=~t,--t,t<0&&(t=0)),n[t].offset};U.addSeconds=function(e,n,t){if(!p(e))throw new A("julianDate is required.");if(!p(n))throw new A("seconds is required.");if(!p(t))throw new A("result is required.");return Ne(e.dayNumber,e.secondsOfDay+n,t)};U.addMinutes=function(e,n,t){if(!p(e))throw new A("julianDate is required.");if(!p(n))throw new A("minutes is required.");if(!p(t))throw new A("result is required.");let o=e.secondsOfDay+n*ce.SECONDS_PER_MINUTE;return Ne(e.dayNumber,o,t)};U.addHours=function(e,n,t){if(!p(e))throw new A("julianDate is required.");if(!p(n))throw new A("hours is required.");if(!p(t))throw new A("result is required.");let o=e.secondsOfDay+n*ce.SECONDS_PER_HOUR;return Ne(e.dayNumber,o,t)};U.addDays=function(e,n,t){if(!p(e))throw new A("julianDate is required.");if(!p(n))throw new A("days is required.");if(!p(t))throw new A("result is required.");let o=e.dayNumber+n;return Ne(o,e.secondsOfDay,t)};U.lessThan=function(e,n){return U.compare(e,n)<0};U.lessThanOrEquals=function(e,n){return U.compare(e,n)<=0};U.greaterThan=function(e,n){return U.compare(e,n)>0};U.greaterThanOrEquals=function(e,n){return U.compare(e,n)>=0};U.prototype.clone=function(e){return U.clone(this,e)};U.prototype.equals=function(e){return U.equals(this,e)};U.prototype.equalsEpsilon=function(e,n){return U.equalsEpsilon(this,e,n)};U.prototype.toString=function(){return U.toIso8601(this)};U.leapSeconds=[new te(new U(2441317,43210,H.TAI),10),new te(new U(2441499,43211,H.TAI),11),new te(new U(2441683,43212,H.TAI),12),new te(new U(2442048,43213,H.TAI),13),new te(new U(2442413,43214,H.TAI),14),new te(new U(2442778,43215,H.TAI),15),new te(new U(2443144,43216,H.TAI),16),new te(new U(2443509,43217,H.TAI),17),new te(new U(2443874,43218,H.TAI),18),new te(new U(2444239,43219,H.TAI),19),new te(new U(2444786,43220,H.TAI),20),new te(new U(2445151,43221,H.TAI),21),new te(new U(2445516,43222,H.TAI),22),new te(new U(2446247,43223,H.TAI),23),new te(new U(2447161,43224,H.TAI),24),new te(new U(2447892,43225,H.TAI),25),new te(new U(2448257,43226,H.TAI),26),new te(new U(2448804,43227,H.TAI),27),new te(new U(2449169,43228,H.TAI),28),new te(new U(2449534,43229,H.TAI),29),new te(new U(2450083,43230,H.TAI),30),new te(new U(2450630,43231,H.TAI),31),new te(new U(2451179,43232,H.TAI),32),new te(new U(2453736,43233,H.TAI),33),new te(new U(2454832,43234,H.TAI),34),new te(new U(2456109,43235,H.TAI),35),new te(new U(2457204,43236,H.TAI),36),new te(new U(2457754,43237,H.TAI),37)];var me=U;var io=Ze(We(),1);function ir(e){return(e.length===0||e[e.length-1]!=="/")&&(e=`${e}/`),e}var Dt=ir;function Nt(e,n){if(e===null||typeof e!="object")return e;n=O(n,!1);let t=new e.constructor;for(let o in e)if(e.hasOwnProperty(o)){let i=e[o];n&&(i=Nt(i,n)),t[o]=i}return t}var tn=Nt;function cr(){let e,n,t=new Promise(function(o,i){e=o,n=i});return{resolve:e,reject:n,promise:t}}var Qe=cr;var kt=Ze(We(),1);function et(e,n){let t;return typeof document<"u"&&(t=document),et._implementation(e,n,t)}et._implementation=function(e,n,t){if(!p(e))throw new A("relative uri is required.");if(!p(n)){if(typeof t>"u")return e;n=O(t.baseURI,t.location.href)}let o=new kt.default(e);return o.scheme()!==""?o.toString():o.absoluteTo(n).toString()};var mn=et;var Ft=Ze(We(),1);function sr(e,n){if(!p(e))throw new A("uri is required.");let t="",o=e.lastIndexOf("/");return o!==-1&&(t=e.substring(0,o+1)),n&&(e=new Ft.default(e),e.query().length!==0&&(t+=`?${e.query()}`),e.fragment().length!==0&&(t+=`#${e.fragment()}`)),t}var Lt=sr;var Bt=Ze(We(),1);function ar(e){if(!p(e))throw new A("uri is required.");let n=new Bt.default(e);n.normalize();let t=n.path(),o=t.lastIndexOf("/");return o!==-1&&(t=t.substr(o+1)),o=t.lastIndexOf("."),o===-1?t="":t=t.substr(o+1),t}var xt=ar;var Wt={};function fr(e,n,t){p(n)||(n=e.width),p(t)||(t=e.height);let o=Wt[n];p(o)||(o={},Wt[n]=o);let i=o[t];if(!p(i)){let r=document.createElement("canvas");r.width=n,r.height=t,i=r.getContext("2d",{willReadFrequently:!0}),i.globalCompositeOperation="copy",o[t]=i}return i.drawImage(e,0,0,n,t),i.getImageData(0,0,n,t).data}var nt=fr;var ur=/^blob:/i;function pr(e){return s.typeOf.string("uri",e),ur.test(e)}var zn=pr;var ve;function hr(e){p(ve)||(ve=document.createElement("a")),ve.href=window.location.href;let n=ve.host,t=ve.protocol;return ve.href=e,ve.href=ve.href,t!==ve.protocol||n!==ve.host}var Qt=hr;var dr=/^data:/i;function mr(e){return s.typeOf.string("uri",e),dr.test(e)}var In=mr;function yr(e){let n=document.createElement("script");return n.async=!0,n.src=e,new Promise((t,o)=>{window.crossOriginIsolated&&n.setAttribute("crossorigin","anonymous");let i=document.getElementsByTagName("head")[0];n.onload=function(){n.onload=void 0,i.removeChild(n),t()},n.onerror=function(r){o(r)},i.appendChild(n)})}var Ht=yr;function lr(e){if(!p(e))throw new A("obj is required.");let n="";for(let t in e)if(e.hasOwnProperty(t)){let o=e[t],i=`${encodeURIComponent(t)}=`;if(Array.isArray(o))for(let r=0,a=o.length;r<a;++r)n+=`${i+encodeURIComponent(o[r])}&`;else n+=`${i+encodeURIComponent(o)}&`}return n=n.slice(0,-1),n}var $t=lr;function wr(e){if(!p(e))throw new A("queryString is required.");let n={};if(e==="")return n;let t=e.replace(/\+/g,"%20").split(/[&;]/);for(let o=0,i=t.length;o<i;++o){let r=t[o].split("="),a=decodeURIComponent(r[0]),u=r[1];p(u)?u=decodeURIComponent(u):u="";let d=n[a];typeof d=="string"?n[a]=[d,u]:Array.isArray(d)?d.push(u):n[a]=u}return n}var Vt=wr;var br={UNISSUED:0,ISSUED:1,ACTIVE:2,RECEIVED:3,CANCELLED:4,FAILED:5},fe=Object.freeze(br);var Or={TERRAIN:0,IMAGERY:1,TILES3D:2,OTHER:3},Yt=Object.freeze(Or);function qn(e){e=O(e,O.EMPTY_OBJECT);let n=O(e.throttleByServer,!1),t=O(e.throttle,!1);this.url=e.url,this.requestFunction=e.requestFunction,this.cancelFunction=e.cancelFunction,this.priorityFunction=e.priorityFunction,this.priority=O(e.priority,0),this.throttle=t,this.throttleByServer=n,this.type=O(e.type,Yt.OTHER),this.serverKey=e.serverKey,this.state=fe.UNISSUED,this.deferred=void 0,this.cancelled=!1}qn.prototype.cancel=function(){this.cancelled=!0};qn.prototype.clone=function(e){return p(e)?(e.url=this.url,e.requestFunction=this.requestFunction,e.cancelFunction=this.cancelFunction,e.priorityFunction=this.priorityFunction,e.priority=this.priority,e.throttle=this.throttle,e.throttleByServer=this.throttleByServer,e.type=this.type,e.serverKey=this.serverKey,e.state=fe.UNISSUED,e.deferred=void 0,e.cancelled=!1,e):new qn(this)};var Xt=qn;function gr(e){let n={};if(!e)return n;let t=e.split(`\r
`);for(let o=0;o<t.length;++o){let i=t[o],r=i.indexOf(": ");if(r>0){let a=i.substring(0,r),u=i.substring(r+2);n[a]=u}}return n}var Zt=gr;function Jt(e,n,t){this.statusCode=e,this.response=n,this.responseHeaders=t,typeof this.responseHeaders=="string"&&(this.responseHeaders=Zt(this.responseHeaders))}Jt.prototype.toString=function(){let e="Request has failed.";return p(this.statusCode)&&(e+=` Status Code: ${this.statusCode}`),e};var yn=Jt;var Dn=Ze(We(),1);function ln(){this._listeners=[],this._scopes=[],this._toRemove=[],this._insideRaiseEvent=!1}Object.defineProperties(ln.prototype,{numberOfListeners:{get:function(){return this._listeners.length-this._toRemove.length}}});ln.prototype.addEventListener=function(e,n){s.typeOf.func("listener",e),this._listeners.push(e),this._scopes.push(n);let t=this;return function(){t.removeEventListener(e,n)}};ln.prototype.removeEventListener=function(e,n){s.typeOf.func("listener",e);let t=this._listeners,o=this._scopes,i=-1;for(let r=0;r<t.length;r++)if(t[r]===e&&o[r]===n){i=r;break}return i!==-1?(this._insideRaiseEvent?(this._toRemove.push(i),t[i]=void 0,o[i]=void 0):(t.splice(i,1),o.splice(i,1)),!0):!1};function _r(e,n){return n-e}ln.prototype.raiseEvent=function(){this._insideRaiseEvent=!0;let e,n=this._listeners,t=this._scopes,o=n.length;for(e=0;e<o;e++){let r=n[e];p(r)&&n[e].apply(t[e],arguments)}let i=this._toRemove;if(o=i.length,o>0){for(i.sort(_r),e=0;e<o;e++){let r=i[e];n.splice(r,1),t.splice(r,1)}i.length=0}this._insideRaiseEvent=!1};var Gt=ln;function He(e){s.typeOf.object("options",e),s.defined("options.comparator",e.comparator),this._comparator=e.comparator,this._array=[],this._length=0,this._maximumLength=void 0}Object.defineProperties(He.prototype,{length:{get:function(){return this._length}},internalArray:{get:function(){return this._array}},maximumLength:{get:function(){return this._maximumLength},set:function(e){s.typeOf.number.greaterThanOrEquals("maximumLength",e,0);let n=this._length;if(e<n){let t=this._array;for(let o=e;o<n;++o)t[o]=void 0;this._length=e,t.length=e}this._maximumLength=e}},comparator:{get:function(){return this._comparator}}});function tt(e,n,t){let o=e[n];e[n]=e[t],e[t]=o}He.prototype.reserve=function(e){e=O(e,this._length),this._array.length=e};He.prototype.heapify=function(e){e=O(e,0);let n=this._length,t=this._comparator,o=this._array,i=-1,r=!0;for(;r;){let a=2*(e+1),u=a-1;u<n&&t(o[u],o[e])<0?i=u:i=e,a<n&&t(o[a],o[i])<0&&(i=a),i!==e?(tt(o,i,e),e=i):r=!1}};He.prototype.resort=function(){let e=this._length;for(let n=Math.ceil(e/2);n>=0;--n)this.heapify(n)};He.prototype.insert=function(e){s.defined("element",e);let n=this._array,t=this._comparator,o=this._maximumLength,i=this._length++;for(i<n.length?n[i]=e:n.push(e);i!==0;){let a=Math.floor((i-1)/2);if(t(n[i],n[a])<0)tt(n,i,a),i=a;else break}let r;return p(o)&&this._length>o&&(r=n[o],this._length=o),r};He.prototype.pop=function(e){if(e=O(e,0),this._length===0)return;s.typeOf.number.lessThan("index",e,this._length);let n=this._array,t=n[e];return tt(n,e,--this._length),this.heapify(e),n[this._length]=void 0,t};var Kt=He;function Sr(e,n){return e.priority-n.priority}var K={numberOfAttemptedRequests:0,numberOfActiveRequests:0,numberOfCancelledRequests:0,numberOfCancelledActiveRequests:0,numberOfFailedRequests:0,numberOfActiveRequestsEver:0,lastNumberOfActiveRequests:0},on=20,we=new Kt({comparator:Sr});we.maximumLength=on;we.reserve(on);var Ce=[],Me={},Er=typeof document<"u"?new Dn.default(document.location.href):new Dn.default,Nn=new Gt;function se(){}se.maximumRequests=50;se.maximumRequestsPerServer=18;se.requestsByServer={};se.throttleRequests=!0;se.debugShowStatistics=!1;se.requestCompletedEvent=Nn;Object.defineProperties(se,{statistics:{get:function(){return K}},priorityHeapLength:{get:function(){return on},set:function(e){if(e<on)for(;we.length>e;){let n=we.pop();$e(n)}on=e,we.maximumLength=e,we.reserve(e)}}});function eo(e){p(e.priorityFunction)&&(e.priority=e.priorityFunction())}se.serverHasOpenSlots=function(e,n){n=O(n,1);let t=O(se.requestsByServer[e],se.maximumRequestsPerServer);return Me[e]+n<=t};se.heapHasOpenSlots=function(e){return we.length+e<=on};function no(e){return e.state===fe.UNISSUED&&(e.state=fe.ISSUED,e.deferred=Qe()),e.deferred.promise}function Tr(e){return function(n){if(e.state===fe.CANCELLED)return;let t=e.deferred;--K.numberOfActiveRequests,--Me[e.serverKey],Nn.raiseEvent(),e.state=fe.RECEIVED,e.deferred=void 0,t.resolve(n)}}function Rr(e){return function(n){e.state!==fe.CANCELLED&&(++K.numberOfFailedRequests,--K.numberOfActiveRequests,--Me[e.serverKey],Nn.raiseEvent(n),e.state=fe.FAILED,e.deferred.reject(n))}}function to(e){let n=no(e);return e.state=fe.ACTIVE,Ce.push(e),++K.numberOfActiveRequests,++K.numberOfActiveRequestsEver,++Me[e.serverKey],e.requestFunction().then(Tr(e)).catch(Rr(e)),n}function $e(e){let n=e.state===fe.ACTIVE;if(e.state=fe.CANCELLED,++K.numberOfCancelledRequests,p(e.deferred)){let t=e.deferred;e.deferred=void 0,t.reject()}n&&(--K.numberOfActiveRequests,--Me[e.serverKey],++K.numberOfCancelledActiveRequests),p(e.cancelFunction)&&e.cancelFunction()}se.update=function(){let e,n,t=0,o=Ce.length;for(e=0;e<o;++e){if(n=Ce[e],n.cancelled&&$e(n),n.state!==fe.ACTIVE){++t;continue}t>0&&(Ce[e-t]=n)}Ce.length-=t;let i=we.internalArray,r=we.length;for(e=0;e<r;++e)eo(i[e]);we.resort();let a=Math.max(se.maximumRequests-Ce.length,0),u=0;for(;u<a&&we.length>0;){if(n=we.pop(),n.cancelled){$e(n);continue}if(n.throttleByServer&&!se.serverHasOpenSlots(n.serverKey)){$e(n);continue}to(n),++u}vr()};se.getServerKey=function(e){s.typeOf.string("url",e);let n=new Dn.default(e);n.scheme()===""&&(n=n.absoluteTo(Er),n.normalize());let t=n.authority();/:/.test(t)||(t=`${t}:${n.scheme()==="https"?"443":"80"}`);let o=Me[t];return p(o)||(Me[t]=0),t};se.request=function(e){if(s.typeOf.object("request",e),s.typeOf.string("request.url",e.url),s.typeOf.func("request.requestFunction",e.requestFunction),In(e.url)||zn(e.url))return Nn.raiseEvent(),e.state=fe.RECEIVED,e.requestFunction();if(++K.numberOfAttemptedRequests,p(e.serverKey)||(e.serverKey=se.getServerKey(e.url)),se.throttleRequests&&e.throttleByServer&&!se.serverHasOpenSlots(e.serverKey))return;if(!se.throttleRequests||!e.throttle)return to(e);if(Ce.length>=se.maximumRequests)return;eo(e);let n=we.insert(e);if(p(n)){if(n===e)return;$e(n)}return no(e)};function vr(){se.debugShowStatistics&&(K.numberOfActiveRequests===0&&K.lastNumberOfActiveRequests>0&&(K.numberOfAttemptedRequests>0&&(console.log(`Number of attempted requests: ${K.numberOfAttemptedRequests}`),K.numberOfAttemptedRequests=0),K.numberOfCancelledRequests>0&&(console.log(`Number of cancelled requests: ${K.numberOfCancelledRequests}`),K.numberOfCancelledRequests=0),K.numberOfCancelledActiveRequests>0&&(console.log(`Number of cancelled active requests: ${K.numberOfCancelledActiveRequests}`),K.numberOfCancelledActiveRequests=0),K.numberOfFailedRequests>0&&(console.log(`Number of failed requests: ${K.numberOfFailedRequests}`),K.numberOfFailedRequests=0)),K.lastNumberOfActiveRequests=K.numberOfActiveRequests)}se.clearForSpecs=function(){for(;we.length>0;){let n=we.pop();$e(n)}let e=Ce.length;for(let n=0;n<e;++n)$e(Ce[n]);Ce.length=0,Me={},K.numberOfAttemptedRequests=0,K.numberOfActiveRequests=0,K.numberOfCancelledRequests=0,K.numberOfCancelledActiveRequests=0,K.numberOfFailedRequests=0,K.numberOfActiveRequestsEver=0,K.lastNumberOfActiveRequests=0};se.numberOfActiveRequestsByServer=function(e){return Me[e]};se.requestHeap=we;var kn=se;var oo=Ze(We(),1);var wn={},rn={};wn.add=function(e,n){if(!p(e))throw new A("host is required.");if(!p(n)||n<=0)throw new A("port is required to be greater than 0.");let t=`${e.toLowerCase()}:${n}`;p(rn[t])||(rn[t]=!0)};wn.remove=function(e,n){if(!p(e))throw new A("host is required.");if(!p(n)||n<=0)throw new A("port is required to be greater than 0.");let t=`${e.toLowerCase()}:${n}`;p(rn[t])&&delete rn[t]};function Cr(e){let n=new oo.default(e);n.normalize();let t=n.authority();if(t.length!==0){if(n.authority(t),t.indexOf("@")!==-1&&(t=t.split("@")[1]),t.indexOf(":")===-1){let o=n.scheme();if(o.length===0&&(o=window.location.protocol,o=o.substring(0,o.length-1)),o==="http")t+=":80";else if(o==="https")t+=":443";else return}return t}}wn.contains=function(e){if(!p(e))throw new A("url is required.");let n=Cr(e);return!!(p(n)&&p(rn[n]))};wn.clear=function(){rn={}};var ot=wn;var co=function(){try{let e=new XMLHttpRequest;return e.open("GET","#",!0),e.responseType="blob",e.responseType==="blob"}catch{return!1}}();function M(e){e=O(e,O.EMPTY_OBJECT),typeof e=="string"&&(e={url:e}),s.typeOf.string("options.url",e.url),this._url=void 0,this._templateValues=Ae(e.templateValues,{}),this._queryParameters=Ae(e.queryParameters,{}),this.headers=Ae(e.headers,{}),this.request=O(e.request,new Xt),this.proxy=e.proxy,this.retryCallback=e.retryCallback,this.retryAttempts=O(e.retryAttempts,0),this._retryCount=0,O(e.parseUrl,!0)?this.parseUrl(e.url,!0,!0):this._url=e.url,this._credits=e.credits}function Ae(e,n){return p(e)?tn(e):n}M.createIfNeeded=function(e){return e instanceof M?e.getDerivedResource({request:e.request}):typeof e!="string"?e:new M({url:e})};var cn;M.supportsImageBitmapOptions=function(){return p(cn)?cn:typeof createImageBitmap!="function"?(cn=Promise.resolve(!1),cn):(cn=M.fetchBlob({url:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAABGdBTUEAAE4g3rEiDgAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAADElEQVQI12Ng6GAAAAEUAIngE3ZiAAAAAElFTkSuQmCC"}).then(function(n){let t={imageOrientation:"flipY",premultiplyAlpha:"none",colorSpaceConversion:"none"};return Promise.all([createImageBitmap(n,t),createImageBitmap(n)])}).then(function(n){let t=nt(n[0]),o=nt(n[1]);return t[1]!==o[1]}).catch(function(){return!1}),cn)};Object.defineProperties(M,{isBlobSupported:{get:function(){return co}}});Object.defineProperties(M.prototype,{queryParameters:{get:function(){return this._queryParameters}},templateValues:{get:function(){return this._templateValues}},url:{get:function(){return this.getUrlComponent(!0,!0)},set:function(e){this.parseUrl(e,!1,!1)}},extension:{get:function(){return xt(this._url)}},isDataUri:{get:function(){return In(this._url)}},isBlobUri:{get:function(){return zn(this._url)}},isCrossOriginUrl:{get:function(){return Qt(this._url)}},hasHeaders:{get:function(){return Object.keys(this.headers).length>0}},credits:{get:function(){return this._credits}}});M.prototype.toString=function(){return this.getUrlComponent(!0,!0)};M.prototype.parseUrl=function(e,n,t,o){let i=new io.default(e),r=Ar(i.query());this._queryParameters=n?Ln(r,this.queryParameters,t):r,i.search(""),i.fragment(""),p(o)&&i.scheme()===""&&(i=i.absoluteTo(mn(o))),this._url=i.toString()};function Ar(e){return e.length===0?{}:e.indexOf("=")===-1?{[e]:void 0}:Vt(e)}function Ln(e,n,t){if(!t)return De(e,n);let o=tn(e,!0);for(let i in n)if(n.hasOwnProperty(i)){let r=o[i],a=n[i];p(r)?(Array.isArray(r)||(r=o[i]=[r]),o[i]=r.concat(a)):o[i]=Array.isArray(a)?a.slice():a}return o}M.prototype.getUrlComponent=function(e,n){if(this.isDataUri)return this._url;let t=this._url;e&&(t=`${t}${jr(this.queryParameters)}`),t=t.replace(/%7B/g,"{").replace(/%7D/g,"}");let o=this._templateValues;return Object.keys(o).length>0&&(t=t.replace(/{(.*?)}/g,function(i,r){let a=o[r];return p(a)?encodeURIComponent(a):i})),n&&p(this.proxy)&&(t=this.proxy.getURL(t)),t};function jr(e){let n=Object.keys(e);return n.length===0?"":n.length===1&&!p(e[n[0]])?`?${n[0]}`:`?${$t(e)}`}M.prototype.setQueryParameters=function(e,n){n?this._queryParameters=Ln(this._queryParameters,e,!1):this._queryParameters=Ln(e,this._queryParameters,!1)};M.prototype.appendQueryParameters=function(e){this._queryParameters=Ln(e,this._queryParameters,!0)};M.prototype.setTemplateValues=function(e,n){n?this._templateValues=De(this._templateValues,e):this._templateValues=De(e,this._templateValues)};M.prototype.getDerivedResource=function(e){let n=this.clone();if(n._retryCount=0,p(e.url)){let t=O(e.preserveQueryParameters,!1);n.parseUrl(e.url,!0,t,this._url)}return p(e.queryParameters)&&(n._queryParameters=De(e.queryParameters,n.queryParameters)),p(e.templateValues)&&(n._templateValues=De(e.templateValues,n.templateValues)),p(e.headers)&&(n.headers=De(e.headers,n.headers)),p(e.proxy)&&(n.proxy=e.proxy),p(e.request)&&(n.request=e.request),p(e.retryCallback)&&(n.retryCallback=e.retryCallback),p(e.retryAttempts)&&(n.retryAttempts=e.retryAttempts),n};M.prototype.retryOnError=function(e){let n=this.retryCallback;if(typeof n!="function"||this._retryCount>=this.retryAttempts)return Promise.resolve(!1);let t=this;return Promise.resolve(n(this,e)).then(function(o){return++t._retryCount,o})};M.prototype.clone=function(e){return p(e)?(e._url=this._url,e._queryParameters=tn(this._queryParameters),e._templateValues=tn(this._templateValues),e.headers=tn(this.headers),e.proxy=this.proxy,e.retryCallback=this.retryCallback,e.retryAttempts=this.retryAttempts,e._retryCount=0,e.request=this.request.clone(),e):new M({url:this._url,queryParameters:this.queryParameters,templateValues:this.templateValues,headers:this.headers,proxy:this.proxy,retryCallback:this.retryCallback,retryAttempts:this.retryAttempts,request:this.request.clone(),parseUrl:!1,credits:p(this.credits)?this.credits.slice():void 0})};M.prototype.getBaseUri=function(e){return Lt(this.getUrlComponent(e),e)};M.prototype.appendForwardSlash=function(){this._url=Dt(this._url)};M.prototype.fetchArrayBuffer=function(){return this.fetch({responseType:"arraybuffer"})};M.fetchArrayBuffer=function(e){return new M(e).fetchArrayBuffer()};M.prototype.fetchBlob=function(){return this.fetch({responseType:"blob"})};M.fetchBlob=function(e){return new M(e).fetchBlob()};M.prototype.fetchImage=function(e){e=O(e,O.EMPTY_OBJECT);let n=O(e.preferImageBitmap,!1),t=O(e.preferBlob,!1),o=O(e.flipY,!1),i=O(e.skipColorSpaceConversion,!1);if(it(this.request),!co||this.isDataUri||this.isBlobUri||!this.hasHeaders&&!t)return rt({resource:this,flipY:o,skipColorSpaceConversion:i,preferImageBitmap:n});let r=this.fetchBlob();if(!p(r))return;let a,u,d,m;return M.supportsImageBitmapOptions().then(function(l){return a=l,u=a&&n,r}).then(function(l){if(!p(l))return;if(m=l,u)return M.createImageBitmapFromBlob(l,{flipY:o,premultiplyAlpha:!1,skipColorSpaceConversion:i});let w=window.URL.createObjectURL(l);return d=new M({url:w}),rt({resource:d,flipY:o,skipColorSpaceConversion:i,preferImageBitmap:!1})}).then(function(l){if(p(l))return l.blob=m,u||window.URL.revokeObjectURL(d.url),l}).catch(function(l){return p(d)&&window.URL.revokeObjectURL(d.url),l.blob=m,Promise.reject(l)})};function rt(e){let n=e.resource,t=e.flipY,o=e.skipColorSpaceConversion,i=e.preferImageBitmap,r=n.request;r.url=n.url,r.requestFunction=function(){let u=!1;!n.isDataUri&&!n.isBlobUri&&(u=n.isCrossOriginUrl);let d=Qe();return M._Implementations.createImage(r,u,d,t,o,i),d.promise};let a=kn.request(r);if(p(a))return a.catch(function(u){return r.state!==fe.FAILED?Promise.reject(u):n.retryOnError(u).then(function(d){return d?(r.state=fe.UNISSUED,r.deferred=void 0,rt({resource:n,flipY:t,skipColorSpaceConversion:o,preferImageBitmap:i})):Promise.reject(u)})})}M.fetchImage=function(e){return new M(e).fetchImage({flipY:e.flipY,skipColorSpaceConversion:e.skipColorSpaceConversion,preferBlob:e.preferBlob,preferImageBitmap:e.preferImageBitmap})};M.prototype.fetchText=function(){return this.fetch({responseType:"text"})};M.fetchText=function(e){return new M(e).fetchText()};M.prototype.fetchJson=function(){let e=this.fetch({responseType:"text",headers:{Accept:"application/json,*/*;q=0.01"}});if(p(e))return e.then(function(n){if(p(n))return JSON.parse(n)})};M.fetchJson=function(e){return new M(e).fetchJson()};M.prototype.fetchXML=function(){return this.fetch({responseType:"document",overrideMimeType:"text/xml"})};M.fetchXML=function(e){return new M(e).fetchXML()};M.prototype.fetchJsonp=function(e){e=O(e,"callback"),it(this.request);let n;do n=`loadJsonp${I.nextRandomNumber().toString().substring(2,8)}`;while(p(window[n]));return so(this,e,n)};function so(e,n,t){let o={};o[n]=t,e.setQueryParameters(o);let i=e.request,r=e.url;i.url=r,i.requestFunction=function(){let u=Qe();return window[t]=function(d){u.resolve(d);try{delete window[t]}catch{window[t]=void 0}},M._Implementations.loadAndExecuteScript(r,t,u),u.promise};let a=kn.request(i);if(p(a))return a.catch(function(u){return i.state!==fe.FAILED?Promise.reject(u):e.retryOnError(u).then(function(d){return d?(i.state=fe.UNISSUED,i.deferred=void 0,so(e,n,t)):Promise.reject(u)})})}M.fetchJsonp=function(e){return new M(e).fetchJsonp(e.callbackParameterName)};M.prototype._makeRequest=function(e){let n=this;it(n.request);let t=n.request,o=n.url;t.url=o,t.requestFunction=function(){let r=e.responseType,a=De(e.headers,n.headers),u=e.overrideMimeType,d=e.method,m=e.data,l=Qe(),w=M._Implementations.loadWithXhr(o,r,d,m,a,l,u);return p(w)&&p(w.abort)&&(t.cancelFunction=function(){w.abort()}),l.promise};let i=kn.request(t);if(p(i))return i.then(function(r){return t.cancelFunction=void 0,r}).catch(function(r){return t.cancelFunction=void 0,t.state!==fe.FAILED?Promise.reject(r):n.retryOnError(r).then(function(a){return a?(t.state=fe.UNISSUED,t.deferred=void 0,n.fetch(e)):Promise.reject(r)})})};function it(e){if(e.state===fe.ISSUED||e.state===fe.ACTIVE)throw new Se("The Resource is already being fetched.");e.state=fe.UNISSUED,e.deferred=void 0}var Pr=/^data:(.*?)(;base64)?,(.*)$/;function Fn(e,n){let t=decodeURIComponent(n);return e?atob(t):t}function ro(e,n){let t=Fn(e,n),o=new ArrayBuffer(t.length),i=new Uint8Array(o);for(let r=0;r<t.length;r++)i[r]=t.charCodeAt(r);return o}function Mr(e,n){n=O(n,"");let t=e[1],o=!!e[2],i=e[3],r,a;switch(n){case"":case"text":return Fn(o,i);case"arraybuffer":return ro(o,i);case"blob":return r=ro(o,i),new Blob([r],{type:t});case"document":return a=new DOMParser,a.parseFromString(Fn(o,i),t);case"json":return JSON.parse(Fn(o,i));default:throw new A(`Unhandled responseType: ${n}`)}}M.prototype.fetch=function(e){return e=Ae(e,{}),e.method="GET",this._makeRequest(e)};M.fetch=function(e){return new M(e).fetch({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};M.prototype.delete=function(e){return e=Ae(e,{}),e.method="DELETE",this._makeRequest(e)};M.delete=function(e){return new M(e).delete({responseType:e.responseType,overrideMimeType:e.overrideMimeType,data:e.data})};M.prototype.head=function(e){return e=Ae(e,{}),e.method="HEAD",this._makeRequest(e)};M.head=function(e){return new M(e).head({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};M.prototype.options=function(e){return e=Ae(e,{}),e.method="OPTIONS",this._makeRequest(e)};M.options=function(e){return new M(e).options({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};M.prototype.post=function(e,n){return s.defined("data",e),n=Ae(n,{}),n.method="POST",n.data=e,this._makeRequest(n)};M.post=function(e){return new M(e).post(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};M.prototype.put=function(e,n){return s.defined("data",e),n=Ae(n,{}),n.method="PUT",n.data=e,this._makeRequest(n)};M.put=function(e){return new M(e).put(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};M.prototype.patch=function(e,n){return s.defined("data",e),n=Ae(n,{}),n.method="PATCH",n.data=e,this._makeRequest(n)};M.patch=function(e){return new M(e).patch(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};M._Implementations={};M._Implementations.loadImageElement=function(e,n,t){let o=new Image;o.onload=function(){o.naturalWidth===0&&o.naturalHeight===0&&o.width===0&&o.height===0&&(o.width=300,o.height=150),t.resolve(o)},o.onerror=function(i){t.reject(i)},n&&(ot.contains(e)?o.crossOrigin="use-credentials":o.crossOrigin=""),o.src=e};M._Implementations.createImage=function(e,n,t,o,i,r){let a=e.url;M.supportsImageBitmapOptions().then(function(u){if(!(u&&r)){M._Implementations.loadImageElement(a,n,t);return}let d="blob",m="GET",l=Qe(),w=M._Implementations.loadWithXhr(a,d,m,void 0,void 0,l,void 0,void 0,void 0);return p(w)&&p(w.abort)&&(e.cancelFunction=function(){w.abort()}),l.promise.then(function(T){if(!p(T)){t.reject(new Se(`Successfully retrieved ${a} but it contained no content.`));return}return M.createImageBitmapFromBlob(T,{flipY:o,premultiplyAlpha:!1,skipColorSpaceConversion:i})}).then(function(T){t.resolve(T)})}).catch(function(u){t.reject(u)})};M.createImageBitmapFromBlob=function(e,n){return s.defined("options",n),s.typeOf.bool("options.flipY",n.flipY),s.typeOf.bool("options.premultiplyAlpha",n.premultiplyAlpha),s.typeOf.bool("options.skipColorSpaceConversion",n.skipColorSpaceConversion),createImageBitmap(e,{imageOrientation:n.flipY?"flipY":"none",premultiplyAlpha:n.premultiplyAlpha?"premultiply":"none",colorSpaceConversion:n.skipColorSpaceConversion?"none":"default"})};function Ur(e,n,t,o,i,r,a){fetch(e,{method:t,headers:i}).then(async u=>{if(!u.ok){let d={};u.headers.forEach((m,l)=>{d[l]=m}),r.reject(new yn(u.status,u,d));return}switch(n){case"text":r.resolve(u.text());break;case"json":r.resolve(u.json());break;default:r.resolve(new Uint8Array(await u.arrayBuffer()).buffer);break}}).catch(()=>{r.reject(new yn)})}var zr=typeof XMLHttpRequest>"u";M._Implementations.loadWithXhr=function(e,n,t,o,i,r,a){let u=Pr.exec(e);if(u!==null){r.resolve(Mr(u,n));return}if(zr){Ur(e,n,t,o,i,r,a);return}let d=new XMLHttpRequest;if(ot.contains(e)&&(d.withCredentials=!0),d.open(t,e,!0),p(a)&&p(d.overrideMimeType)&&d.overrideMimeType(a),p(i))for(let l in i)i.hasOwnProperty(l)&&d.setRequestHeader(l,i[l]);p(n)&&(d.responseType=n);let m=!1;return typeof e=="string"&&(m=e.indexOf("file://")===0||typeof window<"u"&&window.location.origin==="file://"),d.onload=function(){if((d.status<200||d.status>=300)&&!(m&&d.status===0)){r.reject(new yn(d.status,d.response,d.getAllResponseHeaders()));return}let l=d.response,w=d.responseType;if(t==="HEAD"||t==="OPTIONS"){let R=d.getAllResponseHeaders().trim().split(/[\r\n]+/),P={};R.forEach(function(C){let q=C.split(": "),k=q.shift();P[k]=q.join(": ")}),r.resolve(P);return}if(d.status===204)r.resolve(void 0);else if(p(l)&&(!p(n)||w===n))r.resolve(l);else if(n==="json"&&typeof l=="string")try{r.resolve(JSON.parse(l))}catch(T){r.reject(T)}else(w===""||w==="document")&&p(d.responseXML)&&d.responseXML.hasChildNodes()?r.resolve(d.responseXML):(w===""||w==="text")&&p(d.responseText)?r.resolve(d.responseText):r.reject(new Se("Invalid XMLHttpRequest response type."))},d.onerror=function(l){r.reject(new yn)},d.send(o),d};M._Implementations.loadAndExecuteScript=function(e,n,t){return Ht(e,n).catch(function(o){t.reject(o)})};M._DefaultImplementations={};M._DefaultImplementations.createImage=M._Implementations.createImage;M._DefaultImplementations.loadWithXhr=M._Implementations.loadWithXhr;M._DefaultImplementations.loadAndExecuteScript=M._Implementations.loadAndExecuteScript;M.DEFAULT=Object.freeze(new M({url:typeof document>"u"?"":document.location.href.split("?")[0]}));var ke=M;function On(e){e=O(e,O.EMPTY_OBJECT),this._dates=void 0,this._samples=void 0,this._dateColumn=-1,this._xPoleWanderRadiansColumn=-1,this._yPoleWanderRadiansColumn=-1,this._ut1MinusUtcSecondsColumn=-1,this._xCelestialPoleOffsetRadiansColumn=-1,this._yCelestialPoleOffsetRadiansColumn=-1,this._taiMinusUtcSecondsColumn=-1,this._columnCount=0,this._lastIndex=-1,this._addNewLeapSeconds=O(e.addNewLeapSeconds,!0),p(e.data)?ao(this,e.data):ao(this,{columnNames:["dateIso8601","modifiedJulianDateUtc","xPoleWanderRadians","yPoleWanderRadians","ut1MinusUtcSeconds","lengthOfDayCorrectionSeconds","xCelestialPoleOffsetRadians","yCelestialPoleOffsetRadians","taiMinusUtcSeconds"],samples:[]})}On.fromUrl=async function(e,n){s.defined("url",e),n=O(n,O.EMPTY_OBJECT);let t=ke.createIfNeeded(e),o;try{o=await t.fetchJson()}catch{throw new Se(`An error occurred while retrieving the EOP data from the URL ${t.url}.`)}return new On({addNewLeapSeconds:n.addNewLeapSeconds,data:o})};On.NONE=Object.freeze({compute:function(e,n){return p(n)?(n.xPoleWander=0,n.yPoleWander=0,n.xPoleOffset=0,n.yPoleOffset=0,n.ut1MinusUtc=0):n=new hn(0,0,0,0,0),n}});On.prototype.compute=function(e,n){if(!p(this._samples))return;if(p(n)||(n=new hn(0,0,0,0,0)),this._samples.length===0)return n.xPoleWander=0,n.yPoleWander=0,n.xPoleOffset=0,n.yPoleOffset=0,n.ut1MinusUtc=0,n;let t=this._dates,o=this._lastIndex,i=0,r=0;if(p(o)){let u=t[o],d=t[o+1],m=me.lessThanOrEquals(u,e),l=!p(d),w=l||me.greaterThanOrEquals(d,e);if(m&&w)return i=o,!l&&d.equals(e)&&++i,r=i+1,uo(this,t,this._samples,e,i,r,n),n}let a=xe(t,e,me.compare,this._dateColumn);return a>=0?(a<t.length-1&&t[a+1].equals(e)&&++a,i=a,r=a):(r=~a,i=r-1,i<0&&(i=0)),this._lastIndex=i,uo(this,t,this._samples,e,i,r,n),n};function Ir(e,n){return me.compare(e.julianDate,n)}function ao(e,n){if(!p(n.columnNames))throw new Se("Error in loaded EOP data: The columnNames property is required.");if(!p(n.samples))throw new Se("Error in loaded EOP data: The samples property is required.");let t=n.columnNames.indexOf("modifiedJulianDateUtc"),o=n.columnNames.indexOf("xPoleWanderRadians"),i=n.columnNames.indexOf("yPoleWanderRadians"),r=n.columnNames.indexOf("ut1MinusUtcSeconds"),a=n.columnNames.indexOf("xCelestialPoleOffsetRadians"),u=n.columnNames.indexOf("yCelestialPoleOffsetRadians"),d=n.columnNames.indexOf("taiMinusUtcSeconds");if(t<0||o<0||i<0||r<0||a<0||u<0||d<0)throw new Se("Error in loaded EOP data: The columnNames property must include modifiedJulianDateUtc, xPoleWanderRadians, yPoleWanderRadians, ut1MinusUtcSeconds, xCelestialPoleOffsetRadians, yCelestialPoleOffsetRadians, and taiMinusUtcSeconds columns");let m=e._samples=n.samples,l=e._dates=[];e._dateColumn=t,e._xPoleWanderRadiansColumn=o,e._yPoleWanderRadiansColumn=i,e._ut1MinusUtcSecondsColumn=r,e._xCelestialPoleOffsetRadiansColumn=a,e._yCelestialPoleOffsetRadiansColumn=u,e._taiMinusUtcSecondsColumn=d,e._columnCount=n.columnNames.length,e._lastIndex=void 0;let w,T=e._addNewLeapSeconds;for(let R=0,P=m.length;R<P;R+=e._columnCount){let C=m[R+t],q=m[R+d],k=C+ce.MODIFIED_JULIAN_DATE_DIFFERENCE,L=new me(k,q,H.TAI);if(l.push(L),T){if(q!==w&&p(w)){let F=me.leapSeconds,x=xe(F,L,Ir);if(x<0){let W=new te(L,q);F.splice(~x,0,W)}}w=q}}}function fo(e,n,t,o,i){let r=t*o;i.xPoleWander=n[r+e._xPoleWanderRadiansColumn],i.yPoleWander=n[r+e._yPoleWanderRadiansColumn],i.xPoleOffset=n[r+e._xCelestialPoleOffsetRadiansColumn],i.yPoleOffset=n[r+e._yCelestialPoleOffsetRadiansColumn],i.ut1MinusUtc=n[r+e._ut1MinusUtcSecondsColumn]}function bn(e,n,t){return n+e*(t-n)}function uo(e,n,t,o,i,r,a){let u=e._columnCount;if(r>n.length-1)return a.xPoleWander=0,a.yPoleWander=0,a.xPoleOffset=0,a.yPoleOffset=0,a.ut1MinusUtc=0,a;let d=n[i],m=n[r];if(d.equals(m)||o.equals(d))return fo(e,t,i,u,a),a;if(o.equals(m))return fo(e,t,r,u,a),a;let l=me.secondsDifference(o,d)/me.secondsDifference(m,d),w=i*u,T=r*u,R=t[w+e._ut1MinusUtcSecondsColumn],P=t[T+e._ut1MinusUtcSecondsColumn],C=P-R;if(C>.5||C<-.5){let q=t[w+e._taiMinusUtcSecondsColumn],k=t[T+e._taiMinusUtcSecondsColumn];q!==k&&(m.equals(o)?R=P:P-=k-q)}return a.xPoleWander=bn(l,t[w+e._xPoleWanderRadiansColumn],t[T+e._xPoleWanderRadiansColumn]),a.yPoleWander=bn(l,t[w+e._yPoleWanderRadiansColumn],t[T+e._yPoleWanderRadiansColumn]),a.xPoleOffset=bn(l,t[w+e._xCelestialPoleOffsetRadiansColumn],t[T+e._xCelestialPoleOffsetRadiansColumn]),a.yPoleOffset=bn(l,t[w+e._yCelestialPoleOffsetRadiansColumn],t[T+e._yCelestialPoleOffsetRadiansColumn]),a.ut1MinusUtc=bn(l,R,P),a}var po=On;function be(e,n,t){this.heading=O(e,0),this.pitch=O(n,0),this.roll=O(t,0)}be.fromQuaternion=function(e,n){if(!p(e))throw new A("quaternion is required");p(n)||(n=new be);let t=2*(e.w*e.y-e.z*e.x),o=1-2*(e.x*e.x+e.y*e.y),i=2*(e.w*e.x+e.y*e.z),r=1-2*(e.y*e.y+e.z*e.z),a=2*(e.w*e.z+e.x*e.y);return n.heading=-Math.atan2(a,r),n.roll=Math.atan2(i,o),n.pitch=-I.asinClamped(t),n};be.fromDegrees=function(e,n,t,o){if(!p(e))throw new A("heading is required");if(!p(n))throw new A("pitch is required");if(!p(t))throw new A("roll is required");return p(o)||(o=new be),o.heading=e*I.RADIANS_PER_DEGREE,o.pitch=n*I.RADIANS_PER_DEGREE,o.roll=t*I.RADIANS_PER_DEGREE,o};be.clone=function(e,n){if(p(e))return p(n)?(n.heading=e.heading,n.pitch=e.pitch,n.roll=e.roll,n):new be(e.heading,e.pitch,e.roll)};be.equals=function(e,n){return e===n||p(e)&&p(n)&&e.heading===n.heading&&e.pitch===n.pitch&&e.roll===n.roll};be.equalsEpsilon=function(e,n,t,o){return e===n||p(e)&&p(n)&&I.equalsEpsilon(e.heading,n.heading,t,o)&&I.equalsEpsilon(e.pitch,n.pitch,t,o)&&I.equalsEpsilon(e.roll,n.roll,t,o)};be.prototype.clone=function(e){return be.clone(this,e)};be.prototype.equals=function(e){return be.equals(this,e)};be.prototype.equalsEpsilon=function(e,n,t){return be.equalsEpsilon(this,e,n,t)};be.prototype.toString=function(){return`(${this.heading}, ${this.pitch}, ${this.roll})`};var ct=be;var ho=/((?:.*\/)|^)Cesium\.js(?:\?|\#|$)/;function qr(){let e=document.getElementsByTagName("script");for(let n=0,t=e.length;n<t;++n){let o=e[n].getAttribute("src"),i=ho.exec(o);if(i!==null)return i[1]}}var Bn;function mo(e){return typeof document>"u"?e:(p(Bn)||(Bn=document.createElement("a")),Bn.href=e,Bn.href)}var Ve;function yo(){if(p(Ve))return Ve;let e;if(typeof CESIUM_BASE_URL<"u"?e=CESIUM_BASE_URL:p(import.meta?.url)?e=mn(".",import.meta.url):typeof define=="object"&&p(define.amd)&&!define.amd.toUrlUndefined&&p(Sn.toUrl)?e=mn("..",Ye("Core/buildModuleUrl.js")):e=qr(),!p(e))throw new A("Unable to determine Cesium base URL automatically, try defining a global variable called CESIUM_BASE_URL.");return Ve=new ke({url:mo(e)}),Ve.appendForwardSlash(),Ve}function Dr(e){return mo(Sn.toUrl(`../${e}`))}function lo(e){return yo().getDerivedResource({url:e}).url}var xn;function Ye(e){return p(xn)||(typeof define=="object"&&p(define.amd)&&!define.amd.toUrlUndefined&&p(Sn.toUrl)?xn=Dr:xn=lo),xn(e)}Ye._cesiumScriptRegex=ho;Ye._buildModuleUrlFromBaseUrl=lo;Ye._clearBaseResource=function(){Ve=void 0};Ye.setBaseUrl=function(e){Ve=ke.DEFAULT.getDerivedResource({url:e})};Ye.getCesiumBaseUrl=yo;var wo=Ye;function Nr(e,n,t){this.x=e,this.y=n,this.s=t}var Wn=Nr;function ft(e){e=O(e,O.EMPTY_OBJECT),this._xysFileUrlTemplate=ke.createIfNeeded(e.xysFileUrlTemplate),this._interpolationOrder=O(e.interpolationOrder,9),this._sampleZeroJulianEphemerisDate=O(e.sampleZeroJulianEphemerisDate,24423965e-1),this._sampleZeroDateTT=new me(this._sampleZeroJulianEphemerisDate,0,H.TAI),this._stepSizeDays=O(e.stepSizeDays,1),this._samplesPerXysFile=O(e.samplesPerXysFile,1e3),this._totalSamples=O(e.totalSamples,27426),this._samples=new Array(this._totalSamples*3),this._chunkDownloadsInProgress=[];let n=this._interpolationOrder,t=this._denominators=new Array(n+1),o=this._xTable=new Array(n+1),i=Math.pow(this._stepSizeDays,n);for(let r=0;r<=n;++r){t[r]=i,o[r]=r*this._stepSizeDays;for(let a=0;a<=n;++a)a!==r&&(t[r]*=r-a);t[r]=1/t[r]}this._work=new Array(n+1),this._coef=new Array(n+1)}var kr=new me(0,0,H.TAI);function st(e,n,t){let o=kr;return o.dayNumber=n,o.secondsOfDay=t,me.daysDifference(o,e._sampleZeroDateTT)}ft.prototype.preload=function(e,n,t,o){let i=st(this,e,n),r=st(this,t,o),a=i/this._stepSizeDays-this._interpolationOrder/2|0;a<0&&(a=0);let u=r/this._stepSizeDays-this._interpolationOrder/2|0+this._interpolationOrder;u>=this._totalSamples&&(u=this._totalSamples-1);let d=a/this._samplesPerXysFile|0,m=u/this._samplesPerXysFile|0,l=[];for(let w=d;w<=m;++w)l.push(at(this,w));return Promise.all(l)};ft.prototype.computeXysRadians=function(e,n,t){let o=st(this,e,n);if(o<0)return;let i=o/this._stepSizeDays|0;if(i>=this._totalSamples)return;let r=this._interpolationOrder,a=i-(r/2|0);a<0&&(a=0);let u=a+r;u>=this._totalSamples&&(u=this._totalSamples-1,a=u-r,a<0&&(a=0));let d=!1,m=this._samples;if(p(m[a*3])||(at(this,a/this._samplesPerXysFile|0),d=!0),p(m[u*3])||(at(this,u/this._samplesPerXysFile|0),d=!0),d)return;p(t)?(t.x=0,t.y=0,t.s=0):t=new Wn(0,0,0);let l=o-a*this._stepSizeDays,w=this._work,T=this._denominators,R=this._coef,P=this._xTable,C,q;for(C=0;C<=r;++C)w[C]=l-P[C];for(C=0;C<=r;++C){for(R[C]=1,q=0;q<=r;++q)q!==C&&(R[C]*=w[q]);R[C]*=T[C];let k=(a+C)*3;t.x+=R[C]*m[k++],t.y+=R[C]*m[k++],t.s+=R[C]*m[k]}return t};function at(e,n){if(e._chunkDownloadsInProgress[n])return e._chunkDownloadsInProgress[n];let t,o=e._xysFileUrlTemplate;p(o)?t=o.getDerivedResource({templateValues:{0:n}}):t=new ke({url:wo(`Assets/IAU2006_XYS/IAU2006_XYS_${n}.json`)});let i=t.fetchJson().then(function(r){e._chunkDownloadsInProgress[n]=!1;let a=e._samples,u=r.samples,d=n*e._samplesPerXysFile*3;for(let m=0,l=u.length;m<l;++m)a[d+m]=u[m]});return e._chunkDownloadsInProgress[n]=i,i}var bo=ft;function E(e,n,t,o){this.x=O(e,0),this.y=O(n,0),this.z=O(t,0),this.w=O(o,0)}var gn=new _;E.fromAxisAngle=function(e,n,t){s.typeOf.object("axis",e),s.typeOf.number("angle",n);let o=n/2,i=Math.sin(o);gn=_.normalize(e,gn);let r=gn.x*i,a=gn.y*i,u=gn.z*i,d=Math.cos(o);return p(t)?(t.x=r,t.y=a,t.z=u,t.w=d,t):new E(r,a,u,d)};var Fr=[1,2,0],Lr=new Array(3);E.fromRotationMatrix=function(e,n){s.typeOf.object("matrix",e);let t,o,i,r,a,u=e[$.COLUMN0ROW0],d=e[$.COLUMN1ROW1],m=e[$.COLUMN2ROW2],l=u+d+m;if(l>0)t=Math.sqrt(l+1),a=.5*t,t=.5/t,o=(e[$.COLUMN1ROW2]-e[$.COLUMN2ROW1])*t,i=(e[$.COLUMN2ROW0]-e[$.COLUMN0ROW2])*t,r=(e[$.COLUMN0ROW1]-e[$.COLUMN1ROW0])*t;else{let w=Fr,T=0;d>u&&(T=1),m>u&&m>d&&(T=2);let R=w[T],P=w[R];t=Math.sqrt(e[$.getElementIndex(T,T)]-e[$.getElementIndex(R,R)]-e[$.getElementIndex(P,P)]+1);let C=Lr;C[T]=.5*t,t=.5/t,a=(e[$.getElementIndex(P,R)]-e[$.getElementIndex(R,P)])*t,C[R]=(e[$.getElementIndex(R,T)]+e[$.getElementIndex(T,R)])*t,C[P]=(e[$.getElementIndex(P,T)]+e[$.getElementIndex(T,P)])*t,o=-C[0],i=-C[1],r=-C[2]}return p(n)?(n.x=o,n.y=i,n.z=r,n.w=a,n):new E(o,i,r,a)};var Oo=new E,go=new E,ut=new E,_o=new E;E.fromHeadingPitchRoll=function(e,n){return s.typeOf.object("headingPitchRoll",e),_o=E.fromAxisAngle(_.UNIT_X,e.roll,Oo),ut=E.fromAxisAngle(_.UNIT_Y,-e.pitch,n),n=E.multiply(ut,_o,ut),go=E.fromAxisAngle(_.UNIT_Z,-e.heading,Oo),E.multiply(go,n,n)};var Qn=new _,pt=new _,Te=new E,So=new E,Hn=new E;E.packedLength=4;E.pack=function(e,n,t){return s.typeOf.object("value",e),s.defined("array",n),t=O(t,0),n[t++]=e.x,n[t++]=e.y,n[t++]=e.z,n[t]=e.w,n};E.unpack=function(e,n,t){return s.defined("array",e),n=O(n,0),p(t)||(t=new E),t.x=e[n],t.y=e[n+1],t.z=e[n+2],t.w=e[n+3],t};E.packedInterpolationLength=3;E.convertPackedArrayForInterpolation=function(e,n,t,o){E.unpack(e,t*4,Hn),E.conjugate(Hn,Hn);for(let i=0,r=t-n+1;i<r;i++){let a=i*3;E.unpack(e,(n+i)*4,Te),E.multiply(Te,Hn,Te),Te.w<0&&E.negate(Te,Te),E.computeAxis(Te,Qn);let u=E.computeAngle(Te);p(o)||(o=[]),o[a]=Qn.x*u,o[a+1]=Qn.y*u,o[a+2]=Qn.z*u}};E.unpackInterpolationResult=function(e,n,t,o,i){p(i)||(i=new E),_.fromArray(e,0,pt);let r=_.magnitude(pt);return E.unpack(n,o*4,So),r===0?E.clone(E.IDENTITY,Te):E.fromAxisAngle(pt,r,Te),E.multiply(Te,So,i)};E.clone=function(e,n){if(p(e))return p(n)?(n.x=e.x,n.y=e.y,n.z=e.z,n.w=e.w,n):new E(e.x,e.y,e.z,e.w)};E.conjugate=function(e,n){return s.typeOf.object("quaternion",e),s.typeOf.object("result",n),n.x=-e.x,n.y=-e.y,n.z=-e.z,n.w=e.w,n};E.magnitudeSquared=function(e){return s.typeOf.object("quaternion",e),e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w};E.magnitude=function(e){return Math.sqrt(E.magnitudeSquared(e))};E.normalize=function(e,n){s.typeOf.object("result",n);let t=1/E.magnitude(e),o=e.x*t,i=e.y*t,r=e.z*t,a=e.w*t;return n.x=o,n.y=i,n.z=r,n.w=a,n};E.inverse=function(e,n){s.typeOf.object("result",n);let t=E.magnitudeSquared(e);return n=E.conjugate(e,n),E.multiplyByScalar(n,1/t,n)};E.add=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t.x=e.x+n.x,t.y=e.y+n.y,t.z=e.z+n.z,t.w=e.w+n.w,t};E.subtract=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t.x=e.x-n.x,t.y=e.y-n.y,t.z=e.z-n.z,t.w=e.w-n.w,t};E.negate=function(e,n){return s.typeOf.object("quaternion",e),s.typeOf.object("result",n),n.x=-e.x,n.y=-e.y,n.z=-e.z,n.w=-e.w,n};E.dot=function(e,n){return s.typeOf.object("left",e),s.typeOf.object("right",n),e.x*n.x+e.y*n.y+e.z*n.z+e.w*n.w};E.multiply=function(e,n,t){s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t);let o=e.x,i=e.y,r=e.z,a=e.w,u=n.x,d=n.y,m=n.z,l=n.w,w=a*u+o*l+i*m-r*d,T=a*d-o*m+i*l+r*u,R=a*m+o*d-i*u+r*l,P=a*l-o*u-i*d-r*m;return t.x=w,t.y=T,t.z=R,t.w=P,t};E.multiplyByScalar=function(e,n,t){return s.typeOf.object("quaternion",e),s.typeOf.number("scalar",n),s.typeOf.object("result",t),t.x=e.x*n,t.y=e.y*n,t.z=e.z*n,t.w=e.w*n,t};E.divideByScalar=function(e,n,t){return s.typeOf.object("quaternion",e),s.typeOf.number("scalar",n),s.typeOf.object("result",t),t.x=e.x/n,t.y=e.y/n,t.z=e.z/n,t.w=e.w/n,t};E.computeAxis=function(e,n){s.typeOf.object("quaternion",e),s.typeOf.object("result",n);let t=e.w;if(Math.abs(t-1)<I.EPSILON6||Math.abs(t+1)<I.EPSILON6)return n.x=1,n.y=n.z=0,n;let o=1/Math.sqrt(1-t*t);return n.x=e.x*o,n.y=e.y*o,n.z=e.z*o,n};E.computeAngle=function(e){return s.typeOf.object("quaternion",e),Math.abs(e.w-1)<I.EPSILON6?0:2*Math.acos(e.w)};var ht=new E;E.lerp=function(e,n,t,o){return s.typeOf.object("start",e),s.typeOf.object("end",n),s.typeOf.number("t",t),s.typeOf.object("result",o),ht=E.multiplyByScalar(n,t,ht),o=E.multiplyByScalar(e,1-t,o),E.add(ht,o,o)};var Eo=new E,dt=new E,mt=new E;E.slerp=function(e,n,t,o){s.typeOf.object("start",e),s.typeOf.object("end",n),s.typeOf.number("t",t),s.typeOf.object("result",o);let i=E.dot(e,n),r=n;if(i<0&&(i=-i,r=Eo=E.negate(n,Eo)),1-i<I.EPSILON6)return E.lerp(e,r,t,o);let a=Math.acos(i);return dt=E.multiplyByScalar(e,Math.sin((1-t)*a),dt),mt=E.multiplyByScalar(r,Math.sin(t*a),mt),o=E.add(dt,mt,o),E.multiplyByScalar(o,1/Math.sin(a),o)};E.log=function(e,n){s.typeOf.object("quaternion",e),s.typeOf.object("result",n);let t=I.acosClamped(e.w),o=0;return t!==0&&(o=t/Math.sin(t)),_.multiplyByScalar(e,o,n)};E.exp=function(e,n){s.typeOf.object("cartesian",e),s.typeOf.object("result",n);let t=_.magnitude(e),o=0;return t!==0&&(o=Math.sin(t)/t),n.x=e.x*o,n.y=e.y*o,n.z=e.z*o,n.w=Math.cos(t),n};var Br=new _,xr=new _,_n=new E,sn=new E;E.computeInnerQuadrangle=function(e,n,t,o){s.typeOf.object("q0",e),s.typeOf.object("q1",n),s.typeOf.object("q2",t),s.typeOf.object("result",o);let i=E.conjugate(n,_n);E.multiply(i,t,sn);let r=E.log(sn,Br);E.multiply(i,e,sn);let a=E.log(sn,xr);return _.add(r,a,r),_.multiplyByScalar(r,.25,r),_.negate(r,r),E.exp(r,_n),E.multiply(n,_n,o)};E.squad=function(e,n,t,o,i,r){s.typeOf.object("q0",e),s.typeOf.object("q1",n),s.typeOf.object("s0",t),s.typeOf.object("s1",o),s.typeOf.number("t",i),s.typeOf.object("result",r);let a=E.slerp(e,n,i,_n),u=E.slerp(t,o,i,sn);return E.slerp(a,u,2*i*(1-i),r)};var Wr=new E,To=1.9011074535173003,$n=un.supportsTypedArrays()?new Float32Array(8):[],Vn=un.supportsTypedArrays()?new Float32Array(8):[],Ue=un.supportsTypedArrays()?new Float32Array(8):[],ze=un.supportsTypedArrays()?new Float32Array(8):[];for(let e=0;e<7;++e){let n=e+1,t=2*n+1;$n[e]=1/(n*t),Vn[e]=n/t}$n[7]=To/(8*17);Vn[7]=To*8/17;E.fastSlerp=function(e,n,t,o){s.typeOf.object("start",e),s.typeOf.object("end",n),s.typeOf.number("t",t),s.typeOf.object("result",o);let i=E.dot(e,n),r;i>=0?r=1:(r=-1,i=-i);let a=i-1,u=1-t,d=t*t,m=u*u;for(let R=7;R>=0;--R)Ue[R]=($n[R]*d-Vn[R])*a,ze[R]=($n[R]*m-Vn[R])*a;let l=r*t*(1+Ue[0]*(1+Ue[1]*(1+Ue[2]*(1+Ue[3]*(1+Ue[4]*(1+Ue[5]*(1+Ue[6]*(1+Ue[7])))))))),w=u*(1+ze[0]*(1+ze[1]*(1+ze[2]*(1+ze[3]*(1+ze[4]*(1+ze[5]*(1+ze[6]*(1+ze[7])))))))),T=E.multiplyByScalar(e,w,Wr);return E.multiplyByScalar(n,l,o),E.add(T,o,o)};E.fastSquad=function(e,n,t,o,i,r){s.typeOf.object("q0",e),s.typeOf.object("q1",n),s.typeOf.object("s0",t),s.typeOf.object("s1",o),s.typeOf.number("t",i),s.typeOf.object("result",r);let a=E.fastSlerp(e,n,i,_n),u=E.fastSlerp(t,o,i,sn);return E.fastSlerp(a,u,2*i*(1-i),r)};E.equals=function(e,n){return e===n||p(e)&&p(n)&&e.x===n.x&&e.y===n.y&&e.z===n.z&&e.w===n.w};E.equalsEpsilon=function(e,n,t){return t=O(t,0),e===n||p(e)&&p(n)&&Math.abs(e.x-n.x)<=t&&Math.abs(e.y-n.y)<=t&&Math.abs(e.z-n.z)<=t&&Math.abs(e.w-n.w)<=t};E.ZERO=Object.freeze(new E(0,0,0,0));E.IDENTITY=Object.freeze(new E(0,0,0,1));E.prototype.clone=function(e){return E.clone(this,e)};E.prototype.equals=function(e){return E.equals(this,e)};E.prototype.equalsEpsilon=function(e,n){return E.equalsEpsilon(this,e,n)};E.prototype.toString=function(){return`(${this.x}, ${this.y}, ${this.z}, ${this.w})`};var Xe=E;var X={},yt={up:{south:"east",north:"west",west:"south",east:"north"},down:{south:"west",north:"east",west:"north",east:"south"},south:{up:"west",down:"east",west:"down",east:"up"},north:{up:"east",down:"west",west:"up",east:"down"},west:{up:"north",down:"south",north:"down",south:"up"},east:{up:"south",down:"north",north:"up",south:"down"}},an={north:[-1,0,0],east:[0,1,0],up:[0,0,1],south:[1,0,0],west:[0,-1,0],down:[0,0,-1]},lt={},ge={east:new _,north:new _,up:new _,west:new _,south:new _,down:new _},Fe=new _,Le=new _,Be=new _;X.localFrameToFixedFrameGenerator=function(e,n){if(!yt.hasOwnProperty(e)||!yt[e].hasOwnProperty(n))throw new A("firstAxis and secondAxis must be east, north, up, west, south or down.");let t=yt[e][n],o,i=e+n;return p(lt[i])?o=lt[i]:(o=function(r,a,u){if(!p(r))throw new A("origin is required.");if(isNaN(r.x)||isNaN(r.y)||isNaN(r.z))throw new A("origin has a NaN component");if(p(u)||(u=new G),_.equalsEpsilon(r,_.ZERO,I.EPSILON14))_.unpack(an[e],0,Fe),_.unpack(an[n],0,Le),_.unpack(an[t],0,Be);else if(I.equalsEpsilon(r.x,0,I.EPSILON14)&&I.equalsEpsilon(r.y,0,I.EPSILON14)){let d=I.sign(r.z);_.unpack(an[e],0,Fe),e!=="east"&&e!=="west"&&_.multiplyByScalar(Fe,d,Fe),_.unpack(an[n],0,Le),n!=="east"&&n!=="west"&&_.multiplyByScalar(Le,d,Le),_.unpack(an[t],0,Be),t!=="east"&&t!=="west"&&_.multiplyByScalar(Be,d,Be)}else{a=O(a,Ie.WGS84),a.geodeticSurfaceNormal(r,ge.up);let d=ge.up,m=ge.east;m.x=-r.y,m.y=r.x,m.z=0,_.normalize(m,ge.east),_.cross(d,m,ge.north),_.multiplyByScalar(ge.up,-1,ge.down),_.multiplyByScalar(ge.east,-1,ge.west),_.multiplyByScalar(ge.north,-1,ge.south),Fe=ge[e],Le=ge[n],Be=ge[t]}return u[0]=Fe.x,u[1]=Fe.y,u[2]=Fe.z,u[3]=0,u[4]=Le.x,u[5]=Le.y,u[6]=Le.z,u[7]=0,u[8]=Be.x,u[9]=Be.y,u[10]=Be.z,u[11]=0,u[12]=r.x,u[13]=r.y,u[14]=r.z,u[15]=1,u},lt[i]=o),o};X.eastNorthUpToFixedFrame=X.localFrameToFixedFrameGenerator("east","north");X.northEastDownToFixedFrame=X.localFrameToFixedFrameGenerator("north","east");X.northUpEastToFixedFrame=X.localFrameToFixedFrameGenerator("north","up");X.northWestUpToFixedFrame=X.localFrameToFixedFrameGenerator("north","west");var Qr=new Xe,Hr=new _(1,1,1),$r=new G;X.headingPitchRollToFixedFrame=function(e,n,t,o,i){s.typeOf.object("HeadingPitchRoll",n),o=O(o,X.eastNorthUpToFixedFrame);let r=Xe.fromHeadingPitchRoll(n,Qr),a=G.fromTranslationQuaternionRotationScale(_.ZERO,r,Hr,$r);return i=o(e,t,i),G.multiply(i,a,i)};var Vr=new G,Yr=new $;X.headingPitchRollQuaternion=function(e,n,t,o,i){s.typeOf.object("HeadingPitchRoll",n);let r=X.headingPitchRollToFixedFrame(e,n,t,o,Vr),a=G.getMatrix3(r,Yr);return Xe.fromRotationMatrix(a,i)};var Xr=new _(1,1,1),Zr=new _,Ro=new G,Jr=new G,Gr=new $,Kr=new Xe;X.fixedFrameToHeadingPitchRoll=function(e,n,t,o){s.defined("transform",e),n=O(n,Ie.WGS84),t=O(t,X.eastNorthUpToFixedFrame),p(o)||(o=new ct);let i=G.getTranslation(e,Zr);if(_.equals(i,_.ZERO))return o.heading=0,o.pitch=0,o.roll=0,o;let r=G.inverseTransformation(t(i,n,Ro),Ro),a=G.setScale(e,Xr,Jr);a=G.setTranslation(a,_.ZERO,a),r=G.multiply(r,a,r);let u=Xe.fromRotationMatrix(G.getMatrix3(r,Gr),Kr);return u=Xe.normalize(u,u),ct.fromQuaternion(u,o)};var ei=6*3600+41*60+50.54841,ni=8640184812866e-6,ti=.093104,oi=-62e-7,ri=11772758384668e-32,ii=72921158553e-15,ci=I.TWO_PI/86400,Yn=new me;X.computeTemeToPseudoFixedMatrix=function(e,n){if(!p(e))throw new A("date is required.");Yn=me.addSeconds(e,-me.computeTaiMinusUtc(e),Yn);let t=Yn.dayNumber,o=Yn.secondsOfDay,i,r=t-2451545;o>=43200?i=(r+.5)/ce.DAYS_PER_JULIAN_CENTURY:i=(r-.5)/ce.DAYS_PER_JULIAN_CENTURY;let u=(ei+i*(ni+i*(ti+i*oi)))*ci%I.TWO_PI,d=ii+ri*(t-24515455e-1),m=(o+ce.SECONDS_PER_DAY*.5)%ce.SECONDS_PER_DAY,l=u+d*m,w=Math.cos(l),T=Math.sin(l);return p(n)?(n[0]=w,n[1]=-T,n[2]=0,n[3]=T,n[4]=w,n[5]=0,n[6]=0,n[7]=0,n[8]=1,n):new $(w,T,0,-T,w,0,0,0,1)};X.iau2006XysData=new bo;X.earthOrientationParameters=po.NONE;var Ot=32.184,si=2451545;X.preloadIcrfFixed=function(e){let n=e.start.dayNumber,t=e.start.secondsOfDay+Ot,o=e.stop.dayNumber,i=e.stop.secondsOfDay+Ot;return X.iau2006XysData.preload(n,t,o,i)};X.computeIcrfToFixedMatrix=function(e,n){if(!p(e))throw new A("date is required.");p(n)||(n=new $);let t=X.computeFixedToIcrfMatrix(e,n);if(p(t))return $.transpose(t,n)};var ai=new Wn(0,0,0),fi=new hn(0,0,0,0,0,0),wt=new $,bt=new $;X.computeFixedToIcrfMatrix=function(e,n){if(!p(e))throw new A("date is required.");p(n)||(n=new $);let t=X.earthOrientationParameters.compute(e,fi);if(!p(t))return;let o=e.dayNumber,i=e.secondsOfDay+Ot,r=X.iau2006XysData.computeXysRadians(o,i,ai);if(!p(r))return;let a=r.x+t.xPoleOffset,u=r.y+t.yPoleOffset,d=1/(1+Math.sqrt(1-a*a-u*u)),m=wt;m[0]=1-d*a*a,m[3]=-d*a*u,m[6]=a,m[1]=-d*a*u,m[4]=1-d*u*u,m[7]=u,m[2]=-a,m[5]=-u,m[8]=1-d*(a*a+u*u);let l=$.fromRotationZ(-r.s,bt),w=$.multiply(m,l,wt),T=e.dayNumber,R=e.secondsOfDay-me.computeTaiMinusUtc(e)+t.ut1MinusUtc,P=T-2451545,C=R/ce.SECONDS_PER_DAY,q=.779057273264+C+.00273781191135448*(P+C);q=q%1*I.TWO_PI;let k=$.fromRotationZ(q,bt),L=$.multiply(w,k,wt),F=Math.cos(t.xPoleWander),x=Math.cos(t.yPoleWander),W=Math.sin(t.xPoleWander),Q=Math.sin(t.yPoleWander),ee=o-si+i/ce.SECONDS_PER_DAY;ee/=36525;let re=-47e-6*ee*I.RADIANS_PER_DEGREE/3600,Z=Math.cos(re),oe=Math.sin(re),J=bt;return J[0]=F*Z,J[1]=F*oe,J[2]=W,J[3]=-x*oe+Q*W*Z,J[4]=x*Z+Q*W*oe,J[5]=-Q*F,J[6]=-Q*oe-x*W*Z,J[7]=Q*Z-x*W*oe,J[8]=x*F,$.multiply(L,J,n)};var ui=new qe;X.pointToWindowCoordinates=function(e,n,t,o){return o=X.pointToGLWindowCoordinates(e,n,t,o),o.y=2*n[5]-o.y,o};X.pointToGLWindowCoordinates=function(e,n,t,o){if(!p(e))throw new A("modelViewProjectionMatrix is required.");if(!p(n))throw new A("viewportTransformation is required.");if(!p(t))throw new A("point is required.");p(o)||(o=new Oe);let i=ui;return G.multiplyByVector(e,qe.fromElements(t.x,t.y,t.z,1,i),i),qe.multiplyByScalar(i,1/i.w,i),G.multiplyByVector(n,i,i),Oe.fromCartesian4(i,o)};var pi=new _,hi=new _,di=new _;X.rotationMatrixFromPositionVelocity=function(e,n,t,o){if(!p(e))throw new A("position is required.");if(!p(n))throw new A("velocity is required.");let i=O(t,Ie.WGS84).geodeticSurfaceNormal(e,pi),r=_.cross(n,i,hi);_.equalsEpsilon(r,_.ZERO,I.EPSILON6)&&(r=_.clone(_.UNIT_X,r));let a=_.cross(r,n,di);return _.normalize(a,a),_.cross(n,a,r),_.negate(r,r),_.normalize(r,r),p(o)||(o=new $),o[0]=n.x,o[1]=n.y,o[2]=n.z,o[3]=r.x,o[4]=r.y,o[5]=r.z,o[6]=a.x,o[7]=a.y,o[8]=a.z,o};var vo=new G(0,0,1,0,1,0,0,0,0,1,0,0,0,0,0,1),Co=new Pe,gt=new _,mi=new _,yi=new $,_t=new G,Ao=new G;X.basisTo2D=function(e,n,t){if(!p(e))throw new A("projection is required.");if(!p(n))throw new A("matrix is required.");if(!p(t))throw new A("result is required.");let o=G.getTranslation(n,mi),i=e.ellipsoid,r;if(_.equals(o,_.ZERO))r=_.clone(_.ZERO,gt);else{let l=i.cartesianToCartographic(o,Co);r=e.project(l,gt),_.fromElements(r.z,r.x,r.y,r)}let a=X.eastNorthUpToFixedFrame(o,i,_t),u=G.inverseTransformation(a,Ao),d=G.getMatrix3(n,yi),m=G.multiplyByMatrix3(u,d,t);return G.multiply(vo,m,t),G.setTranslation(t,r,t),t};X.wgs84To2DModelMatrix=function(e,n,t){if(!p(e))throw new A("projection is required.");if(!p(n))throw new A("center is required.");if(!p(t))throw new A("result is required.");let o=e.ellipsoid,i=X.eastNorthUpToFixedFrame(n,o,_t),r=G.inverseTransformation(i,Ao),a=o.cartesianToCartographic(n,Co),u=e.project(a,gt);_.fromElements(u.z,u.x,u.y,u);let d=G.fromTranslation(u,_t);return G.multiply(vo,r,t),G.multiply(d,t,t),t};var jo=X;function B(e,n,t,o){this.west=O(e,0),this.south=O(n,0),this.east=O(t,0),this.north=O(o,0)}Object.defineProperties(B.prototype,{width:{get:function(){return B.computeWidth(this)}},height:{get:function(){return B.computeHeight(this)}}});B.packedLength=4;B.pack=function(e,n,t){return s.typeOf.object("value",e),s.defined("array",n),t=O(t,0),n[t++]=e.west,n[t++]=e.south,n[t++]=e.east,n[t]=e.north,n};B.unpack=function(e,n,t){return s.defined("array",e),n=O(n,0),p(t)||(t=new B),t.west=e[n++],t.south=e[n++],t.east=e[n++],t.north=e[n],t};B.computeWidth=function(e){s.typeOf.object("rectangle",e);let n=e.east,t=e.west;return n<t&&(n+=I.TWO_PI),n-t};B.computeHeight=function(e){return s.typeOf.object("rectangle",e),e.north-e.south};B.fromDegrees=function(e,n,t,o,i){return e=I.toRadians(O(e,0)),n=I.toRadians(O(n,0)),t=I.toRadians(O(t,0)),o=I.toRadians(O(o,0)),p(i)?(i.west=e,i.south=n,i.east=t,i.north=o,i):new B(e,n,t,o)};B.fromRadians=function(e,n,t,o,i){return p(i)?(i.west=O(e,0),i.south=O(n,0),i.east=O(t,0),i.north=O(o,0),i):new B(e,n,t,o)};B.fromCartographicArray=function(e,n){s.defined("cartographics",e);let t=Number.MAX_VALUE,o=-Number.MAX_VALUE,i=Number.MAX_VALUE,r=-Number.MAX_VALUE,a=Number.MAX_VALUE,u=-Number.MAX_VALUE;for(let d=0,m=e.length;d<m;d++){let l=e[d];t=Math.min(t,l.longitude),o=Math.max(o,l.longitude),a=Math.min(a,l.latitude),u=Math.max(u,l.latitude);let w=l.longitude>=0?l.longitude:l.longitude+I.TWO_PI;i=Math.min(i,w),r=Math.max(r,w)}return o-t>r-i&&(t=i,o=r,o>I.PI&&(o=o-I.TWO_PI),t>I.PI&&(t=t-I.TWO_PI)),p(n)?(n.west=t,n.south=a,n.east=o,n.north=u,n):new B(t,a,o,u)};B.fromCartesianArray=function(e,n,t){s.defined("cartesians",e),n=O(n,Ie.WGS84);let o=Number.MAX_VALUE,i=-Number.MAX_VALUE,r=Number.MAX_VALUE,a=-Number.MAX_VALUE,u=Number.MAX_VALUE,d=-Number.MAX_VALUE;for(let m=0,l=e.length;m<l;m++){let w=n.cartesianToCartographic(e[m]);o=Math.min(o,w.longitude),i=Math.max(i,w.longitude),u=Math.min(u,w.latitude),d=Math.max(d,w.latitude);let T=w.longitude>=0?w.longitude:w.longitude+I.TWO_PI;r=Math.min(r,T),a=Math.max(a,T)}return i-o>a-r&&(o=r,i=a,i>I.PI&&(i=i-I.TWO_PI),o>I.PI&&(o=o-I.TWO_PI)),p(t)?(t.west=o,t.south=u,t.east=i,t.north=d,t):new B(o,u,i,d)};var li=new _,wi=new _,bi=new _,Oi=new _,gi=new _,St=new Array(5);for(let e=0;e<St.length;++e)St[e]=new _;B.fromBoundingSphere=function(e,n,t){s.typeOf.object("boundingSphere",e);let o=e.center,i=e.radius;if(p(n)||(n=Ie.WGS84),p(t)||(t=new B),_.equals(o,_.ZERO))return B.clone(B.MAX_VALUE,t),t;let r=jo.eastNorthUpToFixedFrame(o,n,li),a=G.multiplyByPointAsVector(r,_.UNIT_X,wi);_.normalize(a,a);let u=G.multiplyByPointAsVector(r,_.UNIT_Y,bi);_.normalize(u,u),_.multiplyByScalar(u,i,u),_.multiplyByScalar(a,i,a);let d=_.negate(u,gi),m=_.negate(a,Oi),l=St,w=l[0];return _.add(o,u,w),w=l[1],_.add(o,m,w),w=l[2],_.add(o,d,w),w=l[3],_.add(o,a,w),l[4]=o,B.fromCartesianArray(l,n,t)};B.clone=function(e,n){if(p(e))return p(n)?(n.west=e.west,n.south=e.south,n.east=e.east,n.north=e.north,n):new B(e.west,e.south,e.east,e.north)};B.equalsEpsilon=function(e,n,t){return t=O(t,0),e===n||p(e)&&p(n)&&Math.abs(e.west-n.west)<=t&&Math.abs(e.south-n.south)<=t&&Math.abs(e.east-n.east)<=t&&Math.abs(e.north-n.north)<=t};B.prototype.clone=function(e){return B.clone(this,e)};B.prototype.equals=function(e){return B.equals(this,e)};B.equals=function(e,n){return e===n||p(e)&&p(n)&&e.west===n.west&&e.south===n.south&&e.east===n.east&&e.north===n.north};B.prototype.equalsEpsilon=function(e,n){return B.equalsEpsilon(this,e,n)};B.validate=function(e){s.typeOf.object("rectangle",e);let n=e.north;s.typeOf.number.greaterThanOrEquals("north",n,-I.PI_OVER_TWO),s.typeOf.number.lessThanOrEquals("north",n,I.PI_OVER_TWO);let t=e.south;s.typeOf.number.greaterThanOrEquals("south",t,-I.PI_OVER_TWO),s.typeOf.number.lessThanOrEquals("south",t,I.PI_OVER_TWO);let o=e.west;s.typeOf.number.greaterThanOrEquals("west",o,-Math.PI),s.typeOf.number.lessThanOrEquals("west",o,Math.PI);let i=e.east;s.typeOf.number.greaterThanOrEquals("east",i,-Math.PI),s.typeOf.number.lessThanOrEquals("east",i,Math.PI)};B.southwest=function(e,n){return s.typeOf.object("rectangle",e),p(n)?(n.longitude=e.west,n.latitude=e.south,n.height=0,n):new Pe(e.west,e.south)};B.northwest=function(e,n){return s.typeOf.object("rectangle",e),p(n)?(n.longitude=e.west,n.latitude=e.north,n.height=0,n):new Pe(e.west,e.north)};B.northeast=function(e,n){return s.typeOf.object("rectangle",e),p(n)?(n.longitude=e.east,n.latitude=e.north,n.height=0,n):new Pe(e.east,e.north)};B.southeast=function(e,n){return s.typeOf.object("rectangle",e),p(n)?(n.longitude=e.east,n.latitude=e.south,n.height=0,n):new Pe(e.east,e.south)};B.center=function(e,n){s.typeOf.object("rectangle",e);let t=e.east,o=e.west;t<o&&(t+=I.TWO_PI);let i=I.negativePiToPi((o+t)*.5),r=(e.south+e.north)*.5;return p(n)?(n.longitude=i,n.latitude=r,n.height=0,n):new Pe(i,r)};B.intersection=function(e,n,t){s.typeOf.object("rectangle",e),s.typeOf.object("otherRectangle",n);let o=e.east,i=e.west,r=n.east,a=n.west;o<i&&r>0?o+=I.TWO_PI:r<a&&o>0&&(r+=I.TWO_PI),o<i&&a<0?a+=I.TWO_PI:r<a&&i<0&&(i+=I.TWO_PI);let u=I.negativePiToPi(Math.max(i,a)),d=I.negativePiToPi(Math.min(o,r));if((e.west<e.east||n.west<n.east)&&d<=u)return;let m=Math.max(e.south,n.south),l=Math.min(e.north,n.north);if(!(m>=l))return p(t)?(t.west=u,t.south=m,t.east=d,t.north=l,t):new B(u,m,d,l)};B.simpleIntersection=function(e,n,t){s.typeOf.object("rectangle",e),s.typeOf.object("otherRectangle",n);let o=Math.max(e.west,n.west),i=Math.max(e.south,n.south),r=Math.min(e.east,n.east),a=Math.min(e.north,n.north);if(!(i>=a||o>=r))return p(t)?(t.west=o,t.south=i,t.east=r,t.north=a,t):new B(o,i,r,a)};B.union=function(e,n,t){s.typeOf.object("rectangle",e),s.typeOf.object("otherRectangle",n),p(t)||(t=new B);let o=e.east,i=e.west,r=n.east,a=n.west;o<i&&r>0?o+=I.TWO_PI:r<a&&o>0&&(r+=I.TWO_PI),o<i&&a<0?a+=I.TWO_PI:r<a&&i<0&&(i+=I.TWO_PI);let u=I.negativePiToPi(Math.min(i,a)),d=I.negativePiToPi(Math.max(o,r));return t.west=u,t.south=Math.min(e.south,n.south),t.east=d,t.north=Math.max(e.north,n.north),t};B.expand=function(e,n,t){return s.typeOf.object("rectangle",e),s.typeOf.object("cartographic",n),p(t)||(t=new B),t.west=Math.min(e.west,n.longitude),t.south=Math.min(e.south,n.latitude),t.east=Math.max(e.east,n.longitude),t.north=Math.max(e.north,n.latitude),t};B.contains=function(e,n){s.typeOf.object("rectangle",e),s.typeOf.object("cartographic",n);let t=n.longitude,o=n.latitude,i=e.west,r=e.east;return r<i&&(r+=I.TWO_PI,t<0&&(t+=I.TWO_PI)),(t>i||I.equalsEpsilon(t,i,I.EPSILON14))&&(t<r||I.equalsEpsilon(t,r,I.EPSILON14))&&o>=e.south&&o<=e.north};var _i=new Pe;B.subsample=function(e,n,t,o){s.typeOf.object("rectangle",e),n=O(n,Ie.WGS84),t=O(t,0),p(o)||(o=[]);let i=0,r=e.north,a=e.south,u=e.east,d=e.west,m=_i;m.height=t,m.longitude=d,m.latitude=r,o[i]=n.cartographicToCartesian(m,o[i]),i++,m.longitude=u,o[i]=n.cartographicToCartesian(m,o[i]),i++,m.latitude=a,o[i]=n.cartographicToCartesian(m,o[i]),i++,m.longitude=d,o[i]=n.cartographicToCartesian(m,o[i]),i++,r<0?m.latitude=r:a>0?m.latitude=a:m.latitude=0;for(let l=1;l<8;++l)m.longitude=-Math.PI+l*I.PI_OVER_TWO,B.contains(e,m)&&(o[i]=n.cartographicToCartesian(m,o[i]),i++);return m.latitude===0&&(m.longitude=d,o[i]=n.cartographicToCartesian(m,o[i]),i++,m.longitude=u,o[i]=n.cartographicToCartesian(m,o[i]),i++),o.length=i,o};B.subsection=function(e,n,t,o,i,r){if(s.typeOf.object("rectangle",e),s.typeOf.number.greaterThanOrEquals("westLerp",n,0),s.typeOf.number.lessThanOrEquals("westLerp",n,1),s.typeOf.number.greaterThanOrEquals("southLerp",t,0),s.typeOf.number.lessThanOrEquals("southLerp",t,1),s.typeOf.number.greaterThanOrEquals("eastLerp",o,0),s.typeOf.number.lessThanOrEquals("eastLerp",o,1),s.typeOf.number.greaterThanOrEquals("northLerp",i,0),s.typeOf.number.lessThanOrEquals("northLerp",i,1),s.typeOf.number.lessThanOrEquals("westLerp",n,o),s.typeOf.number.lessThanOrEquals("southLerp",t,i),p(r)||(r=new B),e.west<=e.east){let u=e.east-e.west;r.west=e.west+n*u,r.east=e.west+o*u}else{let u=I.TWO_PI+e.east-e.west;r.west=I.negativePiToPi(e.west+n*u),r.east=I.negativePiToPi(e.west+o*u)}let a=e.north-e.south;return r.south=e.south+t*a,r.north=e.south+i*a,n===1&&(r.west=e.east),o===1&&(r.east=e.east),t===1&&(r.south=e.north),i===1&&(r.north=e.north),r};B.MAX_VALUE=Object.freeze(new B(-Math.PI,-I.PI_OVER_TWO,Math.PI,I.PI_OVER_TWO));var ff=B;function D(e,n,t,o){this[0]=O(e,0),this[1]=O(t,0),this[2]=O(n,0),this[3]=O(o,0)}D.packedLength=4;D.pack=function(e,n,t){return s.typeOf.object("value",e),s.defined("array",n),t=O(t,0),n[t++]=e[0],n[t++]=e[1],n[t++]=e[2],n[t++]=e[3],n};D.unpack=function(e,n,t){return s.defined("array",e),n=O(n,0),p(t)||(t=new D),t[0]=e[n++],t[1]=e[n++],t[2]=e[n++],t[3]=e[n++],t};D.packArray=function(e,n){s.defined("array",e);let t=e.length,o=t*4;if(!p(n))n=new Array(o);else{if(!Array.isArray(n)&&n.length!==o)throw new A("If result is a typed array, it must have exactly array.length * 4 elements");n.length!==o&&(n.length=o)}for(let i=0;i<t;++i)D.pack(e[i],n,i*4);return n};D.unpackArray=function(e,n){if(s.defined("array",e),s.typeOf.number.greaterThanOrEquals("array.length",e.length,4),e.length%4!==0)throw new A("array length must be a multiple of 4.");let t=e.length;p(n)?n.length=t/4:n=new Array(t/4);for(let o=0;o<t;o+=4){let i=o/4;n[i]=D.unpack(e,o,n[i])}return n};D.clone=function(e,n){if(p(e))return p(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n):new D(e[0],e[2],e[1],e[3])};D.fromArray=D.unpack;D.fromColumnMajorArray=function(e,n){return s.defined("values",e),D.clone(e,n)};D.fromRowMajorArray=function(e,n){return s.defined("values",e),p(n)?(n[0]=e[0],n[1]=e[2],n[2]=e[1],n[3]=e[3],n):new D(e[0],e[1],e[2],e[3])};D.fromScale=function(e,n){return s.typeOf.object("scale",e),p(n)?(n[0]=e.x,n[1]=0,n[2]=0,n[3]=e.y,n):new D(e.x,0,0,e.y)};D.fromUniformScale=function(e,n){return s.typeOf.number("scale",e),p(n)?(n[0]=e,n[1]=0,n[2]=0,n[3]=e,n):new D(e,0,0,e)};D.fromRotation=function(e,n){s.typeOf.number("angle",e);let t=Math.cos(e),o=Math.sin(e);return p(n)?(n[0]=t,n[1]=o,n[2]=-o,n[3]=t,n):new D(t,-o,o,t)};D.toArray=function(e,n){return s.typeOf.object("matrix",e),p(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n):[e[0],e[1],e[2],e[3]]};D.getElementIndex=function(e,n){return s.typeOf.number.greaterThanOrEquals("row",n,0),s.typeOf.number.lessThanOrEquals("row",n,1),s.typeOf.number.greaterThanOrEquals("column",e,0),s.typeOf.number.lessThanOrEquals("column",e,1),e*2+n};D.getColumn=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,1),s.typeOf.object("result",t);let o=n*2,i=e[o],r=e[o+1];return t.x=i,t.y=r,t};D.setColumn=function(e,n,t,o){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,1),s.typeOf.object("cartesian",t),s.typeOf.object("result",o),o=D.clone(e,o);let i=n*2;return o[i]=t.x,o[i+1]=t.y,o};D.getRow=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,1),s.typeOf.object("result",t);let o=e[n],i=e[n+2];return t.x=o,t.y=i,t};D.setRow=function(e,n,t,o){return s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,1),s.typeOf.object("cartesian",t),s.typeOf.object("result",o),o=D.clone(e,o),o[n]=t.x,o[n+2]=t.y,o};var Si=new Oe;D.setScale=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("scale",n),s.typeOf.object("result",t);let o=D.getScale(e,Si),i=n.x/o.x,r=n.y/o.y;return t[0]=e[0]*i,t[1]=e[1]*i,t[2]=e[2]*r,t[3]=e[3]*r,t};var Ei=new Oe;D.setUniformScale=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.number("scale",n),s.typeOf.object("result",t);let o=D.getScale(e,Ei),i=n/o.x,r=n/o.y;return t[0]=e[0]*i,t[1]=e[1]*i,t[2]=e[2]*r,t[3]=e[3]*r,t};var Po=new Oe;D.getScale=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n.x=Oe.magnitude(Oe.fromElements(e[0],e[1],Po)),n.y=Oe.magnitude(Oe.fromElements(e[2],e[3],Po)),n};var Mo=new Oe;D.getMaximumScale=function(e){return D.getScale(e,Mo),Oe.maximumComponent(Mo)};var Ti=new Oe;D.setRotation=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("result",t);let o=D.getScale(e,Ti);return t[0]=n[0]*o.x,t[1]=n[1]*o.x,t[2]=n[2]*o.y,t[3]=n[3]*o.y,t};var Ri=new Oe;D.getRotation=function(e,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let t=D.getScale(e,Ri);return n[0]=e[0]/t.x,n[1]=e[1]/t.x,n[2]=e[2]/t.y,n[3]=e[3]/t.y,n};D.multiply=function(e,n,t){s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t);let o=e[0]*n[0]+e[2]*n[1],i=e[0]*n[2]+e[2]*n[3],r=e[1]*n[0]+e[3]*n[1],a=e[1]*n[2]+e[3]*n[3];return t[0]=o,t[1]=r,t[2]=i,t[3]=a,t};D.add=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t[0]=e[0]+n[0],t[1]=e[1]+n[1],t[2]=e[2]+n[2],t[3]=e[3]+n[3],t};D.subtract=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t[0]=e[0]-n[0],t[1]=e[1]-n[1],t[2]=e[2]-n[2],t[3]=e[3]-n[3],t};D.multiplyByVector=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("cartesian",n),s.typeOf.object("result",t);let o=e[0]*n.x+e[2]*n.y,i=e[1]*n.x+e[3]*n.y;return t.x=o,t.y=i,t};D.multiplyByScalar=function(e,n,t){return s.typeOf.object("matrix",e),s.typeOf.number("scalar",n),s.typeOf.object("result",t),t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3]*n,t};D.multiplyByScale=function(e,n,t){return s.typeOf.object("matrix",e),s.typeOf.object("scale",n),s.typeOf.object("result",t),t[0]=e[0]*n.x,t[1]=e[1]*n.x,t[2]=e[2]*n.y,t[3]=e[3]*n.y,t};D.multiplyByUniformScale=function(e,n,t){return s.typeOf.object("matrix",e),s.typeOf.number("scale",n),s.typeOf.object("result",t),t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3]*n,t};D.negate=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n[0]=-e[0],n[1]=-e[1],n[2]=-e[2],n[3]=-e[3],n};D.transpose=function(e,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let t=e[0],o=e[2],i=e[1],r=e[3];return n[0]=t,n[1]=o,n[2]=i,n[3]=r,n};D.abs=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n[0]=Math.abs(e[0]),n[1]=Math.abs(e[1]),n[2]=Math.abs(e[2]),n[3]=Math.abs(e[3]),n};D.equals=function(e,n){return e===n||p(e)&&p(n)&&e[0]===n[0]&&e[1]===n[1]&&e[2]===n[2]&&e[3]===n[3]};D.equalsArray=function(e,n,t){return e[0]===n[t]&&e[1]===n[t+1]&&e[2]===n[t+2]&&e[3]===n[t+3]};D.equalsEpsilon=function(e,n,t){return t=O(t,0),e===n||p(e)&&p(n)&&Math.abs(e[0]-n[0])<=t&&Math.abs(e[1]-n[1])<=t&&Math.abs(e[2]-n[2])<=t&&Math.abs(e[3]-n[3])<=t};D.IDENTITY=Object.freeze(new D(1,0,0,1));D.ZERO=Object.freeze(new D(0,0,0,0));D.COLUMN0ROW0=0;D.COLUMN0ROW1=1;D.COLUMN1ROW0=2;D.COLUMN1ROW1=3;Object.defineProperties(D.prototype,{length:{get:function(){return D.packedLength}}});D.prototype.clone=function(e){return D.clone(this,e)};D.prototype.equals=function(e){return D.equals(this,e)};D.prototype.equalsEpsilon=function(e,n){return D.equalsEpsilon(this,e,n)};D.prototype.toString=function(){return`(${this[0]}, ${this[2]})
(${this[1]}, ${this[3]})`};var lf=D;export{qe as a,G as b,De as c,ke as d,wo as e,Xe as f,jo as g,ff as h,lf as i};
