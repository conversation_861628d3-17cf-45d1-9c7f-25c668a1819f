### 1.定位信息

`记录终端当前位置的信息，包括经度、纬度、高程等。`

| 序号	 | 字段名          | 	中文名      | 	类型      | 	备注                                                                |
|:----|:-------------|:----------|:---------|:-------------------------------------------------------------------|
| 1	  | id	          | 主键	       | Bigint   | 	唯一标识码                                                             |
| 2	  | target_id    | 	目标id     | 	Bigint  | 	——                                                                |
| 3	  | target_type	 | 目标类别      | 	Tinyint | 	1-车辆，<br/>2-人员，<br/>3-基础设施、<br/>4-集装箱                             |
| 4	  | device_id	   | 设备id      | 	Bigint  | 	——                                                                |
| 5   | 	device_num  | 	赋码编号    | 	String  | 	——                                                                |
| 6	  | device_type	 | 设备类别	     | String	  | 1-北斗定位终端，<br/>2-北斗穿戴式终端，<br/>3-北斗短报文终端，<br/>4-北斗监测终端，<br/>5-北斗授时终端 |
| 7	  | longitude    | 	经度	      | Double	  | 单位：°，至少精确到小数点后6位                                                   |
| 8	  | latitude     | 	纬度	      | Double   | 	单位：°，至少精确到小数点后6位                                                  |
| 9	  | altitude     | 	高程	      | Int	     | 海拔，单位m，保留1位小数                                                      |
| 10	 | bearing      | 	方向	      | Smallint | 	0-359                                                             |
| 11  | 	time        | 	定位时间	    | Bigint   | 	单位：s                                                              |
| 12  | 	mileage	    | 行驶里程      | 	Float	  | 单位：km                                                              |
| 13	 | valid	       | 定位有效性     | 	Tinyint | 	0-无效，<br/>1-有效                                                    |
| 14	 | recv_time    | 	平台接受定位时间 | 	Bigint  | 	单位：s                                                              |
| 15  | 	batch	      | 上传类型	     | Tinyint  | 	0-正常上传，<br/>1-批量上传，<br/>2-补传                                      |

### 2.轨迹信息

`对终端在特定时间和空间内运动运动路径进行描述和记录。`

| 序号 | 字段名         | 中文名      | 类型       | 备注                                                                 |
|:---|:------------|:---------|:---------|:-------------------------------------------------------------------|
| 1  | id          | 主键       | Bigint   | 唯一标识码                                                              |
| 2  | target_id   | 目标id     | Bigint   | ——                                                                 |
| 3  | target_type | 目标类别     | Tinyint  | 1-车辆，<br/>2-人员，<br/>3-基础设施、<br/>4-集装箱                              |
| 4  | device_id   | 设备id     | Bigint   | ——                                                                 |
| 5  | device_num  | 赋码编号    | String   | ——                                                                 |
| 6  | device_type | 设备类别     | String   | 1-北斗定位终端，<br/>2-北斗穿戴式终端，<br/>3-北斗短报文终端，<br/>4-北斗监测终端，<br/>5-北斗授时终端 |
| 7  | longitude   | 经度       | Double   | 单位：°，至少精确到小数点后6位                                                   |
| 8  | latitude    | 纬度       | Double   | 单位：°，至少精确到小数点后6位                                                   |
| 9  | laltitude   | 高程       | Int      | 海拔，单位m，保留1位小数                                                      |
| 10 | speed       | 定位速度     | Float    | 单位：km/h                                                            |
| 11 | bearing     | 方向       | Smallint | 0-359                                                              |
| 12 | time        | 定位时间     | Bigint   | 单位：s                                                               |
| 13 | mileage     | 行驶里程     | Float    | 单位：km                                                              |                                                                    |
| 14 | valid       | 定位有效性    | Tinyint  | 0-无效，<br/>1-有效                                                     |
| 15 | auxiliary   | 定位附加信息   | String   | ——                                                                 |
| 16 | recv_time   | 平台接受定位时间 | Bigint   | 单位：s                                                               |
| 17 | batch       | 上传类型     | Tinyint  | 0-正常上传，<br/>1-批量上传，<br/>2-补传                                       |
| 18 | correction  | 定位纠偏     | Tinyint  | 0-未纠正，<br/>1-纠正点                                                   |                                                 |                                           |                                                                    |
| 19 | pos_sys     | 定位系统     | Tinyint  | 0-未知，<br/>1-卫星，<br/>2-网络基站，<br/>3-WIFI，<br/>4-蓝牙，<br/>5-惯导         |                                     |          |             |                                                                    |

### 3.设备状态信息

`反映终端设备当前运行运行状况，包括但不限于电源状态、硬件健康状况等。`

| 序号 | 字段名         | 中文名      | 类型        | 备注                                    |
|:---|:------------|:---------|:----------|:--------------------------------------|
| 1  | id          | 主键       | Bigint    | 唯一标识码                                 |
| 2  | device_id   | 终端设备id   | Bigint    | ——                                    |
| 3  | device_type | 终端类别   | Tinyint   | ——                                    |
| 4  | unique_id   | 序列号     | Vchar(20) | ——                                    |
| 5  | device_num  | 赋码编号  | Vchar(16) | ——                                    |
| 6  | target_id   | 监控对象id   | Bigint    | ——                                    |
| 7  | target_type | 监控对象类别   | Int       | 1-车辆，<br/>2-人员，<br/>3-基础设施、<br/>4-集装箱 |             |          |           |                |
| 8  | target_name | 监控对象名称   | Vchar(50) | ——                                    |
| 9  | dept_id     | 设备所属部门id | Bigint    | ——                                    |
| 10 | action      | 终端上下线状态  | Tinyint   | 0-上线，<br/>1-下线                        |
| 11 | action_time | 终端上下线时刻  | Datetime  | YYYYMMDDHHmmss                        |
| 12 | fault_count | 设备故障数    | Smallint  | ——                                    |

### 4.告警信息

`记录设备在运行过程中，检测到某种异常或不符合预期状态时，由设备自身或监控平台发出的通知，包括告警类型、告警级别、发生时间等。`

| 序号 | 字段名             | 中文名     | 类型         | 备注                              |
|:---|:----------------|:--------|:-----------|:--------------------------------|
| 1  | Id              | 主键      | Bigint     | 雪花算法生成                          |  
| 2  | device_id       | 终端设备id  | Bigint     | ——                              |
| 3  | device_type     | 终端类别  | Tinyint    | ——                              |                                 |
| 4  | device_num      | 赋码编号 | Vchar(16)  | ——                              |
| 5  | target_id       | 监控对象id  | Bigint     | ——                              |
| 6  | target_type     | 监控对象类别  | Tinyint    | ——                              |
| 7  | dept_id         | 所属部门id  | Bigint     | ——                              |
| 8  | type            | 告警类型    | Smallint   | ——                              |                             |
| 9  | level           | 告警等级    | Tinyint    | ——                              |                             |
| 10 | start_lon       | 告警起点经度  | Double     | ——                              |
| 11 | start_lat       | 告警起点纬度  | Double     | ——                              |
| 12 | start_time      | 告警开始时刻  | Bigint     | 单位：s                            |
| 13 | start_addr      | 告警开始地址  | Vchar(128) | ——                              |
| 14 | end_lon         | 告警结束点经度 | Double     | ——                              |                             |
| 15 | end_lat         | 告警结束点纬度 | Double     | ——                              |                             |
| 16 | end_time        | 告警结束点时刻 | Bigint     | ——                              |                             |
| 17 | end_addr        | 告警结束地址  | Vchar(128) | ——                              |
| 18 | source          | 告警来源    | Tinyint    | ——                              |                             |
| 19 | completed       | 完成状态    | Tinyint    | 0-未完成，<br/>1-已完成                |
| 20 | auxiliary       | 告警附加信息  | String     | ——                              |                                 |                                 |                             |
| 21 | handler         | 告警处理人   | Bigint     | ——                              |                                 |                                 |                             |
| 22 | handle_state    | 处理状态    | Tinyint    | 0-未处理<br/>1-确认报警（已处理），<br/>2-误报 |
| 23 | handle_measures | 处理措施    | Vchar(50)  | ——                              |
| 24 | handle_content  | 处理内容    | Vchar(255) | ——                              |
| 25 | handle_time     | 处理时间    | Bigint     | 单位：s                            |
| 26 | create_time     | 创建时间    | Bigint     | 单位：s                            |

