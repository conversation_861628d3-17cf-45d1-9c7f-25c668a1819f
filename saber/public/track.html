<!DOCTYPE html>
<html>

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <title>轨迹集成</title>
  <style>
    html,
    body,
    #app {
      height: 100%;
      margin: 0;
      padding: 0;
    }

    .avue-home {
      background-color: #303133;
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .avue-home__main {
      user-select: none;
      width: 100%;
      flex-grow: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    .avue-home__title {
      color: #FFF;
      font-size: 14px;
      margin-bottom: 10px;
    }

    .notification {
      display: flex;
      width: 330px;
      padding: 14px 26px 14px 13px;
      border-radius: 8px;
      box-sizing: border-box;
      border: 1px solid #ebeef5;
      position: fixed;
      top: 16px;
      right: 16px;
      z-index: 2010;
      background-color: #fff;
      box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
      transition: opacity .3s,transform .3s,left .3s,right .3s,top .4s,bottom .3s;
      overflow: hidden;
      display: none;
    }

    .notification__group {
      margin-left: 13px;
      margin-right: 8px;
    }
    
    .notification__title {
      font-weight: 700;
      font-size: 16px;
      color: #303133;
      margin: 0;
    }

    .notification__content {
      font-size: 14px;
      line-height: 21px;
      margin: 6px 0 0;
      color: red;
      text-align: justify;
    }

    .notification__p {
      margin: 0;
    }

    .notification__closeBtn {
      position: absolute;
      top: 10px;
      right: 15px;
      cursor: pointer;
      color: #909399;
      font-size: 22px;
    }

  </style>
</head>
<script>
  const menu = {
    iconDefault: 'iconfont icon-caidan',
    props: {
      label: 'name',
      path: 'path',
      icon: 'source',
      children: 'children'
    }
  };
  const keyName = 'saber-';
  const url = new URL(window.location.href);
  const code = url.search.match(/code=([^&]+)/);
  const content = url.search.match(/content=([^&]+)/);
  const sign = url.search.match(/sign=([^&]+)/);
  const params = new URLSearchParams(url.search);
  const unitCode = params.get('unitCode');
  const dataType = params.get('dataType');
  fetch(`${url.origin}/bdsPlatformApi/vdm-inter-auth/celoc/getPageToken`, {
    method: 'POST',
    headers: {
      'Content-type': 'application/json'
    },
    body: JSON.stringify({
      username: params.get('username'),
      code: code[1],
      content: content[1],
      sign: sign[1]
    })
  }).then(response => response.json()).then(data => {
    if (data.code) {
      let dom = document.getElementsByClassName('notification')[0];
      let p = dom.getElementsByClassName('notification__p')[0];
      p.innerHTML = data.text;
      dom.style.display = 'block';
      setTimeout(() => {
        dom.style.display = 'none';
      }, 3000)
      return;
    }
    setStore({ name: 'token', content: data.access_token })
    setStore({ name: 'refreshToken', content: data.refresh_token })
    setStore({ name: 'tenantId', content: data.tenant_id })
    setStore({ name: 'userInfo', content: data })
    setStore({ name: 'official-unitCode', content: unitCode })
    setStore({ name: 'official-dataType', content: dataType })
    localStorage.setItem('user', JSON.stringify(data)) // 放到缓存中方便api模块调用
    setCookie('saber-access-token', data.access_token)
    fetch(`${url.origin}/bdsPlatformApi/blade-system/menu/routes`, {
      headers: {
        'Content-type': 'application/json',
        'Blade-Auth': `bearer ${data.access_token}`,
        'Authorization': 'Basic c2FiZXI6c2FiZXJfc2VjcmV0'
      }
    }).then(response => response.json()).then(res => {
      let menu = deepClone(res.data);
      menu.forEach(ele => {
        addPath(ele, true);
      });
      setStore({ name: 'menuAll', content: menu })
      setStore({ name: 'menu', content: menu })
      window.open(`${url.origin}/bdsplatform/monitoring/trackInfo/forwarding`, '_self');
    })
  }).catch(error => {
    console.error('Error:', error)
  });
  const setStore = (params = {}) => {
    let {
      name,
      content,
      type,
    } = params;
    name = keyName + name
    let obj = {
      dataType: typeof (content),
      content: content,
      type: type,
      datetime: new Date().getTime()
    }
    if (type) window.sessionStorage.setItem(name, JSON.stringify(obj));
    else window.localStorage.setItem(name, JSON.stringify(obj));
  }
  const setCookie = (name, value, days) => {
    var expires = "";
    if (days) {
      var date = new Date();
      date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
      expires = "; expires=" + date.toUTCString();
    }
    document.cookie = name + "=" + (value || "") + expires + "; path=/";
  }
  const deepClone = data => {
    var type = getObjType(data);
    var obj;
    if (type === 'array') {
      obj = [];
    } else if (type === 'object') {
      obj = {};
    } else {
      //不再具有下一层次
      return data;
    }
    if (type === 'array') {
      for (var i = 0, len = data.length; i < len; i++) {
        obj.push(deepClone(data[i]));
      }
    } else if (type === 'object') {
      for (var key in data) {
        obj[key] = deepClone(data[key]);
      }
    }
    return obj;
  };
  const getObjType = obj => {
    var toString = Object.prototype.toString;
    var map = {
      '[object Boolean]': 'boolean',
      '[object Number]': 'number',
      '[object String]': 'string',
      '[object Function]': 'function',
      '[object Array]': 'array',
      '[object Date]': 'date',
      '[object RegExp]': 'regExp',
      '[object Undefined]': 'undefined',
      '[object Null]': 'null',
      '[object Object]': 'object'
    };
    if (obj instanceof Element) {
      return 'element';
    }
    return map[toString.call(obj)];
  };
  const addPath = (ele, first) => {
    const data = menu;
    const propsConfig = data.props;
    const propsDefault = {
      label: propsConfig.label || 'name',
      path: propsConfig.path || 'path',
      icon: propsConfig.icon || 'icon',
      children: propsConfig.children || 'children'
    }
    const icon = ele[propsDefault.icon];
    ele[propsDefault.icon] = validatenull(icon) ? data.iconDefault : icon;
    const isChild = ele[propsDefault.children] && ele[propsDefault.children].length !== 0;
    if (!isChild) ele[propsDefault.children] = [];
    if (!isChild && first && !isURL(ele[propsDefault.path])) {
      ele[propsDefault.path] = ele[propsDefault.path] + '/index'
    } else {
      ele[propsDefault.children].forEach(child => {
        addPath(child);
      })
    }

  }
  const validatenull = (val) => {
    if (typeof val == 'boolean') {
      return false;
    }
    if (typeof val == 'number') {
      return false;
    }
    if (val instanceof Array) {
      if (val.length == 0) return true;
    } else if (val instanceof Object) {
      if (JSON.stringify(val) === '{}') return true;
    } else {
      if (val == 'null' || val == null || val == 'undefined' || val == undefined || val == '') return true;
      return false;
    }
    return false;
  }
  const isURL = (s) => {
    return /^http[s]?:\/\/.*/.test(s)
  }
  const handleClick = () => {
    let dom = document.getElementsByClassName('notification')[0];
    dom.style.display = 'none';
  }
</script>

<body>
  <div id="app">
    <div class="avue-home">
      <div class="avue-home__main">
        <div class="avue-home__title">
          正在加载资源
        </div>
      </div>
    </div>
    <div class="notification">
      <div class="notification__group">
        <h2 class="notification__title">提示</h2>
        <div class="notification__content"><p class="notification__p">请求接口失败</p></div>
        <div class="notification__closeBtn" onclick="handleClick()">×</div>
      </div>
    </div>
  </div>

</body>

</html>