import defaultValue from '../Core/defaultValue'
import defineProperties from '../Core/defineProperties'
import destroyObject from '../Core/destroyObject'
import AssociativeArray from '../Core/AssociativeArray'

'use strict';

/**
 * IndexCollection
 * @description 索引集合，暂时只保留摄像头索引
 * @example var indexCollection = new IndexCollection();
 * @param options
 * @param {Array<Object>} [options.cameraList]
 * @param {Boolean} options.cameraList[].online
 * @param {Object} options.cameraList[].camera 摄像头对象
 * @param {String} options.cameraList[].camera.name
 * @param {AssociativeArray} [options.cameraAssociativeArray] 摄像头索引
 *
 * @constructor
 * @exports IndexCollection
 */
function IndexCollection(options) {
  //>>includeStart('debug', pragmas.debug);
  //>>includeEnd('debug');
  options = defaultValue(options, {});

  /**
   * 根据摄像头ID索引摄像头详情，楼层详情，建筑物详情
   * @example
   * var channelID = '34020001111320000025';
   * var camera = that.indexCollection.cameraAssociativeArray.get(channelID);
   */
  this.cameraAssociativeArray = defaultValue(options.cameraAssociativeArray, new AssociativeArray());

}

defineProperties(IndexCollection.prototype, {
});

/**
 * @returns {Boolean} true if the object has been destroyed, false otherwise.
 */
IndexCollection.prototype.isDestroyed = function() {
  return false;
};

/**
 * Destroys the widget.  Should be called if permanently
 * removing the widget from layout.
 */
IndexCollection.prototype.destroy = function() {
  return destroyObject(this);
};

export default IndexCollection;
