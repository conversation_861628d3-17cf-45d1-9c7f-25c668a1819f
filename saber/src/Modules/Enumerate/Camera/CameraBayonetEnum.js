'use strict';

const cameraBayonetOptions = [
  {
    value: 1,
    label: '卡口相机'
  },
  {
    value: 2,
    label: '其他相机'
  }
];

/**
 * 相机用途(枚举类)
 * @alias CameraBayonetEnum（相机类型枚举类）
 * @exports CameraBayonetEnum
 */
export default {
  /**
   * 卡口相机
   *
   * @type {Number}
   * @constant
   */
  KAKOU : 1,

  /**
   * 其他
   *
   * @type {Number}
   * @constant
   */
  OTHERS : 2,

  /**
   * 获取下拉框的选项
   * @return {<{value: Number, label: String}>[]}
   */
  getOptions: function () {
    return cameraBayonetOptions;
  },
  /**
   * 根据键值获取字符串
   * @param {String} value 枚举值
   * @return {string}
   */
  getLabel: function (value) {
    switch (value){
      case 1:
        return '卡口相机';
      case 2:
        return '其他相机';
    }
    return '错误值';
  }
}
