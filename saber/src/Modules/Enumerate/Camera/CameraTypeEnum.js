'use strict';
/**
 * 相机类型(枚举类)
 * @alias CameraTypeEnum（相机类型枚举类）
 * @exports CameraTypeEnum
 */
const cameraTypeOptions = [
  {
    value: 1,
    label: '枪机'
  },
  {
    value: 2,
    label: '球机'
  }
];

/**
 * 相机用途(枚举类)
 * @alias CameraTypeEnum（相机类型枚举类）
 * @exports CameraTypeEnum
 */
export default {
  /**
   * 枪机
   *
   * @type {Number}
   * @constant
   */
  NORMAL : 1,

  /**
   * 球机
   *
   * @type {Number}
   * @constant
   */
  SPEED_DOME : 2,

  /**
   * 获取下拉框的选项
   * @return {<{value: Number, label: String}>[]}
   */
  getOptions: function () {
    return cameraTypeOptions;
  },
  /**
   * 根据键值获取字符串
   * @param {String} value 枚举值
   * @return {string}
   */
  getLabel: function (value) {
    switch (value){
      case 1:
        return '枪机';
      case 2:
        return '球机';
    }
    return '错误值';
  }
}

