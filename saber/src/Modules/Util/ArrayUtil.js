'use strict';

/**
 * 数组处理帮助模块
 * @exports ArrayUtil
 * @alias ArrayUtil
 */
let ArrayUtil = {};

/**
 * 随机排序
 * @param a
 * @returns {Array}
 */
ArrayUtil.randomSort = function(a){
  let arr = a,
    random = [],
    len = arr.length;
  for (let i = 0; i < len; i++) {
    let index = Math.floor(Math.random()*(len - i));
    random.push(a[index]);
    arr.splice(index, 1);
  }
  return random;
};

/**
 * 循环移动数组 => ab转换为ba => (a逆置b逆置)逆置 = ba
 * @param arr 移动的数组
 * @param count 移动多少位 正数表示左移，负数表示右移
 */
ArrayUtil.recycMoveArray = function(arr, count){
  let end = arr.length - 1; //获取数组的结束下标
  let leftArrEnd = count > 0 ? --count : end + count;
  ArrayUtil.reverse(arr, 0, leftArrEnd);
  ArrayUtil.reverse(arr, leftArrEnd+1, end);
  ArrayUtil.reverse(arr, 0, end);
};

/**
 * 将数组逆置的函数
 * @param arr 逆置的数组
 * @param start 逆置数组的开始下标
 * @param end 逆置数组的结束下标
 */
ArrayUtil.reverse = function(arr, start, end){
  //如果指针不等
  while(start <= end){ //start != end是有缺陷的，当是偶数个时不能跳出
    //调换start和end指向的值
    let temp = arr[start];
    arr[start] = arr[end];
    arr[end] = temp;
    //指针移动
    start++;
    end--;
  }
};

export default ArrayUtil;
