/**
 * 浏览器销毁事件注册中心
 * @constructor
 * @description 已抓包验证可行性
 */
function UploadEvent () {
  this._callbackList = [];
  const beforeUnloadListener = (event) => {
    if (this._callbackList.length > 0) {
      for (let i = 0; i < this._callbackList.length; i++) {
        let callback = this._callbackList[i];
        callback();
      }
      // var dialogText = '中文';
      // event.returnValue = dialogText;
      return true;
    } else {
      return true;
    }
  };

  addEventListener('beforeunload', beforeUnloadListener, {capture: true});
}

UploadEvent.prototype.registerEvent = function (callback) {
  this._callbackList.push(callback);
};

let uploadEvent;
if (window.uploadEvent) {
  // console.log('old');
  uploadEvent = window.uploadEvent;
} else {
  // console.log('new');
  uploadEvent = new UploadEvent();
}

export default uploadEvent;
