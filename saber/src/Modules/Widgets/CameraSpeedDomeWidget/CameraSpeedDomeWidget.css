.lux-CameraSpeedDomeWidget-wrapper {
    /*display: inline-block;*/
    /*height: 100%;*/
    /*width: 130px;*/
    background-color: unset;
    /*pointer-events: all;*/
}

/*九宫格控制*/
.lux-CameraSpeedDomeWidget-baseControlGroup{
    position: relative;
    width: 130px;
    height: 130px;
    background-image: url(./Images/wholeBackground.png);
    background-size: 100% 100%;
}
.lux-CameraSpeedDomeWidget-baseControlGroup button{
    /*position: relative;*/
    width: 100%;
    height: 100%;
    background: unset;
    border: 0;
    color: #ffffff00;/*透明*/
    pointer-events: all;
}
.lux-CameraSpeedDomeWidget-baseControlGroup button:hover{
    border-radius: 10px;
    border: 1px solid #ea4;
    box-shadow: 0 0 8px #fff;
    outline: none;
}

.lux-CameraSpeedDomeWidget-baseControlGroup-leftTopItem{
    position: absolute;
    top:0;
    left: 0;
    width: 33%;
    height: 33%;
}
.lux-CameraSpeedDomeWidget-baseControlGroup-middleTopItem{
    position: absolute;
    top:0;
    left: 33%;
    width: 34%;
    height: 33%;
}
.lux-CameraSpeedDomeWidget-baseControlGroup-rightTopItem{
    position: absolute;
    top:0;
    right: 0;
    width: 33%;
    height: 33%;
}

.lux-CameraSpeedDomeWidget-baseControlGroup-leftMiddleItem{
    position: absolute;
    top:33%;
    left: 0;
    width: 33%;
    height: 34%;
}
.lux-CameraSpeedDomeWidget-baseControlGroup-middleMiddleItem{
    position: absolute;
    top:33%;
    left: 33%;
    width: 34%;
    height: 34%;
}
.lux-CameraSpeedDomeWidget-baseControlGroup-rightMiddleItem{
    position: absolute;
    top:33%;
    right: 0;
    width: 33%;
    height: 34%;
}
.lux-CameraSpeedDomeWidget-baseControlGroup-leftBottomItem{
    position: absolute;
    bottom: 0;
    left: 0;
    width: 33%;
    height: 33%;
}
.lux-CameraSpeedDomeWidget-baseControlGroup-middleBottomItem{
    position: absolute;
    bottom: 0;
    left: 33%;
    width: 34%;
    height: 33%;
}
.lux-CameraSpeedDomeWidget-baseControlGroup-rightBottomItem{
    position: absolute;
    bottom: 0;
    right: 0;
    width: 33%;
    height: 33%;
}

.lux-CameraSpeedDomeWidget-nearFarLightDarkenGroup{
    position: relative;
    display: flex;
    width: 100%;
    height: 25px;
}
.lux-CameraSpeedDomeWidget-nearFarLightDarkenGroup button{
    position: relative;
    width: 20%;
    height: 100%;
    /*background-color: blue;*/
}
.lux-CameraSpeedDomeWidget-nearFarLightDarkenGroup span{
    position: relative;
    width: 60%;
    height: 100%;
}
.lux-CameraSpeedDomeWidget-nearFarLightDarkenGroup label{
    position: relative;
    width: 60%;
    height: 100%;
    text-align: center;
}

.lux-CameraSpeedDomeWidget-preciseControlInformationGroup{
    position: relative;
    width: 100%;
    height: 25px;
    display: flex;
}
.lux-CameraSpeedDomeWidget-preciseControlInformationGroup span{
    width: 20%;
    height: 100%;
    border: 0;
}
.lux-CameraSpeedDomeWidget-preciseControlInformationGroup input{
    position: relative;
    width: 60%;
    height: 100%;
}
.lux-CameraSpeedDomeWidget-preciseControlInformationGroup button{
    width: 20%;
    height: 100%;
}

.lux-CameraSpeedDomeWidget-canvas{
    position: relative;
    width: 100%;
    height: 100px;
    background: #666666;
}

.lux-CameraSpeedDomeWidget-currentPositionPlan{
    position: relative;
    display: flex;
    width: 100%;
    height: 25px;
}
.lux-CameraSpeedDomeWidget-currentPositionPlan button{
    width: 20%;
    height: 100%;
}
.lux-CameraSpeedDomeWidget-currentPositionPlan input{
    height: 100%;;
    width: 60%;
}

.lux-CameraSpeedDomeWidget-planSelectionContainer{
    position: absolute;
    background-color: white;
    color: #000;
    overflow-y: auto;
    width: 100%;
    z-index: 1000;
    border: solid 2px;
    transition: all 1s linear;
}
.lux-CameraSpeedDomeWidget-planSelectionContainer ul{
    width: 100%;
    background-color: transparent;
}

.lux-CameraSpeedDomeWidget-planSelectionContainer-li{
    width: 100%;
    display: flex;
    background-color: transparent;
}
.lux-CameraSpeedDomeWidget-planSelectionContainer-li span{
    width: 80%;
    background-color: transparent;
}
.lux-CameraSpeedDomeWidget-planSelectionContainer-li input{
    width: 80%;
    background-color: transparent;
}
.lux-CameraSpeedDomeWidget-planSelectionContainer-li button{
    width: 20%;
    flex: 1;
    background-color: transparent;
}
.lux-CameraSpeedDomeWidget-planSelectionContainer-li-active{
    background: #48b;
}

.lux-CameraSpeedDomeWidget-historyPlanDetail{
    position: absolute;
    background-color: white;
    color: #000;
    overflow-y: auto;
    width: 80px;
    right: 100%;
    z-index: 1000;
    border: solid 2px;
    transition: all 1s linear;
}
.lux-CameraSpeedDomeWidget-historyPlanDetail span{
    width: 80%;
    height: 100%;
    border: 0;
}
.lux-CameraSpeedDomeWidget-historyPlanDetail button{
    position: relative;
    height: 100%;
}

.lux-CameraSpeedDomeWidget-historyPlanDetail-title{
    display: flex;
}

/*十字鼠标*/
.lux-CameraSpeedDomeWidget-crosshairCursor{
    cursor: crosshair;
}
/*禁止鼠标*/
.lux-CameraSpeedDomeWidget-notAllowCursor{
    cursor: not-allowed;
}
