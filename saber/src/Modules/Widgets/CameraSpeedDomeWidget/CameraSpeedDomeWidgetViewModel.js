import defaultValue from '../../Core/defaultValue'
import defined from '../../Core/defined'
import defineProperties from '../../Core/defineProperties'
import destroyObject from '../../Core/destroyObject'
import createCommand from '../../Widgets/createCommand'
import CameraManufacturerTypeEnum from '../../Enumerate/Camera/CameraManufactureType'
import SpeedDomeCameraProtocol from '../../Protocol/SpeedDomeCameraProtocol'
import knockout from '../../ThirdParty/knockout'

'use strict';

/**
 * CameraSpeedDomeWidgetViewModel
 *
 * @alias CameraSpeedDomeWidgetViewModel
 *
 * @param options
 * @param {VideoPlayerRTMP|VideoPlayerJanus} options.videoPlayer
 *
 * @constructor
 */
function CameraSpeedDomeWidgetViewModel(options) {
  //>>includeStart('debug', pragmas.debug);
  options = defaultValue(options, {});
  //>>includeEnd('debug');

  this._videoPlayer = options.videoPlayer;

  this.show = false;
  this.biggerThanSize = false; // videoplayer是否大于一定的尺寸
  this.initBiggerThanSizeEvent()

  this.historyPlans = [];
  this.initPrecisePanTileZoomFromLocalStorage(); // 20200907注释掉

  this.selectedHistoryPlan = this.historyPlans[0];
  this.planSelectionContainerVisible = false;
  this.togglePlanSelectionContainerButtonText = '▼';
  this.selectedHistoryPlanName = this.selectedHistoryPlan.displayName;

  // 实时角度
  this.currentPanAngle = '';
  this.currentTiltAngle = '';
  this.currentZoomLevel = '';

  // 用户定义角度
  this.panAngle = '0';
  this.tiltAngle = '0';
  this.zoomLevel = '4';

  // 用户偏好历史球机参数详情
  this.historyPlanPanAngle = '';
  this.historyPlanTiltAngle = '';
  this.historyPlanZoomLevel = '';
  this.historyPlanDetailVisible = '';

  // 默认sdk参数 TODO
  this.sdkConfig = {
    manufacturer: 'Dahua',
    ip: '*************',
    port: '37777',
    user: 'admin',
    password: 'admin123'
  };

  // 最新的控制命令
  this._controlEventCount = 0;
  this._controlEvent = {
    count: this._controlEventCount,
    name: ''
  };

  // 球机十字控件是否可见
  this.baseControlGroupVisible = true;

  // 加入readonly属性
  knockout.bindingHandlers.readonly = {
    init: function(element, valueAccessor) {
      if(element.tagName === 'INPUT' && $(element).attr('type') === 'text') {
        if(valueAccessor()) {
          element.readOnly = true;
        }else{
          element.readOnly = false;
        }
      }
    },
    update: function(element, valueAccessor) {
      if(element.tagName === 'INPUT' && $(element).attr('type') === 'text') {
        if(valueAccessor()) {
          element.readOnly = true;
        }else{
          element.readOnly = false;
        }
      }
    }
  };

  knockout.track(this, ['show', 'panAngle', 'tiltAngle', 'zoomLevel', 'currentPanAngle', 'currentTiltAngle', 'currentZoomLevel',
    'historyPlans', 'selectedHistoryPlan', 'selectedHistoryPlanName', 'planSelectionContainerVisible', 'togglePlanSelectionContainerButtonText',
    'historyPlanPanAngle', 'historyPlanTiltAngle', 'historyPlanZoomLevel', 'historyPlanDetailVisible',
    'baseControlGroupVisible', 'biggerThanSize'
  ]);

  var that = this;

  /**
   * 平移旋转
   * @param command
   */
  this.panTiltFunction = function (command) {
    that.panTiltZoom(command);
  };

  /**
   * 变焦、变光圈
   * @param command
   */
  this.nearFarLightDarkenFunction = function (command) {
    that.nearFarLightDarken(command);
  };

  /**
   * 触发下拉框
   */
  this.togglePlanSelectionContainer = function () {
    if(that.planSelectionContainerVisible === true){
      that.togglePlanSelectionContainerButtonText = '▼';
      that.planSelectionContainerVisible = false;
    }else{
      that.togglePlanSelectionContainerButtonText = '▲';
      that.planSelectionContainerVisible = true;
    }
  };

  /**
   * 选择某个方案
   * @param data
   */
  this.activatePlanSelection = function (data) {
    console.log(data);
    if(data.addVisible === false && data.saveVisible === false){
      that.planSelectionContainerVisible = false;
      that.togglePlanSelectionContainerButtonText = '▼';
      that.selectedHistoryPlanName = data.displayName;
    }

    if(defined(data.data) && data.data.currentPanAngle !== '' && data.data.currentTiltAngle !== '' && data.data.currentZoomLevel !== ''){
      that.historyPlanPanAngle = data.data.currentPanAngle;
      that.historyPlanTiltAngle = data.data.currentTiltAngle;
      that.historyPlanZoomLevel = data.data.currentZoomLevel;
      that.panAngle = data.data.currentPanAngle;
      that.tiltAngle = data.data.currentTiltAngle;
      that.zoomLevel = data.data.currentZoomLevel;
      that.precisePanTileZoomControl();
    }

  };

  /**
   * 隐藏用户自定球机位置详情
   */
  this.hideHistoryPlanDetail = function () {
    this.historyPlanDetailVisible = false;
  };

  /**
   * 显示用户自定球机位置详情
   */
  this.showHistoryPlanDetail = function () {
    this.historyPlanDetailVisible = true;
  };

  /**
   * 鼠标悬停在某个选项
   * @param data
   * @param event
   */
  this.handleMouseoverPlanSelection = function (data, event) {
    if (data !== that.selectedHistoryPlan) {
      that.selectedHistoryPlan = data;
    }
  };

  /**
   * 新增按钮事件
   * @param data
   */
  this.addPlanSelectionItemClickFunc = function (data) {
    console.log(data);
    if(data.displayName !== ''){
      that.addPlanSelections(data);
    }else{
      data.displayName = '未命名';
      that.addPlanSelections(data);
    }
  };

  /**
   * 修改按钮事件
   * @param data
   */
  this.modifyPlanSelectionItemClickFunc = function (data) {
    console.log(data);
    that.modifyPlanSelections(data);
  };

  /**
   * 删除按钮事件
   * @param data
   */
  this.deletePlanSelectionItemClickFunc = function (data) {
    console.log(data);
    that.deletePlanSelections(data);
  };

  /**
   * 保存按钮事件
   * @param data
   */
  this.savePlanSelectionItemClickFunc = function (data) {
    console.log(data);
    that.savePlanSelections(data);
  };

  /**
   * 滚轮事件缩放
   * @param event
   * @private
   */
  this._scrollFunc = function (event) {
    that.dragZoomoutZoominGb28181(event);
    // that.dragZoomoutZoominSdk(event);// FIXME 花城汇不使用国标接口
  };

  /**
   * 单击放大事件
   * @param event
   * @private
   */
  this._clickFunc = function (event) {
    that.dragZoomoutZoominGb28181(event);
    // that.dragZoomoutZoominSdk(event);// FIXME 花城汇不使用国标接口
  };

  this._goToTargetLocation = createCommand(function() {
    // console.log('精确控制');
    that.precisePanTileZoomControl();
  });

  knockout.getObservable(this, 'show').subscribe(function(isShow) {
    if(isShow){
      // that._videoPlayer.videoElementOverlay.addEventListener('mousewheel', that._scrollFunc, false);
      // that._videoPlayer.videoElementOverlay.addEventListener('click', that._clickFunc, false);
      //
      // that._videoPlayer.videoElementOverlay.classList.add('lux-CameraSpeedDomeWidget-crosshairCursor');

      that.subscribeMouseZoomEvent();
      that.baseControlGroupVisible = that._videoPlayer.isBigVideoPlayer;

      // FIXME
      // var getMultiCameraDetailPromise = CameraProtocol.getMultiCameraDetail({
      //     equipIds: that._videoPlayer.channelID
      // });
      // getMultiCameraDetailPromise.then(function (json) {
      //     console.log(json);
      //     // that.sdkLogin();
      //     that.formatSdkConfig(json[0]);
      //
      //     that.makeSureSdkIsReadyToUse();
      //
      //     that.subscribeSyncPreciseQueryLocation();
      //
      //     that.initPrecisePanTileZoomFromLocalStorage();
      // }).catch(function (msg) {
      //     console.log(msg);
      // });
    }else{
      that.unsubscribeMouseZoomEvent();

      // that._videoPlayer.videoElementOverlay.removeEventListener('mousewheel', that._scrollFunc, true);
      //
      // that._videoPlayer.videoElementOverlay.style.cursor = '';
      // console.log(that._videoPlayer.videoElementOverlay.style.cursor);
      // that.cancelSubscribeSyncPreciseQueryLocation();
    }
  });

  knockout.getObservable(this._videoPlayer, 'isBigVideoPlayer').subscribe(function(isBigVideoPlayer) {
    // console.log(isBigVideoPlayer);
    that.baseControlGroupVisible = isBigVideoPlayer;
  });

}

defineProperties(CameraSpeedDomeWidgetViewModel.prototype, {
  /**
   * 旋转到特定的状态
   * @memberof CameraSpeedDomeWidgetViewModel.prototype
   *
   * @type {Command}
   */
  goToTargetLocation : {
    get : function() {
      return this._goToTargetLocation;
    }
  }
});

/**
 * 新增用户自定义球机角度方案
 * @param data
 */
CameraSpeedDomeWidgetViewModel.prototype.addPlanSelections = function (data) {
  var historyPlans = this.historyPlans;
  // console.log(historyPlans);
  // console.log(data);
  var newHistoryPlans = [];
  for(var i = 0; i < historyPlans.length; i++){
    if(historyPlans[i] === data){
      newHistoryPlans.push({
        displayName: data.displayName,
        addVisible: false,
        modifyVisible: true,
        deleteVisible: true,
        saveVisible: false,
        nameEditable: false,
        data: {
          currentPanAngle: this.currentPanAngle,
          currentTiltAngle: this.currentTiltAngle,
          currentZoomLevel: this.currentZoomLevel
        }
      });
    }else{
      newHistoryPlans.push(historyPlans[i]);
    }
  }

  if(historyPlans.length < 10){
    var newItem = {
      displayName: '',
      addVisible: true,
      modifyVisible: false,
      deleteVisible: false,
      saveVisible: false,
      nameEditable: true
    };
    newHistoryPlans.push(newItem);
  }
  // console.log(newHistoryPlans);

  this.historyPlans = newHistoryPlans;
  this.savePrecisePanTileZoomToLocalStorage();

};

/**
 * 修改用户自定义球机角度方案
 * @param data
 */
CameraSpeedDomeWidgetViewModel.prototype.modifyPlanSelections = function (data) {
  var historyPlans = this.historyPlans;

  var newHistoryPlans = [];
  for(var i = 0; i < historyPlans.length; i++){
    if(historyPlans[i] === data){
      newHistoryPlans.push({
        displayName: data.displayName,
        addVisible: false,
        modifyVisible: false,
        deleteVisible: false,
        saveVisible: true,
        nameEditable: true,
        data: {
          currentPanAngle: this.currentPanAngle,
          currentTiltAngle: this.currentTiltAngle,
          currentZoomLevel: this.currentZoomLevel
        }
      });
    }else{
      newHistoryPlans.push(historyPlans[i]);
    }
  }
  // console.log(newHistoryPlans);

  this.historyPlans = newHistoryPlans;
  this.savePrecisePanTileZoomToLocalStorage();

};

/**
 * 删除用户自定义球机角度方案
 * @param data
 */
CameraSpeedDomeWidgetViewModel.prototype.deletePlanSelections = function (data) {
  var historyPlans = this.historyPlans;

  var newHistoryPlans = [];
  for(var i = 0; i < historyPlans.length; i++){
    if(historyPlans[i] === data){
      // console.log('什么都不做');
    }else{
      newHistoryPlans.push(historyPlans[i]);
    }
  }
  // console.log(newHistoryPlans);

  this.historyPlans = newHistoryPlans;
  this.savePrecisePanTileZoomToLocalStorage();

};

/**
 * 保存用户自定义球机角度方案
 * @param data
 */
CameraSpeedDomeWidgetViewModel.prototype.savePlanSelections = function (data) {
  var historyPlans = this.historyPlans;

  var newHistoryPlans = [];
  for(var i = 0; i < historyPlans.length; i++){
    if(historyPlans[i] === data){
      newHistoryPlans.push({
        displayName: data.displayName,
        addVisible: false,
        modifyVisible: true,
        deleteVisible: true,
        saveVisible: false,
        nameEditable: false,
        data: {
          currentPanAngle: this.currentPanAngle,
          currentTiltAngle: this.currentTiltAngle,
          currentZoomLevel: this.currentZoomLevel
        }
      });
    }else{
      newHistoryPlans.push(historyPlans[i]);
    }
  }
  // console.log(newHistoryPlans);

  this.historyPlans = newHistoryPlans;
  this.savePrecisePanTileZoomToLocalStorage();

};

/**
 * 缓存精确角度
 */
CameraSpeedDomeWidgetViewModel.prototype.savePrecisePanTileZoomToLocalStorage = function () {
  if(this._videoPlayer.hasValidChannelId !== true){
    return;
  }
  var cameraSpeedDomeWidgetViewModelString = window.localStorage.getItem('CameraSpeedDomeWidgetViewModel');
  var cameraSpeedDomeWidgetViewModelObject = {};
  if(defined(cameraSpeedDomeWidgetViewModelString)){
    cameraSpeedDomeWidgetViewModelObject = JSON.parse(cameraSpeedDomeWidgetViewModelString);
    cameraSpeedDomeWidgetViewModelObject[this._videoPlayer.channelID] = this.historyPlans;
  }else{
    cameraSpeedDomeWidgetViewModelObject[this._videoPlayer.channelID] = this.historyPlans;
  }

  if(defined(cameraSpeedDomeWidgetViewModelObject)){
    window.localStorage.setItem('CameraSpeedDomeWidgetViewModel', JSON.stringify(cameraSpeedDomeWidgetViewModelObject));
  }
};

/**
 * 初始化用户自定义球机角度方案
 */
CameraSpeedDomeWidgetViewModel.prototype.initPrecisePanTileZoomFromLocalStorage = function () {
  var historyPlans;
  var cameraSpeedDomeWidgetViewModelObject;
  var cameraSpeedDomeWidgetViewModelString = window.localStorage.getItem('CameraSpeedDomeWidgetViewModel');

  if(defined(cameraSpeedDomeWidgetViewModelString)){
    cameraSpeedDomeWidgetViewModelObject = JSON.parse(cameraSpeedDomeWidgetViewModelString);
    if(this._videoPlayer.hasValidChannelId === true){
      historyPlans = cameraSpeedDomeWidgetViewModelObject[this._videoPlayer.channelID];
    }
  }

  historyPlans = defaultValue(historyPlans, [{
    displayName: '',
    addVisible: true,
    modifyVisible: false,
    deleteVisible: false,
    saveVisible: false,
    nameEditable: true
  }]);

  this.historyPlans = historyPlans;
};

/**
 * 控制云台转动，平移旋转放大放小控制
 * @param {String} command 控制命令 { left, right, up, down, upleft, upright, downleft,downright,zoomin, zoomout, stop }
 * @param {Number} [speed] 控制速度 0~255，默认为129,可以不填
 */
CameraSpeedDomeWidgetViewModel.prototype.panTiltZoom = function(command, speed) {
  this.addNewControlEvent('panTiltZoom');
  var panTileZoomControlPromise = SpeedDomeCameraProtocol.panTileZoomControl({
    channelID: this._videoPlayer.channelID,
    command: command
  });
  panTileZoomControlPromise.then(function (res) {
    // console.log(res);
  }).catch(function (msg){
    console.log(msg);
  });
};

/**
 * 控制云台焦点和光圈
 * @param {String} command { near拉近 far拉远 light变亮  darken变暗 stop停止}发送一次变化以后需要stop才能停止。
 * @param {Number} [speed] 控制速度 0~255，默认为129,可以不填
 */
CameraSpeedDomeWidgetViewModel.prototype.nearFarLightDarken = function(command, speed) {
  this.addNewControlEvent('nearFarLightDarken');
  var nearFarLightDarkenPromise = SpeedDomeCameraProtocol.nearFarLightDarkenControl({
    channelID: this._videoPlayer.channelID,
    command: command
  });
  nearFarLightDarkenPromise.then(function (res) {
    // console.log(res);
  }).catch(function (msg){
    console.log(msg);
  });

};

/**
 * 鼠标滚轮放大缩小指定位置
 * @description GB28181接口
 * @param event
 */
CameraSpeedDomeWidgetViewModel.prototype.dragZoomoutZoominGb28181 = function(event) {
  this.addNewControlEvent('dragZoomoutZoominGb28181');
  var videoElementOverlay = this._videoPlayer.videoElementOverlay;
  if(event.wheelDelta < 0){
    var dragZoomoutControlPromise = SpeedDomeCameraProtocol.dragZoomoutControl({
      channelID: this._videoPlayer.channelID,
      length: videoElementOverlay.clientWidth,
      width: videoElementOverlay.clientHeight,
      midpointx: event.offsetX,
      midpointy: event.offsetY,
      lengthx: parseInt(videoElementOverlay.clientWidth * 0.5),
      lengthy: parseInt(videoElementOverlay.clientHeight * 0.5)
    });
    dragZoomoutControlPromise.then(function (res) {
      console.log(res);
    }).catch(function (msg){
      console.log(msg);
    });
  }else if(event.wheelDelta > 0){
    var dragZoominControlPromise = SpeedDomeCameraProtocol.dragZoominControl({
      channelID: this._videoPlayer.channelID,
      length: videoElementOverlay.clientWidth,
      width: videoElementOverlay.clientHeight,
      midpointx: event.offsetX,
      midpointy: event.offsetY,
      lengthx: parseInt(videoElementOverlay.clientWidth * 0.5),
      lengthy: parseInt(videoElementOverlay.clientHeight * 0.5)
    });
    dragZoominControlPromise.then(function (res) {
      console.log(res);
    }).catch(function (msg){
      console.log(msg);
    });
  }
};

/**
 * 鼠标滚轮放大缩小指定位置
 * @description SDK接口
 * @param event
 */
CameraSpeedDomeWidgetViewModel.prototype.dragZoomoutZoominSdk = function(event) {
  this.addNewControlEvent('dragZoomoutZoominSdk');
  var videoElementOverlay = this._videoPlayer.videoElementOverlay;
  var widthHalf = parseInt(videoElementOverlay.clientWidth * 0.25);
  var heightHalf = parseInt(videoElementOverlay.clientHeight * 0.25);
  var requireData;
  if(event.wheelDelta < 0){
    requireData = {
      channel: parseInt(this._videoPlayer.cameraObject.nvrChannel),
      manufacturer: this._videoPlayer.cameraObject.manufacturerTypeSdkName,
      width: videoElementOverlay.clientWidth,
      height: videoElementOverlay.clientHeight,
      beginX: Math.max(event.offsetX - widthHalf, 0),
      beginY: Math.max(event.offsetY - heightHalf, 0),
      endX: Math.min(event.offsetX + widthHalf, videoElementOverlay.clientWidth),
      endY: Math.min(event.offsetY + heightHalf, videoElementOverlay.clientHeight)
    };
  }else if(event.wheelDelta > 0){
    requireData = {
      channel: parseInt(this._videoPlayer.cameraObject.nvrChannel),
      manufacturer: this._videoPlayer.cameraObject.manufacturerTypeSdkName,
      width: videoElementOverlay.clientWidth,
      height: videoElementOverlay.clientHeight,
      beginX: Math.min(event.offsetX + widthHalf, videoElementOverlay.clientWidth),
      beginY: Math.min(event.offsetY + heightHalf, videoElementOverlay.clientHeight),
      endX: Math.max(event.offsetX - widthHalf, 0),
      endY: Math.max(event.offsetY - heightHalf, 0)
    };
  }else{
    requireData = {
      channel: parseInt(this._videoPlayer.cameraObject.nvrChannel),
      manufacturer: this._videoPlayer.cameraObject.manufacturerTypeSdkName,
      width: videoElementOverlay.clientWidth,
      height: videoElementOverlay.clientHeight,
      beginX: event.offsetX,
      beginY: event.offsetY,
      endX: event.offsetX,
      endY: event.offsetY
    };
  }

  // console.log(requireData);
  // console.log(this._videoPlayer.cameraObject.nvrChannel);

  var zoomInZoomOutWithSdkPromise = SpeedDomeCameraProtocol.zoomInZoomOutWithSdk(requireData);
  zoomInZoomOutWithSdkPromise.then(function (res) {
    // console.log('zoomInZoomOutWithSdk-->', '成功');
    // console.log(res);
  }).catch(function (msg){
    console.error('zoomInZoomOutWithSdk-->', '失败');
    console.log(msg);
  });

};

/**
 * 精确控制云台转动，平移旋转放大放小控制
 */
CameraSpeedDomeWidgetViewModel.prototype.precisePanTileZoomControl = function() {
  this.addNewControlEvent('precisePanTileZoomControl');
  var that = this;
  var requiredData = {
    // manufacturer: 'Dahua',
    manufacturer: this.sdkConfig.manufacturer,
    // channel: parseInt(this._videoPlayer.channelID),
    channel: 0,
    pan: parseInt(that.panAngle) * 10,
    tilt: parseInt(that.tiltAngle) * 10,
    zoom: parseInt(that.zoomLevel)
  };
  // console.log(requiredData)
  var sdkPrecisePanTileZoomControlPromise = SpeedDomeCameraProtocol.sdkPrecisePanTileZoomControl(requiredData);
  sdkPrecisePanTileZoomControlPromise.then(function (res) {
    // console.log(res);
  }).catch(function (msg){
    console.log(msg);
    var reLoginPromise = that.reLogin();
    reLoginPromise.then(function (reLoginRespond) {
      console.log(reLoginRespond);
      setTimeout(function () {
        that.precisePanTileZoomControl();
        // that.checkSdkConnectionState();
      }, 300);
    }).catch(function (msg) {
      console.log(msg);
    });
  });
};

/**
 * 查询当前的信息
 */
CameraSpeedDomeWidgetViewModel.prototype.syncPreciseQueryLocation = function() {
  var that = this;
  var sdkQueryLocationPromise = SpeedDomeCameraProtocol.sdkQueryLocation({
    // manufacturer: 'Dahua',
    manufacturer: this.sdkConfig.manufacturer,
    // channel: parseInt(this._videoPlayer.channelID)
    channel: 0
  });
  sdkQueryLocationPromise.then(function (res) {
    // console.log('查询成功');
    // console.log(res);
    that.currentPanAngle = parseFloat(res.PTZPan) / 10;
    that.currentTiltAngle = parseFloat(res.PTZTilt) / 10;
    that.currentZoomLevel = parseFloat(res.PTZZoom);

    that.colorPicker.currentPanAngle = that.currentPanAngle;
    that.colorPicker.currentTiltAngle = that.currentTiltAngle;
  }).catch(function (msg){
    console.log('查询失败,准备重登');
    // var reLoginPromise = that.reLogin();

    // var reLoginPromise = that.sdkLogin();
    // reLoginPromise.then(function (reLoginRespond) {
    //     console.log('重新查询');
    //     that.syncPreciseQueryLocation();
    // }).catch(function (msg) {
    //     console.log(msg);
    // });
  });
};

/**
 * 登录
 */
CameraSpeedDomeWidgetViewModel.prototype.sdkLogin = function() {
  var that = this;
  var promise = new window.Promise(function(resolve, reject){
    var sdkLoginPromise = SpeedDomeCameraProtocol.sdkLogin(that.sdkConfig);
    sdkLoginPromise.then(function (res) {
      console.log('登录成功');
      resolve(res);
    }).catch(function (msg){
      console.log(msg);
      reject(msg);
    });
  });
  return promise;
};

/**
 * 登出
 */
CameraSpeedDomeWidgetViewModel.prototype.sdkLogout = function() {
  var that = this;
  var promise = new window.Promise(function(resolve, reject){
    var sdkLogoutPromise = SpeedDomeCameraProtocol.sdkLogout({
      manufacturer: that.sdkConfig.manufacturer
    });
    sdkLogoutPromise.then(function (res) {
      console.log('登出成功');
      resolve(res);
    }).catch(function (msg){
      console.log(msg);
      reject(msg);
    });
  });
  return promise;
};

/**
 * 重新登录
 */
CameraSpeedDomeWidgetViewModel.prototype.reLogin = function() {
  var that = this;
  var promise = new window.Promise(function(resolve, reject){
    var sdkLogoutPromise = that.sdkLogout();
    sdkLogoutPromise.then(function (sdkLogoutPromiseRespond) {
      setTimeout(function () {
        var sdkLoginPromise = that.sdkLogin();
        sdkLoginPromise.then(function (sdkLoginPromiseRespond) {
          // console.log(sdkLoginPromiseRespond);
          console.log('重新登录成功');
          resolve(sdkLoginPromiseRespond);
        }).catch(function (msg) {
          reject(msg);
        });
      }, 3000);
    }).catch(function (msg) {
      reject(msg);
    });
  });
  return promise;
};

/**
 * 格式化sdk登录信息
 * @param options
 * @param {String} options.username
 * @param {String} options.password
 * @param {String} options.ip
 * @param {String} options.manufactorType
 */
CameraSpeedDomeWidgetViewModel.prototype.formatSdkConfig = function(options) {
  this.sdkConfig.user = options.username;
  this.sdkConfig.password = options.password;
  this.sdkConfig.ip = options.ip;
  if(options.manufactorType === CameraManufacturerTypeEnum.DAHUA){
    this.sdkConfig.manufacturer = 'Dahua';
    this.sdkConfig.port = '37777';
  }else if(options.manufactorType === CameraManufacturerTypeEnum.HAIKANG){
    this.sdkConfig.manufacturer = 'Haikang';
    this.sdkConfig.port = '8000';
  }
  console.log(this.sdkConfig);
};

/**
 * 确保已经登录
 */
CameraSpeedDomeWidgetViewModel.prototype.makeSureSdkIsReadyToUse = function() {
  var that = this;
  var promise = new window.Promise(function(resolve, reject){
    var checkSdkConnectionStatePromise = SpeedDomeCameraProtocol.checkSdkConnectionState({
      manufacturer: that.sdkConfig.manufacturer
    });
    checkSdkConnectionStatePromise.then(function (res) {
      console.log('当前状态');
      console.log(res);
      resolve(res);
    }).catch(function (msg){
      console.log(msg);
      reject(msg);

      // that.reLogin();
      that.sdkLogin();
    });
  });
  return promise;
};

/**
 * 检测sdk当前的状态
 */
CameraSpeedDomeWidgetViewModel.prototype.checkSdkConnectionState = function() {
  var that = this;
  var promise = new window.Promise(function(resolve, reject){
    var checkSdkConnectionStatePromise = SpeedDomeCameraProtocol.checkSdkConnectionState({
      manufacturer: that.sdkConfig.manufacturer
    });
    checkSdkConnectionStatePromise.then(function (res) {
      console.log('当前状态');
      console.log(res);
      resolve(res);
    }).catch(function (msg){
      console.log(msg);
      reject(msg);
    });
  });
  return promise;
};

/**
 * 订阅同步球机准确位置的事件
 */
CameraSpeedDomeWidgetViewModel.prototype.subscribeSyncPreciseQueryLocation = function() {
  var that = this;
  if(defined(this.syncPreciseQueryLocationInterval)){
    clearInterval(this.syncPreciseQueryLocationInterval);
  }
  this.syncPreciseQueryLocationInterval = setInterval(function () {
    that.syncPreciseQueryLocation();
  }, 300);
};

/**
 * 取消订阅同步球机准确位置的事件
 */
CameraSpeedDomeWidgetViewModel.prototype.cancelSubscribeSyncPreciseQueryLocation = function() {
  if(defined(this.syncPreciseQueryLocationInterval)){
    clearInterval(this.syncPreciseQueryLocationInterval);
  }
};

/**
 * 订阅球机事件
 * @param {Function} callback
 */
CameraSpeedDomeWidgetViewModel.prototype.subscribeAllControlEvent = function (callback) {
  knockout.track(this, ['_controlEvent']);
  knockout.getObservable(this, '_controlEvent').subscribe(function(newValue) {
    callback(newValue);
  });
};

/**
 * 新增球机控制事件
 * @param {String} name 操作的名称
 */
CameraSpeedDomeWidgetViewModel.prototype.addNewControlEvent = function (name) {
  this._controlEventCount++;
  this._controlEvent = {
    count: this._controlEventCount,
    functionName: name,
    busiRealUid: this._videoPlayer.channelID
  };
};

/**
 * 订阅鼠标事件
 * @description 可点击屏幕控制球机事件
 */
CameraSpeedDomeWidgetViewModel.prototype.subscribeMouseZoomEvent = function () {
  this._videoPlayer.videoElementOverlay.addEventListener('mousewheel', this._scrollFunc, false);
  this._videoPlayer.videoElementOverlay.addEventListener('click', this._clickFunc, false);
  this._videoPlayer.videoElementOverlay.classList.add('lux-CameraSpeedDomeWidget-crosshairCursor');
  // this._videoPlayer.videoElementOverlay.classList.remove('lux-CameraSpeedDomeWidget-notAllowCursor');
};

/**
 * 取消订阅鼠标事件
 * @description 可点击屏幕控制球机事件
 */
CameraSpeedDomeWidgetViewModel.prototype.unsubscribeMouseZoomEvent = function () {
  this._videoPlayer.videoElementOverlay.removeEventListener('mousewheel', this._scrollFunc, true);
  this._videoPlayer.videoElementOverlay.removeEventListener('click', this._clickFunc, true);
  this._videoPlayer.videoElementOverlay.classList.remove('lux-CameraSpeedDomeWidget-crosshairCursor');
  // this._videoPlayer.videoElementOverlay.classList.remove('lux-CameraSpeedDomeWidget-notAllowCursor');
};

/**
 * 禁止zoom鼠标事件
 * @description 可点击屏幕控制球机事件
 */
CameraSpeedDomeWidgetViewModel.prototype.forbidMouseZoomEvent = function () {
  this._videoPlayer.videoElementOverlay.removeEventListener('mousewheel', this._scrollFunc, true);
  this._videoPlayer.videoElementOverlay.removeEventListener('click', this._clickFunc, true);
  this._videoPlayer.videoElementOverlay.classList.remove('lux-CameraSpeedDomeWidget-crosshairCursor');
  this._videoPlayer.videoElementOverlay.classList.add('lux-CameraSpeedDomeWidget-notAllowCursor');
};

/**
 * 取消禁止zoom鼠标事件
 * @description 可点击屏幕控制球机事件
 */
CameraSpeedDomeWidgetViewModel.prototype.cancelForbidMouseZoomEvent = function () {
  // this._videoPlayer.videoElementOverlay.removeEventListener('mousewheel', this._scrollFunc, true);
  // this._videoPlayer.videoElementOverlay.removeEventListener('click', this._clickFunc, true);
  // this._videoPlayer.videoElementOverlay.classList.remove('lux-CameraSpeedDomeWidget-crosshairCursor');
  this._videoPlayer.videoElementOverlay.classList.remove('lux-CameraSpeedDomeWidget-notAllowCursor');
};

/**
 * 就当videoplay的尺寸大于最小值才显示球机控制插件
 */
CameraSpeedDomeWidgetViewModel.prototype.initBiggerThanSizeEvent = function () {
  setInterval(()=>{
    this.biggerThanSize = this._videoPlayer.wrapper.clientWidth > 384
      && this._videoPlayer.wrapper.clientHeight > 216
    // console.log(this.biggerThanSize)
  }, 200)
};

/**
 * @returns {Boolean} true if the object has been destroyed, false otherwise.
 */
CameraSpeedDomeWidgetViewModel.prototype.isDestroyed = function() {
  return false;
};

/**
 * Destroys the view model.
 */
CameraSpeedDomeWidgetViewModel.prototype.destroy = function() {
  // this._eventHelper.removeAll();
  destroyObject(this);
};

export default CameraSpeedDomeWidgetViewModel;
