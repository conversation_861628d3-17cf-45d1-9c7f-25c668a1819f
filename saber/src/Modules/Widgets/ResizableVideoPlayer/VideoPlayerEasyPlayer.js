import defaultValue from '../../Core/defaultValue'
import defined from '../../Core/defined'
import defineProperties from '../../Core/defineProperties'
import destroyObject from '../../Core/destroyObject'
import DeveloperError from '../../Core/DeveloperError'
import JulianDate from '../../Core/JulianDate'
import knockout from '../../ThirdParty/knockout'
import getElement from '../../Widgets/getElement'
import Queue from '../../Core/Queue'
import StringUtil from '../../Util/StringUtil'

import MediaServiceProtocol from '../../Protocol/MediaServiceProtocol'
import FileProtocol from '../../Protocol/FileProtocol'
import ShowTipsWidget from '../../Widgets/ShowTipsWidget/ShowTipsWidget'
import VideoPlayerToolBar from '../../Widgets/VideoPlayerToolBar/VideoPlayerToolBar'
import CameraSpeedDomeWidget from '../../Widgets/CameraSpeedDomeWidget/CameraSpeedDomeWidget'
import moment from 'moment'
import VideoPlayerStatusEnum from './VideoPlayerStatusEnum'
import VideoPlayerStatusEnumHelper from './VideoPlayerStatusEnumHelper'
import VideoPlayerEasyPlayerViewModel from './VideoPlayerEasyPlayerViewModel'

import ResourceUtil from "../../Util/ResourceUtil";

'use strict';

/**
 * VideoPlayerEasyPlayer
 * @alias VideoPlayerEasyPlayer
 * @description EasyPlayer.js H5播放器，是一款能够同时支持HTTP、RTMP、HTTP-FLV、HLS（m3u8）视频直播与视频点播等多种协议，支持H.264、H.265、AAC等多种音视频编码格式，支持mse、wasm等多种解码方式，支持Windows、Linux、Android、iOS全平台终端的H5播放器。
 * @see https://github.com/tsingsee/EasyPlayer.js
 *
 * @param {Object} options
 * @param {VideoPlayerGallery} options.parentGallery 构造此播放器类VideoPlayerGallery
 * @param {String} options.id chplayer使用的id
 * @param {String} [options.url=''] 初始化的视频流地址
 * @param {String|Element} options.container 容器ID或容器元素
 * @param {String|Element} options.id 容器ID或容器元素
 * @param {String} [options.protocol='WS_FLV'] FLV/WS_FLV
 *
 * @constructor
 */
function VideoPlayerEasyPlayer(options) {
  //>>includeStart('debug', pragmas.debug);
  if(!defined(options)){
    options = {};
  }
  if (!defined(options.container)) {
    throw new DeveloperError('options.container is required.');
  }
  //>>includeEnd('debug');

  this._protocol = defaultValue(options.protocol, 'WS_FLV');

  var that = this;
  var viewModel = new VideoPlayerEasyPlayerViewModel(options);

  var container = getElement(options.container);

  var wrapper = document.createElement('div');
  wrapper.className = 'lux-resizable-video-player';
  wrapper.setAttribute('data-bind', 'css: { "lux-resizable-video-player-fullScreen" : showFullScreen}');
  wrapper.setAttribute('draggable', 'true');
  container.appendChild(wrapper);

  // video
  this._videoElementId = defaultValue(options.id, StringUtil.generateLongUid());// 会表现为父框架的<code>id+'0x0'

  var videoElementContainer = document.createElement('div');
  videoElementContainer.id = this._videoElementId
  videoElementContainer.className = 'lux-resizable-video-element lux-resizable-video-element-noPointerEvent'
  videoElementContainer.style.pointerEvents = 'none'
  wrapper.appendChild(videoElementContainer);
  this._videoPlayer = new WasmPlayer(null,
    this._videoElementId, msg=>{console.log(msg)},{
      cbUserPtr:this,
      decodeType:"auto",
      openAudio:false,
      BigPlay:false,
      Height:true
    });
  let videoElement = this._videoPlayer.h5Video
  videoElement.setAttribute('width', '1920');
  videoElement.setAttribute('height', '1080');

  // 一个叠加层，用于绘制嫌疑人框
  var videoElementCanvas = document.createElement('canvas');
  videoElementCanvas.className = 'lux-video-player-flash-overlay';
  videoElementCanvas.setAttribute('draggable', 'false');
  videoElementCanvas.setAttribute('data-bind', 'css: { "lux-video-player-flash-overlay-visible" : showFlashDragOverlay, "lux-video-player-flash-overlay-highlight" : highlightFlashDragOverlay}');
  wrapper.appendChild(videoElementCanvas);

  // 一个叠加层，用于高亮摄像头，不能操作
  var videoElementHighlightOverlay = document.createElement('div');
  videoElementHighlightOverlay.className = 'lux-video-player-active-overlay';
  videoElementHighlightOverlay.setAttribute('draggable', 'true');
  videoElementHighlightOverlay.setAttribute('data-bind', 'css: { "lux-video-player-active-overlay-visible" : showFlashDragOverlay, "lux-video-player-active-overlay-highlight" : highlightActiveOverlay}');
  wrapper.appendChild(videoElementHighlightOverlay);

  // 一个叠加层，用于操作div，比如拖拽，点击选中
  var videoElementOverlay = document.createElement('div');
  videoElementOverlay.className = 'lux-video-player-flash-overlay';
  videoElementOverlay.setAttribute('draggable', 'true');
  videoElementOverlay.setAttribute('data-bind', 'css: { "lux-video-player-flash-overlay-visible" : showFlashDragOverlay, "lux-video-player-flash-overlay-highlight" : highlightFlashDragOverlay}');
  wrapper.appendChild(videoElementOverlay);

  // 一个叠加层，用于操作显示错误信息
  var videoElementErrorOverlay = document.createElement('div');
  videoElementErrorOverlay.className = 'lux-video-player-error-overlay';
  wrapper.appendChild(videoElementErrorOverlay);
  this._showTipsWidget = new ShowTipsWidget({
    container: videoElementErrorOverlay
  });

  // 加载中
  var videoElementLoadingIndicator = document.createElement('div');
  videoElementLoadingIndicator.className = 'loadingIndicator';
  videoElementLoadingIndicator.style.display = 'none';
  wrapper.appendChild(videoElementLoadingIndicator);

  // 球机的控制控件
  var videoSpeedDomeControl = document.createElement('div');
  videoSpeedDomeControl.className = 'lux-video-player-videoSpeedDomeControl';
  wrapper.appendChild(videoSpeedDomeControl);

  // 视频播放器工具栏
  var videoPlayerToolBarContainer = document.createElement('div');
  videoPlayerToolBarContainer.className = 'lux-video-player-videoPlayerToolBar';
  wrapper.appendChild(videoPlayerToolBarContainer);

  knockout.applyBindings(viewModel, wrapper);

  this._parentGallery = options.parentGallery;// 整个父框架对象的指针传进来
  this._viewModel = viewModel;
  this._videoElement = videoElement;
  this._wrapper = wrapper;
  this._container = container;
  this._drawCanvas = videoElementCanvas;
  this._drawCanvasCtx = this._drawCanvas.getContext('2d');
  this._videoElementOverlay = videoElementOverlay;
  this._videoElementLoadingIndicator = videoElementLoadingIndicator;
  this._lastActiveTime = JulianDate.now();
  this._videoUrl = undefined;

  this._channelID = undefined;//options.channelID;
  this._playbackStreamCurrentID = undefined;// 正在使用的回放视频流ID
  this._playbackStreamIDQueue = new Queue();// 队列用于删除
  this._pauseOverlayVisible = false;// 暂停叠加层是否可见

  this._playerName = 'player' + this._videoElementId;

  this._loadedHandlerName = 'loadedHandler' + this._videoElementId;

  this.loaded = false;// flash是否加载完毕
  this._isValidStream = false;// 当前播放的是否是有效视频流

  this.videoPlayerStatus = VideoPlayerStatusEnum.INIT_PLEASE_DRAG;// 默认

  /**
   * 是否在主屏幕
   * @type {boolean}
   */
  this.isBigVideoPlayer = false;
  knockout.track(this, ['isBigVideoPlayer']);

  /**
   * 球机控制控件
   * @private
   * @type {CameraSpeedDomeWidget}
   */
  this._cameraSpeedDomeWidget = new CameraSpeedDomeWidget(videoSpeedDomeControl, {
    videoPlayer: this
  });

  /**
   * 视频播放器工具栏
   * @private
   * @type {VideoPlayerToolBar}
   */
  this._videoPlayerToolBar = new VideoPlayerToolBar(videoPlayerToolBarContainer, {
    videoPlayer: this
  });

  this.loaded = true;
  this.showFlashDragOverlay = true;

  this._initDragEvent();

}

defineProperties(VideoPlayerEasyPlayer.prototype, {
  /**
   * Gets the parent container.
   * @memberof VideoPlayerEasyPlayer.prototype
   *
   * @type {Element}
   */
  container : {
    get : function() {
      return this._container;
    }
  },

  /**
   * Gets the view model.
   * @memberof VideoPlayerEasyPlayer.prototype
   *
   * @type {VideoPlayerEasyPlayer}
   */
  viewModel : {
    get : function() {
      return this._viewModel;
    }
  },

  /**
   * Gets the wrapper.
   * @memberof VideoPlayerEasyPlayer.prototype
   *
   * @type {Element}
   */
  wrapper : {
    get : function() {
      return this._wrapper;
    }
  },

  /**
   * 缩略图地址
   * @memberof VideoPlayerEasyPlayer.prototype
   *
   * @type {String}
   */
  thumbnailUrl: {
    get: function () {
      return this._viewModel.thumbnailUrl;
    },
    set : function(value) {
      this._viewModel.thumbnailUrl = value;
    }
  },

  /**
   * 视频地址
   * @memberof VideoPlayerEasyPlayer.prototype
   *
   * @type {String}
   */
  videoUrl: {
    get: function () {
      return this._videoUrl;
    },
    set : function(value) {
      this._videoUrl = value;
    }
  },

  /**
   * 通道号
   * @memberof VideoPlayerEasyPlayer.prototype
   *
   * @type {String}
   */
  channelID: {
    get: function () {
      return this._channelID;
    },
    set : function(value) {
      this._channelID = value;
    }
  },

  /**
   * 这个播放器的id
   * @memberof VideoPlayerEasyPlayer.prototype
   *
   * @type {String}
   */
  videoElementId: {
    get: function () {
      return this._videoElementId;
    },
    set : function(value) {
      this._videoElementId = value;
    }
  },

  /**
   * 高亮播放器
   * @description 红色
   * @memberof VideoPlayerEasyPlayer.prototype
   *
   * @type {Boolean}
   */
  highlight: {
    get: function () {
      return this._viewModel.highlightActiveOverlay;
    },
    set : function(value) {
      this._viewModel.highlightActiveOverlay = value;
    }
  },

  /**
   * 选中摄像头播放器
   * @description 蓝色
   * @memberof VideoPlayerEasyPlayer.prototype
   *
   * @type {Boolean}
   */
  select: {
    get: function () {
      return this._viewModel.highlightFlashDragOverlay;
    },
    set : function(value) {
      this._viewModel.highlightFlashDragOverlay = value;
    }
  },

  /**
   * 唯一一个可以触发鼠标事件的操作层
   * @description 蓝色
   * @memberof VideoPlayerEasyPlayer.prototype
   *
   * @type {Element}
   */
  videoElementOverlay: {
    get: function () {
      return this._videoElementOverlay;
    }
  },

  /**
   * 球机控件是否可见
   * @memberof VideoPlayerEasyPlayer.prototype
   *
   * @type {Boolean}
   */
  cameraSpeedDomeWidgetVisible: {
    get: function () {
      return this._cameraSpeedDomeWidget.show;
    },
    set:function (value) {
      this._cameraSpeedDomeWidget.show = value;
    }
  },

  /**
   * 视频播放器工具栏
   * @memberof VideoPlayerEasyPlayer.prototype
   *
   * @type {VideoPlayerToolBar}
   */
  videoPlayerToolBar: {
    get: function () {
      return this._videoPlayerToolBar;
    }
  },

  /**
   * 视频播放器工具栏是否可见
   * @memberof VideoPlayerEasyPlayer.prototype
   *
   * @type {Boolean}
   */
  videoPlayerToolBarVisible: {
    get: function () {
      return this._videoPlayerToolBar.show;
    },
    set:function (value) {
      this._videoPlayerToolBar.show = value;
    }
  },

  /**
   * 球机控件
   * @memberof VideoPlayerEasyPlayer.prototype
   *
   * @type {CameraSpeedDomeWidget}
   */
  cameraSpeedDomeWidget: {
    get: function () {
      return this._cameraSpeedDomeWidget;
    }
  },

  /**
   * 当前的时间
   * @memberof VideoPlayerEasyPlayer.prototype
   *
   * @type {JulianDate|undefined}
   */
  currentClockTime: {
    get: function () {
      var time;
      if(defined(this._clock)){
        time = this._clock.currentTime;
      }
      return time;
    }
  },

  /**
   * 是否有有效的channelID
   * @memberof VideoPlayerEasyPlayer.prototype
   *
   * @type {Boolean}
   */
  hasValidChannelId: {
    get: function () {
      return this._channelID !== undefined && this._channelID !== 'rtmp';
    }
  },

  /**
   * 详情信息
   * @memberof VideoPlayerEasyPlayer.prototype
   *
   * @type {Element}
   */
  cameraObject: {
    get: function () {
      if(this.hasValidChannelId){
        return this._parentGallery.channelIdIndexCollection.get(this._channelID).cameraObject;
      }else{
        return undefined;
      }
    }
  },

  /**
   * 画布
   * @memberof VideoPlayerEasyPlayer.prototype
   *
   * @type {Element}
   */
  drawCanvas: {
    get: function () {
      return this._drawCanvas;
    }
  },

  /**
   * 画布ctx
   * @memberof VideoPlayerEasyPlayer.prototype
   */
  drawCanvasCtx: {
    get: function () {
      return this._drawCanvasCtx;
    }
  }

});

/**
 * 初始化拖拽事件
 * @private
 */
VideoPlayerEasyPlayer.prototype._initDragEvent = function () {
  var videoElementOverlay = this._videoElementOverlay;
  var that = this;
  videoElementOverlay.addEventListener('dragstart', function(event) {
    // console.log('dragstart');
    // console.log(event);
    var data = {
      videoUrl: that.videoUrl,
      thumbnailUrl: that.thumbnailUrl,
      origin: 'VIDEO',
      videoElementId: that._videoElementId,
      channelID: that._channelID
    };
    event.dataTransfer.setData('Text', JSON.stringify(data));
  }, false);
  videoElementOverlay.addEventListener('drop', function(event) {
    event.preventDefault();
    console.log('drop');
    console.log(event);

    var data;
    try{
      data = JSON.parse(event.dataTransfer.getData('Text'));
    }catch (e){
      console.log(e);
    }
    console.log(data);
    if(defined(data)){
      if(data.origin === 'BIM'){
        // 获取视频流地址并替换
        if(data.channelID !== undefined ){
          that.reloadChannelID({
            channelID: data.channelID
          });
        }
      }else if (data.origin === 'VIDEO'){
        // 交换同一个gallery的两个视频
        if(defined(that._parentGallery)){
          console.log('尚未开发');
          // if(that.getParentGalleryId(data.videoElementId) === that.getParentGalleryId(that._videoElementId)) {
          //     var position1 = that.getPositionOfParentGallery(data.videoElementId);
          //     var position2 = that.getPositionOfParentGallery(that._videoElementId);
          //     // 只有同一个视频集合图库才能交换播放器
          //     if(defined(position1) && defined(position2)){
          //         that._parentGallery.switchTwoPlayers(position1, position2);
          //     }
          // }
        }else if(defined(data.channelID)){
          that.reloadChannelID(data);
        } else{// 重载视频
          that.reload(data);
        }
      }else if(data.origin === 'JSTREE'){
        // 获取jstree中的数据中的视频流地址并替换
        var obj = data.data;
        if(defined(obj) && StringUtil.leftContainRight(obj.type, 'camera')){
          if(defined(obj.id)){
            // console.log(obj);
            if(StringUtil.leftContainRight(obj.type, 'cameraSpeedDome')){
              that.reloadChannelID({
                channelID: StringUtil.getLastSliceString(obj.id, 20), // id一定是20位的，因此这里可以直接截取
                callback: function () {
                  that.videoPlayerToolBarVisible = true;
                  that.cameraSpeedDomeWidgetVisible = true;
                }
              });
            }else{
              that.reloadChannelID({
                channelID: StringUtil.getLastSliceString(obj.id, 20), // id一定是20位的，因此这里可以直接截取
                callback: function () {
                  that.videoPlayerToolBarVisible = true;
                }
              });
            }
          }
        }
      } else if(data.origin === 'CAMERALIST'){
        that.reloadChannelID({
          channelID: data.channelID,
          callback: function () {
            that.videoPlayerToolBarVisible = false;
          }
        });
      }
    }
  }, false);
  videoElementOverlay.addEventListener('dragenter', function(event) {
    event.preventDefault();
  }, false);
  videoElementOverlay.addEventListener('dragover', function(event) {
    event.preventDefault();
  }, false);
  videoElementOverlay.addEventListener('dragleave', function(event){
    event.preventDefault();
  });
  videoElementOverlay.addEventListener('dragend', function(event){
    event.preventDefault();
  });
};

/**
 * 获取某个channelID的摄像头流媒体地址并播放，然后停掉回放的流
 * @description 会调用reload()
 * @param {Object} options
 * @param {String} [options.channelID] 摄像头通道号
 * @param {Function} [options.callback] 回调函数
 * @see VideoPlayerEasyPlayer#reloadChannelIdAndUrl
 */
VideoPlayerEasyPlayer.prototype.reloadChannelID = function (options){
  console.log('reloadChannelID:');
  console.log(options);
  // 如果正在播放别的channel，将channel停掉，在这里处理是为了确保每点开一路新的流都会关闭一个旧的
  this.stopLiveChannel();
  this.stopPlayBackStream();

  options = defaultValue(options, {});

  if(defined(options.channelID)){
    // FIXME 20190401
    if(this.checkStreamValid() === true){
      this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_SWITCH;
      this.clearStream();
    }else{
      this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_LOADING;
    }

    this._channelID = options.channelID;
  }else{
    // FIXME 20190401
    if(this.checkStreamValid() === true){
      this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_SWITCH;
      this.clearStream();
    }else{
      this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_LOADING;
    }
  }

  if(!defined(this._channelID)){
    return;
  }

  var that = this;

  console.log('reloadChannelID#MediaServiceProtocol.startChannel')
  var startChannelPromise = MediaServiceProtocol.startChannel({
    channelID: this._channelID
  }, this._protocol);
  startChannelPromise.then(function (json) {
    if(defined(options) && defined(options.callback)){
      console.log(ResourceUtil.urlTranslate(json));
      that.reload({
        videoUrl: ResourceUtil.urlTranslate(json),
        callback: options.callback
      });
    } else {
      that.reload({
        videoUrl: ResourceUtil.urlTranslate(json)
      });
    }
  }).catch(function (msg) {
    that.drawDanmuError('点播' + that._channelID + '错误：' + msg);
    that.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_FAIL;
    that._currentErrorMsg = '点播' + that._channelID + '错误：' + msg;
  });

};

/**
 * 同时替换channelID和Url
 *
 * @description 此函数是为了在请求到多路视频是可以直接塞入数据，只允许VideoPlayerEasyPlayerGallery调用
 * @param options
 * @param {String} options.channelID
 * @param {String} options.videoUrl
 * @see VideoPlayerEasyPlayer#reloadChannelID
 */
VideoPlayerEasyPlayer.prototype.reloadChannelIdAndUrl = function (options){
  options = defaultValue(options, {});

  this.stopLiveChannel();
  this.stopPlayBackStream();

  if(this.checkStreamValid() === true){
    this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_SWITCH;
    this.clearStream();
  }else{
    this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_LOADING;
  }

  this._channelID = options.channelID;
  this.reload({
    videoUrl: options.videoUrl
  });
};

/**
 * 重载视频
 *
 * @description 最基础的切换视频源的方法，其余的根据其他参数生成的方法都会调用此方法
 * @param {Object} [options] 当只是需要刷新视频时无需设置此属性时
 * @param {String} options.videoUrl 视频直播的地址。
 * @param {String} options.thumbnailUrl 缩略图地址
 * @param {String} [options.isSpeedDomeCamera=false] 是不是快球摄像头
 * @param {Function} [options.callback] 回调函数
 */
VideoPlayerEasyPlayer.prototype.reload = function (options) {
  var that = this;
  options = defaultValue(options, {});

  if(defined(options.videoUrl)){
    this._videoUrl = options.videoUrl;
    this._lastActiveTime = JulianDate.now();
  }
  var videoUrl = this._videoUrl;
  if(!videoUrl){
    return
  }
  console.log('VideoPlayerEasyPlayer#reload--> ', this._videoUrl);

  // TODO yln
  this._videoPlayer.destroy();
  this._videoPlayer = new WasmPlayer(null,
    this._videoElementId, msg=>{console.log(msg)},{
      cbUserPtr:this,
      decodeType:"auto",
      openAudio:false,
      BigPlay:false,
      Height:true
    });

  this._videoPlayer.play(this._videoUrl, true, msg=>{console.log(msg)})
  this._videoElement = this._videoPlayer.h5Video
  this._videoElement.setAttribute('width', '1920');
  this._videoElement.setAttribute('height', '1080');

  this.cameraSpeedDomeWidgetVisible = false;
  this.videoPlayerToolBarVisible = false;

  if(defined(options.callback)){
    options.callback();
  }
};

/**
 * 暂停视频
 */
VideoPlayerEasyPlayer.prototype.pause = function () {
  if(defined(this._videoElement)){
    let playPromise = this._videoElement.play()
    if (playPromise !== undefined) {
      playPromise.then(() => {
        this._videoElement.pause();
      }).catch(e => {
        console.log(e);
      })
    }
  }
};

/**
 * 播放视频
 */
VideoPlayerEasyPlayer.prototype.play = function () {
  if(defined(this._videoElement) /*&& this.checkStreamValid() === true*/){
    // this._videoElement.play();

    let playPromise = this._videoElement.play()
    if (playPromise !== undefined) {
      playPromise.then(() => {
        this._videoElement.play()
      }).catch(e => {
        console.log(e);
      })
    }
  }
};

/**
 * 订阅channelId变化事件
 * @param {Function} callback
 */
VideoPlayerEasyPlayer.prototype.subscribeChannelIdChangeEvent = function (callback){
  if(this._hasSubscribeChannelIdChangeEvent !== true){
    knockout.track(this, ['_channelID']);
    this._hasSubscribeChannelIdChangeEvent = true;
  }
  knockout.getObservable(this, '_channelID').subscribe(function(newValue) {
    callback(newValue);
  });
};

/**
 * 重新设置canvas的大小并刷新画布
 * @description 设置width和height后会自动清空画布
 */
VideoPlayerEasyPlayer.prototype.resizeCanvas = function () {
  this._drawCanvas.width = this._drawCanvas.clientWidth;
  this._drawCanvas.height = this._drawCanvas.clientHeight;
};

/**
 * 获取截屏的base64png图像
 * @performance 230ms到260ms
 * @return {String}
 */
VideoPlayerEasyPlayer.prototype.getPrintScreenBase64Png = function(){
  var canvas = document.createElement('canvas');
  canvas.width = this._videoElement.width;
  canvas.height = this._videoElement.height;
  var canvasCtx = canvas.getContext('2d');
  canvasCtx.drawImage(this._videoElement, 0, 0, this._videoElement.width, this._videoElement.height, 0, 0, canvas.width, canvas.height);
  var base64str = canvas.toDataURL('image/png');
  // console.log(base64str);
  return base64str;
};

/**
 * 获取截屏的base64jpg图像
 * @description 得到的stream可以直接放到<img>的src中
 * @param {Number} [quality=100] 截屏质量1到100都为有效值，100最高
 * @performance 90ms到100ms 100质量分数之下 大致快两倍
 * @return {String}
 */
VideoPlayerEasyPlayer.prototype.getPrintScreenBase64Jpg = function(quality){
  var canvas = document.createElement('canvas');
  canvas.width = this._videoElement.width;
  canvas.height = this._videoElement.height;
  var canvasCtx = canvas.getContext('2d');
  var video = this._videoElement;
  canvasCtx.drawImage(video, 0, 0, this._videoElement.width, this._videoElement.height, 0, 0, canvas.width, canvas.height);
  var jpgQuality = defaultValue(quality, 100);
  var base64str = canvas.toDataURL('image/jpeg', jpgQuality / 100);
  // console.log(base64str);
  return base64str;

};

/**
 * 回放视频
 * @param {String} startTime '2018-01-01T10:56:00'
 * @param {String} stopTime '2018-01-01T10:59:00'
 */
VideoPlayerEasyPlayer.prototype.playBack = function (startTime, stopTime) {
  // console.log(startTime);
  // console.log(stopTime);
  var that = this;
  var requiredData = {
    channelID: this._channelID,
    starttime: startTime,
    endtime: stopTime
  };
  this.stopPlayBackStream();

  if(this.checkStreamValid() === true){
    this.videoPlayerStatus = VideoPlayerStatusEnum.PLAY_BACK_SWITCH;
    this.clearStream();
  }else{
    this.videoPlayerStatus = VideoPlayerStatusEnum.PLAY_BACK_LOADING;
  }

  var startPlaybackPromise = MediaServiceProtocol.startPlayback(requiredData);
  startPlaybackPromise.then(function (json) {
    that.reload({
      videoUrl: json.FLV
    });
    var StreamID = json.StreamID;
    that._playbackStreamIDQueue.enqueue(StreamID);
    that._playbackStreamCurrentID = StreamID;
    console.log('回放流streamID的地址为：', StreamID);
  }).catch(function (msg) {
    that.clearStream();
    // that.showError('无有效视频流');
    // that.showErrorTips('回放' + that._channelID + '错误：' + msg);
    // that.drawDanmuError('回放' + that._channelID + '错误：' + msg);

    that._showError({
      videoPlayerStatus: VideoPlayerStatusEnum.PLAY_BACK_FAIL,
      msg: '回放' + that._channelID + '错误：' + msg
    });
  });
};

/**
 * 是否有效视频流
 * @return {Boolean}
 */
VideoPlayerEasyPlayer.prototype.checkStreamValid = function () {
  // if(defined(this._chplayer)){
  //     if(!isNaN(this._chplayer.time)){
  //         return true;
  //     }
  // }
  // TODO
  return true;
};

/**
 * 是否有效视频地址
 * @return {Boolean}
 */
VideoPlayerEasyPlayer.prototype.checkVideoUrlValid = function () {
    return defined(this._videoUrl) && this._videoUrl !== '';
};

/**
 * 清空视频流
 */
VideoPlayerEasyPlayer.prototype.clearStream = function () {
  this._videoPlayer.stop()
  this._videoUrl = undefined
  this.videoPlayerToolBarVisible = false;
};

/**
 * 停止回放的视频
 * @description 将不需要的视频停掉，这个函数是可以放心调用的，流媒体后台会对停播做处理
 */
VideoPlayerEasyPlayer.prototype.stopPlayBackStream = function () {
  var streamID = this._playbackStreamIDQueue.peek();
  // console.log('停止回放的视频流为', streamID);
  if(defined(streamID)){
    var that = this;
    var stopPlaybackStreamPromise = MediaServiceProtocol.stopPlaybackStream({
      streamID: streamID
    });
    stopPlaybackStreamPromise.then(function () {
      var streamID = that._playbackStreamIDQueue.dequeue();
      if(!defined(that._playbackStreamIDQueue.peek)){
        this._playbackStreamCurrentID = undefined;
      }
      // console.log('成功停止了', streamID)
    }).catch(function (msg) {
      that.drawDanmuError('停止回放' + that._playbackStreamIDQueue.peek() + '错误：' + msg);
    });
  }
};

/**
 * 停止直播的视频
 * @deprecated
 * @description 将不需要的直播视频停掉，仅仅是帮助后台维护状态的，不会影响前端
 */
VideoPlayerEasyPlayer.prototype.stopLiveChannel = function () {

};

/**
 * 获取该播放器在父节点中的位置
 * @param {String} [videoElementId=this._videoElementId]
 * @example videoPlayerRTMP.getPositionOfParentGallery(videoPlayerRTMP._videoElementId);
 * @returns {String|undefined}
 */
VideoPlayerEasyPlayer.prototype.getPositionOfParentGallery = function(videoElementId) {
  videoElementId = defaultValue(videoElementId, this._videoElementId);
  if(defined(videoElementId)){
    return videoElementId.slice(-3);
  }else{
    return undefined;
  }
};

/**
 * 获取该播放器在父节点中的编号，从1开始
 * @returns {Number|undefined}
 */
VideoPlayerEasyPlayer.prototype.getIndexOfParentGallery = function() {
  var position = this.getPositionOfParentGallery(this._videoElementId);
  var positionArray = position.split('x', 2);
  var verticalIndex = parseInt(positionArray[0]);
  var horizontalIndex = parseInt(positionArray[1]);
  var verticalNumber = parseInt(this._parentGallery._verticalNumber);
  return verticalIndex * verticalNumber + horizontalIndex + 1;
};

/**
 * 获取父框架的标识
 * @param {String} [videoElementId=this._videoElementId]
 */
VideoPlayerEasyPlayer.prototype.getParentGalleryId = function(videoElementId) {
  /**
   * FIXME 关键操作，可能需要正则
   */
  videoElementId = defaultValue(videoElementId, this._videoElementId);
  return videoElementId.slice(0, -3);
};

/**
 * 通过弹幕的形式绘制错误信息
 * @description 使用弹幕的方式将后台的错误信息直接打印，方便调试
 * @param {String} msg
 * @deprecated
 */
VideoPlayerEasyPlayer.prototype.drawDanmuError = function(msg){
  console.warn('弹幕信息为：', msg);
};

/**
 * 展示错误，一致存在
 * @param {String} msg
 */
VideoPlayerEasyPlayer.prototype.showError = function(msg){
  // console.log('显示的错误信息为：');
  // console.log(msg);

  if(this._pauseOverlayVisible === true){
    console.warn('如果暂停了就先不打印错误了');
    return;
  }

  // console.log(this.videoPlayerStatus);

  if(this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_LOADING){
    msg = '实时视频加载中...';
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_RESUME){
    // msg = '实时视频重连中...';
    msg = this._currentErrorMsg;
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_SWITCH){
    msg = '实时视频切换中...';
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_FAIL){
    // msg = '实时视频错误';
    // console.log(this._lastErrorMsg, this._currentErrorMsg);
    console.warn('实时视频错误');
    msg = this._currentErrorMsg;
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_END){
    msg = '无实时视频';
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_LOADING){
    msg = '历史视频加载中...';
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_RESUME){
    msg = '历史视频重连中...';
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_SWITCH){
    msg = '历史视频切换中...';
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_FAIL){
    // msg = '历史视频错误';
    msg = this._currentErrorMsg;
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_END){
    msg = '历史视频播放结束';
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.INIT_PLEASE_DRAG){
    msg = '拖拽加载';
  }
  // console.log('showError:', msg, '。当前的状态是：', this.videoPlayerStatus);

  if(this._lastErrorMsg === msg){
    return;
  }
  this._lastErrorMsg = msg;

  this._showTipsWidget.show(msg);
};

/**
 * 展示错误信息，出现后又消失
 * @param {String} msg
 */
VideoPlayerEasyPlayer.prototype.showErrorTips = function(msg){
  if(this._pauseOverlayVisible === true){
    return;
  }
  if(this._lastErrorMsg === msg){
    return;
  }
  this._lastErrorMsg = msg;
  console.log(msg);

  var that = this;
  this._showTipsWidget.show(msg);
  setTimeout(function () {
    that._showTipsWidget.hide();
  }, 2000);
};

/**
 * 隐藏指定的错误信息
 */
VideoPlayerEasyPlayer.prototype.hideError = function(msg){
  if(VideoPlayerStatusEnumHelper.isLiveStatus(this.videoPlayerStatus)){
    this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE;
  }else if(VideoPlayerStatusEnumHelper.isPlaybackStatus(this.videoPlayerStatus)){
    this.videoPlayerStatus = VideoPlayerStatusEnum.PLAY_BACK;
  }

  if(this._showTipsWidget._visible === true){
    this._lastErrorMsg = '';
    this._showTipsWidget.hide();
  }
};

/**
 * 控制回放
 * @param {String} command 控制命令"play","pause","scale"
 * @param {Number|String} scale 回放速度，0.5,2
 * @param {String|Number} [range] 播放跳转 视频时间戳
 * @return {Promise}
 */
VideoPlayerEasyPlayer.prototype.controlPlaybackStream = function(command, scale, range){
  console.log('controlPlaybackStream', command, scale, range);
  var that = this;
  return new window.Promise(function(resolve, reject){
    var streamID = that._playbackStreamCurrentID;
    if(!defined(streamID)){
      reject('缺少streamID');
      return;
    }
    var requiredData = {
      streamID: streamID,
      command: command,
      scale: '' + parseInt(scale)
    };

    if(defined(range) && range !== 'now'){
      requiredData.range = parseInt(range);
    }
    console.log('controlPlaybackStream', requiredData);
    var controlPlaybackStreamPromise = MediaServiceProtocol.controlPlaybackStream(requiredData);
    controlPlaybackStreamPromise.then(function (json) {
      // console.log('resolve', json);
      resolve(streamID + '回放控制成功');
    }).catch(function (msg) {
      var text = streamID + '回放控制失败：' + msg;
      console.warn(text);
      that.showErrorTips(text);
      resolve(text);
    });
  });
};

/**
 * 初始化回放视频时的需要的全局时钟
 */
VideoPlayerEasyPlayer.prototype.initPlaybackClock = function (clock) {
  this._clock = clock;
};

/**
 * 开始断线重连
 * @description 视频流有时会因为某些原因而临时断线，这时需要一个重新连接的尝试机制，这里初始化参数，并会用以指数增长的形式主动请求若干次通过reconnect函数，达到重连的目的
 * @see VideoPlayerEasyPlayer#reconnect
 */
VideoPlayerEasyPlayer.prototype.startReconnect = function () {
  var that = this;
  console.log('视频流断线重连函数开始了' + that._videoUrl);
  // 初始化若干参数
  this.reconnectInterval = 1000;
  this.maxReconnectInterval = 30000;
  this.reconnectDecay = 1.5;
  this.timeoutInterval = 2000;
  this.maxReconnectAttempts = null;
  this.reconnectAttempts = 0;

  // 首先排除主动黑屏的情况
  if (this._videoUrl === 'rtmp') {
    that._isValidStream = false;
    // this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_END;
  } else {
    var timeout = that.reconnectInterval * Math.pow(that.reconnectDecay, that.reconnectAttempts);
    setTimeout(function() {
      that.reconnectAttempts++;
      if(that._isValidStream === false){
        // console.log('断线重连：' + that._videoUrl + '第' + that.reconnectAttempts);

        that.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_RESUME;
        // that._currentErrorMsg = '实时视频重连中...';
        that._currentErrorMsg = '断线重连：' + that._videoUrl + '第' + that.reconnectAttempts;
        that.reload();
        that.reconnect();
      }
    }, timeout > that.maxReconnectInterval ? that.maxReconnectInterval : timeout);
  }
};

/**
 * 断线重连
 * @description 视频流有时会因为某些原因而临时断线，需要一个重新连接的尝试机制
 * @see VideoPlayerEasyPlayer#startReconnect
 */
VideoPlayerEasyPlayer.prototype.reconnect = function () {
  var that = this;

  // 首先排除主动黑屏的情况
  if (this._videoUrl === '') {
    that._isValidStream = false;
  } else {
    var timeout = that.reconnectInterval * Math.pow(that.reconnectDecay, that.reconnectAttempts);
    setTimeout(function() {
      that.reconnectAttempts++;
      if(that._isValidStream === false){
        console.log('断线重连：' + that._videoUrl + '第' + that.reconnectAttempts);
        that.reload();
        that.reconnect();
      }
    }, timeout > that.maxReconnectInterval ? that.maxReconnectInterval : timeout);
  }

};

/**
 * 获取回放的数据
 */
VideoPlayerEasyPlayer.prototype.getPlaybackTickPromise = function () {
  var that = this;
  return new window.Promise(function(resolve, reject){
    var streamID = that._playbackStreamCurrentID;
    if(defined(streamID)){
      var sendObj = {
        streamid: streamID
      };
      var getDownloadPlaybackInformationPromise = MediaServiceProtocol.getDownloadPlaybackInformation(sendObj);
      getDownloadPlaybackInformationPromise.then(function (res) {
        if(defined(res)){
          // console.log(res.Duration, res.Progress, res.TimestampSec);
          resolve({
            progress: res.Progress,
            timestampSec: res.TimestampSec
          });
        }
      }).catch(function (msg) {
        // console.log(msg);
        reject(msg);
      });
    }else{
      reject('no streamID');
    }
  });
};

/**
 * 视频截屏
 * @description 得到的stream可以直接放到<img>的src中
 * @param {Number} [quality=100] 截屏质量1到100都为有效值，100最高
 */
VideoPlayerEasyPlayer.prototype.printScreen = function(quality){
  var that = this;
  var jpgQuality = defaultValue(quality, 100);
  var stream = this.getPrintScreenBase64Jpg(jpgQuality);
  var promise = FileProtocol.UploadJpgStream(stream);
  promise.then(function (data) {
    console.log(data);
  }).catch(function (msg) {
    console.log(msg);
  });
};

/**
 * 获取截屏的base64图像
 * @return {String}
 */
VideoPlayerEasyPlayer.prototype.getPrintScreenBase64Png = function(){
  var canvas = document.createElement('canvas');
  canvas.width = this._videoElement.width;
  canvas.height = this._videoElement.height;
  var canvasCtx = canvas.getContext('2d');
  canvasCtx.drawImage(this._videoElement, 0, 0, this._videoElement.width, this._videoElement.height, 0, 0, canvas.width, canvas.height);
  var base64str = canvas.toDataURL('image/png');
  // console.log(base64str);
  return base64str;
};

/**
 * 获取截屏的base64jpg图像
 * @description 得到的stream可以直接放到<img>的src中
 * @param {Number} [quality=100] 截屏质量1到100都为有效值，100最高
 * @performance 90ms到100ms 100质量分数之下 大致快两倍
 * @return {String}
 */
VideoPlayerEasyPlayer.prototype.getPrintScreenBase64Jpg = function (quality) {
  var canvas = document.createElement('canvas');
  canvas.width = this._videoElement.width;
  canvas.height = this._videoElement.height;
  var canvasCtx = canvas.getContext('2d');
  var video = this._videoElement;
  canvasCtx.drawImage(video, 0, 0, this._videoElement.width, this._videoElement.height, 0, 0, canvas.width, canvas.height);
  var jpgQuality = defaultValue(quality, 100);
  var base64str = canvas.toDataURL('image/jpeg', jpgQuality / 100);
  // console.log(base64str);
  return base64str;

};

/**
 * 框选人体
 * @param options
 * @param {Number} [options.quality=100] 截屏质量1到100都为有效值，100最高
 * @param {Number} options.mouseX 鼠标相对位置
 * @param {Number} options.mouseY 鼠标相对位置
 */
VideoPlayerEasyPlayer.prototype.analyzeHumanBody = function(options){
  this._videoElementLoadingIndicator.style.display = 'block';
  var that = this;
  var jpgQuality = defaultValue(options.quality, 100);
  var stream = this._chplayer.printScreen(jpgQuality);
  var promise = FileProtocol.UploadJpgStream(stream);
  promise.then(function (data) {
    var videoAnalyzeProtocolPromise = VideoAnalyzeProtocol.detectHumanBody({
      imageBodyUrl: data
    });
    videoAnalyzeProtocolPromise.then(function (json) {
      var bodyRectArray = json.bodyRect;
      var canvasWidth = that._drawCanvas.clientWidth;
      var canvasHeight = that._drawCanvas.clientHeight;
      var mouseX = options.mouseX;
      var mouseY = options.mouseY;
      var mousePixelX = 1920 * mouseX / canvasWidth;
      var mousePixelY = 1080 * mouseY / canvasHeight;

      for(var i = 0; i < bodyRectArray.length; i++){
        var bodyRect = bodyRectArray[i];
        if(isPointInRect(mousePixelX, mousePixelY, bodyRect)){
          that.drawSuspectRectStatic({
            widthMin: bodyRect.leftTopX,
            widthMax: bodyRect.rightBottomX,
            heightMin: bodyRect.leftTopY,
            heightMax: bodyRect.rightBottomY,
            suspectUid: 'test' + i
          });
        }
      }

      that._videoElementLoadingIndicator.style.display = 'none';

      function isPointInRect(mousePixelX, mousePixelY, bodyRect) {
        return (mousePixelX < bodyRect.rightBottomX
          && mousePixelX > bodyRect.leftTopX
          && mousePixelY < bodyRect.rightBottomY
          && mousePixelY > bodyRect.leftTopY);
      }

    }).catch(function (msg) {
      console.log(msg);
      that._videoElementLoadingIndicator.style.display = 'none';
    });
  }).catch(function (msg) {
    console.log(msg);
    that._videoElementLoadingIndicator.style.display = 'none';
  });
};

/**
 * 获取框选人体的数据
 * @param options
 * @param {Number} [options.quality=100] 截屏质量1到100都为有效值，100最高
 * @param {Number} options.mouseX 鼠标相对位置
 * @param {Number} options.mouseY 鼠标相对位置
 * @param {Function} callback
 */
VideoPlayerEasyPlayer.prototype.getAnalyzeHumanBody = function(options, callback){
  this._videoElementLoadingIndicator.style.display = 'block';
  // this.showErrorTips('正在识别视频中的人体......');
  var that = this;
  // var jpgQuality = defaultValue(options.quality, 100);
  // var stream = this._chplayer.printScreen(jpgQuality);
  // var promise = FileProtocol.UploadJpgStream(stream);
  var stream;
  try{
    stream = this._chplayer.printScreenPNG();
  }catch (e){
    console.log(e);
    callback('chplayer错误-->' + e);
    that._videoElementLoadingIndicator.style.display = 'none';
    return;
  }
  // var stream = this._chplayer.printScreenPNG();
  var promise = FileProtocol.UploadPngStream(stream);

  promise.then(function (data) {
    var videoAnalyzeProtocolPromise = VideoAnalyzeProtocol.detectHumanBody({
      imageBodyUrl: data
    });
    videoAnalyzeProtocolPromise.then(function (json) {
      var bodyRectArray = json.bodyRect;
      var canvasWidth = that._drawCanvas.clientWidth;
      var canvasHeight = that._drawCanvas.clientHeight;
      var mouseX = options.mouseX;
      var mouseY = options.mouseY;
      var mousePixelX = 1920 * mouseX / canvasWidth;
      var mousePixelY = 1080 * mouseY / canvasHeight;
      var dataArray = [];

      for(var i = 0; i < bodyRectArray.length; i++){
        var bodyRect = bodyRectArray[i];
        if(isPointInRect(mousePixelX, mousePixelY, bodyRect)){
          // that.drawSuspectRectStatic({
          //     widthMin: bodyRect.leftTopX,
          //     widthMax: bodyRect.rightBottomX,
          //     heightMin: bodyRect.leftTopY,
          //     heightMax: bodyRect.rightBottomY,
          //     suspectUid: 'test' + i
          // });

          dataArray.push({
            widthMin: bodyRect.leftTopX,
            widthMax: bodyRect.rightBottomX,
            heightMin: bodyRect.leftTopY,
            heightMax: bodyRect.rightBottomY,
            suspectUid: 'test' + i
          });

        }
      }

      callback({
        dataArray: dataArray,
        imageBodyUrl: data,
        originSrc: stream
      });

      that._videoElementLoadingIndicator.style.display = 'none';

      function isPointInRect(mousePixelX, mousePixelY, bodyRect) {
        return (mousePixelX < bodyRect.rightBottomX
          && mousePixelX > bodyRect.leftTopX
          && mousePixelY < bodyRect.rightBottomY
          && mousePixelY > bodyRect.leftTopY);
      }

    }).catch(function (msg) {
      console.log(msg);
      callback(msg);
      that._videoElementLoadingIndicator.style.display = 'none';
    });
  }).catch(function (msg) {
    console.log(msg);
    that._videoElementLoadingIndicator.style.display = 'none';
  });
};

/**
 * 显示暂停叠加层
 * @see VideoPlayerEasyPlayer#pause
 * @description 这个层的主要意义在于回放暂停是视频流会中断，播放器会在若干秒后黑屏，因此需要一个临时的截图叠加层来遮盖黑屏
 */
VideoPlayerEasyPlayer.prototype.showPauseOverlay = function(){
  // console.log('生成图层');
  this._chplayer.showPauseOverlay();
  this._pauseOverlayVisible = true;
};

/**
 * 隐藏暂停叠加层
 * @see VideoPlayerEasyPlayer#reload
 * @see VideoPlayerEasyPlayer#play
 * @deprecated
 */
VideoPlayerEasyPlayer.prototype.hidePauseOverlay = function(){
  this._chplayer.hidePauseOverlay();
  this._pauseOverlayVisible = false;

};

/**
 * 选中此播放器（针对父集合）
 * @see VideoPlayerEasyPlayerGallery#getSelectedPlayer
 * @description 选中当前的播放器，并且将父集合中其他所有的播放器设置为非选中的状态
 */
VideoPlayerEasyPlayer.prototype.focusCurrentPlayerOfParentGallery = function(){
  if(this.select === true){
    this.select = false;
  }else{
    if(defined(this._parentGallery)){
      var selectedPlayer = this._parentGallery.getSelectedPlayer();
      if(defined(selectedPlayer)){
        selectedPlayer.select = false;
      }
      this.select = true;
    }
  }
};

/**
 * 根据时钟来回放视频
 * @param {Clock} clock
 * @param {String} [channelID] 如果有这个参数怎替换掉播放的视频20位id
 * @see VideoPlayerEasyPlayer#playBack
 */
VideoPlayerEasyPlayer.prototype.playBackByClock = function (clock, channelID){
  if(defined(channelID)){
    this.channelID = channelID;
  }

  var startJulian = clock.startTime;
  var stopJulian = clock.stopTime;

  var startJsDate = JulianDate.toDate(startJulian);
  var startMoment = moment(startJsDate);
  var startTimeStr = startMoment.format('YYYY-MM-DDTHH:mm:ss');

  var stopJsDate = JulianDate.toDate(stopJulian);
  var stopMoment = moment(stopJsDate);
  var stopTimeStr = stopMoment.format('YYYY-MM-DDTHH:mm:ss');
  this.playBack(startTimeStr, stopTimeStr);
};

/**
 * 订阅channelId变化事件
 * @param callback
 */
VideoPlayerEasyPlayer.prototype.subscribeChannelIdChangeEvent = function (callback){
  knockout.track(this, ['_channelID']);
  knockout.getObservable(this, '_channelID').subscribe(function(newValue) {
    callback(newValue);
  });
};

/**
 * 订阅videoUrl变化事件
 * @param callback
 */
VideoPlayerEasyPlayer.prototype.subscribeVideoUrlChangeEvent = function (callback){
    knockout.track(this, ['_videoUrl']);
    knockout.getObservable(this, '_videoUrl').subscribe(function(newValue) {
        callback(newValue);
    });
};

/**
 * 重新设置canvas的大小并刷新画布
 * @description 设置width和height后会自动清空画布
 */
VideoPlayerEasyPlayer.prototype.resizeCanvas = function () {
  this._drawCanvas.width = this._drawCanvas.clientWidth;
  this._drawCanvas.height = this._drawCanvas.clientHeight;
};

/**
 * 调整Video的大小
 */
VideoPlayerEasyPlayer.prototype.resizeVideo = function(){
  // this._chplayer.resize();
};

/**
 * Returns true if this object was destroyed; otherwise, false.
 * <br /><br />
 * If this object was destroyed, it should not be used; calling any function other than
 *
 * @returns {Boolean} <code>true</code> if this object was destroyed; otherwise, <code>false</code>.
 *
 * @see VideoPlayerEasyPlayer#destroy
 */
VideoPlayerEasyPlayer.prototype.isDestroyed = function() {
  return false;
};

/**
 * Removes and destroys all created by this instance.
 */
VideoPlayerEasyPlayer.prototype.destroy = function() {
  knockout.cleanNode(this._wrapper);
  this._container.removeChild(this._wrapper);

  return destroyObject(this);
};

export default VideoPlayerEasyPlayer;
