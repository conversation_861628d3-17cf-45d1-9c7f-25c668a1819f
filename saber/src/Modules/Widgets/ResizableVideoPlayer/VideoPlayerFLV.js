import defaultValue from '../../Core/defaultValue';
import defined from '../../Core/defined';
import defineProperties from '../../Core/defineProperties';
import destroyObject from '../../Core/destroyObject';
import DeveloperError from '../../Core/DeveloperError';
import JulianDate from '../../Core/JulianDate';
import knockout from '../../ThirdParty/knockout';
import getElement from '../../Widgets/getElement';
import Queue from '../../Core/Queue';
import StringUtil from '../../Util/StringUtil';

import MediaServiceProtocol from '../../Protocol/MediaServiceProtocol';
import RealtimeVideoApi from '@/api/video/realtimeVideo';
import { getSystemParam } from '@/api/user';
import { startGBChannel, channellist } from '@/api/video/gbVideo';
import historyVideoApi from '@/api/video/historyVideo';
import FileProtocol from '../../Protocol/FileProtocol';
import ShowTipsWidget from '../../Widgets/ShowTipsWidget/ShowTipsWidget';
import VideoPlayerToolBar from '../../Widgets/VideoPlayerToolBar/VideoPlayerToolBar';
import VideoPlayerToolBarForPlayback from '../../Widgets/VideoPlayerToolBarForPlayback/VideoPlayerToolBarForPlayback';
import VideoPlayerInfoWidget from '../../Widgets/VideoPlayerInfoWidget/VideoPlayerInfoWidget';
import VideoPlayerIndexWidget from '../../Widgets/VideoPlayerIndexWidget/VideoPlayerIndexWidget';
import CameraSpeedDomeWidget from '../../Widgets/CameraSpeedDomeWidget/CameraSpeedDomeWidget';
import moment from 'moment';
import VideoPlayerStatusEnum from './VideoPlayerStatusEnum';
import VideoPlayerStatusEnumHelper from './VideoPlayerStatusEnumHelper';
import VideoPlayerFLVViewModel from './VideoPlayerFLVViewModel';

import * as flvjs from '../../ThirdParty/flvjs/publish/flv.js'; // 音频格式g711a
// import flvjs from 'flv.js'; // 音频格式aac
import ResourceUtil from '../../Util/ResourceUtil';
import StreamTypeEnum from '@/enumerate/video/streamType';
import DataTypeEnum from '@/enumerate/video/dataType';
import uploadEvent from '@/Modules/Util/uploadEvent';
import Fullscreen from '@/Modules/Core/Fullscreen';

/**
 * VideoPlayerFLV
 * @alias VideoPlayerFLV
 * @description FLV播放器改自bilibili的开源播放器
 * @see https://github.com/bilibili/flv.js
 * @see https://github.com/bilibili/flv.js/blob/master/docs/api.md
 *
 * @param {Object} options
 * @param {VideoPlayerGallery} options.parentGallery 构造此播放器类VideoPlayerGallery
 * @param {String} [options.url=''] 初始化的视频流地址
 * @param {String|Element} options.container 容器ID或容器元素
 * @param {String|Element} options.id 容器ID或容器元素
 * @param {String} [options.protocol='FLV'] FLV/WS_FLV
 * @param {Number} [options.defaultDataType=0] 数据类型 0：音视频，1：视频，2：双向对讲，3：监听，4：中心广播，5：透传
 * @param {Number} [options.defaultStreamType] 默认的码流类型 码流类型  0：主码流，1：子码流
 * @param {Number} [options.index] 标号
 * @param {Boolean} [options.subscribeDoubleClickFullScreenEvent] 双击全屏
 * @param {Boolean} [options.forPlayback] 是否用于历史回放
 *
 * @constructor
 */
function VideoPlayerFLV (options) {
  // >>includeStart('debug', pragmas.debug);
  if (!defined(options)) {
    options = {};
  }
  if (!defined(options.container)) {
    throw new DeveloperError('options.container is required.');
  }
  // >>includeEnd('debug');

  // 3分钟推流设置
  this.pushTime = null;
  this.pushTimeFn = () => {
    if (this.pushTimer) {
      clearInterval(this.pushTimer);
      this.pushTimer = null;
    }
    this.pushTimer = setInterval(() => {
      this.videoPlayerToolBar.pushTimeTextDomText = this.pushTime;
      this.pushTime--;
      if (!this.pushTimeFlag) {
        clearInterval(this.pushTimer);
        this.pushTimer = null;
        this.pushTime = this.pushTimeDefault;
      }
    }, 1000);
  };

  this.pushTimeReady = () => {
    // 3分钟推流设置
    let localConfig = JSON.parse(localStorage.getItem(`localUserConfig`));
    this.pushTimeDefault = ((localConfig && localConfig.videoLiveTime) ? localConfig.videoLiveTime : 3) * 60; // 默认3分钟
    this.pushTimeFlag = true;
    this.pushTime = this.pushTimeDefault;
    this.pushTimeFn();
    console.log('触发pushTimeFn');
  };

  this._flvjs = flvjs;
  this._protocol = defaultValue(options.protocol, 'WS_FLV');
  this._streamType = defaultValue(options.defaultStreamType, StreamTypeEnum.MAIN_STREAM);
  this._subscribeDoubleClickFullScreenEvent = defaultValue(options.subscribeDoubleClickFullScreenEvent, true);
  this._dataType = defaultValue(options.defaultDataType, DataTypeEnum.VIDEO_AUDIO);
  // 监听模式
  this.monitorModel = false;
  // 当前是否在播放
  this.isPlayer = true;
  // 点击视频窗口中的关闭按钮时用到该字段
  this.isCloseVideo = false;
  /**
   * 标记属性
   * @type {Number|undefined}
   * @private
   */
  this._index = options.index;

  var that = this;
  var viewModel = new VideoPlayerFLVViewModel(options);

  var container = getElement(options.container);

  var wrapper = document.createElement('div');
  wrapper.className = 'lux-resizable-video-player';
  wrapper.setAttribute('data-bind', 'css: { "lux-resizable-video-player-fullScreen" : showFullScreen}');
  wrapper.setAttribute('draggable', 'true');
  container.appendChild(wrapper);

  // video
  this._videoElementId = defaultValue(options.id, StringUtil.generateLongUid());// 会表现为父框架的<code>id+'0x0'
  var videoElement = document.createElement('video');
  videoElement.className = 'lux-resizable-video-element';
  videoElement.setAttribute('id', this._videoElementId);
  videoElement.setAttribute('width', '1920');
  videoElement.setAttribute('height', '1080');
  videoElement.setAttribute('autoplay', '');
  wrapper.appendChild(videoElement);

  // 一个叠加层，用于绘制嫌疑人框
  var videoElementCanvas = document.createElement('canvas');
  videoElementCanvas.className = 'lux-video-player-flash-overlay';
  videoElementCanvas.setAttribute('draggable', 'false');
  videoElementCanvas.setAttribute('data-bind', 'css: { "lux-video-player-flash-overlay-visible" : showFlashDragOverlay, "lux-video-player-flash-overlay-highlight" : highlightFlashDragOverlay}');
  wrapper.appendChild(videoElementCanvas);

  // 一个叠加层，用于高亮摄像头，不能操作
  var videoElementHighlightOverlay = document.createElement('div');
  videoElementHighlightOverlay.className = 'lux-video-player-active-overlay';
  videoElementHighlightOverlay.setAttribute('draggable', 'true');
  videoElementHighlightOverlay.setAttribute('data-bind', 'css: { "lux-video-player-active-overlay-visible" : showFlashDragOverlay, "lux-video-player-active-overlay-highlight" : highlightActiveOverlay}');
  wrapper.appendChild(videoElementHighlightOverlay);

  // 一个叠加层，用于操作div，比如拖拽，点击选中
  var videoElementOverlay = document.createElement('div');
  videoElementOverlay.className = 'lux-video-player-flash-overlay';
  videoElementOverlay.setAttribute('draggable', 'true');
  videoElementOverlay.setAttribute('data-bind', 'css: { "lux-video-player-flash-overlay-visible" : showFlashDragOverlay, "lux-video-player-flash-overlay-highlight" : highlightFlashDragOverlay}');
  wrapper.appendChild(videoElementOverlay);

  // 一个叠加层，用于操作显示错误信息
  var videoElementErrorOverlay = document.createElement('div');
  videoElementErrorOverlay.className = 'lux-video-player-error-overlay';
  wrapper.appendChild(videoElementErrorOverlay);
  this._showTipsWidget = new ShowTipsWidget({
    container: videoElementErrorOverlay
  });

  // 加载中
  var videoElementLoadingIndicator = document.createElement('div');
  videoElementLoadingIndicator.className = 'loadingIndicator';
  videoElementLoadingIndicator.style.display = 'none';
  wrapper.appendChild(videoElementLoadingIndicator);

  // 球机的控制控件
  var videoSpeedDomeControl = document.createElement('div');
  videoSpeedDomeControl.className = 'lux-video-player-videoSpeedDomeControl';
  wrapper.appendChild(videoSpeedDomeControl);

  // 视频播放器工具栏
  var videoPlayerToolBarContainer = document.createElement('div');
  videoPlayerToolBarContainer.className = 'lux-video-player-videoPlayerToolBar';
  wrapper.appendChild(videoPlayerToolBarContainer);

  knockout.applyBindings(viewModel, wrapper);

  this._parentGallery = options.parentGallery;// 整个父框架对象的指针传进来
  this._viewModel = viewModel;
  this._videoElement = videoElement;
  this._wrapper = wrapper;
  this._container = container;
  this._drawCanvas = videoElementCanvas;
  this._drawCanvasCtx = this._drawCanvas.getContext('2d');
  this._videoElementOverlay = videoElementOverlay;
  this._videoElementLoadingIndicator = videoElementLoadingIndicator;
  this._lastActiveTime = JulianDate.now();
  this._videoUrl = undefined;

  this._channelID = undefined;// options.channelID;
  this._playbackStreamCurrentID = undefined;// 正在使用的回放视频流ID
  this._playbackStreamIDQueue = new Queue();// 队列用于删除
  this._pauseOverlayVisible = false;// 暂停叠加层是否可见
  this._forPlayback = defaultValue(options.forPlayback, false);

  this._csMsg = options.customSetup ? options.customSetup.customMsg : undefined;
  this._playerName = 'player' + this._videoElementId;

  this._loadedHandlerName = 'loadedHandler' + this._videoElementId;

  this.loaded = false;// flash是否加载完毕
  this._isValidStream = false;// 当前播放的是否是有效视频流

  this.videoPlayerStatus = VideoPlayerStatusEnum.INIT_PLEASE_DRAG;// 默认

  /**
   * 是否在主屏幕
   * @type {boolean}
   */
  this.isBigVideoPlayer = false;
  knockout.track(this, ['isBigVideoPlayer']);

  /**
   * 球机控制控件
   * @private
   * @type {CameraSpeedDomeWidget}
   */
  this._cameraSpeedDomeWidget = new CameraSpeedDomeWidget(videoSpeedDomeControl, {
    videoPlayer: this
  });

  if (!this._forPlayback) {
    /**
     * 视频播放器工具栏
     * @private
     * @type {VideoPlayerToolBar}
     */
    this._videoPlayerToolBar = new VideoPlayerToolBar(videoPlayerToolBarContainer, {
      videoPlayer: this
    });
  } else {
    /**
     * 视频播放器工具栏
     * @private
     * @type {VideoPlayerToolBarForPlayback}
     */
    this._videoPlayerToolBar = new VideoPlayerToolBarForPlayback(videoPlayerToolBarContainer, {
      videoPlayer: this
    });
  }

  /**
   * 视频播放器标题信息
   * @private
   * @type {VideoPlayerInfoWidget}
   */
  this._videoPlayerInfoWidget = new VideoPlayerInfoWidget({
    videoPlayer: this
  });

  /**
   * 视频播放器标题信息
   * @private
   * @type {VideoPlayerIndexWidget}
   */
  this._videoPlayerIndexWidget = new VideoPlayerIndexWidget({
    videoPlayer: this
  });

  this.loaded = true;
  this.showFlashDragOverlay = true;

  this._initDragEvent();

  that.showError({
    videoPlayerStatus: VideoPlayerStatusEnum.INIT_PLEASE_DRAG
  });

  this._subscribeCurrentTimeEvent();
  // 在离开页面的时候清除
  uploadEvent.registerEvent(() => {
    if (this._videoStreamValid) {
      // this.stopLiveChannel();
      // 离开页面时也需初始化视频播放器工具栏
      this.initializePlayerToggleButton();
    }
  });

  if (this._subscribeDoubleClickFullScreenEvent) {
    this.subscribeDblClickFullscreenEvent();
  }
}

defineProperties(VideoPlayerFLV.prototype, {
  /**
   * Gets the parent container.
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {Element}
   */
  container: {
    get: function () {
      return this._container;
    }
  },

  /**
   * Gets the view model.
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {VideoPlayerFLV}
   */
  viewModel: {
    get: function () {
      return this._viewModel;
    }
  },

  /**
   * Gets the wrapper.
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {Element}
   */
  wrapper: {
    get: function () {
      return this._wrapper;
    }
  },

  /**
   * 缩略图地址
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {String}
   */
  thumbnailUrl: {
    get: function () {
      return this._viewModel.thumbnailUrl;
    },
    set: function (value) {
      this._viewModel.thumbnailUrl = value;
    }
  },

  /**
   * 视频地址
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {String}
   */
  videoUrl: {
    get: function () {
      return this._videoUrl;
    },
    set: function (value) {
      this._videoUrl = value;
    }
  },

  /**
   * 通道号
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {String}
   */
  channelID: {
    get: function () {
      return this._channelID;
    },
    set: function (value) {
      this._channelID = value;
    }
  },

  /**
   * 这个播放器的id
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {String}
   */
  videoElementId: {
    get: function () {
      return this._videoElementId;
    },
    set: function (value) {
      this._videoElementId = value;
    }
  },

  /**
   * 高亮播放器
   * @description 红色
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {Boolean}
   */
  highlight: {
    get: function () {
      return this._viewModel.highlightActiveOverlay;
    },
    set: function (value) {
      this._viewModel.highlightActiveOverlay = value;
    }
  },

  /**
   * 选中摄像头播放器
   * @description 蓝色
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {Boolean}
   */
  select: {
    get: function () {
      return this._viewModel.highlightFlashDragOverlay;
    },
    set: function (value) {
      this._viewModel.highlightFlashDragOverlay = value;
    }
  },

  /**
   * 唯一一个可以触发鼠标事件的操作层
   * @description 蓝色
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {Element}
   */
  videoElementOverlay: {
    get: function () {
      return this._videoElementOverlay;
    }
  },

  /**
   * 球机控件是否可见
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {Boolean}
   */
  cameraSpeedDomeWidgetVisible: {
    get: function () {
      return this._cameraSpeedDomeWidget.show;
    },
    set: function (value) {
      this._cameraSpeedDomeWidget.show = value;
    }
  },

  /**
   * 视频播放器工具栏
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {VideoPlayerToolBar}
   */
  videoPlayerToolBar: {
    get: function () {
      return this._videoPlayerToolBar;
    }
  },

  /**
   * 视频播放器工具栏是否可见
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {Boolean}
   */
  videoPlayerToolBarVisible: {
    get: function () {
      return this._videoPlayerToolBar.show;
    },
    set: function (value) {
      this._videoPlayerToolBar.show = value;
    }
  },

  /**
   * 视频播放器标题信息是否可见
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {Boolean}
   */
  videoPlayerInfoWidgetVisible: {
    get: function () {
      return this._videoPlayerInfoWidget.show;
    },
    set: function (value) {
      this._videoPlayerInfoWidget.show = value;
    }
  },

  /**
   * 视频播放器标号控件是否可见
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {Boolean}
   */
  videoPlayerIndexWidgetVisible: {
    get: function () {
      return this._videoPlayerIndexWidget.show;
    },
    set: function (value) {
      this._videoPlayerIndexWidget.show = value;
    }
  },

  /**
   * 球机控件
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {CameraSpeedDomeWidget}
   */
  cameraSpeedDomeWidget: {
    get: function () {
      return this._cameraSpeedDomeWidget;
    }
  },

  /**
   * 当前的时间
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {JulianDate|undefined}
   */
  currentClockTime: {
    get: function () {
      var time;
      if (defined(this._clock)) {
        time = this._clock.currentTime;
      }
      return time;
    }
  },

  /**
   * 是否有有效的channelID
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {Boolean}
   */
  hasValidChannelId: {
    get: function () {
      return this._channelID !== undefined;
    }
  },

  /**
   * 详情信息
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {Element}
   */
  cameraObject: {
    get: function () {
      if (this.hasValidChannelId) {
        return this._parentGallery.channelIdIndexCollection.get(this._channelID).cameraObject;
      } else {
        return undefined;
      }
    }
  },

  /**
   * 画布
   * @memberof VideoPlayerFLV.prototype
   *
   * @type {Element}
   */
  drawCanvas: {
    get: function () {
      return this._drawCanvas;
    }
  },

  /**
   * 画布ctx
   * @memberof VideoPlayerFLV.prototype
   */
  drawCanvasCtx: {
    get: function () {
      return this._drawCanvasCtx;
    }
  },

  /**
   * 码流（主码流/子码流）
   * @memberof VideoPlayerFLV.prototype
   */
  streamType: {
    get: function () {
      return this._streamType;
    }
  },

  /**
   * 数据模式（音视频/监听）
   * @memberof VideoPlayerFLV.prototype
   */
  dataType: {
    get: function () {
      return this._dataType;
    }
  }
});

/**
 * 初始化拖拽事件
 * @private
 */
VideoPlayerFLV.prototype._initDragEvent = function () {
  var videoElementOverlay = this._videoElementOverlay;
  var that = this;
  videoElementOverlay.addEventListener('dragstart', function (event) {
    // console.log('dragstart');
    // console.log(event);
    var data = {
      videoUrl: that.videoUrl,
      thumbnailUrl: that.thumbnailUrl,
      origin: 'VIDEO',
      videoElementId: that._videoElementId,
      channelID: that._channelID,
      vehicleId: that.vehicleId
    };
    event.dataTransfer.setData('Text', JSON.stringify(data));
  }, false);
  videoElementOverlay.addEventListener('drop', function (event) {
    event.preventDefault();
    // console.log('drop');
    // console.log(event);

    var data;
    try {
      data = JSON.parse(event.dataTransfer.getData('Text'));
    } catch (e) {
      console.log(e);
    }
    // console.log(data);
    if (defined(data)) {
      if (data.origin === 'VIDEO') {
        // 交换同一个gallery的两个视频
        if (defined(that._parentGallery)) {
          console.log('尚未开发');
          // if (that.getParentGalleryId(data.videoElementId) === that.getParentGalleryId(that._videoElementId)) {
          //   let position1 = that.getPositionOfParentGallery(data.videoElementId);
          //   let position2 = that.getPositionOfParentGallery(that._videoElementId);
          //   console.log(position1, position2);
          //   // 只有同一个视频集合图库才能交换播放器
          //   if (defined(position1) && defined(position2)) {
          //     that._parentGallery.switchTwoPlayers(position1, position2);
          //   }
          // }
        } else if (defined(data.channelID)) {
          that.reloadChannelID(data);
        } else { // 重载视频
          that.reload(data);
        }
      } else if (data.origin === 'VEHICLETREE') {
        // that._customData = {
        //   'VEHICLETREE': data.customData
        // };
        // console.log('customData-->', that._customData)
        // 获取VEHICLETREE中的数据中的视频流地址并替换
        // console.log(data);
        that.reloadChannelID({
          channelID: data.channelID,
          vehicleId: data.vehicleId,
          customData: {
            'VEHICLETREE': {
              treeNodeKey: data.treeNodeKey
            }
          },
          callback: function () {
            that.videoPlayerToolBarVisible = true;
          }
        });
      }
    }
  }, false);
  videoElementOverlay.addEventListener('dragenter', function (event) {
    event.preventDefault();
  }, false);
  videoElementOverlay.addEventListener('dragover', function (event) {
    event.preventDefault();
  }, false);
  videoElementOverlay.addEventListener('dragleave', function (event) {
    event.preventDefault();
  });
  videoElementOverlay.addEventListener('dragend', function (event) {
    event.preventDefault();
  });
};

/**
 * 获取某个channelID的摄像头流媒体地址并播放，然后停掉回放的流
 * @description 会调用reload()
 * @param {Object} [options]
 * @param {String} [options.channelID] 摄像头通道号，这个参数由车牌号+通道数据拼接而成，如粤A6T81S_2代表粤A6T81S车的通道2
 * @param {Function} [options.callback] 回调函数
 * @param {Object} [options.customData] 自定义数据
 * @see VideoPlayerFLV#reloadChannelIdAndUrl
 */
VideoPlayerFLV.prototype.reloadChannelID = async function (options) {
  // console.log('reloadChannelID:', options);
  // console.log(options);
  // 如果正在播放别的channel，将channel停掉，在这里处理是为了确保每点开一路新的流都会关闭一个旧的
  // this.stopLiveChannel();
  // this.stopPlayBackStream();
  console.log('触发reloadChannelID');
  this.pushTime = this.pushTimeDefault;
  this.pushTimeFlag = true;

  options = defaultValue(options, {});
  this._customData = options.customData;
  this.vehicleId = +options.vehicleId;
  this.deviceId = options.deviceId;
  this.deviceType = options.deviceType;
  this.isGBLive = options.treeCategory === '202' ? true : false;
  this.hasVideo = options.hasVideo ? options.hasVideo : null;
  this.hasAudio = options.hasAudio ? options.hasAudio : null;
  this.monitorModel = false;
  this.isPlayer = true;
  console.log(options, 55555);
  this.treeNodeKey = options.customData['VEHICLETREE'].treeNodeKey;

  if (options.streamType || options.streamType === 0) { // 有streamtype直接用
    this._streamType = options.streamType;
  }
  // 通道
  if (defined(options.channelID)) {
    if (this.checkStreamValid() === true) {
      this.clearStream();
      this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_SWITCH;
    } else {
      this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_LOADING;
    }
    this._customData = options.customData;
    this._channelID = options.channelID;
  } else {
    if (this.checkStreamValid() === true) {
      this.clearStream();
      this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_SWITCH;
    } else {
      this.videoPlayerStatus = VideoPlayerStatusEnum.INIT_PLEASE_DRAG;
    }
    this._customData = undefined;
    this._channelID = undefined;
  }
  if (!defined(this._channelID)) {
    return;
  }

  var that = this;

  // console.log('reloadChannelID#RealtimeVideoApi.startChannel', this._channelID);
  let tempArray = this._channelID.split('_');
  let licencePlate = tempArray[0];
  let channel = tempArray[1];
  if (this._streamType === 1) { // 显示子码流按钮
    this._videoPlayerToolBar.mainStreamButton.style.display = 'none';
    this._videoPlayerToolBar.subStreamButton.style.display = 'inline-block';
  } else { // 显示主码流按钮
    this._videoPlayerToolBar.mainStreamButton.style.display = 'inline-block';
    this._videoPlayerToolBar.subStreamButton.style.display = 'none';
  }

  let startParams = options.changeApi ? {
    channel: parseInt(channel),
    // licencePlate: licencePlate,
    streamType: this._streamType,
    cmd: options.changeCmd ? options.changeCmd : 1, // 1-切换码流(切换时固定位1)，2-停止推流
    deviceId: this.deviceId,
    deviceType: this.deviceType
  } : {
    deviceId: this.deviceId,
    deviceType: this.deviceType,
    // licencePlate: licencePlate,
    channel: parseInt(channel),
    dataType: this._dataType,
    streamType: this._streamType
  };

  let startChannelPromise;
  // GB28181
  if (this.isGBLive) {
    const { code: terminalCode, data: terminalData } = await RealtimeVideoApi.terminalDetails(this.deviceId);
    if (!that.isPlayer) { // 点击播放视频并快速关闭视频时, 停止播放不显示报错信息
      return;
    }
    if (terminalCode !== 200 || !terminalData || !terminalData.channelIds) {
      if (that._channelID) {
        that.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_FAIL;
        that._currentErrorMsg = `点播${licencePlate}通道${channel}错误：获取终端信息失败`;
      }
      this.$message.error('终端信息获取失败');
      return;
    }
    const requestResult = await getSystemParam('gbsRequestUrl');
    const { code: gbsRequestCode, data: gbsRequestData } = requestResult.data;
    if (gbsRequestCode !== 200 || !gbsRequestData) {
      if (that._channelID) {
        that.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_FAIL;
        that._currentErrorMsg = `点播${licencePlate}通道${channel}错误：获取接口前缀失败`;
      }
      this.$message.error('获取接口前缀失败');
      return;
    }
    // 使用ws地址播放视频就用gbsStreamingUrl参数
    const streamingResult = await getSystemParam('gbsStreamingUrl');
    const { code: gbsStreamingCode, data: gbsStreamingData } = streamingResult.data;
    if (gbsStreamingCode !== 200 || !gbsStreamingData) {
      if (that._channelID) {
        that.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_FAIL;
        that._currentErrorMsg = `点播${licencePlate}通道${channel}错误：获取流媒体前缀失败`;
      }
      this.$message.error('获取流媒体前缀失败');
      return;
    }
    this.channelIds = terminalData.channelIds;
    const { ChannelList: channelData } = await channellist({serial: this.channelIds, url: gbsRequestData});
    if (!that.isPlayer) { // 点击播放视频并快速关闭视频时, 停止播放不显示报错信息
      return;
    }
    if (!channelData || !channelData.length) {
      if (that._channelID) {
        that.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_FAIL;
        that._currentErrorMsg = `点播${licencePlate}通道${channel}错误：获取视频通道编号失败`;
      }
      this.$message.error('视频通道编号获取失败');
      return;
    }
    channelData.sort((a, b) => a.Channel - b.Channel);
    startChannelPromise = startGBChannel({
      serial: this.channelIds,
      code: channelData[parseInt(channel) - 1].ID,
      url: gbsRequestData,
      prefix: gbsStreamingData
    });
  } else { // 808
    startChannelPromise = RealtimeVideoApi.startChannel(startParams, this._protocol, options.changeApi ? true : null);
  }
  startChannelPromise.then(function (json) {
    // add判定
    if (defined(options) && defined(options.callback)) {
      setTimeout(() => {
        that.reload({
          videoUrl: ResourceUtil.urlTranslate(json),
          hasVideo: defaultValue(options.hasVideo, true),
          callback: options.callback
        });
      }, 1000);
    } else {
      setTimeout(() => {
        that.reload({
          videoUrl: ResourceUtil.urlTranslate(json),
          hasVideo: defaultValue(options.hasVideo, true)
        });
      }, 1000);
    }
    that.videoPlayerToolBarVisible = true;
    if (that._dataType === 3) {
      that.videoPlayerStatus = VideoPlayerStatusEnum.PLAY_AUDIO;
    }
  }).catch(function (msg) {
    // 点击播放视频并快速关闭视频时, 停止播放不显示报错信息
    if (!that.isPlayer) {
      return;
    }
    if (that._channelID) {
      that.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_FAIL;
      that._currentErrorMsg = `点播${licencePlate}通道${channel}错误：${msg}`;
    }
  });
  // 无论切换推流是否成功，前端播放器状态都已经改变
  this.monitorModel = this._dataType === 3;
};

/**
 * 同时替换channelID和Url
 *
 * @description 此函数是为了在请求到多路视频是可以直接塞入数据，只允许VideoPlayerGallery调用
 * @param options
 * @param {String} options.channelID
 * @param {String} options.videoUrl
 * @see VideoPlayerFLV#reloadChannelID
 */
VideoPlayerFLV.prototype.reloadChannelIdAndUrl = function (options) {
  options = defaultValue(options, {});

  // this.stopLiveChannel();
  // this.stopPlayBackStream();

  if (this.checkStreamValid() === true) {
    this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_SWITCH;
    this.clearStream();
  } else {
    this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_LOADING;
  }

  this._channelID = options.channelID;
  this.reload({
    videoUrl: options.videoUrl
  });
  this.videoPlayerToolBarVisible = true;
};

/**
 * 重载视频
 *
 * @description 最基础的切换视频源的方法，其余的根据其他参数生成的方法都会调用此方法
 * @param {Object} [options] 当只是需要刷新视频时无需设置此属性时
 * @param {String} options.videoUrl 视频直播的地址
 * @param {String} [options.thumbnailUrl] 缩略图地址
 * @param {Boolean} [options.hasAudio=true] 是否解析音频
 * @param {Boolean} [options.hasVideo=true] 是否解析视频
 * @param {Boolean} [options.muted=false] 是否静音
 * @param {String} [options.isSpeedDomeCamera=false] 是不是快球摄像头
 * @param {Function} [options.callback] 回调函数
 */
VideoPlayerFLV.prototype.reload = function (options) {
  var that = this;
  options = defaultValue(options, {});
  if (options.fromIntercom === 1) {
    this.videoPlayerStatus = VideoPlayerStatusEnum.PLAY_INTERCOM;
  } else if(options.fromIntercom === 2) {
    this.videoPlayerStatus = VideoPlayerStatusEnum.PLAY_AUDIO;
  }

  if (defined(this._videoPlayer)) {
    this._videoPlayer.pause();
    this._videoPlayer.unload();
    this._videoPlayer.detachMediaElement();
    this._videoPlayer.destroy();
    this._videoPlayer = undefined;
  }
  // 点击播放视频并快速关闭视频时, 停止播放
  if (!this.isPlayer) {
    return;
  }
  if (defined(options.videoUrl)) {
    this._videoUrl = options.videoUrl;
    this._lastActiveTime = JulianDate.now();
  }
  var videoUrl = this._videoUrl;
  if (!videoUrl) {
    return;
  }
  console.log('VideoPlayerFLV#reload-->', this._videoUrl);

  var videoElement = this._videoElement;
  var flvPlayer = this._flvjs.createPlayer({
    type: 'flv',
    url: videoUrl,
    isLive: true,
    hasAudio: this.hasAudio ? this.hasAudio : defaultValue(options.hasAudio, true),
    hasVideo: this.hasVideo ? this.hasVideo : defaultValue(options.hasVideo, true)
  }, {
    enableStashBuffer: false,
    stashInitialSize: 128,
    fixAudioTimestampGap: false
    // Fixme
    /* lazyLoad: false,
      lazyLoadMaxDuration: 0,
      lazyLoadRecoverDuration: 0,
      deferLoadAfterSourceOpen: false, */
  });
  flvPlayer.attachMediaElement(videoElement);
  flvPlayer.load();
  flvPlayer.muted = defaultValue(options.muted, false); // 开启声音
  // flvPlayer.play(); // 已经使用了autoplay标记，无需再使用play了
  this._videoPlayer = flvPlayer;

  this._flyPlayerHandelLoading = true;
  // flvPlayer.on('error', err => {
  //   if (err === 'NetworkError') {
  //     this._flyPlayerHandelLoading = false;
  //     this.reload({
  //       videoUrl: options.videoUrl,
  //       hasAudio: options.hasAudio,
  //       isSpeedDomeCamera: options.isSpeedDomeCamera
  //     });
  //   }
  // });
  // flvPlayer.on('metadata_arrived', metadata => {
  //   this._flyPlayerHandelLoading = false;
  // });
  // flvPlayer.on('statistics_info', statisticsInfo => {
  //   let bps = parseInt(statisticsInfo.bps_video);
  //   if (!isNaN(bps)) {
  //     this.videoPlayerToolBar.bpsTextDomText = parseInt(statisticsInfo.bps_video) + 'bps';
  //   }
  // });
  flvPlayer.on(flvPlayer.PlayerEvents.ERROR, err => {
    if (err === flvPlayer.ErrorTypes.NETWORK_ERROR) {
      this._flyPlayerHandelLoading = false;
      this.reload({
        videoUrl: options.videoUrl,
        hasAudio: options.hasAudio,
        isSpeedDomeCamera: options.isSpeedDomeCamera
      });
    }
  });
  flvPlayer.on(flvPlayer.PlayerEvents.METADATA_ARRIVED, metadata => {
    this._flyPlayerHandelLoading = false;
  });
  flvPlayer.on(flvPlayer.TransmuxingEvents.STATISTICS_INFO, statisticsInfo => {
    let bps = parseInt(statisticsInfo.bps_video);
    if (!isNaN(bps)) {
      this.videoPlayerToolBar.bpsTextDomText = parseInt(statisticsInfo.bps_video) + 'bps';
    }
  });

  if (!this.isGBLive) {
    this.pushTimeReady();
  }

  this.cameraSpeedDomeWidgetVisible = false;
  // this.videoPlayerToolBarVisible = false;

  if (defined(options.callback)) {
    options.callback();
  }
};

/**
 * 暂停视频
 */
VideoPlayerFLV.prototype.pause = function () {
  if (defined(this._videoElement)) {
    if (!this._videoElement.paused) {
      this._videoElement.pause();
    }
  }
};

/**
 * 播放视频
 */
VideoPlayerFLV.prototype.play = function () {
  if (defined(this._videoElement)) {
    if (this._videoElement.paused) {
      this._videoElement.play().catch(e => { console.log(e); });
    }
  }
};

/**
 * 订阅channelId变化事件
 * @param {Function} callback
 */
VideoPlayerFLV.prototype.subscribeChannelIdChangeEvent = function (callback) {
  if (this._hasSubscribeChannelIdChangeEvent !== true) {
    knockout.track(this, ['_channelID']);
    this._hasSubscribeChannelIdChangeEvent = true;
  }
  knockout.getObservable(this, '_channelID').subscribe(function (newValue) {
    callback(newValue);
  });
};

/**
 * 重新设置canvas的大小并刷新画布
 * @description 设置width和height后会自动清空画布
 */
VideoPlayerFLV.prototype.resizeCanvas = function () {
  this._drawCanvas.width = this._drawCanvas.clientWidth;
  this._drawCanvas.height = this._drawCanvas.clientHeight;
};

/**
 * 回放视频
 * @param {Object} options
 * @param {Function} options.callback
 * @param {Function} options.parme
 * @param {String} options.parme.licencePlate 车牌号码
 * @param {Number} options.parme.channelId 通道号
 * @param {String} options.parme.startTime 开始时间
 * @param {Number} options.parme.startTimeStamp 秒时间戳
 * @param {String} options.parme.endTime 结束时间
 * @param {Number} options.parme.endTimeStamp 秒时间戳
 * @param {Number} options.parme.alarm 告警
 * @param {String} options.parme.fileSize 文件大小
 * @param {Number} options.parme.storageType 存储类型
 * @param {Number} options.parme.streamType 码流类型
 * @param {Number} options.parme.mediaType 媒体类型
 */
VideoPlayerFLV.prototype.playBack = function (options) {
  var that = this;
  let parme = options.parme;
  parme.vehicleId = parme.vehicleId || that.vehicleId;
  this.vehicleId = parme.vehicleId;
  let licencePlate = parme.licencePlate;
  let channelId = parme.channelId;
  this._channelID = licencePlate + '_' + channelId;
  this.stopPlayBackStream().then(() => { // 这里其实没有也不需要调用后台接口
    if (this.checkStreamValid() === true) {
      this.clearStream();
      this.videoPlayerStatus = VideoPlayerStatusEnum.PLAY_BACK_SWITCH;
    } else {
      this.videoPlayerStatus = VideoPlayerStatusEnum.PLAY_BACK_LOADING;
    }
    this._channelID = licencePlate + '_' + channelId;
    historyVideoApi.playHistoryVideo(parme).then(res => {
      if (res.code === 200) {
        let videoUrl = res.data.wsUrl || res.data.flvUrl;
        this.reload({
          videoUrl: videoUrl,
          callback: options.callback
        });
      } else {
        options.callback('点播失败');
      }
    });
  }).catch(errorMsg => {
    options.callback(errorMsg);
  });
};

/**
 * 控制回放
 * @param {String} command 控制命令"play","pause","faster","slower","scale"
 * @param {Number} [playTime] 播放跳帧，秒时间戳
 * @return {Promise}
 */
VideoPlayerFLV.prototype.controlPlaybackStream = function (command, playTime, param) {
  console.log('controlPlaybackStream', command, playTime);
  var that = this;
  if (!this._channelID) {
    that.showErrorTips('未指定通道');
    return;
  }
  let tempArray = this._channelID.split('_');
  let licencePlate = tempArray[0];
  let channel = tempArray[1];
  return new window.Promise((resolve, reject) => {
    if (command === 'faster') {
      if (!defined(this._playbackForwardTimes)) { // 快进或快退倍数0：无效，1:1倍，2:2倍,3:4倍，4:8倍，5:16倍 星航终端目前只支持2倍
        this._playbackForwardTimes = 2;
      } else {
        this._playbackForwardTimes = Math.min(this._playbackForwardTimes + 1, 5);
      }
      this._playbackControlCmd = 3;
    } else if (command === 'slower') {
      if (!defined(this._playbackForwardTimes)) { // 快进或快退倍数0：无效，1:1倍，2:2倍,3:4倍，4:8倍，5:16倍 星航终端目前只支持2倍
        this._playbackForwardTimes = 1;
      } else {
        this._playbackForwardTimes = Math.max(this._playbackForwardTimes - 1, 1);
      }
      this._playbackControlCmd = 3;
    } else if (command === 'play') {
      this._playbackForwardTimes = 1;
      this._playbackControlCmd = 3;
    } else if (command === 'pause') {
      if (!defined(this._playbackForwardTimes)) { // 快进或快退倍数0：无效，1:1倍，2:2倍,3:4倍，4:8倍，5:16倍
        this._playbackForwardTimes = 1;
      }
      this._playbackControlCmd = 1;
    } else if (command === 'scale') {
      if (!defined(this._playbackForwardTimes)) { // 快进或快退倍数0：无效，1:1倍，2:2倍,3:4倍，4:8倍，5:16倍
        this._playbackForwardTimes = 1;
      }
      this._playbackControlCmd = 5;
    }

    var requiredData = {
      vehicleId: that.vehicleId || param.vehicle_id,
      licencePlate: licencePlate,
      channelId: parseInt(channel),
      controlCmd: this._playbackControlCmd, // 回放控制 0：开始回放，1：暂停回放，2：结束回放，3：快进回放 4：关键帧快退回放，5：拖动回放，6：关键帧播放
      forwardTimes: this._playbackForwardTimes, // 快进或快退倍数0：无效，1:1倍，2:2倍,3:4倍，4:8倍，5:16倍
      playTime: defaultValue(playTime, undefined) // 通常为空就好，需要跳帧时再用这个
    };
    console.log('controlPlaybackStream', requiredData, param);
    var controlPlaybackStreamPromise = historyVideoApi.controlHistoryVideo(requiredData);
    controlPlaybackStreamPromise.then(function (json) {
      console.log('resolve', json);
      resolve('回放控制成功');
      switch (that._playbackForwardTimes) {
      case 1: {
        that.videoPlayerToolBar.speedText = 'X1';
        break;
      }
      case 2: {
        that.videoPlayerToolBar.speedText = 'X2';
        break;
      }
      case 3: {
        that.videoPlayerToolBar.speedText = 'X4';
        break;
      }
      case 4: {
        that.videoPlayerToolBar.speedText = 'X8';
        break;
      }
      case 5: {
        that.videoPlayerToolBar.speedText = 'X16';
        break;
      }
      }
    }).catch(function (msg) {
      var text = '回放控制失败：' + msg;
      // console.warn(text);
      that.showErrorTips(text);
      resolve(text);
    });
  });
};

/**
 * 是否有效视频流
 * @return {Boolean}
 */
VideoPlayerFLV.prototype.checkStreamValid = function () {
  if (defined(this._videoElement) && defined(this._videoUrl)) {
    if (this.getCurrentTime() > 0) {
      return true;
    }
  }
  return false;
};

/**
 * 是否有效视频地址
 * @return {Boolean}
 */
VideoPlayerFLV.prototype.checkVideoUrlValid = function () {
  return defined(this._videoUrl) && this._videoUrl !== '';
};

/**
 * 获取当前的时间
 * @return {Number}
 */
VideoPlayerFLV.prototype.getCurrentTime = function () {
  // console.log('getCurrentTime', this._videoElement.currentTime);
  return this._videoElement.currentTime;
};

/**
 * 订阅当前时间戳的事件
 * @description 监听视频流的状态
 * @private
 */
VideoPlayerFLV.prototype._subscribeCurrentTimeEvent = function () {
  var that = this;
  if (defined(this._subscribeCurrentTimeEventIntervalHandle)) {
    clearInterval(this._subscribeCurrentTimeEventIntervalHandle);
  }
  this._subscribeCurrentTimeEventIntervalHandle = setInterval(function () {
    var videoTime = that.getCurrentTime();

    if (defined(that._lastVideoTime)) {
      if (videoTime > that._lastVideoTime) { // 时间戳递增意味着流在正常播放
        that._videoStreamValid = true;
        that._lastVideoTime = videoTime;
        that._noReceiveNewFrameCount = 0;
        that._stopReconnect();
        that._hideError();
      } else if (videoTime < that._lastVideoTime) { // 时间戳倒转意味着重新拉流了
        that._lastVideoTime = videoTime;
        that._videoStreamValid = true;
        that._noReceiveNewFrameCount = 0;

        that.showError();
      } else { // 时间戳相等意味着视频停住了，但有可能是暂停或断流导致的
        if (that._videoElement.paused === false) {
          that._noReceiveNewFrameCount++;
          if (that._noReceiveNewFrameCount > 50) { // 5000ms未解码任何数据
            // that._videoStreamValid = false;
            if (that._videoStreamValid === true) {
              that.startReconnect(); // FIXME
            }
            that._videoStreamValid = false;
          } else { // 未解码数据，暂时忽略，只是卡了
            // console.log('未解码数据，暂时忽略-->', that._channelID, '_', that._videoUrl);
            that._videoStreamValid = true;
          }
        } else {
          that._videoStreamValid = true;
        }

        if (that._lastVideoTime === 0) {
          that.showError();
        }
      }
    } else {
      that._lastVideoTime = videoTime;
    }
  }, 100);
};

/**
 * 设置参数
 * @description 点击视频窗口中的关闭按钮时需要通知外层组件将数组中当前勾选项移除, 并将isCloseVideo置为默认值, 防止重复通知
 */
VideoPlayerFLV.prototype.editVideoParam = function () {
  this.isCloseVideo = false;
};

/**
 * 清空视频流
 * @description 清空流相当于删除播放器，因为MSE的参数是不可改的，只能重新生成一个新的
 */
VideoPlayerFLV.prototype.clearStream = function () {
  console.log('🎦VideoPlayerFLV#clearStream');
  if (defined(this._videoPlayer)) {
    this._videoPlayer.unload();
    this._videoPlayer.detachMediaElement();
    this._videoPlayer.destroy();
    this._videoPlayer = undefined;
  }
  if (defined(this._videoUrl)) {
    this._videoUrl = undefined;
  }
  this._channelID = undefined;
  this._customData = undefined;
  // this.vehicleId = undefined;
  this.videoPlayerToolBarVisible = false;
  this.videoPlayerToolBar.bpsTextDomText = '0bps';

  // 请拖拽
  this.videoPlayerStatus = VideoPlayerStatusEnum.INIT_PLEASE_DRAG;
  this.showError();
};

/**
 * 停止回放的视频
 * @description 将不需要的视频停掉，这个函数是可以放心调用的，流媒体后台会对停播做处理
 * @param {Boolean} [force=false] 是否强制播放 一般的情况下，将这个状态交给后台去维护
 * @param {Function} [callback] 回调函数
 */
VideoPlayerFLV.prototype.stopPlayBackStream = function (force, callback) {
  force = defaultValue(force, false);
  callback = defaultValue(callback, () => {});
  if (force) {
    return new Promise((resolve, reject) => {
      if (defined(this._channelID)) {
        let tempArray = this._channelID.split('_');
        let licencePlate = tempArray[0];
        let channel = tempArray[1];
        let requiredData = {
          licencePlate: licencePlate,
          channelId: parseInt(channel),
          vehicleId: this.vehicleId
        };
        let controlPlaybackStreamPromise = historyVideoApi.stopHistoryVideo(requiredData);
        controlPlaybackStreamPromise.then(function (json) {
          callback();
          resolve(json);
        }).catch(function (msg) {
          callback();
          resolve(msg);
        });
      } else {
        resolve();
      }
    });
  } else {
    return new Promise((resolve, reject) => {
      resolve();
    });
  }
};

/**
 * 停止直播的视频
 * @deprecated
 * @description 不再去停止直播视频了，因为会造成其他账户访问相同视频时出问题，将停播视频的逻辑交给后台
 */
VideoPlayerFLV.prototype.stopLiveChannel = function () {
  this.pushTimeFlag = false;
  this.isCloseVideo = true;
  if (!defined((this._channelID))) {
    return;
  }
  // FIXME 不停！！
  // let tempArray = this._channelID.split('_');
  // let licencePlate = tempArray[0];
  // let channel = tempArray[1];
  // let stopChannelPromise = RealtimeVideoApi.stopChannel({
  //   licencePlate: licencePlate,
  //   channel: parseInt(channel),
  //   dataType: this._dataType,
  //   streamType: this._streamType,
  //   // 关闭音视频类型 0：关闭该通道有关的音视频数据，1：只关闭该通道有关的音频，保留该通道有关视频 ，2：只关闭该通道有关的视频，保留该通道有关音频
  //   closeType: this.monitorModel ? 1 : 0
  // });
  // stopChannelPromise.then(function (json) {
  // }).catch(function (msg) {
  // });
  this.videoPlayerStatus = VideoPlayerStatusEnum.INIT_PLEASE_DRAG;
};

/**
 * 停止直播推流的视频（3分钟自动停止）
 * @deprecated
 * @description 不再去停止直播视频了，因为会造成其他账户访问相同视频时出问题，将停播视频的逻辑交给后台
 */
VideoPlayerFLV.prototype.stopPushChannel = function () {
  if (!this._channelID) {
    return;
  }
  let tempArray = this._channelID.split('_');
  let licencePlate = tempArray[0];
  let channel = tempArray[1];
  let startChannelPromise = RealtimeVideoApi.startChannel({
    channel: parseInt(channel),
    // licencePlate: licencePlate,
    streamType: this._streamType,
    cmd: 2, // 1-切换码流(切换时固定位1)，2-停止推流
    deviceId: this.deviceId,
    deviceType: this.deviceType
  }, this._protocol, true);
  startChannelPromise.then(() => {
    // this.clearStream();
  }).catch(function (msg) {
  });
};

/**
 * 获取该播放器在父节点中的位置
 * @param {String} [videoElementId=this._videoElementId]
 * @example VideoPlayerFLV.getPositionOfParentGallery(VideoPlayerFLV._videoElementId);
 * @returns {String|undefined}
 */
VideoPlayerFLV.prototype.getPositionOfParentGallery = function (videoElementId) {
  videoElementId = defaultValue(videoElementId, this._videoElementId);
  if (defined(videoElementId)) {
    return videoElementId.slice(-3);
  } else {
    return undefined;
  }
};

/**
 * 获取该播放器在父节点中的编号，从1开始
 * @returns {Number|undefined}
 */
VideoPlayerFLV.prototype.getIndexOfParentGallery = function () {
  return this._index;
};

/**
 * 获取父框架的标识
 * @return {Number|undefined}
 */
VideoPlayerFLV.prototype.getParentGalleryId = function () {
  return this._parentGallery._uid;
};

/**
 * 通过弹幕的形式绘制错误信息
 * @description 使用弹幕的方式将后台的错误信息直接打印，方便调试
 * @param {String} msg
 * @deprecated
 */
VideoPlayerFLV.prototype.drawDanmuError = function (msg) {
  console.warn('弹幕信息为：', msg);
};

/**
 * 展示错误，一致存在
 * @param {String} [msg]
 */
VideoPlayerFLV.prototype.showError = function (msg) {
  if (this._pauseOverlayVisible === true) {
    // console.warn('如果暂停了就先不打印错误了');
    return;
  }
  // console.log(this.videoPlayerStatus);
  msg = defaultValue(msg, '');
  if (this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_LOADING) {
    msg = '实时视频加载中...';
  } else if (this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_RESUME) {
    // msg = '实时视频重连中...';
    msg = this._currentErrorMsg;
  } else if (this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_SWITCH) {
    msg = '实时视频切换中...';
  } else if (this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_FAIL) {
    // msg = '实时视频错误';
    // console.log(this._lastErrorMsg, this._currentErrorMsg);
    // console.warn('实时视频错误');
    msg = this._currentErrorMsg;
  } else if (this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_END) {
    msg = '无实时视频';
  } else if (this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_LOADING) {
    msg = '历史视频加载中...';
  } else if (this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_RESUME) {
    msg = '历史视频重连中...';
  } else if (this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_SWITCH) {
    msg = '历史视频切换中...';
  } else if (this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_FAIL) {
    // msg = '历史视频错误';
    msg = this._currentErrorMsg;
  } else if (this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_END) {
    msg = '历史视频播放结束';
  } else if (this.videoPlayerStatus === VideoPlayerStatusEnum.INIT_PLEASE_DRAG) {
    if (this._csMsg) {
      msg = this._csMsg;
    } else if (!this._forPlayback) {
      msg = '勾选加载';
    } else {
      msg = '请选择视频加载';
    }
  } else if (this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_AUDIO) {
    msg = '音频监听中...';
  } else if (this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_INTERCOM) {
    msg = '双向对讲中...';
  } else if (this.videoPlayerStatus === VideoPlayerStatusEnum.TWO_MINUTES_STOP) {
    msg = '视频已经连续播放2分钟，继续播放将会消耗更多流量，视频将暂停播放，如需观看请重新勾选';
  }
  // console.log('showError:', msg, '。当前的状态是：', this.videoPlayerStatus);

  if (this._lastErrorMsg === msg) {
    return;
  }
  this._lastErrorMsg = msg;

  if (msg) {
    this._showTipsWidget.show(msg);
  }
};

/**
 * 展示错误信息，出现后又消失
 * @param {String} msg
 */
VideoPlayerFLV.prototype.showErrorTips = function (msg) {
  if (this._pauseOverlayVisible === true) {
    return;
  }
  if (this._lastErrorMsg === msg) {
    return;
  }
  this._lastErrorMsg = msg;
  console.log(msg);

  var that = this;
  this._showTipsWidget.show(msg);
  setTimeout(function () {
    that._showTipsWidget.hide();
  }, 2000);
};

/**
 * 隐藏指定的错误信息
 */
VideoPlayerFLV.prototype._hideError = function (msg) {
  if (VideoPlayerStatusEnumHelper.isLiveStatus(this.videoPlayerStatus)) {
    this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE;
  } else if (VideoPlayerStatusEnumHelper.isPlaybackStatus(this.videoPlayerStatus)) {
    this.videoPlayerStatus = VideoPlayerStatusEnum.PLAY_BACK;
  }

  if (this._showTipsWidget._visible === true) {
    this._lastErrorMsg = '';
    this._showTipsWidget.hide();
  }
};

/**
 * 初始化回放视频时的需要的全局时钟
 */
VideoPlayerFLV.prototype.initPlaybackClock = function (clock) {
  this._clock = clock;
};

/**
 * 开始断线重连
 * @description 视频流有时会因为某些原因而临时断线，这时需要一个重新连接的尝试机制，这里初始化参数，并会用以指数增长的形式主动请求若干次通过reconnect函数，达到重连的目的
 * @see VideoPlayerFLV#reconnect
 */
VideoPlayerFLV.prototype.startReconnect = function () {
  var that = this;
  console.log('视频流断线重连函数开始了' + that._videoUrl);
  // 初始化若干参数
  this.reconnectInterval = 1000; // 这个地方保持1s比较好
  this.maxReconnectInterval = 30000;
  this.reconnectDecay = 1.5;
  this.timeoutInterval = 2000;
  this.maxReconnectAttempts = null;
  this.reconnectAttempts = 0;
  this._needReconnect = true;

  // 首先排除主动黑屏的情况
  if (defined(that._videoUrl)) {
    var timeout = that.reconnectInterval * Math.pow(that.reconnectDecay, that.reconnectAttempts);
    setTimeout(function () {
      that.reconnectAttempts++;
      if (that._isValidStream === false) {
        // console.log('断线重连：' + that._videoUrl + '第' + that.reconnectAttempts);

        that.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_RESUME;
        that._currentErrorMsg = `断线重连第${that.reconnectAttempts}次，下一次在${Math.floor(timeout / 1000)}秒后`;
        if (!this._flyPlayerHandelLoading) {
          that.reload();
        }
        that.reconnect();
      }
    }, timeout > that.maxReconnectInterval ? that.maxReconnectInterval : timeout);
  }
};

/**
 * 停止断线重连
 * @private
 */
VideoPlayerFLV.prototype._stopReconnect = function () {
  this._needReconnect = false;
};

/**
 * 断线重连
 * @description 视频流有时会因为某些原因而临时断线，需要一个重新连接的尝试机制
 * @see VideoPlayerFLV#startReconnect
 */
VideoPlayerFLV.prototype.reconnect = function () {
  var that = this;

  // 首先排除主动黑屏的情况
  if (this._videoUrl === '') {
    that._isValidStream = false;
  } else {
    var timeout = that.reconnectInterval * Math.pow(that.reconnectDecay, that.reconnectAttempts);
    setTimeout(() => {
      that.reconnectAttempts++;
      if (defined(that._videoUrl) && that._needReconnect !== false) {
        // console.log('断线重连：' + that._videoUrl + '第' + that.reconnectAttempts + '次');
        that.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_RESUME;
        that._currentErrorMsg = `断线重连第${that.reconnectAttempts}次，下一次在${Math.floor(timeout / 1000)}秒后`;
        that.reload();
        that.reconnect();
      }
    }, timeout > that.maxReconnectInterval ? that.maxReconnectInterval : timeout);
  }
};

/**
 * 获取回放的数据
 */
VideoPlayerFLV.prototype.getPlaybackTickPromise = function () {
  var that = this;
  return new window.Promise(function (resolve, reject) {
    var streamID = that._playbackStreamCurrentID;
    if (defined(streamID)) {
      var sendObj = {
        streamid: streamID
      };
      var getDownloadPlaybackInformationPromise = MediaServiceProtocol.getDownloadPlaybackInformation(sendObj);
      getDownloadPlaybackInformationPromise.then(function (res) {
        if (defined(res)) {
          // console.log(res.Duration, res.Progress, res.TimestampSec);
          resolve({
            progress: res.Progress,
            timestampSec: res.TimestampSec
          });
        }
      }).catch(function (msg) {
        // console.log(msg);
        reject(msg);
      });
    } else {
      reject('no streamID');
    }
  });
};

/**
 * 视频截屏
 * @description 得到的stream可以直接放到<img>的src中
 * @param {Number} [quality=100] 截屏质量1到100都为有效值，100最高
 */
VideoPlayerFLV.prototype.printScreen = function (quality) {
  var jpgQuality = defaultValue(quality, 100);
  var stream = this.getPrintScreenBase64Jpg(jpgQuality);
  var promise = FileProtocol.UploadJpgStream(stream);
  promise.then(function (data) {
    console.log(data);
  }).catch(function (msg) {
    console.log(msg);
  });
};

/**
 * 获取截屏的base64图像
 * @return {String}
 */
VideoPlayerFLV.prototype.getPrintScreenBase64Png = function () {
  var canvas = document.createElement('canvas');
  canvas.width = this._videoElement.videoWidth;
  canvas.height = this._videoElement.videoHeight;
  var canvasCtx = canvas.getContext('2d');
  canvasCtx.drawImage(this._videoElement, 0, 0, canvas.width, canvas.height, 0, 0, canvas.width, canvas.height);
  var base64str = canvas.toDataURL('image/png');
  // console.log(base64str);
  return base64str;
};

/**
 * 获取截屏的base64jpg图像
 * @description 得到的stream可以直接放到<img>的src中
 * @param {Number} [quality=100] 截屏质量1到100都为有效值，100最高
 * @performance 90ms到100ms 100质量分数之下 大致快两倍
 * @return {String}
 */
VideoPlayerFLV.prototype.getPrintScreenBase64Jpg = function (quality) {
  var canvas = document.createElement('canvas');
  canvas.width = this._videoElement.videoWidth;
  canvas.height = this._videoElement.videoHeight;
  var canvasCtx = canvas.getContext('2d');
  canvasCtx.drawImage(this._videoElement, 0, 0, canvas.width, canvas.height, 0, 0, canvas.width, canvas.height);
  var jpgQuality = defaultValue(quality, 100);
  var base64str = canvas.toDataURL('image/jpeg', jpgQuality / 100);
  // console.log(base64str);
  return base64str;
};

/**
 * 选中此播放器（针对父集合）
 * @see VideoPlayerGallery#getSelectedPlayer
 * @description 选中当前的播放器，并且将父集合中其他所有的播放器设置为非选中的状态
 */
VideoPlayerFLV.prototype.focusCurrentPlayerOfParentGallery = function () {
  if (this.select === true) {
    this.select = false;
  } else {
    if (defined(this._parentGallery)) {
      var selectedPlayer = this._parentGallery.getSelectedPlayer();
      if (defined(selectedPlayer)) {
        selectedPlayer.select = false;
      }
      this.select = true;
    }
  }
};

/**
 * 根据时钟来回放视频
 * @param {Clock} clock
 * @param {String} [channelID] 如果有这个参数怎替换掉播放的视频20位id
 * @see VideoPlayerFLV#playBack
 */
VideoPlayerFLV.prototype.playBackByClock = function (clock, channelID) {
  if (defined(channelID)) {
    this.channelID = channelID;
  }

  var startJulian = clock.startTime;
  var stopJulian = clock.stopTime;

  var startJsDate = JulianDate.toDate(startJulian);
  var startMoment = moment(startJsDate);
  var startTimeStr = startMoment.format('YYYY-MM-DDTHH:mm:ss');

  var stopJsDate = JulianDate.toDate(stopJulian);
  var stopMoment = moment(stopJsDate);
  var stopTimeStr = stopMoment.format('YYYY-MM-DDTHH:mm:ss');
  this.playBack(startTimeStr, stopTimeStr);
};

/**
 * 订阅channelId变化事件
 * @param callback
 */
VideoPlayerFLV.prototype.subscribeChannelIdChangeEvent = function (callback) {
  knockout.track(this, ['_channelID']);
  knockout.getObservable(this, '_channelID').subscribe(function (newValue) {
    callback(newValue);
  });
};

/**
 * 订阅videoUrl变化事件
 * @param callback
 */
VideoPlayerFLV.prototype.subscribeVideoUrlChangeEvent = function (callback) {
  knockout.track(this, ['_videoUrl']);
  knockout.getObservable(this, '_videoUrl').subscribe(function (newValue) {
    callback(newValue);
  });
};

/**
 * 重新设置canvas的大小并刷新画布
 * @description 设置width和height后会自动清空画布
 */
VideoPlayerFLV.prototype.resizeCanvas = function () {
  this._drawCanvas.width = this._drawCanvas.clientWidth;
  this._drawCanvas.height = this._drawCanvas.clientHeight;
};

/**
 * 设置静音
 * @param {Boolean} flag
 */
VideoPlayerFLV.prototype.setMuted = function (flag) {
  flag = defaultValue(flag, true);
  let video = this._videoElement;
  video.muted = flag;
};

/**
 * 控制flvjs静音
 * @param {Boolean} flag
 */
VideoPlayerFLV.prototype.setFLVMuted = function (flag) {
  this._videoPlayer.muted = flag;
};

/**
 * 切换到主码流
 */
VideoPlayerFLV.prototype.switchToMainStream = function () {
  if (this._streamType === StreamTypeEnum.SUB_STREAM) {
    console.log('切换到主码流');
    this._streamType = StreamTypeEnum.MAIN_STREAM;
    this.reloadChannelID({
      vehicleId: this.vehicleId,
      channelID: this.channelID,
      customData: this._customData,
      changeApi: true
    });
  }
};

/**
 * 切换到子码流
 */
VideoPlayerFLV.prototype.switchToSubStream = function () {
  if (this._streamType === StreamTypeEnum.MAIN_STREAM) {
    console.log('切换到子码流');
    this._streamType = StreamTypeEnum.SUB_STREAM;
    this.reloadChannelID({
      vehicleId: this.vehicleId,
      channelID: this.channelID,
      customData: this._customData,
      changeApi: true
    });
  }
};

/**
 * 切换到监听模式
 */
VideoPlayerFLV.prototype.switchToMonitor = function () {
  if (this._dataType === DataTypeEnum.VIDEO_AUDIO) {
    console.log('切换到监听模式');
    this._dataType = DataTypeEnum.MONITOR;
    this.reloadChannelID({
      vehicleId: this.vehicleId,
      channelID: this.channelID,
      customData: this._customData,
      hasVideo: false
    });
  }
};

/**
 * 切换到音视频模式
 */
VideoPlayerFLV.prototype.switchToVideoAudio = function () {
  if (this._dataType === DataTypeEnum.MONITOR) {
    console.log('切换到音视频模式');
    this._dataType = DataTypeEnum.VIDEO_AUDIO;
    this.reloadChannelID({
      vehicleId: this.vehicleId,
      channelID: this.channelID,
      customData: this._customData,
      hasVideo: true
    });
  }
};

/**
 * 订阅双击后全屏视频事件
 * @private
 */
VideoPlayerFLV.prototype.subscribeDblClickFullscreenEvent = function () {
  if (Fullscreen.supportsFullscreen()) {
    // 全屏切换
    var that = this;
    var tmpIsFullscreen = knockout.observable(Fullscreen.fullscreen);
    /**
     * Gets whether or not fullscreen mode is active.  This property is observable.
     *
     * @type {Boolean}
     */
    this.isFullscreen = undefined;
    knockout.defineProperty(this, 'isFullscreen', {
      get: function () {
        return tmpIsFullscreen();
      }
    });
    this._fullscreenElement = defaultValue(getElement(this._wrapper), document.body);
    this._callback = function () {
      tmpIsFullscreen(Fullscreen.fullscreen);

      if (Fullscreen.fullscreen) {
        Fullscreen.exitFullscreen();
      } else {
        Fullscreen.requestFullscreen(that._fullscreenElement);
      }
    };
    this._fullscreenElement.addEventListener('dblclick', this._callback);
  }
};
/**
 * 初始化播放器切换按钮.
 */
VideoPlayerFLV.prototype.initializePlayerToggleButton = function () {
  this._videoPlayerToolBar.initializeToggleButton();
  this._dataType = DataTypeEnum.VIDEO_AUDIO;
  this._streamType = StreamTypeEnum.SUB_STREAM;
};

/**
 * Returns true if this object was destroyed; otherwise, false.
 * <br /><br />
 * If this object was destroyed, it should not be used; calling any function other than
 *
 * @returns {Boolean} <code>true</code> if this object was destroyed; otherwise, <code>false</code>.
 *
 * @see VideoPlayerFLV#destroy
 */
VideoPlayerFLV.prototype.isDestroyed = function () {
  return false;
};

/**
 * Removes and destroys all created by this instance.
 */
VideoPlayerFLV.prototype.destroy = function () {
  knockout.cleanNode(this._wrapper);
  this._container.removeChild(this._wrapper);

  this.isDestroyed = function returnTrue () {
    return true;
  };
  return true;
};

export default VideoPlayerFLV;
