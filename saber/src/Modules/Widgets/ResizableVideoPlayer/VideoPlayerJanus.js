import defaultValue from '../../Core/defaultValue'
import defined from '../../Core/defined'
import defineProperties from '../../Core/defineProperties'
import destroyObject from '../../Core/destroyObject'
import DeveloperError from '../../Core/DeveloperError'
import JulianDate from '../../Core/JulianDate'
import knockout from '../../ThirdParty/knockout'
import getElement from '../../Widgets/getElement'
import Queue from '../../Core/Queue'
import StringUtil from '../../Util/StringUtil'

import MediaServiceProtocol from '../../Protocol/MediaServiceProtocol'
import ShowTipsWidget from '../../Widgets/ShowTipsWidget/ShowTipsWidget'
import VideoPlayerToolBar from '../../Widgets/VideoPlayerToolBar/VideoPlayerToolBar'
import VideoPlayerStatusEnum from './VideoPlayerStatusEnum'
import VideoPlayerStatusEnumHelper from './VideoPlayerStatusEnumHelper'
import CameraManufacturerTypeEnum from '../../Enumerate/Camera/CameraManufactureType'

import Janus from '../../ThirdParty/webRTC/janus-es6'
import VideoPlayerJanusViewModel  from './VideoPlayerJanusViewModel'

'use strict';

/**
 * VideoPlayerJanus
 * @alias VideoPlayerJanus（webRTC播放器janus）
 *
 * @description WebRTC 播放器
 *
 * @see https://github.com/meetecho/janus-gateway
 * @param {Object} options
 * @param {VideoPlayerGallery} options.parentGallery 构造此播放器类VideoPlayerGallery
 * @param {String} [options.url='rtmp'] 初始化的视频流地址
 * @param {String|Element} options.container 容器ID或容器元素
 * @param {String|Element} options.id 容器ID或容器元素
 * @param {JanusInit} [options.janusInit] janus初始化对象
 * @param {String} [options.protocol='RTSP'] RTSP/RTP/AUTO 协议的类型 AUTO意味着会尝试会去找合适协议，此时只有少数的VideoUrl相关的函数有效
 * @param {Boolean} [options.ignoreGb28181ForWebRTC=false] 是否在使用janus的webRTC时不去调用GB28181的接口（可节约时间，但需配置后台的摄像头数据）
 * @param {Boolean} [options.subStream=true] 是否使用子码流，默认启用，节约带宽
 * @param {Boolean} [options.defaultStream='第三码流'] 可选项为'第三码流' '辅码流' '主码流'，暂时只支持海康和大华的摄像头
 * @param {Boolean} [options.bigVideoPlayerUseMainStream=true] 如果是主屏就使用主码流
 *
 * @constructor
 */
function VideoPlayerJanus(options) {
  //>>includeStart('debug', pragmas.debug);
  if(!defined(options)){
    options = {};
  }
  if (!defined(options.container)) {
    throw new DeveloperError('options.container is required.');
  }
  //>>includeEnd('debug');

  if(!defined(options.janusInit)){
    this._janusInit = new JanusInit();
  }else{
    this._janusInit = options.janusInit;
  }

  this._protocol = defaultValue(options.protocol, 'RTSP');
  this._ignoreGb28181ForWebRTC = defaultValue(options.ignoreGb28181ForWebRTC, false);
  this._subStream = defaultValue(options.subStream, false);//FIXME yln
  this._defaultStream = defaultValue(options.defaultStream, '第三码流')
  this._bigVideoPlayerUseMainStream = defaultValue(options.bigVideoPlayerUseMainStream, false);//FIXME yln

  var that = this;
  var viewModel = new VideoPlayerJanusViewModel(options);

  var container = getElement(options.container);

  var wrapper = document.createElement('div');
  wrapper.className = 'lux-resizable-video-player';
  wrapper.setAttribute('data-bind', 'css: { "lux-resizable-video-player-fullScreen" : showFullScreen}');
  wrapper.setAttribute('draggable', 'true');
  container.appendChild(wrapper);

  // video
  this._videoElementId = defaultValue(options.id, StringUtil.generateLongUid());
  var videoElement = document.createElement('video');
  videoElement.className = 'lux-resizable-video-element';
  videoElement.setAttribute('id', this._videoElementId);
  videoElement.setAttribute('width', '1920');
  videoElement.setAttribute('height', '1080');
  videoElement.setAttribute('autoplay', 'autoplay');
  videoElement.setAttribute('playsinline', 'playsinline');
  videoElement.setAttribute('muted', 'muted');
  wrapper.appendChild(videoElement);

  // 一个叠加层，用于绘制嫌疑人框
  var videoElementCanvas = document.createElement('canvas');
  videoElementCanvas.className = 'lux-video-player-flash-overlay';
  videoElementCanvas.setAttribute('draggable', 'false');
  videoElementCanvas.setAttribute('data-bind', 'css: { "lux-video-player-flash-overlay-visible" : showFlashDragOverlay, "lux-video-player-flash-overlay-highlight" : highlightFlashDragOverlay}');
  wrapper.appendChild(videoElementCanvas);

  // 一个叠加层，用于高亮摄像头，不能操作
  var videoElementHighlightOverlay = document.createElement('div');
  videoElementHighlightOverlay.className = 'lux-video-player-active-overlay';
  videoElementHighlightOverlay.setAttribute('draggable', 'true');
  videoElementHighlightOverlay.setAttribute('data-bind', 'css: { "lux-video-player-active-overlay-visible" : showFlashDragOverlay, "lux-video-player-active-overlay-highlight" : highlightActiveOverlay}');
  wrapper.appendChild(videoElementHighlightOverlay);

  // 一个叠加层，用于操作div，比如拖拽，点击选中
  var videoElementOverlay = document.createElement('div');
  videoElementOverlay.className = 'lux-video-player-flash-overlay';
  videoElementOverlay.setAttribute('draggable', 'true');
  videoElementOverlay.setAttribute('data-bind', 'css: { "lux-video-player-flash-overlay-visible" : showFlashDragOverlay, "lux-video-player-flash-overlay-highlight" : highlightFlashDragOverlay}');
  wrapper.appendChild(videoElementOverlay);

  // 一个叠加层，用于操作显示错误信息
  var videoElementErrorOverlay = document.createElement('div');
  videoElementErrorOverlay.className = 'lux-video-player-error-overlay';
  wrapper.appendChild(videoElementErrorOverlay);
  this._showTipsWidget = new ShowTipsWidget({
    container: videoElementErrorOverlay
  });

  // 加载中
  var videoElementLoadingIndicator = document.createElement('div');
  videoElementLoadingIndicator.className = 'loadingIndicator';
  videoElementLoadingIndicator.style.display = 'none';
  wrapper.appendChild(videoElementLoadingIndicator);

  // 球机的控制控件
  var videoSpeedDomeControl = document.createElement('div');
  videoSpeedDomeControl.className = 'lux-video-player-videoSpeedDomeControl';
  wrapper.appendChild(videoSpeedDomeControl);

  // 视频播放器工具栏
  var videoPlayerToolBarContainer = document.createElement('div');
  videoPlayerToolBarContainer.className = 'lux-video-player-videoPlayerToolBar';
  wrapper.appendChild(videoPlayerToolBarContainer);

  knockout.applyBindings(viewModel, wrapper);

  this._parentGallery = options.parentGallery;// 整个父框架对象的指针传进来
  this._viewModel = viewModel;
  this._videoElement = videoElement;
  this._wrapper = wrapper;
  this._container = container;
  this._drawCanvas = videoElementCanvas;
  this._drawCanvasCtx = this._drawCanvas.getContext('2d');
  this._videoElementOverlay = videoElementOverlay;
  this._videoElementLoadingIndicator = videoElementLoadingIndicator;
  this._lastActiveTime = JulianDate.now();

  this._channelID = undefined;//options.channelID;
  this._playbackStreamCurrentID = undefined;// 正在使用的回放视频流ID
  this._playbackStreamIDQueue = new Queue();// 队列用于删除

  this._playerName = 'player' + this._videoElementId;

  this._isValidStream = false;// 当前播放的是否是有效视频流

  this.videoPlayerStatus = VideoPlayerStatusEnum.INIT_PLEASE_DRAG;
  this._noReceiveNewFrameCount = 0;// 断流已经多少次没接收到视频了

  /**
   * 是否在主屏幕
   * @type {boolean}
   */
  this.isBigVideoPlayer = false;
  knockout.track(this, ['isBigVideoPlayer']);

  /**
   * 视频播放器工具栏
   * @private
   * @type {VideoPlayerToolBar}
   */
  this._videoPlayerToolBar = new VideoPlayerToolBar(videoPlayerToolBarContainer, {
    videoPlayer: this
  });

  if(!Janus.isWebrtcSupported()) {
    that._showError({
      videoPlayerStatus: VideoPlayerStatusEnum.NO_WEBRTC_SUPPORT
    });
    return;
  }

  if(this._bigVideoPlayerUseMainStream === true){
    knockout.getObservable(this, 'isBigVideoPlayer').subscribe(newValue=> {
      if(newValue === true){
        this.switchToMainStream();
      }else{
        this.switchToSubStream();
      }
    });
  }

  this._init();
  this._initDragEvent();
}

defineProperties(VideoPlayerJanus.prototype, {
  /**
   * Gets the parent container.
   * @memberof VideoPlayerJanus.prototype
   *
   * @type {Element}
   */
  container : {
    get : function() {
      return this._container;
    }
  },

  /**
   * Gets the view model.
   * @memberof VideoPlayerJanus.prototype
   *
   * @type {VideoPlayerJanus}
   */
  viewModel : {
    get : function() {
      return this._viewModel;
    }
  },

  /**
   * Gets the wrapper.
   * @memberof VideoPlayerJanus.prototype
   *
   * @type {Element}
   */
  wrapper : {
    get : function() {
      return this._wrapper;
    }
  },

  /**
   * 缩略图地址
   * @memberof VideoPlayerJanus.prototype
   *
   * @type {String}
   */
  thumbnailUrl: {
    get: function () {
      return this._viewModel.thumbnailUrl;
    },
    set : function(value) {
      this._viewModel.thumbnailUrl = value;
    }
  },

  /**
   * 视频地址
   * @memberof VideoPlayerJanus.prototype
   *
   * @type {String}
   */
  videoUrl: {
    get: function () {
      return this._videoUrl;
    },
    set : function(value) {
      this._videoUrl = value;
    }
  },

  /**
   * 通道号
   * @memberof VideoPlayerJanus.prototype
   *
   * @type {String}
   */
  channelID: {
    get: function () {
      return this._channelID;
    },
    set : function(value) {
      this._channelID = value;
    }
  },

  /**
   * 这个播放器的id
   * @memberof VideoPlayerJanus.prototype
   *
   * @type {String}
   */
  videoElementId: {
    get: function () {
      return this._videoElementId;
    },
    set : function(value) {
      this._videoElementId = value;
    }
  },

  /**
   * 高亮播放器
   * @description 红色
   * @memberof VideoPlayerJanus.prototype
   *
   * @type {Boolean}
   */
  highlight: {
    get: function () {
      return this._viewModel.highlightActiveOverlay;
    },
    set : function(value) {
      this._viewModel.highlightActiveOverlay = value;
    }
  },

  /**
   * 选中摄像头播放器
   * @description 蓝色
   * @memberof VideoPlayerJanus.prototype
   *
   * @type {Boolean}
   */
  select: {
    get: function () {
      return this._viewModel.highlightFlashDragOverlay;
    },
    set : function(value) {
      this._viewModel.highlightFlashDragOverlay = value;
    }
  },

  /**
   * 唯一一个可以触发鼠标事件的操作层
   * @description 蓝色
   * @memberof VideoPlayerJanus.prototype
   *
   * @type {Element}
   */
  videoElementOverlay: {
    get: function () {
      return this._videoElementOverlay;
    }
  },

  /**
   * 视频播放器工具栏
   * @memberof VideoPlayerJanus.prototype
   *
   * @type {VideoPlayerToolBar}
   */
  videoPlayerToolBar: {
    get: function () {
      return this._videoPlayerToolBar;
    }
  },

  /**
   * 视频播放器工具栏是否可见
   * @memberof VideoPlayerJanus.prototype
   *
   * @type {Boolean}
   */
  videoPlayerToolBarVisible: {
    get: function () {
      return this._videoPlayerToolBar.show;
    },
    set:function (value) {
      this._videoPlayerToolBar.show = value;
    }
  },

  /**
   * 当前的时间
   * @memberof VideoPlayerJanus.prototype
   *
   * @type {JulianDate|undefined}
   */
  currentClockTime: {
    get: function () {
      var time;
      if(defined(this._clock)){
        time = this._clock.currentTime;
      }
      return time;
    }
  },

  /**
   * 是否有有效的channelID
   * @memberof VideoPlayerJanus.prototype
   *
   * @type {Boolean}
   */
  hasValidChannelId: {
    get: function () {
      return this._channelID !== undefined;
    }
  },

  /**
   * 详情信息
   * @memberof VideoPlayerJanus.prototype
   *
   * @type {Object}
   */
  cameraObject: {
    get: function () {
      if(this.hasValidChannelId){
        if(this._parentGallery && this._parentGallery.channelIdIndexCollection && this._parentGallery.channelIdIndexCollection.get(this._channelID)){
          return this._parentGallery.channelIdIndexCollection.get(this._channelID);
        }
      }
      return undefined;
    }
  },

  /**
   * 画布
   * @memberof VideoPlayerJanus.prototype
   *
   * @type {Element}
   */
  drawCanvas: {
    get: function () {
      return this._drawCanvas;
    }
  },

  /**
   * 画布ctx
   * @memberof VideoPlayerJanus.prototype
   */
  drawCanvasCtx: {
    get: function () {
      return this._drawCanvasCtx;
    }
  },

  /**
   * 停止视频流
   * @memberof VideoPlayerJanus.prototype
   * @type {Function}
   */
  stopStream: {
    get: function () {
      return this._stopStream;
    }
  },

  /**
   * 播放器是否加载完成
   * @memberof VideoPlayerJanus.prototype
   * @type {Boolean}
   */
  loaded: {
    get: function () {
      return this._viewModel.loaded;
    }
  }

});

/**
 * 初始化
 * @private
 */
VideoPlayerJanus.prototype._init = function () {
  this._janus = null;
  this._streaming = null;
  var opaqueId = 'streamingtest-' + Janus.randomString(12);

  this._bitrateTimer = undefined;
  this._selectedStream = undefined;

  var that = this;

  knockout.getObservable(this._janusInit, 'success').subscribe(function(newValue) {
    if(newValue === true){
      that._janus = that._janusInit.janus;
      that._janus.attach(
        {
          plugin: 'janus.plugin.streaming',
          opaqueId: opaqueId,
          success: function(pluginHandle) {
            // console.log('success');
            that._streaming = pluginHandle;
            Janus.log('Plugin attached! (' + that._streaming.getPlugin() + ', id=' + that._streaming.getId() + ')');

            // Setup streaming session
            that._viewModel.loaded = true;
            // that._showError();

            that._subscribeCurrentTimeEvent();

            // console.log('重连plugin.streaming成功...');
            if(that.hasValidChannelId){
              // console.log('重连视频，不能在第一次的时候执行');
              that.reload();
            }else{
              that._showError({
                videoPlayerStatus: VideoPlayerStatusEnum.INIT_PLEASE_DRAG
              });
            }

          },
          error: function(error) {
            Janus.error('  -- Error attaching plugin... ', error);
          },
          onmessage: function(msg, jsep) {
            Janus.debug(' ::: Got a message :::');
            Janus.debug(msg);
            var result = msg['result'];
            if(result !== null && result !== undefined) {
              if(result['status'] !== undefined && result['status'] !== null) {
                var status = result['status'];
                if(status === 'starting') {
                  // console.log('Starting, please wait...');
                } else if(status === 'started') {
                  // console.log('Started');
                } else if(status === 'stopped') {
                  that._stopStream();
                }
              } else if(msg['streaming'] === 'event') {
                console.log('msg[\'streaming\'] === \'event\'');
              }
            } else if(msg['error'] !== undefined && msg['error'] !== null) {
              console.error(msg['error']);
              that._stopStream();
              return;
            }
            if(jsep !== undefined && jsep !== null) {
              Janus.debug('Handling SDP as well...');
              Janus.debug(jsep);
              // Offer from the plugin, let's answer
              that._streaming.createAnswer({
                jsep: jsep,
                // We want recvonly audio/video and, if negotiated, datachannels
                media: {
                  audioSend: false,
                  videoSend: false,
                  data: true
                },
                success: function(jsep) {
                  Janus.debug('Got SDP!');
                  Janus.debug(jsep);
                  var body = {
                    'request': 'start'
                  };

                  that._streaming.send({
                    'message': body,
                    'jsep': jsep
                  });
                },
                error: function(error) {
                  Janus.error('WebRTC error:', error);
                }
              });
            }
          },
          onremotestream: function(stream) {
            // console.log(' ::: Got a remote stream :::');
            // console.log(stream);
            // Janus.debug(' ::: Got a remote stream :::');
            // Janus.debug(stream);
            // Janus.attachMediaStream(that._videoElement, stream);
            var videoTracks = stream.getVideoTracks();
            if(videoTracks === null || videoTracks === undefined || videoTracks.length === 0) {
              // console.log('No remote video');
              // FIXME
              if(!defined(that._testNoRemoteVideoCount)){
                that._testNoRemoteVideoCount = 0;
              }
              that._testNoRemoteVideoCount ++;
              // console.log(that._videoElementId + '-->第' + that._testNoRemoteVideoCount + '次断流!');
              // that._startReconnect();
            } else {
              // console.log('has remote video', stream, videoTracks);
              // console.log(stream);
              // console.log(videoTracks);
              that._addVideoElementCandidate(stream);
              // Janus.attachMediaStream(that._videoElement, stream);
            }
            if(videoTracks && videoTracks.length &&
              (Janus.webRTCAdapter.browserDetails.browser === 'chrome' ||
                Janus.webRTCAdapter.browserDetails.browser === 'firefox' ||
                Janus.webRTCAdapter.browserDetails.browser === 'safari')) {
              that._clearBitRateTimer();
              // FIXME yln0117
              that._bitrateTimer = setInterval(function() {
                // Display updated bitrate, if supported
                if(that.checkStreamValid() === true){
                  // that._stopReconnect();
                  // console.log('_hideError');
                  // that._hideError();
                }else{
                  // FIXME yln 20200818
                  that._startReconnect();
                }
              }, 1000);
            }
          },
          ondataopen: function(data) {
            Janus.log('The DataChannel is available-->');
          },
          ondata: function(data) {
            Janus.debug('We got data from the DataChannel! ' + data);
          },
          oncleanup: function() {
            Janus.log(' ::: Got a cleanup notification :::');
            that._clearBitRateTimer();
          }
        });
    }
  });

  knockout.getObservable(this._janusInit, 'error').subscribe(function(newValue) {
    that._showError(newValue);
  });

};

/**
 * 初始化拖拽事件
 * @private
 */
VideoPlayerJanus.prototype._initDragEvent = function () {
  var videoElementOverlay = this._videoElementOverlay;
  var that = this;
  videoElementOverlay.addEventListener('dragstart', function(event) {
    // console.log('dragstart');
    // console.log(event);
    var data = {
      videoUrl: that.videoUrl,
      thumbnailUrl: that.thumbnailUrl,
      origin: 'VIDEO',
      videoElementId: that._videoElementId,
      channelID: that._channelID
    };
    event.dataTransfer.setData('Text', JSON.stringify(data));
  }, false);
  videoElementOverlay.addEventListener('drop', function(event) {
    event.preventDefault();
    console.log('drop');
    console.log(event);

    var data;
    try{
      data = JSON.parse(event.dataTransfer.getData('Text'));
    }catch (e){
      console.log(e);
    }
    console.log(data);
    if(defined(data)){
      if(data.origin === 'BIM'){
        // 获取视频流地址并替换
        if(data.channelID !== undefined ){
          that.reloadChannelID({
            channelID: data.channelID
          });
        }
      }else if (data.origin === 'VIDEO'){
        // 交换同一个gallery的两个视频
        if(defined(that._parentGallery)){
          console.log('尚未开发');
          // if(that.getParentGalleryId(data.videoElementId) === that.getParentGalleryId(that._videoElementId)) {
          //     var position1 = that.getPositionOfParentGallery(data.videoElementId);
          //     var position2 = that.getPositionOfParentGallery(that._videoElementId);
          //     // 只有同一个视频集合图库才能交换播放器
          //     if(defined(position1) && defined(position2)){
          //         that._parentGallery.switchTwoPlayers(position1, position2);
          //     }
          // }
        }else if(defined(data.channelID)){
          that.reloadChannelID(data);
        } else{// 重载视频
          that.reload(data);
        }
      }else if(data.origin === 'JSTREE'){
        // 获取jstree中的数据中的视频流地址并替换
        var obj = data.data;
        if(defined(obj) && StringUtil.leftContainRight(obj.type, 'camera')){
          if(defined(obj.id)){
            // console.log(obj);
            if(StringUtil.leftContainRight(obj.type, 'cameraSpeedDome')){
              that.reloadChannelID({
                channelID: StringUtil.getLastSliceString(obj.id, 20), // id一定是20位的，因此这里可以直接截取
                callback: function () {
                  that.videoPlayerToolBarVisible = true;
                }
              });
            }else{
              that.reloadChannelID({
                channelID: StringUtil.getLastSliceString(obj.id, 20), // id一定是20位的，因此这里可以直接截取
                callback: function () {
                  that.videoPlayerToolBarVisible = true;
                }
              });
            }
          }
        }
      }else if(data.origin === 'CAMERALIST'){
        that.reloadChannelID({
          channelID: data.channelID,
          callback: function () {
            that.videoPlayerToolBarVisible = false;
          }
        });
      }
    }
  }, false);
  videoElementOverlay.addEventListener('dragenter', function(event) {
    event.preventDefault();
  }, false);
  videoElementOverlay.addEventListener('dragover', function(event) {
    event.preventDefault();
  }, false);
  videoElementOverlay.addEventListener('dragleave', function(event){
    event.preventDefault();
  });
  videoElementOverlay.addEventListener('dragend', function(event){
    event.preventDefault();
  });
};

/**
 * 停止视频流
 * @see VideoPlayerJanus#_clearBitRateTimer
 * @private
 */
VideoPlayerJanus.prototype._stopStream = function () {
  var body = {
    'request': 'stop'
  };
  this._streaming.send({
    'message': body
  });
  this._streaming.hangup();
  this._clearBitRateTimer();
};

/**
 * 开始连接视频流
 * @param {String} [id]
 * @see VideoPlayerJanus#_selectedStream
 * @private
 */
VideoPlayerJanus.prototype._startStream = function(id) {
  Janus.log('Selected video id #' + this._selectedStream);

  if(defined(id)){
    this._selectedStream = id;
  }

  if(!defined(this._selectedStream)) {
    return;
  }

  var body = {
    'request': 'watch',
    id: this._selectedStream
  };
  console.log('VideoPlayerJanus#_startStream-->', body);
  this._streaming.send({
    'message': body
  });
};

/**
 * 清除码率定时器
 * @see VideoPlayerJanus#_bitrateTimer
 * @private
 */
VideoPlayerJanus.prototype._clearBitRateTimer = function () {
  if(defined(this._bitrateTimer)){
    clearInterval(this._bitrateTimer);
  }
  this._bitrateTimer = undefined;
};

/**
 * 展示错误
 * @param {Object} options
 * @param {Number} [options.videoPlayerStatus] VideoPlayerStatusEnum 希望切换到的状态
 * @param {String} [options.msg] 错误字符串
 */
VideoPlayerJanus.prototype._showError = function(options){
  var msg;
  if(defined(options)){
    if(defined(options.videoPlayerStatus)){
      this.videoPlayerStatus = options.videoPlayerStatus;
    }
    if(defined(options.msg)){
      this._currentErrorMsg = options.msg;
    }
  }

  if(this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_LOADING){
    msg = '实时视频加载中...';
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_RESUME){
    // msg = '实时视频重连中...';
    msg = this._currentErrorMsg;
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_SWITCH){
    msg = '实时视频切换中...';
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_FAIL){
    // msg = '实时视频错误';
    // console.log(this._lastErrorMsg, this._currentErrorMsg);
    // console.warn('实时视频错误');
    msg = this._currentErrorMsg;
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.LIVE_END){
    msg = '无实时视频';
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_LOADING){
    msg = '历史视频加载中...';
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_RESUME){
    msg = '历史视频重连中...';
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_SWITCH){
    msg = '历史视频切换中...';
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_FAIL){
    // msg = '历史视频错误';
    msg = this._currentErrorMsg;
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.PLAY_BACK_END){
    msg = '历史视频播放结束';
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.INIT_PLEASE_DRAG){
    msg = '拖拽加载';
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.NO_WEBRTC_SUPPORT){
    msg = '抱歉，您的浏览器不支持WebRTC';
  }else if(this.videoPlayerStatus === VideoPlayerStatusEnum.NO_CONNECT_WEBRTC_SERVER){
    msg = '连接到webRTC服务器出错，请联系开发人员！';
  }
  // console.log('showError:', msg, '。当前的状态是：', this.videoPlayerStatus);

  if(this._lastErrorMsg === msg){
    return;
  }
  this._lastErrorMsg = msg;

  this._showTipsWidget.show(msg);
};

/**
 * 隐藏指定的错误信息
 */
VideoPlayerJanus.prototype._hideError = function(){
  if(VideoPlayerStatusEnumHelper.isLiveStatus(this.videoPlayerStatus)){
    this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE;
  }else if(VideoPlayerStatusEnumHelper.isPlaybackStatus(this.videoPlayerStatus)){
    this.videoPlayerStatus = VideoPlayerStatusEnum.PLAY_BACK;
  }

  if(this._showTipsWidget._visible === true){
    this._lastErrorMsg = '';
    this._showTipsWidget.hide();
  }
};

/**
 * 注册RTSP视频
 * @param {String} videoUrl RTSP的地址 如rtsp://admin:admin123@*************又如rtsp://*************:20396/34020000001180000175_34020001111320000009
 * @description 成功的时候返回 streamId
 * @private
 * @returns {Promise}
 */
VideoPlayerJanus.prototype._registerNewRTSP = function(videoUrl) {
  var that = this;
  return new window.Promise(function(resolve, reject){
    if(that._checkVideoUrlAuthorizationRTSP(videoUrl) === true){
      var rtspUser = videoUrl.match(/rtsp:\/\/(\S*):\w{1,20}@\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}/)[1];
      var rtspPwd = videoUrl.match(/rtsp:\/\/\w{1,20}:(\S*)@\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}/)[1];
      that._streaming.send({
        'message': {
          'request': 'create',
          'type': 'rtsp',
          'name': videoUrl,
          'description': videoUrl,
          'is_private': false,
          'audio': false,
          'video': true,
          'url' : videoUrl,
          'rtsp_user' : rtspUser,
          'rtsp_pwd' : rtspPwd,
          'videofmtp' : 'profile-level-id=42e01f;packetization-mode=1',// FIXME yln
          // 'videobufferkf': true,// FIXME yln
          // 'threads': 3// FIXME yln
        },
        success: function(result) {
          if(defined(result) && defined(result.stream) && defined(result.stream.id)){
            console.log('VideoPlayerJanus#_registerNewRTSP#require-->', {
              'message': {
                'request': 'create',
                'type': 'rtsp',
                'name': videoUrl,
                'description': videoUrl,
                'is_private': false,
                'audio': false,
                'video': true,
                'url' : videoUrl,
                'rtsp_user' : rtspUser,
                'rtsp_pwd' : rtspPwd,
                'videofmtp' : 'profile-level-id=42e01f;packetization-mode=1',// FIXME yln
                // 'videobufferkf': true,// FIXME yln
                // 'threads': 3// FIXME yln
              }}, 'VideoPlayerJanus#_registerNewRTSP#result-->', result);
            resolve(result.stream.id);
          }else{
            reject(result);
          }
        },
        error: function (msg) {
          console.log(msg);
          reject(msg);
        }
      });
    }else if(that._checkVideoUrlNormalRTSP(videoUrl) === true){
      that._streaming.send({
        'message': {
          'request': 'create',
          'type': 'rtsp',
          'name': videoUrl,
          'description': videoUrl,
          'is_private': false,
          'audio': false,
          'video': true,
          'url' : videoUrl
        },
        success: function(result) {
          if(defined(result) && defined(result.stream) && defined(result.stream.id)){
            resolve(result.stream.id);
          }else{
            reject(result);
          }
        },
        error: function (msg) {
          console.log(msg);
          reject(msg);
        }
      });
    }else {
      reject('RTSP 地址无效' + videoUrl);
    }

  });

};

/**
 * 注册RTP视频
 * @param {String} videoUrl RTP的地址 如rtp://*************:40001
 * @description 成功的时候返回 streamId
 * @private
 * @returns {Promise}
 */
VideoPlayerJanus.prototype._registerNewRTP = function(videoUrl) {
  var that = this;

  return new window.Promise(function(resolve, reject){
    if(that._checkVideoUrlRTP(videoUrl) === true){
      var videoPort = videoUrl.match(/rtp:\/\/\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}:(\S*)/)[1];
      var body = {
        'request': 'create',
        'type': 'rtp',
        'name': videoUrl,
        'description': videoUrl,
        'is_private': false,
        'audio': false,
        'video': true,
        'url' : videoUrl,
        'videoport' : parseInt(videoPort),
        'videopt' : 96,
        'videortpmap': 'H264/90000',
        'videofmtp': 'packetization-mode=1',
        'secret': 'adminpwd'
      };
      that._streaming.send({
        'message': body,
        success: function(result) {
          if(defined(result) && defined(result.stream) && defined(result.stream.id)){
            resolve(result.stream.id);
          }else{
            reject(result);
          }
        },
        error: function (msg) {
          console.log(msg);
          reject(msg);
        }
      });
    }else {
      reject('RTP 地址无效' + videoUrl);
    }

  });

};

/**
 * 检查带有RTP地址是否有效
 * @param {String} videoUrl rtp://*************:30000
 * @private
 */
VideoPlayerJanus.prototype._checkVideoUrlRTP = function (videoUrl) {
  var rtp = /rtp:\/\/\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}:\d{4,5}/;
  return rtp.test(videoUrl);
};

/**
 * 检查带有用户名和密码的RTSP地址是否有效
 * @param {String} videoUrl 视频地址
 * @return {Boolean}
 * @private
 */
VideoPlayerJanus.prototype._checkVideoUrlAuthorizationRTSP = function (videoUrl) {
  var authorizationRTSP = /rtsp:\/\/\w{1,20}:\w{1,20}@\d{1,3}.\d{1,3}.\d{1,3}.\d{1,3}/;
  return authorizationRTSP.test(videoUrl);
};

/**
 * 检查不带有用户名和密码的普通RTSP地址是否有效
 * @param {String} videoUrl 视频地址
 * @return {Boolean}
 * @private
 */
VideoPlayerJanus.prototype._checkVideoUrlNormalRTSP = function (videoUrl) {
  return StringUtil.leftContainRight(videoUrl, 'rtsp://');
};

/**
 * 获取一个webRTC的streamId
 * @param {String} videoUrl RTSP流或RTP流的地址
 * @private
 */
VideoPlayerJanus.prototype._getStreamId = function (videoUrl) {
  var that = this;
  return new window.Promise(function(resolve, reject){
    var getStreamsListPromise = that._getStreamsList();
    getStreamsListPromise.then(function (list) {
      for(var i = 0; i < list.length; i++){
        if(videoUrl === list[i].description){
          resolve(list[i].id);
          return;
        }
      }

      var protocol;
      if(that._protocol === 'AUTO'){
        if(StringUtil.leftContainRight(videoUrl, 'rtsp')) {
          protocol = 'RTSP';
        }else if(StringUtil.leftContainRight(videoUrl, 'rtp')){
          protocol = 'RTP';
        }
      }else{
        protocol = that._protocol;
      }

      if(defined(protocol)){
        if(protocol === 'RTSP'){
          var registerNewRTSPPromise = that._registerNewRTSP(videoUrl);
          registerNewRTSPPromise.then(function (streamId) {
            resolve(streamId);
          }).catch(function (msg) {
            reject(msg);
          });
        }else if (protocol === 'RTP'){
          var registerNewRTPPromise = that._registerNewRTP(videoUrl);
          registerNewRTPPromise.then(function (streamId) {
            resolve(streamId);
          }).catch(function (msg) {
            reject(msg);
          });
        }else {
          reject('未知的协议，请通知开发者处理！');
        }
      }else{
        reject('未知的协议，请通知开发者处理！');
      }
    }).catch(function (msg) {
      reject(msg);
    });
  });

};

/**
 * 获取当前的流的列表
 * @private
 */
VideoPlayerJanus.prototype._getStreamsList = function() {
  var that = this;
  return new window.Promise(function(resolve, reject){
    var body = {
      'request': 'list'
    };

    that._streaming.send({
      'message': body,
      success: function(result) {
        // console.log('_getStreamsList', result);
        if(result === null || result === undefined) {
          // console.error('Got no response to our query for available streams');
          resolve([]);
        }
        if(result['list'] !== undefined && result['list'] !== null) {
          var list = result['list'];
          resolve(list);
        }
      },
      error: function (msg) {
        reject(msg);
      }
    });

  });

};

/**
 * 是否有效视频流
 * @return {Boolean}
 */
VideoPlayerJanus.prototype.checkStreamValid = function () {
  if(defined(this._streaming)){
    // var bitRate = this._streaming.getBitrate();
    // console.log(bitRate);
    // var numericalBitRate = bitRate.split(' ')[0];
    // // console.log(numericalBitRate);
    // if(numericalBitRate > 0){
    //     return true;
    // }
    return this._videoStreamValid;
  }
  return false;
};

/**
 * 清空视频流
 * @see VideoPlayerJanus#_stopStream
 */
VideoPlayerJanus.prototype.clearStream = function () {
  this._stopStream();
};

/**
 * 获取某个channelID的摄像头流媒体地址并播放，然后停掉回放的流
 * @description 会调用reload()
 * @param {Object} options
 * @param {String} [options.channelID] 摄像头通道号
 * @param {Function} [options.callback] 回调函数
 * @see VideoPlayerJanus#reloadChannelIdAndUrl
 */
VideoPlayerJanus.prototype.reloadChannelID = function (options){
  console.log('VideoPlayerJanus#reloadChannelID-->', options);
  options = defaultValue(options, {});

  if(defined(options.channelID)){
    if(this.checkStreamValid() === true){
      this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_SWITCH;
      this._stopStream();
    }else{
      this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_LOADING;
    }
    this._channelID = options.channelID;
  }else{
    if(this.checkStreamValid() === true){
      this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_SWITCH;
      this._stopStream();
    }else{
      this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_LOADING;
    }
  }

  if(!defined(this._channelID)){
    return;
  }

  var that = this;
  if(this._ignoreGb28181ForWebRTC === true){// 假点播
    if(defined(options) && defined(options.callback)){
      that.reload({
        videoUrl: 'janusFake',
        callback: options.callback
      });
    } else {
      that.reload({
        videoUrl: 'janusFake'
      });
    }
  }else{
    // console.time('VideoPlayerJanus#time-->' + options.channelID);// 国标的第一次点播大概1500ms到2200ms，后续40ms
    var startChannelPromise = MediaServiceProtocol.startChannel({
      channelID: this._channelID
    }, this._protocol);
    startChannelPromise.then(function (json) {
      // console.timeEnd('VideoPlayerJanus#time-->' + options.channelID);
      if(defined(options) && defined(options.callback)){
        that.reload({
          videoUrl: json,
          callback: options.callback
        });
      } else {
        that.reload({
          videoUrl: json
        });
      }
    }).catch(function (msg) {
      that.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_FAIL;
      that._currentErrorMsg = '点播' + that._channelID + '错误：' + msg;
    });
  }

};

/**
 * 同时替换channelID和Url
 *
 * @description 此函数是为了在请求到多路视频是可以直接塞入数据，只允许VideoPlayerJanusGallery调用
 * @param options
 * @param {String} options.channelID
 * @param {String} options.videoUrl
 * @see VideoPlayerJanus#reloadChannelID
 */
VideoPlayerJanus.prototype.reloadChannelIdAndUrl = function (options){
  options = defaultValue(options, {});

  if(this.checkStreamValid() === true){
    this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_SWITCH;
    this._stopStream();
  }else{
    this.videoPlayerStatus = VideoPlayerStatusEnum.LIVE_LOADING;
  }

  this._channelID = options.channelID;

  this.reload({
    videoUrl: options.videoUrl
  });
};

/**
 * 重载视频
 *
 * @description 最基础的切换视频源的方法，其余的根据其他参数生成的方法都会调用此方法
 * @param {Object} [options] 当只是需要刷新视频时无需设置此属性时
 * @param {String} options.videoUrl 视频直播的地址。
 * @param {String} [options.isSpeedDomeCamera=false] 是不是快球摄像头
 * @param {Function} [options.callback] 回调函数
 * @param {Function} [options.needClearLastStream=true] 是否要停掉上一个视频流
 */
VideoPlayerJanus.prototype.reload = function (options) {
  var that = this;
  // 若是没加载完成，等待加载完成后再接收视频流
  if(this.loaded === false){
    setTimeout(function () {
      that.reload(options);
    }, 300);
    return;
  }

  options = defaultValue(options, {});

  if(!defined(options.needClearLastStream) || options.needClearLastStream === true){
    this._stopStream();
  }

  if(defined(options.videoUrl)){
    if(this._ignoreGb28181ForWebRTC){// 如果没有连接就用直流
      this._videoUrl = this._getIpCameraStream(options.videoUrl); // FIXME janus流
    }else {
      this._videoUrl = options.videoUrl; // FIXME 国标流
    }
    this._lastActiveTime = JulianDate.now();
  }

  console.log('VideoPlayerJanus#reload-->', this._videoUrl);
  var getStreamIdPromise = this._getStreamId(this._videoUrl);
  getStreamIdPromise.then(function (streamId) {
    if(defined(streamId)){
      // console.log('VideoPlayerJanus#reload-->', streamId);
      that._startStream(streamId);
    }
  }).catch(function (msg) {
    console.log(msg);
  });

  this.videoPlayerToolBarVisible = false;

  if(defined(options.callback)){
    options.callback();
  }

};

/**
 * 暂停视频
 */
VideoPlayerJanus.prototype.pause = function () {
  if(defined(this._videoElement) /*&& this.checkStreamValid() === true*/){
    this._videoElement.pause();
  }
};

/**
 * 播放视频
 */
VideoPlayerJanus.prototype.play = function () {
  if(defined(this._videoElement) && this.checkStreamValid() === true && this._videoUrl){
    this._videoElement.play().then(success=>{
      console.log('success->', success)
    }).catch(error=>{
      console.log('error->', error)
    });
  }
};

/**
 * 订阅channelId变化事件
 * @param {Function} callback
 */
VideoPlayerJanus.prototype.subscribeChannelIdChangeEvent = function (callback){
  if(this._hasSubscribeChannelIdChangeEvent !== true){
    knockout.track(this, ['_channelID']);
    this._hasSubscribeChannelIdChangeEvent = true;
  }
  knockout.getObservable(this, '_channelID').subscribe(function(newValue) {
    callback(newValue);
  });
};

/**
 * 重新设置canvas的大小并刷新画布
 * @description 设置width和height后会自动清空画布
 */
VideoPlayerJanus.prototype.resizeCanvas = function () {
  this._drawCanvas.width = this._drawCanvas.clientWidth;
  this._drawCanvas.height = this._drawCanvas.clientHeight;
};

/**
 * 获取截屏的base64png图像
 * @performance 230ms到260ms
 * @return {String}
 */
VideoPlayerJanus.prototype.getPrintScreenBase64Png = function(){
  var canvas = document.createElement('canvas');
  canvas.width = this._videoElement.width;
  canvas.height = this._videoElement.height;
  var canvasCtx = canvas.getContext('2d');
  canvasCtx.drawImage(this._videoElement, 0, 0, this._videoElement.width, this._videoElement.height, 0, 0, canvas.width, canvas.height);
  var base64str = canvas.toDataURL('image/png');
  // console.log(base64str);
  return base64str;
};

/**
 * 获取截屏的base64jpg图像
 * @description 得到的stream可以直接放到<img>的src中
 * @param {Number} [quality=100] 截屏质量1到100都为有效值，100最高
 * @performance 90ms到100ms 100质量分数之下 大致快两倍
 * @return {String}
 */
VideoPlayerJanus.prototype.getPrintScreenBase64Jpg = function(quality){
  var canvas = document.createElement('canvas');
  canvas.width = this._videoElement.width;
  canvas.height = this._videoElement.height;
  var canvasCtx = canvas.getContext('2d');
  var video = this._videoElement;
  canvasCtx.drawImage(video, 0, 0, this._videoElement.width, this._videoElement.height, 0, 0, canvas.width, canvas.height);
  var jpgQuality = defaultValue(quality, 100);
  var base64str = canvas.toDataURL('image/jpeg', jpgQuality / 100);
  // console.log(base64str);
  return base64str;

};

/**
 * 获取截屏的base64png图像的一个部分
 * @param {Object} rect
 * @param {Number} rect.imageLeft 相对于视频的左边界
 * @param {Number} rect.imageRight 相对于视频的左边界
 * @param {Number} rect.imageTop 相对于视频的上边界
 * @param {Number} rect.imageBottom 相对于视频的上边界
 * @param {Object} [options]
 * @param {Number} [options.width=1920] 视频分辨率宽度
 * @param {Number} [options.height=1080] 视频分辨率高度
 * @performance 230ms到260ms
 * @return {String}
 */
VideoPlayerJanus.prototype.getPrintScreenBase64PngByRect = function(rect, options){
  var canvas = document.createElement('canvas');
  options = defaultValue(options, {})
  let videoWidth = defaultValue(options.width, 1920)
  let videoHeight = defaultValue(options.height, 1080)
  let width = rect.imageRight - rect.imageLeft;
  let height = rect.imageBottom - rect.imageTop;
  canvas.width = parseInt(width * videoWidth / 1920);
  canvas.height = parseInt(height * videoHeight / 1080);
  var canvasCtx = canvas.getContext('2d');
  canvasCtx.drawImage(this._videoElement, parseInt(rect.imageLeft * videoWidth / 1920), parseInt(rect.imageTop * videoHeight / 1080), canvas.width, canvas.height, 0, 0, canvas.width, canvas.height);
  var base64str = canvas.toDataURL('image/png');
  // console.log(base64str);
  return base64str;
};

/**
 * 获取IPCamera的地址
 * FIXME
 * @param {String} videoUrl
 * @return {String}
 * @private
 */
VideoPlayerJanus.prototype._getIpCameraStream = function (videoUrl) {
  var out = videoUrl;

  // rtsp://admin:admin123@*************
  // console.log('cameraObject-->', this.cameraObject);
  var cameraObject = this.cameraObject;
  if(defined(cameraObject)){
    if(this._subStream){
      // 辅码流
      if(cameraObject.manufactorType === CameraManufacturerTypeEnum.DAHUA){
        out = 'rtsp://' + cameraObject.username + ':' + cameraObject.password + '@' + cameraObject.ip + ':554/cam/realmonitor?channel=1?subtype=1';
      }else if(cameraObject.manufactorType === CameraManufacturerTypeEnum.HAIKANG){
        out = 'rtsp://' + cameraObject.username + ':' + cameraObject.password + '@' + cameraObject.ip + ':554/h264/ch1/sub/av_stream';
      }
    }else{
      if(this._defaultStream === '主码流'){
        out = 'rtsp://' + cameraObject.username + ':' + cameraObject.password + '@' + cameraObject.ip;
      }else if(this._defaultStream === '辅码流'){
        if(cameraObject.manufactorType === CameraManufacturerTypeEnum.DAHUA){
          out = 'rtsp://' + cameraObject.username + ':' + cameraObject.password + '@' + cameraObject.ip + ':554/cam/realmonitor?channel=1?subtype=1';
        }else if(cameraObject.manufactorType === CameraManufacturerTypeEnum.HAIKANG){
          out = 'rtsp://' + cameraObject.username + ':' + cameraObject.password + '@' + cameraObject.ip + ':554/h264/ch1/sub/av_stream';
        }
      }else if(this._defaultStream === '第三码流'){
        if(cameraObject.manufactorType === CameraManufacturerTypeEnum.DAHUA){
          out = 'rtsp://' + cameraObject.username + ':' + cameraObject.password + '@' + cameraObject.ip + ':554/cam/realmonitor?channel=1?subtype=2';
        }else if(cameraObject.manufactorType === CameraManufacturerTypeEnum.HAIKANG){
          out = 'rtsp://' + cameraObject.username + ':' + cameraObject.password + '@' + cameraObject.ip + ':554/Streaming/Channels/103';
        }
      }else{
        out = 'rtsp://' + cameraObject.username + ':' + cameraObject.password + '@' + cameraObject.ip;
      }

    }
  }
  return out;
};

/**
 * 开始断线重连
 * @description 视频流有时会因为某些原因而临时断线，这时需要一个重新连接的尝试机制，
 * 这里初始化参数，并会用以指数增长的形式主动请求若干次通过reconnect函数，达到重连的目的；
 * 经过调试_startReconnect函数只需要在janus判断出断流之后再触发就可以了；
 * 有一点值得注意，如果点播后janus的响应时间很慢的话，可能会导致问题，因此需要网络状态判断的机制
 * @see VideoPlayerJanus#_reconnect
 * @see VideoPlayerJanus#_init
 */
VideoPlayerJanus.prototype._startReconnect = function () {
  var that = this;
  if(that._startedReconnect === true){
    return;
  }else {
    that._startedReconnect = true;
  }
  console.log('视频流断线重连函数开始了' + that._videoUrl);
  // 初始化若干参数
  this.reconnectInterval = 1000;
  this.maxReconnectInterval = 30000;
  this.reconnectDecay = 1.5;
  this.timeoutInterval = 2000;
  this.maxReconnectAttempts = null;
  this.reconnectAttempts = 0;

  that._needReconnect = true;
  // this._reconnect();

  var firstTimeDelaySeconds = 5;
  setTimeout(function () {
    that._reconnect();
  }, firstTimeDelaySeconds * 1000);

};

/**
 * 停止断线重连
 * @private
 */
VideoPlayerJanus.prototype._stopReconnect = function () {
  // console.log(this._videoUrl, '_stopReconnect', this._needReconnect, this._streaming.getBitrate());
  this._needReconnect = false;
};

/**
 * 断线重连
 * @description 视频流有时会因为某些原因而临时断线，需要一个重新连接的尝试机制
 * @see VideoPlayerJanus#startReconnect
 */
VideoPlayerJanus.prototype._reconnect = function () {
  var that = this;

  // 首先排除主动黑屏的情况
  if (defined(that._videoUrl)) {
    var timeout = that.reconnectInterval * Math.pow(that.reconnectDecay, that.reconnectAttempts);
    setTimeout(function() {
      that.reconnectAttempts++;
      if(defined(that._videoUrl) && that._needReconnect !== false){
        console.log('断线重连：' + that._videoUrl + '第' + that.reconnectAttempts + '次');
        that.reload({
          needClearLastStream: true
        });
        // that._showError({
        //   videoPlayerStatus: VideoPlayerStatusEnum.LIVE_RESUME,
        //   msg: '断线重连：' + that._videoUrl + '第' + that.reconnectAttempts + '次'
        // });

        that._reconnect();
      }else{
        that._startedReconnect = false;
      }
    }, timeout > that.maxReconnectInterval ? that.maxReconnectInterval : timeout);
  }
};

/**
 * 获取当前的时间
 * @return {Number}
 */
VideoPlayerJanus.prototype.getCurrentTime = function () {
  return this._videoElement.currentTime;
};

/**
 * 订阅当前时间戳的事件
 * @private
 */
VideoPlayerJanus.prototype._subscribeCurrentTimeEvent = function () {
  var that = this;
  if(defined(this._subscribeCurrentTimeEventIntervalHandle)){
    clearInterval(this._subscribeCurrentTimeEventIntervalHandle);
  }
  this._subscribeCurrentTimeEventIntervalHandle = setInterval(function () {
    var videoTime = that.getCurrentTime();

    // if(videoTime !== 0 && that._lastVideoTime !== 0){// FIXME yln2020/8/12
    //     // console.log('VideoPlayerJanus#_subscribeCurrentTimeEvent', videoTime, that._lastVideoTime);
    //
    //     if(!this._countDemo1 && !this._countDemo2){
    //       this._countDemo1 = 0;
    //       this._countDemo2 = 0;
    //     }
    //     if(videoTime === that._lastVideoTime){
    //       this._countDemo1++;
    //       console.log('VideoPlayerJanus#_subscribeCurrentTimeEvent', '视频流停住了' , this._countDemo1 / (this._countDemo1 + this._countDemo2));
    //     }else{
    //       this._countDemo2++;
    //     }
    // }

    if(defined(that._lastVideoTime)){
      if(videoTime > that._lastVideoTime){// 时间戳递增意味着流在正常播放
        that._videoStreamValid = true;
        that._lastVideoTime = videoTime;
        that._noReceiveNewFrameCount = 0;
        that._stopReconnect();
        // console.log('_hideError');
        that._hideError();
      }else if(videoTime < that._lastVideoTime){// 时间戳倒转意味着重新拉流了
        // console.error('时间戳倒转意味着重新拉流了');
        that._lastVideoTime = videoTime;
        that._videoStreamValid = true;
        that._noReceiveNewFrameCount = 0;
        // if(videoTime !== 0 && that._lastVideoTime !== 0){// FIXME yln2020/1/20
        //     console.log('VideoPlayerJanus#_subscribeCurrentTimeEvent-->', '时间戳倒转意味着重新拉流了');
        // }
      }else {// 时间戳相等意味着视频停住了，但有可能是暂停或断流导致的
        if(that._videoElement.paused === false){
          that._noReceiveNewFrameCount++;
          if(that._noReceiveNewFrameCount > 5){
            if(that._channelID && that._videoUrl){
              console.log('500ms未解码任何数据-->', that._channelID, '_', that._videoUrl);
            }
            that._videoStreamValid = false;
          }else{
            // console.log('未解码数据，暂时忽略-->', that._channelID, '_', that._videoUrl);
            that._videoStreamValid = true;
          }
          // that._videoStreamValid = false;
        }else{
          that._videoStreamValid = true;
        }

        // if(videoTime !== 0 && that._lastVideoTime !== 0){// FIXME yln2020/1/20
        //     console.log('VideoPlayerJanus#_subscribeCurrentTimeEvent-->', '停住了!');
        // }
      }
    }else{
      that._lastVideoTime = videoTime;
    }
  }, 100);
};

/**
 * 获取视频
 * @param {String} stream
 * @private
 */
VideoPlayerJanus.prototype._addVideoElementCandidate = function (stream) {
  var videoElement = document.createElement('video');
  videoElement.className = 'lux-resizable-video-element';
  videoElement.setAttribute('width', '1920');
  videoElement.setAttribute('height', '1080');

  videoElement.setAttribute('autoplay', 'autoplay');
  videoElement.setAttribute('playsinline', 'playsinline');
  videoElement.setAttribute('muted', 'muted');

  Janus.attachMediaStream(videoElement, stream);
  var that = this;
  function tick() {
    // console.log('waiting' + that._videoUrl.substr(-2));
    if(videoElement.currentTime > 0){
      // console.log('got it');
      that._wrapper.appendChild(videoElement);
      // that._wrapper.insertBefore(that._wrapper, videoElement); // FIXME videoElement

      var lastVideoElement = that._videoElement;
      that._videoElement = videoElement;
      that._videoElement.setAttribute('id', that._videoElementId);
      setTimeout(function () {
        that._wrapper.removeChild(lastVideoElement);
      }, 400);
    }else{
      requestAnimationFrame(tick);
    }
  }
  tick();
};

/**
 * 切换到主码流
 */
VideoPlayerJanus.prototype.switchToMainStream = function () {
  if(this._subStream === true){
    console.log('切换到主码流');
    this._subStream = false;
    this.reload({
      videoUrl: this._videoUrl
    });
  }
};

/**
 * 切换到子码流
 */
VideoPlayerJanus.prototype.switchToSubStream = function () {
  if(this._subStream === false){
    console.log('切换到子码流');
    this._subStream = true;
    this.reload({
      videoUrl: this._videoUrl
    });
  }
};

/**
 * Returns true if this object was destroyed; otherwise, false.
 * <br /><br />
 * If this object was destroyed, it should not be used; calling any function other than
 *
 * @returns {Boolean} <code>true</code> if this object was destroyed; otherwise, <code>false</code>.
 *
 * @see VideoPlayerJanus#destroy
 */
VideoPlayerJanus.prototype.isDestroyed = function() {
  return false;
};

/**
 * Removes and destroys all created by this instance.
 */
VideoPlayerJanus.prototype.destroy = function() {
  knockout.cleanNode(this._wrapper);
  this._container.removeChild(this._wrapper);

  return destroyObject(this);
};

export default VideoPlayerJanus;
