import VideoPlayerStatusEnum from './VideoPlayerStatusEnum'

'use strict';

/**
 * VideoPlayerStatusEnumHelper
 * @alias VideoPlayerStatusEnumHelper
 * @exports VideoPlayerStatusEnumHelper
 */
var VideoPlayerStatusEnumHelper = {};

/**
 * 是不是直播的状态
 * @param status
 * @return {boolean}
 */
VideoPlayerStatusEnumHelper.isLiveStatus = function (status) {
  return (status >= VideoPlayerStatusEnum.LIVE) && (status < VideoPlayerStatusEnum.LIVE + 10);
};

/**
 * 是不是回放的状态
 * @param status
 * @return {boolean}
 */
VideoPlayerStatusEnumHelper.isPlaybackStatus = function (status) {
  return (status >= VideoPlayerStatusEnum.PLAYBACK) && (status < VideoPlayerStatusEnum.PLAYBACK + 10);
};

export default VideoPlayerStatusEnumHelper;
