import defaultValue from '../../Core/defaultValue'
import defined from '../../Core/defined'
import defineProperties from '../../Core/defineProperties'
import destroyObject from '../../Core/destroyObject'
import DeveloperError from '../../Core/DeveloperError'
import getElement from '../../Widgets/getElement'
import TextFx from '../../Widgets/TextFx/TextFx'

'use strict';

/**
 * 错误信息展示控件
 * @alias ShowTipsWidget（错误信息展示控件）
 * @description 使用了动画库，可以自动在附着的container上居中显示
 *
 * @param options
 * @constructor
 */
function ShowTipsWidget(options) {
  //>>includeStart('debug', pragmas.debug);
  if(!defined(options)){
    options = {};
  }
  if (!defined(options.container)) {
    throw new DeveloperError('options.container is required.');
  }
  //>>includeEnd('debug');

  var container = getElement(options.container);

  var wrapper = document.createElement('div');
  container.appendChild(wrapper);

  var tipsElement = document.createElement('h2');
  tipsElement.className = 'lux-ShowTipsWidget';
  tipsElement.innerText = '';

  wrapper.appendChild(tipsElement);

  this._tipsElement = tipsElement;
  this._textFx = new TextFx(this._tipsElement);
  this._visible = false;

}

defineProperties(ShowTipsWidget.prototype, {
  /**
   * Gets the parent container.
   * @memberof ShowTipsWidget.prototype
   *
   * @type {Element}
   */
  container : {
    get : function() {
      return this._container;
    }
  },

  /**
   * Gets the wrapper.
   * @memberof ShowTipsWidget.prototype
   *
   * @type {Element}
   */
  wrapper : {
    get : function() {
      return this._wrapper;
    }
  },

  /**
   * 是否在显示
   * @memberof ShowTipsWidget.prototype
   *
   * @type {Boolean}
   */
  visible : {
    get : function() {
      return this._visible;
    }
  }

});

ShowTipsWidget.prototype.show = function(msg, effect) {
  if(this.msg === msg){
    return;
  }
  this.msg = msg;
  effect = defaultValue(effect, 'fx1');
  this._textFx .changeLetters(msg);
  this._textFx .show(effect, function() {

  });
  this._visible = true;
};

ShowTipsWidget.prototype.hide = function(effect) {
  this.msg = '';
  effect = defaultValue(effect, 'fx1');
  // this._textFx .changeLetters(msg);
  this._textFx .hide(effect, function() {
  });
  this._visible = false;
};

ShowTipsWidget.prototype.isDestroyed = function() {
  return false;
};

ShowTipsWidget.prototype.destroy = function() {
  this._container.removeChild(this._wrapper);

  return destroyObject(this);
};

export default ShowTipsWidget;
