@import url(./videoPlayer-template-1big5small-leftTop.css);
@import url(./videoPlayer-template-1big7small-leftTop.css);
@import url(./videoPlayer-template-4balance-leftTop.css);
@import url(./videoPlayer-template-2TopBottom-Top.css);
@import url(./videoPlayer-template-3big4small-leftTop.css);
@import url(./videoPlayer-template-8balanceTopBottom-Top.css);
@import url(./videoPlayer-template-1big-full.css);
@import url(./videoPlayer-template-5leftRight-left.css);
@import url(./videoPlayer-template-9balance-leftTop.css);
@import url(./videoPlayer-template-16balance-leftTop.css);

.lux-VideoPlayerGallery-wrapper {
  --space: 0%;
  --transitionAnimation: all 0.5s linear;
  position: absolute;
  overflow: hidden;
}

/*主视频*/
.videoPlayerBig {
  --space: 0%;
  position: absolute;
  left: calc(var(--space) / (var(--width)));
  top: calc(var(--space) / (var(--height)));
  right: calc(var(--space) / (var(--width)));
  bottom: calc(var(--space) / (var(--height)));
}

/*四周的小视频*/
.videoPlayerSmall {
  position: absolute;
  left: calc(var(--space) / (var(--width)));
  top: calc(var(--space) / (var(--height)));
  right: calc(var(--space) / (var(--width)));
  bottom: calc(var(--space) / (var(--height)));
}

/*收缩的的小视频*/
.lux-VideoPlayerGallery-videoPlayerCollapsed {
  left: calc(100% / var(--columns) * 0);
  top: calc(100% / var(--rows) * var(--rows));
  width: calc(100% / var(--columns) * 1);
  height: calc(100% / var(--rows) * 1);
  transition: var(--transitionAnimation);
  position: absolute;
  --width: 1;
  --height: 1;
}
