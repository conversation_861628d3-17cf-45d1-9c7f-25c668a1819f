/*

 **********************************
 *                     *     2    *
 *         1           ************
 *                     *     3    *
 **********************************
 *    6    *     5     *     4    *
 **********************************

*/
.videoPlayer-template-1big5small-leftTop {
    --columns: 3;
    --rows: 3;
}
/*第一个大视频*/
.videoPlayer-left0-top0-column2-row2{
    left: calc(100% / var(--columns) * 0);
    top: calc(100% / var(--rows) * 0);
    width: calc(100% / var(--columns) * 2);
    height: calc(100% / var(--rows) * 2);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 2;
    --height: 2;
}
/*右上第一个*/
.videoPlayer-left2-top0-column1-row1{
    left: calc(100% / var(--columns) * 2);
    top: calc(100% / var(--rows) * 0);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*右上第二个*/
.videoPlayer-left2-top1-column1-row1{
    left: calc(100% / var(--columns) * 2);
    top: calc(100% / var(--rows) * 1);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*右下*/
.videoPlayer-left2-top2-column1-row1{
    left: calc(100% / var(--columns) * 2);
    top: calc(100% / var(--rows) * 2);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*左下第二个*/
.videoPlayer-left1-top2-column1-row1{
    left: calc(100% / var(--columns) * 1);
    top: calc(100% / var(--rows) * 2);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*左下第一个*/
.videoPlayer-left0-top2-column1-row1{
    left: calc(100% / var(--columns) * 0);
    top: calc(100% / var(--rows) * 2);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
