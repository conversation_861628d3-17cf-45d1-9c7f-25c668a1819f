/*

 *********************************************
 *                                *     2    *
 *                                ************
 *               1                *     3    *
 *                                ************
 *                                *     4    *
 *********************************************
 *    8    *     7     *     6    *     5    *
 *********************************************

*/

.videoPlayer-template-1big7small-leftTop {
    --columns: 4;
    --rows: 4;
}
/*第一个大视频*/
.videoPlayer-left0-top0-column3-row3{
    left: calc(100% / var(--columns) * 0);
    top: calc(100% / var(--rows) * 0);
    width: calc(100% / var(--columns) * 3);
    height: calc(100% / var(--rows) * 3);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 3;
    --height: 3;
}
/*右上第一个*/
.videoPlayer-left3-top0-column1-row1{
    left: calc(100% / var(--columns) * 3);
    top: calc(100% / var(--rows) * 0);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*右上第二个*/
.videoPlayer-left3-top1-column1-row1{
    left: calc(100% / var(--columns) * 3);
    top: calc(100% / var(--rows) * 1);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*右上第三个*/
.videoPlayer-left3-top2-column1-row1{
    left: calc(100% / var(--columns) * 3);
    top: calc(100% / var(--rows) * 2);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*右下*/
.videoPlayer-left3-top3-column1-row1{
    left: calc(100% / var(--columns) * 3);
    top: calc(100% / var(--rows) * 3);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*左下第三个*/
.videoPlayer-left2-top3-column1-row1{
    left: calc(100% / var(--columns) * 2);
    top: calc(100% / var(--rows) * 3);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*左下第二个*/
.videoPlayer-left1-top3-column1-row1{
    left: calc(100% / var(--columns) * 1);
    top: calc(100% / var(--rows) * 3);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*左下第一个*/
.videoPlayer-left0-top3-column1-row1{
    left: calc(100% / var(--columns) * 0);
    top: calc(100% / var(--rows) * 3);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
