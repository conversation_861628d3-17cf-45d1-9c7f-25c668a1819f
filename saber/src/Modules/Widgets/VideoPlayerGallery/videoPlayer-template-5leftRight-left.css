/*

 ***********************   ***********************   ***********************   ***********************  ***********************
 *                     *   *                     *   *                     *   *                     *  *                     *
 *         1           *   *          2          *   *          3          *   *          4          *  *          5          *
 *                     *   *                     *   *                     *   *                     *  *                     *
 ***********************   ***********************   ***********************   ***********************  ***********************

*/

.videoPlayer-template-5leftRight-left {
    --columns: 5;
    --rows: 1;
}
/*左主视频*/
.videoPlayer-left0-top0-column1-row1{
    left: calc(100% / var(--columns) * 0);
    top: calc(100% / var(--rows) * 0);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*左2*/
.videoPlayer-left1-top0-column1-row1{
    left: calc(100% / var(--columns) * 1);
    top: calc(100% / var(--rows) * 0);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*左2*/
.videoPlayer-left2-top0-column1-row1{
  left: calc(100% / var(--columns) * 2);
  top: calc(100% / var(--rows) * 0);
  width: calc(100% / var(--columns) * 1);
  height: calc(100% / var(--rows) * 1);
  transition: var(--transitionAnimation);
  position: absolute;
  --width: 1;
  --height: 1;
}
/*左3*/
.videoPlayer-left3-top0-column1-row1{
  left: calc(100% / var(--columns) * 3);
  top: calc(100% / var(--rows) * 0);
  width: calc(100% / var(--columns) * 1);
  height: calc(100% / var(--rows) * 1);
  transition: var(--transitionAnimation);
  position: absolute;
  --width: 1;
  --height: 1;
}
/*左4*/
.videoPlayer-left4-top0-column1-row1{
  left: calc(100% / var(--columns) * 4);
  top: calc(100% / var(--rows) * 0);
  width: calc(100% / var(--columns) * 1);
  height: calc(100% / var(--rows) * 1);
  transition: var(--transitionAnimation);
  position: absolute;
  --width: 1;
  --height: 1;
}
