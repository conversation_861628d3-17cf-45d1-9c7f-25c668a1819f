/*

 ***********************   ***********************   ***********************
 *                     *   *                     *   *                     *
 *         1           *   *          2          *   *          3          *
 *                     *   *                     *   *                     *
 ***********************   ***********************   ***********************

 ***********************   ***********************   ***********************
 *                     *   *                     *   *                     *
 *         6           *   *          5          *   *          4          *
 *                     *   *                     *   *                     *
 ***********************   ***********************   ***********************

 ***********************   ***********************   ***********************
 *                     *   *                     *   *                     *
 *         7           *   *          8          *   *          9          *
 *                     *   *                     *   *                     *
 ***********************   ***********************   ***********************

*/

.videoPlayer-template-9balance-leftTop {
    --columns: 3;
    --rows: 3;
}
/*左上主视频*/
.videoPlayer-left0-top0-column1-row1{
    left: calc(100% / var(--columns) * 0);
    top: calc(100% / var(--rows) * 0);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*中上*/
.videoPlayer-left1-top0-column1-row1{
    left: calc(100% / var(--columns) * 1);
    top: calc(100% / var(--rows) * 0);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*右上*/
.videoPlayer-left2-top0-column1-row1{
    left: calc(100% / var(--columns) * 2);
    top: calc(100% / var(--rows) * 0);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}

/*右中*/
.videoPlayer-left2-top1-column1-row1{
    left: calc(100% / var(--columns) * 2);
    top: calc(100% / var(--rows) * 1);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*中中*/
.videoPlayer-left1-top1-column1-row1{
    left: calc(100% / var(--columns) * 1);
    top: calc(100% / var(--rows) * 1);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*左中*/
.videoPlayer-left0-top1-column1-row1{
    left: calc(100% / var(--columns) * 0);
    top: calc(100% / var(--rows) * 1);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}

/*左下*/
.videoPlayer-left0-top2-column1-row1{
    left: calc(100% / var(--columns) * 0);
    top: calc(100% / var(--rows) * 2);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*中下*/
.videoPlayer-left1-top2-column1-row1{
    left: calc(100% / var(--columns) * 1);
    top: calc(100% / var(--rows) * 2);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
/*右下*/
.videoPlayer-left2-top2-column1-row1{
    left: calc(100% / var(--columns) * 2);
    top: calc(100% / var(--rows) * 2);
    width: calc(100% / var(--columns) * 1);
    height: calc(100% / var(--rows) * 1);
    transition: var(--transitionAnimation);
    position: absolute;
    --width: 1;
    --height: 1;
}
