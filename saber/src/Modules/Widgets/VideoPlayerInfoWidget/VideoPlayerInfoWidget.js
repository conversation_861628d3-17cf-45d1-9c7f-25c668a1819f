'use strict';
import defined from '../../Core/defined';
import defineProperties from '../../Core/defineProperties';
import DeveloperError from '../../Core/DeveloperError';
import getElement from '../../Widgets/getElement';
import StringUtil from '../../Util/StringUtil';

/**
 * VideoPlayerInfoWidget
 * @alias VideoPlayerInfoWidget
 *
 * @param {Object} options
 * @param {VideoPlayerFLV} options.videoPlayer
 *
 * @exception {DeveloperError} videoPlayerGallery is required.
 *
 * @constructor
 */
function VideoPlayerInfoWidget (options) {
  if (!defined(options.videoPlayer)) {
    throw new DeveloperError('videoPlayer is required.');
  }

  this._videoPlayer = options.videoPlayer;
  this._init();
}

defineProperties(VideoPlayerInfoWidget.prototype, {
});

/**
 * 初始化
 * @private
 */
VideoPlayerInfoWidget.prototype._init = function () {
  let nameDom = document.createElement('div');
  nameDom.innerText = '';
  nameDom.className = 'xh-VideoPlayerInfoWidget-nameDom';
  getElement(this._videoPlayer._container).appendChild(nameDom);
  this._videoPlayer.subscribeChannelIdChangeEvent(channelID => {
    // console.log('subscribeChannelIdChangeEvent-->', channelID);
    if (channelID) {
      let tempArray = channelID.split('_');
      let licencePlate = tempArray[0];
      let channel = tempArray[1];
      // nameDom.innerText = `${licencePlate}-通道${channel}`;
      // nameDom.innerHTML = `<div>车牌号：</div>
      //                       <div>${licencePlate}</div>
      //                       <div>通道号：</div>
      //                       <div>${channel}</div>`;
      nameDom.innerHTML = `<div>${licencePlate}-通道${channel}</div>`;
    } else {
      nameDom.innerText = '';
    }
  });
};
export default VideoPlayerInfoWidget;
