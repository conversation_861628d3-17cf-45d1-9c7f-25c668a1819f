'use strict';
import defined from '../../Core/defined';
import defineProperties from '../../Core/defineProperties';
import DeveloperError from '../../Core/DeveloperError';
import getElement from '../../Widgets/getElement';
import StringUtil from '../../Util/StringUtil';

/**
 * VideoPlayerNameMixin
 * @alias VideoPlayerNameMixin
 *
 * @param {Object} options
 * @param {VideoPlayerGallery} options.videoPlayerGallery
 *
 * @exception {DeveloperError} videoPlayerGallery is required.
 *
 * @constructor
 */
function VideoPlayerNameMixin (options) {
  if (!defined(options.videoPlayerGallery)) {
    throw new DeveloperError('videoPlayerGallery is required.');
  }

  this._videoPlayerGallery = options.videoPlayerGallery;
  this._init();
}

defineProperties(VideoPlayerNameMixin.prototype, {
});

/**
 * 初始化
 * @private
 */
VideoPlayerNameMixin.prototype._init = function () {
  let idPrefix = StringUtil.generateShortUid() + 'xh_VideoPlayerNameMixin_nameDom_';
  for (var i = 0; i < this._videoPlayerGallery.maxVideoCount; i++) {
    var nameDom = document.createElement('div');
    nameDom.innerHTML = '';
    nameDom.className = 'xh-VideoPlayerNameMixin-nameDom';
    nameDom.id = idPrefix + i;
    getElement(this._videoPlayerGallery._videoElementIdPrefix + i).appendChild(nameDom);
  }
  for (var k = 0; k < this._videoPlayerGallery.maxVideoCount; k++) {
    getElement(idPrefix + k).innerText = '通道' + (1 + k);
  }

  // var that = this;
  // this._videoPlayerGallery.subscribeVideoUrlChangeEvent(function (dataArray) {
  //   for(var i = 0; i < dataArray.length; i++){
  //     if(defined(dataArray[i].channelID)){
  //       var camera = that._indexCollection.cameraAssociativeArray.get(dataArray[i].channelID);
  //       if(defined(camera)){
  //         getElement(idPrefix + i).innerText = camera.name;
  //       }else{
  //         getElement(idPrefix + i).innerText = '';
  //       }
  //     }else{
  //       getElement(idPrefix + i).innerText = '';
  //     }
  //   }
  // });
};
export default VideoPlayerNameMixin;
