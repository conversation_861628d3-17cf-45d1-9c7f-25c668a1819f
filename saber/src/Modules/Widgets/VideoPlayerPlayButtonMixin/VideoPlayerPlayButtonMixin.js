import defined from '../../Core/defined'
import defineProperties from '../../Core/defineProperties'
import DeveloperError from '../../Core/DeveloperError'
import getElement from '../../Widgets/getElement'
import StringUtil from '../../Util/StringUtil'
'use strict';

/**
 * VideoPlayerPlayButtonMixin
 * @alias VideoPlayerPlayButtonMixin
 *
 * @param {Object} options
 * @param {VideoPlayerGallery} options.videoPlayerGallery
 * @param {Function} options.callback
 *
 * @exception {DeveloperError} videoPlayerGallery is required.
 *
 * @constructor
 */
function VideoPlayerPlayButtonMixin(options) {
  //>>includeStart('debug', pragmas.debug);
  if (!defined(options.videoPlayerGallery)) {
    throw new DeveloperError('videoPlayerGallery is required.');
  }
  //>>includeEnd('debug');

  this._videoPlayerGallery = options.videoPlayerGallery;
  this._callback = options.callback;
  this._init();
}

defineProperties(VideoPlayerPlayButtonMixin.prototype, {
});

/**
 * 初始化
 * @private
 */
VideoPlayerPlayButtonMixin.prototype._init = function () {
  let idPrefix = StringUtil.generateShortUid() + 'xh_VideoPlayerPlayButtonMixin_playButtonDom_';
  for(let i = 0 ; i < this._videoPlayerGallery.maxVideoCount; i++){
    let playButtonDom = document.createElement('div');
    playButtonDom.innerHTML = '';
    playButtonDom.className = 'xh-VideoPlayerPlayButtonMixin-playButtonDom';
    playButtonDom.id = idPrefix + i;
    getElement(this._videoPlayerGallery._videoElementIdPrefix + i).appendChild(playButtonDom);

    playButtonDom.addEventListener('click', ()=>{
      if(defined(this._callback)){
        this._callback(i)
      }
    })
  }

  this._videoPlayerGallery.subscribeVideoUrlChangeEvent((dataArray)=> {
    console.log(dataArray);
    for(let i = 0; i < dataArray.length; i++){
      if(defined(dataArray[i].videoUrl)){
          if(this._videoPlayerGallery.get(i).checkVideoUrlValid()){
              getElement(idPrefix + i).style.display = 'none';
          }else{
              getElement(idPrefix + i).style.display = 'block';
          }
      }else{
        // getElement(idPrefix + i).style.display = 'none';
        getElement(idPrefix + i).style.display = 'block';
      }

    }
  });

};

export default VideoPlayerPlayButtonMixin;
