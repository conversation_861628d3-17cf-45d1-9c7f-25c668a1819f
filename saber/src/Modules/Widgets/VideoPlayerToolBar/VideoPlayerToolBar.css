.lux-VideoPlayerToolBar-wrapper {
    /*display: inline-block;*/
    /*height: 100%;*/
    /*width: 130px;*/
    background-color: unset;
    /*过度动画动画*/
    --lux-VideoPlayerToolBar-transitionDefault: all linear 500ms;

    --lux-VideoPlayerToolBar-rectButtonWidth: 20px;
    /*pointer-events: all;*/
}

/*九宫格控制*/
.lux-VideoPlayerToolBar-baseControlGroup{
    position: absolute;
    left: 2px;
    right: 2px;
    bottom: 2px;
    height: var(--lux-VideoPlayerToolBar-rectButtonWidth);
    background-color: rgba(0, 0, 0, 0.647058823529412);
    border: none;
    pointer-events: all;
    transition: var(--lux-VideoPlayerToolBar-transitionDefault);
    opacity: 0;
    overflow: hidden;
}
.lux-VideoPlayerToolBar-baseControlGroup:hover{
    opacity: 1;
}

.lux-VideoPlayerToolBar-baseControlGroup button{
    width: var(--lux-VideoPlayerToolBar-rectButtonWidth);
    height: var(--lux-VideoPlayerToolBar-rectButtonWidth);
    border: 0;
    pointer-events: all;
    background-color: unset;
    background-size: 80% 80%;
    background-position: center;
    background-repeat: no-repeat;
}
.lux-VideoPlayerToolBar-baseControlGroup button:hover{
    border-radius: 10px;
    border: 1px solid #444;
    box-shadow: 0 0 8px #fff;
    border-color: #ea4;
    outline: none;
}

.lux-VideoPlayerToolBar-baseControlGroup-textDom{
    display: inline-block;
    height: var(--lux-VideoPlayerToolBar-rectButtonWidth);
    border: 0;
    pointer-events: none;
    background-color: unset;
    color: #ffffff;
    float: right;
    text-align: center;
    vertical-align: middle;
    line-height: var(--lux-VideoPlayerToolBar-rectButtonWidth);
    margin: 0;
    padding: 1px 8px;
}
