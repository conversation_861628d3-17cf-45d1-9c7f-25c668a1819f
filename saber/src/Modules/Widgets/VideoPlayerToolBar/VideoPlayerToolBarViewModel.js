import defaultValue from '../../Core/defaultValue'
import defineProperties from '../../Core/defineProperties'
import destroyObject from '../../Core/destroyObject'
import knockout from '../../ThirdParty/knockout'

/**
 * 快球控制viewModel
 *
 * @alias CameraSpeedDomeWidget（快球控制控件viewModel）
 *
 * @param options
 * @param {VideoPlayerRTMP} options.videoPlayer
 *
 * @constructor
 */
function VideoPlayerToolBarViewModel(options) {
  //>>includeStart('debug', pragmas.debug);
  options = defaultValue(options, {});
  //>>includeEnd('debug');

  this._videoPlayer = options.videoPlayer;

  this.show = false;
  this.hasAtLeastOneButton = false;

  knockout.track(this, ['show', 'hasAtLeastOneButton'
  ]);

  var that = this;

  /**
   * 单击放大事件
   * @param event
   * @private
   */
  this._clickFunc = function (event) {

  };

  knockout.getObservable(this, 'show').subscribe(function(isShow) {

  });

}

defineProperties(VideoPlayerToolBarViewModel.prototype, {
  /**
   * 旋转到特定的状态
   * @memberof VideoPlayerToolBarViewModel.prototype
   *
   * @type {Command}
   */
  goToTargetLocation : {
    get : function() {
      return this._goToTargetLocation;
    }
  }
});

/**
 * 订阅球机事件
 * @param {Function} callback
 */
VideoPlayerToolBarViewModel.prototype.subscribeAllControlEvent = function (callback) {
  knockout.track(this, ['_controlEvent']);
  knockout.getObservable(this, '_controlEvent').subscribe(function(newValue) {
    callback(newValue);
  });
};

/**
 * 订阅鼠标事件
 * @description 可点击屏幕控制球机事件
 */
VideoPlayerToolBarViewModel.prototype.subscribeMouseZoomEvent = function () {
  this._videoPlayerRTMP.videoElementOverlay.addEventListener('mousewheel', this._scrollFunc, false);
  this._videoPlayerRTMP.videoElementOverlay.addEventListener('click', this._clickFunc, false);
  this._videoPlayerRTMP.videoElementOverlay.classList.add('lux-CameraSpeedDomeWidget-crosshairCursor');
  // this._videoPlayerRTMP.videoElementOverlay.classList.remove('lux-CameraSpeedDomeWidget-notAllowCursor');
};

/**
 * @returns {Boolean} true if the object has been destroyed, false otherwise.
 */
VideoPlayerToolBarViewModel.prototype.isDestroyed = function() {
  return false;
};

/**
 * Destroys the view model.
 */
VideoPlayerToolBarViewModel.prototype.destroy = function() {
  // this._eventHelper.removeAll();
  destroyObject(this);
};

export default VideoPlayerToolBarViewModel;
