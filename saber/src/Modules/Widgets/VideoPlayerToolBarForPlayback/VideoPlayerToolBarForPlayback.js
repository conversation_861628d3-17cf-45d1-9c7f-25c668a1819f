import defined from '../../Core/defined';
import defineProperties from '../../Core/defineProperties';
import destroyObject from '../../Core/destroyObject';
import DeveloperError from '../../Core/DeveloperError';
import getElement from '../../Widgets/getElement';
import knockout from '../../ThirdParty/knockout';
import VideoPlayerToolBarForPlaybackViewModel from './VideoPlayerToolBarForPlaybackViewModel';
import download from '@/utils/download/download';
import { Notification } from 'element-ui';
import moment from 'moment';

/**
 * VideoPlayerToolBarForPlayback
 *
 * @alias VideoPlayerToolBarForPlayback（视频播放器工具栏）
 *
 * @param {Element|String} container 控件容器
 * @param options
 * @param {VideoPlayerRTMP} options.videoPlayer
 *
 * @exception {DeveloperError} container is required.
 *
 * @constructor
 */
function VideoPlayerToolBarForPlayback (container, options) {
  // >>includeStart('debug', pragmas.debug);
  if (!defined(container)) {
    throw new DeveloperError('container is required.');
  }
  // >>includeEnd('debug');

  container = getElement(container);
  this._videoPlayer = options.videoPlayer;

  var viewModel = new VideoPlayerToolBarForPlaybackViewModel(options);

  /**
   * 包裹层
   * @type {HTMLDivElement}
   */
  var wrapper = document.createElement('div');
  wrapper.className = 'lux-VideoPlayerToolBarForPlayback-wrapper';
  wrapper.setAttribute('data-bind', 'visible: show & hasAtLeastOneButton');
  container.appendChild(wrapper);

  var baseControlGroup = document.createElement('div');
  baseControlGroup.className = 'lux-VideoPlayerToolBarForPlayback-baseControlGroup';
  baseControlGroup.innerHTML = '';
  wrapper.appendChild(baseControlGroup);

  knockout.applyBindings(viewModel, wrapper);

  this._viewModel = viewModel;
  this._wrapper = wrapper;
  this._container = container;
  this._baseControlGroup = baseControlGroup;
  this._videoPlayer = options.videoPlayer;

  // 新增若干个默认的按钮
  // 关闭静音
  this.closeMutedButton = this.addButton('/static/images/icons/audioCloseWhite.svg', () => {
    this._videoPlayer.setFLVMuted(false);
    this.MutedButton.style.display = 'inline-block';
    this.closeMutedButton.style.display = 'none';
  }, '关闭静音');

  // 开启静音
  this.MutedButton = this.addButton('/static/images/icons/audioOpenWhite.svg', () => {
    this._videoPlayer.setFLVMuted(true);
    this.closeMutedButton.style.display = 'inline-block';
    this.MutedButton.style.display = 'none';
  }, '开启静音');
  this.closeMutedButton.style.display = 'none';
  // 关闭视频
  this.addButton('/static/images/icons/videoCloseWhite.svg', () => {
    this._videoPlayer.stopPlayBackStream(true);
    this._videoPlayer.clearStream();
    // 初始化开启声音 todo
    this.MutedButton.style.display = 'inline-block';
    this.closeMutedButton.style.display = 'none';
  }, '关闭视频');

  // 下载画面截屏
  this.addButton('/static/images/icons/videoDownloadWhite.svg', () => {
    if (!defined(this._videoPlayer.channelID)) {
      return;
    }
    let tempArray = this._videoPlayer.channelID.split('_');
    let licencePlate = tempArray[0];
    let channel = parseInt(tempArray[1]);
    let base64 = this._videoPlayer.getPrintScreenBase64Jpg();
    if(!base64) {
      return false
    }
    download(base64, `${licencePlate}通道${channel}视频截图${moment().format('YYYYMMDDHHmmss')}.jpg`);
    Notification.success({
      title: `${licencePlate}通道${channel}视频截图下载到本地！`
    });
  }, '下载画面截屏');

  // 暂停
  this._pauseButton = this.addButton('/static/images/icons/videoPause.svg', () => {
    this._videoPlayer.controlPlaybackStream('pause');
    this._videoPlayer.pause();
    this.playButtonVisible = true;
    this.pauseButtonVisible = false;
  }, '暂停');

  // 播放
  this._playButton = this.addButton('/static/images/icons/videoPlay.svg', () => {
    this._videoPlayer.controlPlaybackStream('play');
    this._videoPlayer.play();
    this.playButtonVisible = false;
    this.pauseButtonVisible = true;
  }, '播放');
  this._videoPlayer.subscribeChannelIdChangeEvent(() => {
    this.playButtonVisible = false;
    this.pauseButtonVisible = false;
  });
  this.playButtonVisible = false;

  // 减慢
  this.addButton('/static/images/icons/videoSlower.svg', () => {
    this._videoPlayer.controlPlaybackStream('slower');
    this._videoPlayer.play();
  }, '减慢');

  // 加快
  this.addButton('/static/images/icons/videoFaster.svg', () => {
    this._videoPlayer.controlPlaybackStream('faster');
    this._videoPlayer.play();
  }, '加快');

  // 视频播放器
  this._speedTextDom = this.addTextDom('X1', () => {}, '播放速度');
  this._bpsTextDom = this.addTextDom('0bps', () => {}, 'bps');

}

defineProperties(VideoPlayerToolBarForPlayback.prototype, {
  /**
   * Gets the parent container.
   * @memberof VideoPlayerToolBarForPlayback.prototype
   *
   * @type {Element}
   */
  container: {
    get: function () {
      return this._container;
    }
  },

  /**
   * Gets the view model.
   * @memberof VideoPlayerToolBarForPlayback.prototype
   *
   * @type {VideoPlayerToolBarForPlaybackViewModel}
   */
  viewModel: {
    get: function () {
      return this._viewModel;
    }
  },

  /**
   * Gets the wrapper.
   * @memberof VideoPlayerToolBarForPlayback.prototype
   *
   * @type {Element}
   */
  wrapper: {
    get: function () {
      return this._wrapper;
    }
  },

  /**
   * Gets the wrapper.
   * @memberof VideoPlayerToolBarForPlayback.prototype
   *
   * @type {Element}
   */
  show: {
    get: function () {
      return this._viewModel.show;
    },
    set: function (value) {
      this._viewModel.show = value;
    }
  },

  /**
   * 播放器
   * @memberof VideoPlayerToolBarForPlayback.prototype
   *
   * @type {VideoPlayerRTMP}
   */
  videoPlayer: {
    get: function () {
      return this._videoPlayer;
    }
  },

  /**
   * 拍照按钮是否可见.
   * @memberof VideoPlayerToolBarForPlayback.prototype
   *
   * @type {Element}
   */
  takePhotoButtonVisible: {
    get: function () {
      return this._takePhotoButton.style.display === 'inline-block';
    },
    set: function (value) {
      if (value) {
        this._takePhotoButton.style.display = 'inline-block';
      } else {
        this._takePhotoButton.style.display = 'none';
      }
    }
  },

  /**
   * 播放按钮是否可见.
   * @memberof VideoPlayerToolBarForPlayback.prototype
   *
   * @type {Boolean}
   */
  playButtonVisible: {
    get: function () {
      return this._playButton.style.display === 'inline-block';
    },
    set: function (value) {
      if (value) {
        this._playButton.style.display = 'inline-block';
      } else {
        this._playButton.style.display = 'none';
      }
    }
  },

  /**
   * 暂停按钮是否可见.
   * @memberof VideoPlayerToolBarForPlayback.prototype
   *
   * @type {Boolean}
   */
  pauseButtonVisible: {
    get: function () {
      return this._pauseButton.style.display === 'inline-block';
    },
    set: function (value) {
      if (value) {
        this._pauseButton.style.display = 'inline-block';
      } else {
        this._pauseButton.style.display = 'none';
      }
    }
  },

  /**
   * 播放速度.
   * @memberof VideoPlayerToolBarForPlayback.prototype
   *
   * @type {String}
   */
  speedText: {
    get: function () {
      return this._speedTextDom.innerText;
    },
    set: function (value) {
      if (value) {
        this._speedTextDom.innerText = value;
      }
    }
  },
  /**
   * bps.
   * @memberof VideoPlayerToolBarForPlayback.prototype
   *
   * @type {String}
   */
  bpsTextDomText: {
    get: function () {
      return this._bpsTextDom.innerText;
    },
    set: function (value) {
      if (value) {
        this._bpsTextDom.innerText = value;
      }
    }
  }
});

/**
 * 新增按钮
 * @param {String} imgUrl 按钮图片的地址
 * @param {Function} callback
 * @param {String} tips 提示
 */
VideoPlayerToolBarForPlayback.prototype.addButton = function (imgUrl, callback, tips) {
  var newButton = document.createElement('button');
  newButton.style.backgroundImage = 'url("' + imgUrl + '")';
  this._baseControlGroup.appendChild(newButton);
  var that = this;
  newButton.addEventListener('click', function (event) {
    callback(that, event);
  });
  this._viewModel.hasAtLeastOneButton = true;
  newButton.title = tips;
  return newButton;
};

/**
 * 初始化视频播放器切换按钮.
 */
VideoPlayerToolBarForPlayback.prototype.initializeToggleButton = function () {
  // 什么都不做，但要保留这个函数
};

/**
 * 新增文本
 * @param text
 * @param callback
 * @param tips
 * @return {HTMLDivElement}
 */
VideoPlayerToolBarForPlayback.prototype.addTextDom = function (text, callback, tips) {
  let newDom = document.createElement('div');
  newDom.className = 'lux-VideoPlayerToolBarForPlayback-baseControlGroup-textDom';
  newDom.innerText = text;
  this._baseControlGroup.appendChild(newDom);
  newDom.title = tips;
  return newDom;
};

/**
 * 订阅球机转动事件
 * @param {Function} callback
 */
VideoPlayerToolBarForPlayback.prototype.subscribeAllControlEvent = function (callback) {
  this._viewModel.subscribeAllControlEvent(callback);
};

/**
 *  禁止zoom球机控制鼠标事件
 */
VideoPlayerToolBarForPlayback.prototype.forbidMouseZoomEvent = function () {
  this._viewModel.forbidMouseZoomEvent();
};

/**
 *  取消禁止zoom球机控制鼠标事件
 */
VideoPlayerToolBarForPlayback.prototype.cancelForbidMouseZoomEvent = function () {
  this._viewModel.cancelForbidMouseZoomEvent();
};

/**
 * @returns {Boolean} true if the object has been destroyed, false otherwise.
 */
VideoPlayerToolBarForPlayback.prototype.isDestroyed = function () {
  return false;
};

/**
 * Destroys the widget.  Should be called if permanently
 * removing the widget from layout.
 */
VideoPlayerToolBarForPlayback.prototype.destroy = function () {
  knockout.cleanNode(this._wrapper);
  this._container.removeChild(this._wrapper);
  return destroyObject(this);
};

export default VideoPlayerToolBarForPlayback;
