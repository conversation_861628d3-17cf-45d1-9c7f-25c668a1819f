import request from "@/router/axios";
import md5 from "js-md5";
import jsonToUnderline from "@/utils/helper/jsonToUnderline";
import jsonToHump from "@/utils/helper/jsonToHump";
import formatPaginationParamNew from "@/utils/helper/formatPaginationParamNew";

export function pagination(queryParams) {
  queryParams.current = queryParams.page + 1;
  queryParams.size = queryParams.size;
  const { current, size, account } = queryParams;
  const url = account ? `&account=${account}` : "";
  return new Promise((resolve, reject) => {
    request
      .get(`/vdm-inter-manager/user/list?current=${current}&size=${size}${url}`)
      .then((res) => {
        let resHump = jsonToHump(res.data);
        let result = {
          code: resHump.code,
          msg: resHump.msg,
          data: {
            content: resHump.data.records,
            total: resHump.data.total,
          },
        };
        console.log(result, res);
        resolve(result);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

export function detail(id) {
  return new Promise((resolve, reject) => {
    request
      .post(`/vdm-inter-manager/user/detail/${id}`)
      .then((res) => {
        let resHump = jsonToHump(res);
        let result = {
          code: resHump.code,
          msg: resHump.msg,
          data: resHump.data.resData,
        };
        resolve(result);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

export function del(ids) {
  const formatIds = ids.join(",");
  return new Promise((resolve, reject) => {
    request
      .get(`/vdm-inter-manager/user/remove`, {
        params: {
          ids: formatIds,
        },
      })
      .then((res) => {
        resolve(res);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

export function add({ password, ...row }) {
  return new Promise((resolve, reject) => {
    request
      .post("/vdm-inter-manager/user/save", {
        ...row,
        password: md5(password),
      })
      .then((res) => {
        let resHump = jsonToHump(res);
        resolve(resHump);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

export function edit(queryParams) {
  return new Promise((resolve, reject) => {
    request
      .post("/vdm-inter-manager/user/update", queryParams)
      .then((res) => {
        let resHump = jsonToHump(res);
        resolve(resHump);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

export const getUser = (id) => {
  return request({
    url: "/vdm-inter-manager/user/detail",
    method: "get",
    params: {
      id,
    },
  });
};

export const resetPassword = ({ userIds, password }) => {
  return request({
    url: "/vdm-inter-manager/user/resetPassword",
    method: "get",
    params: {
      userIds,
      password: md5(password),
    },
  });
};

export const freeze = (userIds) => {
  return request({
    url: "/vdm-inter-manager/user/freeze",
    method: "get",
    params: {
      userIds,
    },
  });
};

export const unfreeze = (userIds) => {
  return request({
    url: "/vdm-inter-manager/user/unfreeze",
    method: "get",
    params: {
      userIds,
    },
  });
};

export const interAuth = (userIds, interfaces) => {
  return request({
    url: "/vdm-inter-manager/user/interAuth",
    method: "get",
    params: {
      userIds,
      interfaces,
    },
  });
};

export const bindDept = (userIds, deptIds) => {
  return request({
    url: "/vdm-inter-manager/user/bindDept",
    method: "get",
    params: {
      userIds,
      deptIds,
    },
  });
};

export const bindAlarmRule = (userIds, ruleIds) => {
  return request({
    url: "/vdm-inter-manager/user/bindAlarmRule",
    method: "get",
    params: {
      userIds,
      ruleIds,
    },
  });
};

export const bindList = (params) => {
  return request({
    url: "/vdm-inter-manager/interface/interfaces",
    method: "get",
    params
  });
};

export const bindedList = (params) => {
  return request({
    url: "/vdm-inter-manager/interface/getInterfaceAuth",
    method: "get",
    params
  });
}

export const inAndOut = (params) => {
  return request({
    url: "/vdm-inter-manager/interface/updateUserInterface",
    method: "get",
    params
  });
}

export const getInterfaceAuth = (userId) => {
  return request({
    url: "/vdm-inter-manager/interface/getInterfaceAuth",
    method: "get",
    params: {
      userId,
    },
  });
};
export const resetPwd = () => {
  return request({
    url: '/vdm-inter-manager/interManager/sminterfacesystem/resetPosKey',
    method: "get",
  })
}
export default {
  add,
  edit,
  del,
  pagination,
  detail,
};
