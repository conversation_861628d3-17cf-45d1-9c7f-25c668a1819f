import request from '../utils/request'; // FIXME 真实联调的时候请更换到request
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import jsonToHump from '@/utils/helper/jsonToHump';
import formatPaginationParamNew from '@/utils/helper/formatPaginationParamNew';

/**
 * @typedef Driver 司乘人员
 * @property {String} name 司乘人员姓名
 * @property {String} idcard 司乘人员身份证号
 * @property {Int} driverType 司乘人员类别
 * @property {Int} deptId 司乘人员车组ID
 * @property {String} deptName 司乘人员车组名称
 * @property {String} phone 司乘人员联系电话
 * @property {String} certId 司乘人员从业资格证ID
 * @property {String} certAuth 从业资格证发证机构
 */

/**
 * 司乘人员分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @param {String} [queryParams.driverName] 【司乘名称】模糊搜索
 * @returns {Promise}
 */
export function pagination(queryParams) {
  queryParams.current = queryParams.page + 1;
  queryParams.size = queryParams.size;
  // queryParams = formatPaginationParamNew(queryParams);
  const {current, size, sort, page, ... params} = queryParams
  return new Promise((resolve, reject) => {
    request.post(`/vdm-base-info/base/container/list?current=${current}&size=${size}`, params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.records,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 司乘人员新增
 * @param {Object} queryParams
 * @param {String} queryParams.driverName 司乘人员姓名
 * @param {String} queryParams.driverIdcard 司乘人员身份证号
 * @param {Int} queryParams.driverType 司乘人员类别
 * @param {Int} queryParams.deptId 司乘人员车组ID
 * @param {Int} queryParams.driverPhone 司乘人员联系电话
 * @param {Int} queryParams.certId 司乘人员从业资格证ID
 * @param {Int} queryParams.certAuth 从业资格证发证机构
 * @return {Promise<any>}
 */
export function add(queryParams) {
  // queryParams.vehicleType = queryParams.vehicleType ? queryParams.vehicleType.join() : undefined;
  // queryParams.certificateType = queryParams.certificateType ? queryParams.certificateType.join() : undefined;
  // let params = jsonToUnderline(queryParams);
  console.log(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/base/container/save', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 司乘人员删除
 * @param {Number[]} ids 主键
 * @return {Promise<any>}
 */
export function del(ids) {
  const formatIds = ids.join(',');
  return new Promise((resolve, reject) => {
    request.get(`/vdm-base-info/base/container/delete`, {
      params: {
        ids: formatIds
      }
    }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 司乘人员修改
 * @param {Object} queryParams
 * @param {Int} queryParams.id 主键
 * @param {String} queryParams.driverName 司乘人员姓名
 * @param {String} queryParams.driverIdcard 司乘人员身份证号
 * @param {Int} queryParams.driverType 司乘人员类别
 * @param {Int} queryParams.deptId 司乘人员车组ID
 * @param {String} queryParams.driverPhone 司乘人员联系电话
 * @param {String} queryParams.certId 司乘人员从业资格证ID
 * @param {String} queryParams.certAuth 从业资格证发证机构
 * @return {Promise<any>}
 */
export function edit(queryParams) {

  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/base/container/update', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 司乘人员详情
 * @param {Number} id 主键
 * @return {Promise<any>}
 */
export function detail(id) {
  return new Promise((resolve, reject) => {
    request.post(`/vdm-base-info/base/container/${id}`).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data.resData
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 司乘人员导出
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中,data为文件完整下载路径
 * @returns {Promise<Result>}
 */
export function exportAll(queryParams) {
  queryParams.current = queryParams.page+1;
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/base/container/export', queryParams).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 获取司乘绑定的车辆
 * @param {Object} queryParams
 * @param {Int} queryParams.id 司乘ID
 * @param {Int} queryParams.licencePlate 车牌号码
 * @param {Int} queryParams.deptId 车组ID
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function driverBindVehicleDetail(queryParams) {
  let id = queryParams.id;
  return new Promise((resolve, reject) => {
    request.get(`/vdm-base-info/base/container/terminalInfo/${id}`).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 获取司乘绑定的车辆列表
 * @param {Object} queryParams
 * @param {Int} queryParams.id 司乘ID
 * @param {Int} queryParams.licencePlate 车牌号码
 * @param {Int} queryParams.deptId 车组ID
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function driverBindVehicleDetailList(queryParams) {
  let params = { terminalId: queryParams.terminalId };
  return new Promise((resolve, reject) => {
    request.post(`/vdm-base-info/base/container/selectBind?id=${queryParams.id}`, { params: params }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 获取司乘未绑定的车辆
 * @param {Object} queryParams
 * @param {Int} queryParams.id 司乘ID
 * @param {Int} queryParams.licencePlate 车牌号码
 * @param {Int} queryParams.deptId 车组ID
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function driverUnbindVehicleDetail(queryParams) {
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/vdm-base-info/base/container/select`, queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 批量导入人员
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function addbatch(queryParams, importDeptId) {
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/base/container/importExcel?deptId='+importDeptId, queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 获取终端类别
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function deviceType() {
  return new Promise((resolve, reject) => {
    request.get('/vdm-base-info/person/worker/deviceType').then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function unbind({id, targetType, deptId}) {
  return new Promise((resolve, reject) => {
    request.get(`/vdm-base-info/base/container/unbind?id=${id}&targetType=${targetType}&deptId=${deptId}`).then(res => {
      let resHump = jsonToHump(res);
      // console.log('resHump', resHump)
      resolve(resHump.data?.resData || []);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 批量修改组织
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function updateDeptBatch(queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/base/container/batchUpdate', queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default {
  add,
  edit,
  updateDeptBatch,
  del,
  pagination,
  detail,
  exportAll,
  addbatch,
  driverBindVehicleDetailList,
  deviceType
};
