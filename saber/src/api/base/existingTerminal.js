import request from '../utils/request';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import jsonToHump from '@/utils/helper/jsonToHump';
import axios from 'axios';
import website from '@/config/website';
import { getToken } from '@/util/auth';

/**
 * 存量终端管理分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 */
export function pagination(queryParams) {
  queryParams.current = queryParams.page + 1;
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/device/existingTerminal/list', queryParams).then(res => {
      // TODO 临时解决方案, 用户提出的问题, 如果当前页码大于总页数, 则请求最后一页数据
      if (res.data.pages && res.data.pages < queryParams.current) {
        request.post('/vdm-base-info/device/existingTerminal/list', {
          ...queryParams,
          current: res.data.pages
        }).then(response => {
          let resHump = jsonToHump(response);
          let result = {
            code: resHump.code,
            msg: resHump.msg,
            data: {
              content: resHump.data.records,
              total: resHump.data.total
            }
          };
          resolve(result);
        }).catch((error) => {
          reject(error);
        });
      }
      else {
        let resHump = jsonToHump(res);
        let result = {
          code: resHump.code,
          msg: resHump.msg,
          data: {
            content: resHump.data.records,
            total: resHump.data.total
          }
        };
        resolve(result);
      }
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 存量终端新增
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function add(queryParams) {
  return new Promise((resolve, reject) => {
    request.post('vdm-base-info/device/existingTerminal/save', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 存量终端删除
 * @param {Number} ids
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function del(ids) {
  return new Promise((resolve, reject) => {
    request.post(`vdm-base-info/device/existingTerminal/batchDelete`, ids).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 存量终端修改
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function edit(queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/device/existingTerminal/update', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}


/**
 * 批量导入存量终端
 * @param {formData} formData
 * @returns {Promise<Result>}
 */
export function addbatch(formData) {
  console.log(formData);
  return new Promise((resolve, reject) => {
    fetch('/bdsPlatformApi/vdm-base-info/device/existingTerminal/import', {
      method: 'POST',
      headers: {
        [website.tokenHeader]: 'bearer ' + getToken()
      },
      body: formData
    })
      .then(response => response.json())
      .then(data => {
        let resHump = jsonToHump(data);
        let result = {
          code: resHump.code,
          msg: resHump.msg,
          data: resHump.data
        };
        resolve(result);
      })
      .catch(error => reject(error));
  });
}

/**
 * 批量导出存量终端
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function exportAll(queryParams) {
  console.log('exportAll', queryParams);
  queryParams.current = queryParams.page + 1;
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/device/existingTerminal/export', queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 详情
 * @param {Vehicle} id
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function toDetail(id) {
  return new Promise((resolve, reject) => {
    request.get(`vdm-base-info/device/existingTerminal/queryById?id=${id}`).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 应用方向列表
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function listMainDeviceClasses(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/vdm-base-info/device/existingTerminal/listMainDeviceClasses', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 终端类别列表
 * @param {Vehicle} code
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function listSubClassesByMainClass(code) {
  return new Promise((resolve, reject) => {
    request.get(`/vdm-base-info/device/existingTerminal/listSubClassesByMainClass?code=${code}`).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}
export default {
  pagination,
  add,
  edit,
  del,
  addbatch,
  exportAll,
  toDetail,
};
