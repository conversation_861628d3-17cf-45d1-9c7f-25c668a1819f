import request from '../utils/request';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import jsonToHump from '@/utils/helper/jsonToHump';
import formatPaginationParams from '@/utils/helper/formatPaginationParams';

/**
 * 基础设施管理分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  queryParams = formatPaginationParams(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/vdm-base-info/base/facility/list`, queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.records,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 基础设施新增
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function add (queryParams) {
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/base/facility/save', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 基础设施删除
 * @param {Number} id
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function del (id) {
  let params = {
    ids: id.join(',')
  };
  return new Promise((resolve, reject) => {
    request.get('/vdm-base-info/base/facility/delete', {params: params}).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 基础设施修改
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function edit (queryParams) {
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/base/facility/update', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 基础设施导出
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中,data为文件完整下载路径
 * @returns {Promise<Result>}
 */
export function exportAll (queryParams) {
  queryParams.current = queryParams.page+1;
  queryParams = formatPaginationParams(queryParams);
  const { headNameList, ...params} = queryParams;
  const list = headNameList.filter( item => item!=='绑定终端');
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/base/facility/export', {...params, headNameList: list}).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}



/**
 * 批量导入基础设施
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function addbatch (queryParams) {
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/base/facility/importExcel', queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 查看绑定终端
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function terminalInfo (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/vdm-base-info/base/facility/terminalInfo', {params: params}).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 未绑定终端列表
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.current=1] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 */
export function facilitySelect (queryParams) {
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/vdm-base-info/base/facility/selectNoBind`, queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data?.records || [],
          total: resHump.data?.total || 0
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 已绑定终端列表
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.current=1] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 */
export function facilitySelectBind (queryParams) {
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/vdm-base-info/base/facility/selectBind?id=${queryParams.id}`, queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data || [],
          total: resHump.data.length
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 绑定终端
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function facilityConnect (queryParams, deptId) {
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/vdm-base-info/base/facility/connect?id=${queryParams.id}&deptId=${deptId}`, queryParams.terminalList).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 获取终端类别
 * @param {Vehicle} queryParams
 * @returns {Promise<Result>}
 */
export function queryDeviceType () {
  return new Promise((resolve, reject) => {
    request.get('/vdm-base-info/base/facility/deviceType').then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { pagination, add, edit, del, exportAll, addbatch, terminalInfo, facilitySelect, facilitySelectBind, facilityConnect, queryDeviceType };
