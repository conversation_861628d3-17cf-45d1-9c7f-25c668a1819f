import request from '../utils/request'; // FIXME 真实联调的时候请更换到request，假数据可用requestMock
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import jsonToHump from '@/utils/helper/jsonToHump';
import defaultValue from '@/utils/core/defaultValue';
import formatPaginationParams from '@/utils/helper/formatPaginationParams';
import formatPaginationParamNew from '@/utils/helper/formatPaginationParamNew';

/**
 * 车辆分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @typedef {{total: Number, content: Array.<Vehicle>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  const current = queryParams.page + 1;
  const size = queryParams.size;
  queryParams = formatPaginationParamNew(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/vdm-base-info/precision/assembly/list?current=${current}&size=${size}`, queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.records,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车辆新增
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function add (queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/precision/assembly/save', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车辆删除
 * @param {Number} id
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function del (id) {
  return new Promise((resolve, reject) => {
    request.get(`/vdm-base-info/precision/assembly/delete?ids=${id}`).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车辆修改
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function edit (queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/precision/assembly/update', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车辆详情
 * @param {Int} id
 * @typedef {{code: Number, msg: String, data: Vehicle}} Result
 * @returns {Promise<Result>}
 */
export function detail (id) {
  return new Promise((resolve, reject) => {
    request.get('-wrapper/precision/assembly/detail', {
      params: {
        id: id
      }
    }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}



/**
 * 车辆信息导出
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中,data为文件完整下载路径
 * @returns {Promise<Result>}
 */
export function exportAll (queryParams) {
  // queryParams = formatPaginationParams(queryParams);
  queryParams.current = queryParams.page+1;
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/precision/assembly/export', queryParams).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function addbatch (queryParams, importDeptId) {
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/precision/assembly/importExcel?deptId='+importDeptId, queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function unbind({id, targetType, deptId}) {
  return new Promise((resolve, reject) => {
    request.get(`/vdm-base-info/precision/assembly/unbind?id=${id}&targetType=${targetType}&deptId=${deptId}`).then(res => {
      let resHump = jsonToHump(res);
      // console.log('resHump', resHump)
      resolve(resHump.data?.resData || []);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 批量修改组织
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function updateDeptBatch(queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/precision/assembly/batchUpdate', queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default {
  add,
  edit,
  del,
  pagination,
  updateDeptBatch,
  exportAll,
};
