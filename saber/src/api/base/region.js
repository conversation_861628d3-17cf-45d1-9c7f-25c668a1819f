import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import formatPaginationParam from '@/utils/helper/formatPaginationParam';

/**
 * @typedef Region 区域实体
 *
 * @property {Int} id 区域ID
 *
 * @property {Int} regionName 告警类型
 * @property {Int} regionAlias 提醒方式 前端自定义
 * @property {Int} regionType 区域类型 Enumerate_RegionType
 * @property {String} attribute 区域属性
 * @property {String} remark 备注
 * @property {Array.<Point>} polygon_points 区域点
 */

/**
 * @typedef Point
 * @property {Number} longitude 经度（°）
 * @property {Number} latitude 纬度（°）
 */

/**
 * 区域分页
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.regionName] 区域名称
 * @typedef {{total: Number, content: Array.<Region>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  queryParams = formatPaginationParam(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo/region/list', {params: params}).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 区域新增
 * @param {Region} queryParams
 * @typedef {{code: Number, msg: String, data: Int}} Result 其中，data包含了告警规则ID
 * @returns {Promise<Result>}
 */
export function add (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/baseinfo/region/add', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 区域删除
 * @param {Number} id 主键
 * @typedef {{code: Number, msg: String, data: null}} Result
 * @returns {Promise<Result>}
 */
export function del (id) {
  return new Promise((resolve, reject) => {
    request.post('/baseinfo/region/delete', {
      id: id
    }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 区域修改
 * @param {Region} queryParams
 * @typedef {{code: Number, msg: String, data: null}} Result
 * @returns {Promise<Result>}
 */
export function edit (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/baseinfo/region/edit', params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 区域详情
 * @param {Number} id 主键
 * @typedef {{code: Number, msg: String, data: Region}} Result
 * @returns {Promise<Result>}
 */
export function detail (id) {
  return new Promise((resolve, reject) => {
    request.get('/baseinfo/region/detail', {
      params: {
        id: id
      }
    }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 区域导出
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中,data为文件完整下载路径
 * @returns {Promise<Result>}
 */
export function exportAll (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/baseinfo/region/export', {params: params}).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 获取区域分类列表(新)
 * @param
 * @typedef {{code: Number, msg: String, data: Region}} Result
 * @returns {Promise<Result>}
 */
export function regionGroups () {
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/region/groups').then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { add, edit, del, pagination, detail, exportAll, regionGroups };
