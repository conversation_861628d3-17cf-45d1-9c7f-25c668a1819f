import request from '../utils/request'; // FIXME 真实联调的时候请更换到request，假数据可用requestMock
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import jsonToHump from '@/utils/helper/jsonToHump';
import defaultValue from '@/utils/core/defaultValue';
import formatPaginationParams from '@/utils/helper/formatPaginationParams';
/**
 * 车辆分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @typedef {{total: Number, content: Array.<Vehicle>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  queryParams = formatPaginationParams(queryParams);
  const { size, current, ...params } = queryParams
  return new Promise((resolve, reject) => {
    request.post(`/vdm-base-info/mining/truck/list?size=${size}&current=${current}`, params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.records,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 获取全部车辆
 * @param {Object} [queryParams]
 * @typedef {{total: Number, content: Array.<Vehicle>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function all (queryParams) {
  queryParams = defaultValue(queryParams, {});
  queryParams.start = 0;
  queryParams.count = 9999;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/mining/truck/get', {params: params}).then(res => {
      let resHump = jsonToHump(res);
      // console.log('resHump', resHump)
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车辆新增
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function add (queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/mining/truck/save', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车辆删除
 * @param {Number} id
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function del (id) {
  return new Promise((resolve, reject) => {
    request.get(`/vdm-base-info/mining/truck/delete?ids=${id}`).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车辆修改
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function edit (queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/mining/truck/update', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车辆详情
 * @param {Int} id
 * @typedef {{code: Number, msg: String, data: Vehicle}} Result
 * @returns {Promise<Result>}
 */
export function detail (id) {
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/mining/truck/detail', {
      params: {
        id: id
      }
    }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 查询多个车辆的详情
 * @param {Array.<Int>|String} multiIds
 * @typedef {{code: Number, msg: String, data: Array.<Vehicle>}} Result
 * @returns {Promise<Result>}
 */
export function multiDetail (multiIds) {
  if (typeof multiIds === 'object') {
    multiIds = multiIds.join(',');
  }
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/mining/truck/moredetail', {
      params: {
        ids: multiIds
      }
    }).then(res => {
      let resHump = jsonToHump(res);
      console.log(9999, resHump);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });

  // return new Promise((resolve, reject) => {
  //   let multiDetails = [];
  //   for (let i = 0; i < multiIds.length; i++) {
  //     multiDetails.push(new Promise((resolve, reject) => {
  //       request.get('/mining/truck/detail', {
  //         params: {
  //           id: multiIds[i]
  //         }
  //       }).then(res => {
  //         let resHump = jsonToHump(res);
  //         resolve(resHump.data);
  //       }).catch((error) => {
  //         reject(error);
  //       });
  //     }));
  //   }
  //   Promise.all(multiDetails).then(dataArray=>{
  //     let result = {
  //       code: 0,
  //       msg: '',
  //       data: dataArray
  //     };
  //     resolve(result);
  //   }).catch(error => {
  //     reject(error);
  //   })
  // });
}

/**
 * 查询某车辆里程统计
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function searchCarMileage (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/security-wrapper/securitymanagement/mileage/querylatelymileagebyvehicle', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data.resData
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 查询某车辆告警统计
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function searchCarAlarm (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/security-wrapper/securitymanagement/historicalalarm/querylatelyalarmbyvehicle', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data.resData
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 查询车辆归属
 * @param {Int}
 * @typedef {{code: Number, msg: String, data: Vehicle}} Result
 * @returns {Promise<Result>}
 */
export function searchVehicleOwnerId () {
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/thirdplatform/select', {
      params: {
        is_top: 1
      }
    }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车辆信息导出
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中,data为文件完整下载路径
 * @returns {Promise<Result>}
 */
export function exportAll (queryParams) {
  // queryParams = formatPaginationParams(queryParams);
  queryParams.current = queryParams.page+1;
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/mining/truck/export', queryParams).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车辆不在线导出
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中,data为文件完整下载路径
 * @returns {Promise<Result>}
 */
export function offlineExport (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/mining/truck/offline/export', {params: params}).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 树结构，没有通道号
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: Object}} Result 其中,data.resData为树结构数组
 * @returns {Promise<Result>}
 */
export function tree (queryParams) {
  queryParams = defaultValue(queryParams, {});
  // queryParams = autoInsertParamDeptId(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/mining/truck/tree', {params: params}).then(res => {
      // console.log('/mining/truck/tree', res);
      let resHump = jsonToHump(res);
      // console.log('resHump', resHump)
      resolve(resHump.data?.resData || []);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 树结构，带有通道号
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: Object}} Result 其中,data.resData为树结构数组
 * @returns {Promise<Result>}
 */
export function treeWithChannel (queryParams) {
  queryParams = defaultValue(queryParams, {});
  // queryParams = autoInsertParamDeptId(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/mining/truck/rnsstree/channel', {params: params}).then(res => {
      let resHump = jsonToHump(res);
      // console.log('resHump', resHump)
      resolve(resHump.data?.resData || []);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 树结构(公务车)
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: Object}} Result 其中,data.resData为树结构数组
 * @returns {Promise<Result>}
 */
export function officialTree (queryParams) {
  queryParams.unitCode = queryParams.unitCode ? queryParams.unitCode : undefined;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/mining/truck/treegwc', {params: params}).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump.data?.resData || []);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车牌号模糊检索
 * @param {*} queryParams
 * @returns {Promise<Result>}
 */
export function searchVehicle (queryParams, id) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/mining/truck/match', { params: params }).then(res => {
      let resHump = jsonToHump(res);
      resolve(id === 'id' ? resHump.data.list : resHump.data.resData);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 批量导入车辆
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function addbatch (queryParams) {
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/mining/truck/importExcel', queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 终端手机号/唯一性编码 模糊检索
 * @param {*} queryParams
 * @returns {Promise<Result>}
 */
export function queryPhone (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/device/queryterminalphoneblur', { params: params }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 外设序列号模糊检索
 * @param {*} queryParams
 * @returns {Promise<Result>}
 */
export function queryDeviceSerial (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/device/querydeviceserialblur', { params: params }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 获取上级平台
 * @param {Object} queryParams
 * @param {Array} [queryParams.id]
 * @returns {Promise<Result>}
 */
export function getPlatformList () {
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/thirdplatform/select', {
      params: {
        is_top: 1
      }
    }).then(res => {
      let resHump = jsonToHump(res);
      let list = [];
      Object.keys(resHump.data).forEach((item)=>{
        let form = {
          label: resHump.data[item],
          value: item
        };
        list.push(form);
      });
      resolve(list);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function getRuleBindRealtime() {
  return new Promise((resolve, reject) => {
    request.get('/security-wrapper/securitymanagement/rulebindrealtime').then(res => {
      resolve(res.data);
    }).catch((error) => {
      reject(error);
    });
  });
}
export function pushRule(data){
  return new Promise((resolve, reject) => {
    request.post('/security-wrapper/securitymanagement/pushrule', data).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}
// export function getPlatformList (queryParams) {
//   // let params = jsonToUnderline(queryParams);
//   return new Promise((resolve, reject) => {
//     request.get('/regulatorycenter/platform', {params: queryParams}).then(res => {
//       let result = res.data.res_data.map(item => {
//         return {
//           value: item.id,
//           label: item.name
//         };
//       });
//       resolve(result);
//     }).catch((error) => {
//       reject(error);
//     });
//   });
// }

/**
 * 获取单位树结构
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function deptTree () {
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/baseinfo/dept/getdepttree').then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 名称模糊检索
 * @param {*} queryParams
 * @returns {Promise<Result>}
 */
export function searchName (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/mining/truck/matchdevice', { params: params }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump.data);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function rnssTree (queryParams) {
  queryParams = defaultValue(queryParams, {});
  // queryParams = autoInsertParamDeptId(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/mining/truck/tree/rnss', {params: params}).then(res => {
      let resHump = jsonToHump(res);
      // console.log('resHump', resHump)
      resolve(resHump.data?.resData || []);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function rnssChannelTree (queryParams) {
  queryParams = defaultValue(queryParams, {});
  // queryParams = autoInsertParamDeptId(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/baseinfo-wrapper/mining/truck/tree/channelrnss', {params: params}).then(res => {
      let resHump = jsonToHump(res);
      // console.log('resHump', resHump)
      resolve(resHump.data?.resData || []);
    }).catch((error) => {
      reject(error);
    });
  });
}


export function unbind({id, targetType, deptId}) {
  return new Promise((resolve, reject) => {
    request.get(`/vdm-base-info/mining/truck/unbind?id=${id}&targetType=${targetType}&deptId=${deptId}`).then(res => {
      let resHump = jsonToHump(res);
      // console.log('resHump', resHump)
      resolve(resHump.data?.resData || []);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default {
  add,
  edit,
  del,
  pagination,
  detail,
  multiDetail,
  searchCarMileage,
  searchCarAlarm,
  all,
  exportAll,
  tree,
  treeWithChannel,
  officialTree,
  searchVehicleOwnerId,
  searchVehicle,
  addbatch,
  getPlatformList,
  queryPhone,
  queryDeviceSerial,
  getRuleBindRealtime,
  pushRule,
  deptTree
};
