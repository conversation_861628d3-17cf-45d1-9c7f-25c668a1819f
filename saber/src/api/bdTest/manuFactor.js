import request from '../utils/request';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import jsonToHump from '@/utils/helper/jsonToHump';
import formatPaginationParamNew from '@/utils/helper/formatPaginationParamNew';

/**
 * 委托企业管理
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 */
export function pagination(queryParams) {
  const current = queryParams.page + 1;
  const size = queryParams.size;
  queryParams = formatPaginationParamNew(queryParams);
  return new Promise((resolve, reject) => {
    request.get(`/vdm-bd-check/bd-check/bdcmanufactor/list?current=${current}&size=${size}`, { params: queryParams }).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data?.records || [],
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 委托企业新增
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function add(queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/vdm-bd-check/bd-check/bdcmanufactor/save', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 委托企业删除
 * @param {Number} ids
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function del(ids) {
  const formatIds = ids.join(',');
  return new Promise((resolve, reject) => {
    request.post(`/vdm-bd-check/bd-check/bdcmanufactor/remove?ids=${formatIds}`).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 委托企业修改
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function edit(queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/vdm-bd-check/bd-check/bdcmanufactor/update', queryParams).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}


/**
 * 批量导入委托企业
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function addbatch(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/baseinfo-wrapper/baseinfo/device/addbatchofflinerecord', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data.resData
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 导出
 * @param {Object} queryParams
 * @returns {Promise<Result>}
 */
export function exportAll(queryParams) {
  // let params = jsonToUnderline(queryParams);
  queryParams.sort = undefined;
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/assessment/conndisconnectdetails', queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default {
  pagination,
  add,
  edit,
  del,
  addbatch,
  exportAll
};
