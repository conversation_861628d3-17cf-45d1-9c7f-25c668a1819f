import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import formatPaginationParam from '@/utils/helper/formatPaginationParam';

/**
 *   北斗实时检测统计报告
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @param {String} [queryParams.blurry] 模糊搜索
 * @typedef {{total: Number, content: Array.<Job>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination(queryParams) {
  queryParams.sort = undefined;
  queryParams.current = queryParams.page + 1;
  queryParams.page = undefined;
  queryParams.deptIdList = queryParams?.deptId ? [queryParams.deptId] : undefined;
  queryParams.deptId = undefined;
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/vdm-bd-check/bdCheck/real/realStat?size=${queryParams.size}&current=${queryParams.current}`, queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump?.data?.records || [],
          total: resHump?.data?.total || []
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 *   北斗实时检测-最近识别情况
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @typedef {{total: Number, content: Array.<Job>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function recentlyPagination(queryParams) {
  return new Promise((resolve, reject) => {
    console.log('-> queryParams recently', queryParams);
    queryParams.sort = undefined;
    queryParams.current = queryParams.page + 1;
    queryParams.page = undefined;
    queryParams.time = undefined;
    // let params = jsonToUnderline(queryParams);
    request.get(`/vdm-bd-check/bdCheck/real/newest`, { params: queryParams }).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.records,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 *   北斗实时检测-识别非北斗信息
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @typedef {{total: Number, content: Array.<Job>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function noBDPagination(queryParams) {
  return new Promise((resolve, reject) => {
    queryParams.sort = undefined;
    queryParams.current = queryParams.page + 1;
    queryParams.page = undefined;
    console.log('-> queryParams noBd', queryParams);
    // let params = jsonToUnderline(queryParams);
    request.get(`/vdm-bd-check/bdCheck/real/nonBD?deviceNum=${queryParams.deviceNum}&size=${queryParams.size}&current=${queryParams.current}`).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.records,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default {
  pagination,
  recentlyPagination,
  noBDPagination
};
