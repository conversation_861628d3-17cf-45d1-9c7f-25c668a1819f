import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import formatPaginationParam from '@/utils/helper/formatPaginationParam';
/**
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @param {String} [queryParams.blurry] 模糊搜索
 * @typedef {{total: Number, content: Array.<Announcement>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  queryParams.current = queryParams.page + 1;
  queryParams.page = undefined;
  /* queryParams.startTime = queryParams.startTime ? moment(queryParams.startTime).unix() : moment(new Date() - 3600 * 1000).unix();
  queryParams.endTime = queryParams.endTime ? moment(queryParams.endTime).unix() : moment(new Date()).unix();
  queryParams.deptId = queryParams.deptId ? queryParams.deptId : 1; */
  queryParams.sort = undefined;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/querybbs', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 新增
 * @param {Object} queryParams
 * @param {Array} [queryParams.deptIds] 公告的单位
 * @param {String} [queryParams.title] 公告的标题
 * @param {String} [queryParams.context] 公告的内容
 * @param {String} [queryParams.attachmentImg] 公告附件
 * @returns {Promise<Result>}
 */
export function add (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/publishbbs', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 详情
 * @param {Object} queryParams
 * @param {Array} [queryParams.id] 公告id
 * @returns {Promise<Result>}
 */
export function detail (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/regulatorycenter-wrapper/regulatorycenter/querybbsdetail', { params: params }).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 编辑
 * @param {Object} queryParams
 * @param {Array} [queryParams.id] 公告的单位id
 * @param {Array} [queryParams.deptIds] 公告的单位
 * @param {String} [queryParams.title] 公告的标题
 * @param {String} [queryParams.context] 公告的内容
 * @param {String} [queryParams.attachmentImg] 公告附件
 * @returns {Promise<Result>}
 * @returns {Promise<Result>}
 */
export function edit (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.put('/regulatorycenter-wrapper/regulatorycenter/updatebbs', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 删除
 * @param {Object} queryParams
 * @param {Array} [queryParams.id] 公告id
 * @returns {Promise<Result>}
 */
export function del (queryParams) {
  return new Promise((resolve, reject) => {
    request({
      url: '/regulatorycenter-wrapper/regulatorycenter/deletebbs',
      method: 'delete',
      data: {ids: queryParams}
    }).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 导出
 * @param {Object} queryParams
 * @param {Number} [queryParams.receive] 发布公告：0，接收公告：1
 * @returns {Promise<Result>}
 */
export function exportAll (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/exportbbs', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { pagination, add, del, edit, detail, exportAll };
