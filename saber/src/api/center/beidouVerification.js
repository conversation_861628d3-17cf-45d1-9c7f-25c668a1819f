import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';

/**
 * @name: 单终端定位查询
 * @param {Object} queryParams
 * @param {String} [queryParams.deviceNum] 赋码编号
 * @typedef {{code: Number, msg: String, data: Object}} PaginationResult
 * @return {Promise<PaginationResult>}
 */
export function queryTerminalIdentification (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars/vehicle/plot', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 终端北斗识别 在线和全部终端数量
 * @typedef {{code: Number, msg: String, data: Array.<District>}} Result
 * @returns {Promise<Result>}
 */
export function getTerminalNumInfo() {
  return new Promise((resolve, reject) => {
    request.get('/vdm-bd-check/bdCheck/terminal/terminalCount').then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 终端北斗识别 在线和全部终端数量(新)
 * @typedef {{code: Number, msg: String, data: Array.<District>}} Result
 * @returns {Promise<Result>}
 */
export function terminalNumInfoNew() {
  return new Promise((resolve, reject) => {
    request.get('/vdm-base-info/home/<USER>/terminalAllCountAndOnlineCount').then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 获取北斗识别ws验证码
 * @returns {Promise<unknown>}
 */
export function getAuthCodeForBD() {
  return new Promise((resolve, reject) => {
    request.get('/vdm-bd-check/bdCheck/authWS/authCode').then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function getBDSatelliteData() {
  return new Promise((resolve, reject) => {
    request.get('/vdm-bd-check/satellite/getSatelliteData').then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function checkDeviceOnlineState(deviceNum){
  return new Promise((resolve, reject) => {
    request.get('/vdm-bd-check/check/checkDeviceOnlineState', {
      params: {
        deviceNum
      }
    }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}
