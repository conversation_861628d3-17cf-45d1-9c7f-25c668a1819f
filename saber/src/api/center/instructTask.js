import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToHumpParam from '@/utils/helper/jsonToHumpParam';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import jsonToUnderlineParam from '@/utils/helper/jsonToUnderlineParam';
import moment from 'moment';
const JSONbig = require('json-bigint');

/**
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @param {String} [queryParams.type] 指令类型
 * @param {String} [queryParams.licensePlate] 车牌号码
 * @typedef {{total: Number, content: Array.<Announcement>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */

export function pagination (queryParams) {
  queryParams.current = queryParams.page + 1;
  queryParams.sort = undefined;
  queryParams.page = undefined;
  queryParams.startTime = queryParams.startTime ? (moment(queryParams.startTime).unix()) : null;
  queryParams.endTime = queryParams.endTime ? (moment(queryParams.endTime).unix()) : null;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/querycommandtask', params).then(res => {
      let resHump = jsonToHumpParam(res, 'param');
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 添加
 * @param {RegionAlarm} queryParams
 * @typedef {{code: Number, msg: String, data: Int}} Result 其中，data包含了告警规则ID
 * @returns {Promise<Result>}
 */
export function add (queryParams) {
  queryParams.startTime = queryParams.startTime / 1000;
  queryParams.endTime = queryParams.endTime / 1000;
  queryParams.vehicleState = queryParams.vehicleState.toString();
  queryParams.devicesInfo = queryParams.devicesInfo.map(item => {
    return {
      deviceType: item.deviceType,
      deviceId: BigInt(item.deviceId)
    };
  });
  Object.keys(queryParams['param']).forEach((key) => {
    if (queryParams['param'][key] === '') {
      queryParams['param'][key] = undefined;
    }
  });
  queryParams['param'] = JSON.stringify(queryParams['param']);
  let params = JSONbig.stringify(jsonToUnderlineParam(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/commandtask', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 编辑
 * @param {DrivingNight} queryParams
 * @typedef {{code: Number, msg: String, data: null}} Result
 * @returns {Promise<Result>}
 */
export function edit (queryParams) {
  queryParams.startTime = queryParams.startTime / 1000;
  queryParams.endTime = queryParams.endTime / 1000;
  queryParams.vehicleState = queryParams.vehicleState.toString();
  queryParams.devicesInfo = queryParams.devicesInfo.map(item => {
    return {
      deviceType: item.deviceType,
      deviceId: BigInt(item.deviceId)
    };
  });
  Object.keys(queryParams['param']).forEach((key) => {
    if (queryParams['param'][key] === '') {
      queryParams['param'][key] = undefined;
    }
  });
  queryParams['param'] = JSON.stringify(queryParams['param']);
  let params = JSONbig.stringify(jsonToUnderlineParam(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/commandtask', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 查询指令模板
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 */

export function instruction (queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/gettemplate', queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 添加指令模板
 * @param {RegionAlarm} queryParams
 * @typedef {{code: Number, msg: String, data: Int}} Result 其中，data包含了告警规则ID
 * @returns {Promise<Result>}
 */
export function instructionAdd (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/addtemplate', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 删除
 * @param {Array} [ids] 数据id
 * @returns {Promise<Result>}
 */
export function del (ids) {
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/delcommandtask', ids).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 查询车辆7位id和串码
 * @param {RegionAlarm} queryParams
 * @typedef {{code: Number, msg: String, data: Int}} Result 其中，data包含了告警规则ID
 * @returns {Promise<Result>}
 */
export function getVehicleattribute (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/getvehicleattribute', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 查询模板参数是否可填写
 * @param {RegionAlarm} queryParams
 * @typedef {{code: Number, msg: String, data: Int}} Result 其中，data包含了告警规则ID
 * @returns {Promise<Result>}
 */
export function getParameterEdit (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/templateparamsmate', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 查询参数
 * @param {Object} queryParams
 * @returns {Promise<PaginationResult>}
 */

export function queryparamconfigtype () {
  return new Promise((resolve, reject) => {
    request.get('/regulatorycenter-wrapper/regulatorycenter/queryparamconfigtype').then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 立即执行任务
 * @param {RegionAlarm} queryParams
 * @typedef {{code: Number, msg: String, data: Int}} Result 其中，data包含了告警规则ID
 * @returns {Promise<Result>}
 */
export function execute (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/executenow', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 复制任务
 * @param {RegionAlarm} queryParams
 * @typedef {{code: Number, msg: String, data: Int}} Result 其中，data包含了告警规则ID
 * @returns {Promise<Result>}
 */
export function copy (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/taskcopy', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 任务状态修改
 * @param {RegionAlarm} queryParams
 * @typedef {{code: Number, msg: String, data: Int}} Result 其中，data包含了告警规则ID
 * @returns {Promise<Result>}
 */
export function stateEdit (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/changestate', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 查询指令参数
 * @param {Object} queryParams
 * @returns {Promise<PaginationResult>}
 */

export function queryParamConfig (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/paramconfig', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { pagination, add, edit, del, instruction, instructionAdd, getVehicleattribute, getParameterEdit, execute, copy, stateEdit, queryParamConfig };
