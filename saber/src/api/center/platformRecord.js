import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import formatPaginationParamNew from '@/utils/helper/formatPaginationParamNew';
import moment from 'moment';
/**
 * @param {Object} queryParams
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 * @returns {Promise<PaginationResult>}
 */

export function pagination (queryParams) {
  queryParams.current = queryParams.page + 1;
  queryParams.page = undefined;
  queryParams.sort = undefined;
  queryParams.startTime = queryParams.startTime ? (moment(queryParams.startTime).unix()) : null;
  queryParams.endTime = queryParams.endTime ? (moment(queryParams.endTime).unix()) : null;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/regulatorycenter-wrapper/regulatorycenter/querycommands`, params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 导出
 * @param {Object} queryParams
 * @param {String} [queryParams.type] 指令类型
 * @param {String} [queryParams.licensePlate] 车牌号码
 * @returns {Promise<Result>}
 */
export function exportAll (queryParams) {
  queryParams.startTime = queryParams.startTime ? (moment(queryParams.startTime).unix()) : null;
  queryParams.endTime = queryParams.endTime ? (moment(queryParams.endTime).unix()) : null;
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/exportcommands', queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { pagination, exportAll };
