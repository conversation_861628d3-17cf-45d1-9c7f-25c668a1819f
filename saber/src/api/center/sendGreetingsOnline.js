import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToHumpParam from '@/utils/helper/jsonToHumpParam';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import jsonToUnderlineParam from '@/utils/helper/jsonToUnderlineParam';
import moment from 'moment';
const JSONbig = require('json-bigint');

/**
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @param {String} [queryParams.type] 指令类型
 * @param {String} [queryParams.licensePlate] 车牌号码
 * @typedef {{total: Number, content: Array.<Announcement>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */

export function pagination (queryParams) {
  queryParams.current = queryParams.page;
  queryParams.sort = undefined;
  queryParams.page = undefined;
  queryParams.startTime = queryParams.startTime ? (moment(queryParams.startTime).unix()) : null;
  queryParams.endTime = queryParams.endTime ? (moment(queryParams.endTime).unix()) : null;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/queryonlinegreetingtask', params).then(res => {
      let resHump = jsonToHumpParam(res, 'param');
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 添加
 * @param {RegionAlarm} queryParams
 * @typedef {{code: Number, msg: String, data: Int}} Result 其中，data包含了告警规则ID
 * @returns {Promise<Result>}
 */
export function add (queryParams) {
  queryParams.devicesInfo = queryParams.devicesInfo.map(item => {
    return {
      deviceType: item.deviceType,
      deviceId: BigInt(item.deviceId)
    };
  });
  let params = JSONbig.stringify(jsonToUnderlineParam(queryParams));
  delete params.id;
  delete params.task_id;
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/onlinegreeting', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 编辑
 * @param {DrivingNight} queryParams
 * @typedef {{code: Number, msg: String, data: null}} Result
 * @returns {Promise<Result>}
 */
export function edit (queryParams) {
  queryParams.devicesInfo = queryParams.devicesInfo.map(item => {
    return {
      deviceType: item.deviceType,
      deviceId: BigInt(item.deviceId)
    };
  });
  let params = JSONbig.stringify(jsonToUnderlineParam(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/onlinegreeting', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 删除
 * @param {Array} [ids] 数据id
 * @returns {Promise<Result>}
 */
export function del (ids) {
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/delonlinegreetingtask', ids).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 立即执行任务
 * @param {RegionAlarm} queryParams
 * @typedef {{code: Number, msg: String, data: Int}} Result 其中，data包含了告警规则ID
 * @returns {Promise<Result>}
 */
export function execute (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/executenowonlinegreetingtask', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 任务状态修改
 * @param {RegionAlarm} queryParams
 * @typedef {{code: Number, msg: String, data: Int}} Result 其中，data包含了告警规则ID
 * @returns {Promise<Result>}
 */
export function stateEdit (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/regulatorycenter-wrapper/regulatorycenter/changeonlinegreetingtaskstate', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}


export default { pagination, add, edit, del, execute, stateEdit };
