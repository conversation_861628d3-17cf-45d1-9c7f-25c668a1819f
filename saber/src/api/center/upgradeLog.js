import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import formatPaginationParams from '@/utils/helper/formatPaginationParams';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import moment from 'moment';

/**
 * 升级记录分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=1] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  queryParams = formatPaginationParams(queryParams);
  queryParams.startTime = queryParams.startTime ? moment(queryParams.startTime).unix() : undefined;
  queryParams.endTime = queryParams.endTime ? moment(queryParams.endTime).unix() : undefined;
  queryParams.deviceCategory = queryParams.deviceCategory ? Number(queryParams.deviceCategory) : undefined;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/monitorcars-wrapper/monitorcars/vehicle/queryupgraderecord`, params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData || [],
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { pagination };
