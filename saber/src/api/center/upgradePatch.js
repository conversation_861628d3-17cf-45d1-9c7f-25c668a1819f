import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import formatPaginationParams from '@/utils/helper/formatPaginationParams';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import moment from 'moment';

/**
 * 升级包管理分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=1] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  queryParams = formatPaginationParams(queryParams);
  queryParams.startTime = queryParams.startTime ? moment(queryParams.startTime).unix() : undefined;
  queryParams.endTime = queryParams.endTime ? moment(queryParams.endTime).unix() : undefined;
  queryParams.deviceCategory = queryParams.deviceCategory ? Number(queryParams.deviceCategory) : undefined;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/monitorcars-wrapper/monitorcars/vehicle/queryupgradepackage`, params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data?.resData || [],
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 升级包新增
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function add (queryParams) {
  queryParams.category = queryParams.category ? Number(queryParams.category) : undefined;
  queryParams.deviceCategory = queryParams.deviceCategory ? Number(queryParams.deviceCategory) : undefined;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/addupgradepackage', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 升级包删除
 * @param {Number} id
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function del (ids) {
  let params = {
    ids: ids
  };
  return new Promise((resolve, reject) => {
    request.post(`/monitorcars-wrapper/monitorcars/vehicle/deleteupgradepackage`, params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 升级包修改
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function edit (queryParams) {
  queryParams.category = queryParams.category ? Number(queryParams.category) : undefined;
  queryParams.deviceCategory = queryParams.deviceCategory ? Number(queryParams.deviceCategory) : undefined;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/editupgradepackage', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { pagination, add, edit, del };
