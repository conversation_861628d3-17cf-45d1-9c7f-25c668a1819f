import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import formatPaginationParams from '@/utils/helper/formatPaginationParams';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import moment from 'moment';
const JSONbig = require('json-bigint');

/**
 * 升级任务管理分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=1] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  queryParams = formatPaginationParams(queryParams);
  queryParams.startTime = queryParams.startTime ? moment(queryParams.startTime).unix() : undefined;
  queryParams.endTime = queryParams.endTime ? moment(queryParams.endTime).unix() : undefined;
  queryParams.category = queryParams.category ? Number(queryParams.category) : undefined;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/monitorcars-wrapper/monitorcars/vehicle/queryupgradetask`, params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData || [],
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 任务升级终端分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.current=1] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 */
export function upgradeTerminalState (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/monitorcars-wrapper/monitorcars/vehicle/queryupgradeterminalnum`, params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData || [],
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 升级包分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.current=1] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 */
export function upgradePageData (queryParams) {
  queryParams.category = queryParams.category ? Number(queryParams.category) : undefined;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/monitorcars-wrapper/monitorcars/vehicle/queryupgradepackage`, params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData || [],
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 根据升级包查询终端分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.current=1] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 */
export function upgradeTerminalPageData (queryParams) {
  queryParams.deptId = queryParams.deptId ? BigInt(queryParams.deptId) : undefined;
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post(`/monitorcars-wrapper/monitorcars/vehicle/queryupgradedevice`, params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData || [],
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 升级任务新增
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function add (queryParams) {
  queryParams.deviceList = queryParams.deviceList.map(item => ({
    ...item,
    id: BigInt(item.id),
    targetId: BigInt(item.targetId),
    deptId: BigInt(item.deptId)
  }));
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/addupgradetask', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 升级任务执行/取消
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function editState (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/batchexecupgradetask', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { pagination, add, upgradeTerminalState, upgradePageData, upgradeTerminalPageData, editState };
