import request from '../utils/request';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import jsonToHump from '@/utils/helper/jsonToHump';
import formatPaginationParamNew from '@/utils/helper/formatPaginationParamNew';

/**
 * 终端赋码分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  const current = queryParams.page + 1;
  const size = queryParams.size;
  queryParams = formatPaginationParamNew(queryParams);
  queryParams.codeMachine = queryParams.codeMachine ? queryParams.codeMachine : undefined;
  queryParams.batchNo = queryParams.batchNo ? queryParams.batchNo.replace(/-/g, '') : undefined;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post(`/vdm-bd-check/coding/process/pass/page?current=${current}&size=${size}`, params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.records || [],
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 指定终端参数查询
 * @param {Object} queryParams
 * @param {String} [queryParams.phone] 终端手机号码
 * @param {Array} [queryParams.paramIds] 查询指令
 * @returns {Promise<PaginationResult>}
 */
export function queryTerminalParam (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/terminalcheck-wrapper/terminalcheck/queryterminalspecparam', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 指定终端参数查询
 * @param {Object} queryParams
 * @param {String} [queryParams.phone] 终端手机号码
 * @param {Array} [queryParams.paramIds] 查询指令
 * @returns {Promise<PaginationResult>}
 */
export function queryTerminalParamMqtt (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-mqtt-check/mqtt/devicenum/queryparam', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.res

      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}


/**
 * 检验终端参数
 * @param {Object} queryParams
 * @param {String} [queryParams.uniqueId] 序列号
 * @param {String} [queryParams.deviceType] 设备类型
 * @param {String} [queryParams.deviceSeq] 终端序列号
 * @param {String} [queryParams.imei] imei号
 * @param {String} [queryParams.chipSeq] 北斗芯片序列号
 * @param {String} [queryParams.manufacturer] 终端厂商
 * @param {String} [queryParams.deviceModel] 终端型号
 * @returns {Promise<PaginationResult>}
 */
export function paramCheck (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-bd-check/coding/process/param/check', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 生成赋码
 * @param {Object} queryParams
 * @param {String} [queryParams.uniqueId] 序列号
 * @param {String} [queryParams.deviceType] 设备类型
 * @param {String} [queryParams.deviceSeq] 终端序列号
 * @param {String} [queryParams.imei] imei号
 * @param {String} [queryParams.chipSeq] 北斗芯片序列号
 * @param {String} [queryParams.manufacturer] 终端厂商
 * @param {String} [queryParams.deviceModel] 终端型号
 * @returns {Promise<PaginationResult>}
 */
export function createCode (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-bd-check/coding/process/form', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 终端赋码
 * @param {Object} queryParams
 * @param {String} [queryParams.phone] 序列号
 * @param {String} [queryParams.sign] 赋码签名
 * @param {String} [queryParams.deviceNum] 赋码编号
 * @returns {Promise<PaginationResult>}
 */
export function setDevicenum (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/terminalcheck-wrapper/terminalcheck/setdevicenum', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 终端赋码
 * @param {Object} queryParams
 * @param {String} [queryParams.phone] 序列号
 * @param {String} [queryParams.sign] 赋码签名
 * @param {String} [queryParams.deviceNum] 赋码编号
 * @returns {Promise<PaginationResult>}
 */
export function setDevicenumMqtt (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-mqtt-check/mqtt/devicenum/set', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 终端赋码(批量)
 * @param {Object} queryParams
 * @param {Array} [queryParams.deviceNoList] 设备编号列表
 * @returns {Promise<PaginationResult>}
 */
export function batchCode (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-bd-check/coding/process/batch/complete', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 终端赋码
 * @param {Object} queryParams
 * @param {String} [queryParams.deviceNo] 序列号
 * @param {String} [queryParams.deviceNumSign] 赋码签名
 * @param {String} [queryParams.deviceNum] 赋码编号
 * @returns {Promise<PaginationResult>}
 */
export function deliver (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-bd-check/coding/process/deliver', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 批量导入
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function addbatch(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-bd-check/coding/process/sync/from-machine', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}



export default { pagination, queryTerminalParam, paramCheck, createCode, setDevicenum, queryTerminalParamMqtt, setDevicenumMqtt, batchCode, deliver, addbatch };
