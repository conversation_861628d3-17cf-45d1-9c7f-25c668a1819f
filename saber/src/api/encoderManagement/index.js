import request from '../utils/request';
import md5 from 'js-md5'

/**
 *  终端在线记录查询
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @param {String} [queryParams.blurry] 模糊搜索
 * @typedef {{total: Number, content: Array.<Job>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  queryParams.sort = undefined;
  queryParams.current = queryParams.page + 1;
  queryParams.page = undefined;
  const {size, current, ...res} = queryParams
  const params = Object.keys(res).reduce((obj, key) => {
    if(res[key] || res[key]===0) {
      obj[key] = res[key]
    }
    return obj
  }, {})
  return new Promise((resolve, reject) => {
    request.post(`/vdm-bd-check/coding/machine/page?size=${size}&current=${current}`, params).then(resHump => {
      // let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.records,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

export const getDetail = (id) => {
  return request({
    url: '/vdm-bd-check/coding/machine/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const add = ({password, code_machine_num, start_date, enable, ...row}) => {
  const data = {...row, code_machine_num: `${code_machine_num}`.toLocaleUpperCase(), password: md5(password)}
  return request({
    url: '/vdm-bd-check/coding/machine/add',
    method: 'post',
    data
  })
}

export const edit = ({password, code_machine_num, ...row}) => {
  const data = {...row, code_machine_num: `${code_machine_num}`.toLocaleUpperCase()}
  if(password) {
    data.password = md5(password)
  }
  return request({
    url: '/vdm-bd-check/coding/machine/edit',
    method: 'post',
    data
  })
}

export default { pagination, getDetail, add, edit };
