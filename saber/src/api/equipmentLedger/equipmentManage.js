import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
/**
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @param {String} [queryParams.blurry] 模糊搜索
 * @typedef {{total: Number, content: Array.<Announcement>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  queryParams.sort = undefined;
  queryParams.current = queryParams.page + 1;
  queryParams.page = undefined;
  const { current, size} = queryParams;
  return new Promise((resolve, reject) => {
    request.post(`/vdm-base-info/device/ledger/page?current=${current}&size=${size}`, queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.records,
          total: resHump.data.total
        }
      };
      console.log('result2', result);
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}


export function detail (id) {
  return new Promise((resolve, reject) => {
    request.get(`/vdm-base-info/device/ledger/detail/${id}`, {
      params: {
        id: id
      }
    }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function ledgerCount () {
  return new Promise((resolve, reject) => {
    request.get(`/vdm-base-info/device/ledger/count`).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function edit (data) {
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/device/ledger/edit', data).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}


export function recycle (data) {
  return new Promise((resolve, reject) => {
    request.post('/vdm-base-info/device/ledger/recycle', data).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function addBatch (queryParams, kind) {
  return new Promise((resolve, reject) => {
    request.post(`/vdm-base-info/device/ledger/enter?kind=${kind}`, queryParams).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}


export function outBatch (ids, userDeptId) {
  const params = {
    ids,
    userDeptId
  }
  return new Promise((resolve, reject) => {
    request.post(`/vdm-base-info/device/ledger/out`, params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

export function getOutlmportList (deviceNumString) {
  return new Promise((resolve, reject) => {
    request.get(`/vdm-base-info/device/ledger/outImport?deviceNumString=${deviceNumString}`).then(resHump => {
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}


export default {pagination, detail, edit};
