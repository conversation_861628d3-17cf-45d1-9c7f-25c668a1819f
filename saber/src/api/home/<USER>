import request from '@/api/utils/request';
const JSONbig = require('json-bigint');

//平台基本信息
export async function apiGetHomeBaseInfo(data) {
  return request({
    url: `/vdm-statistic/index/baseInfo`,
    method: 'get',
    params: data
  })
}

//统计实时告警情况
export async function apiGetAlarmInfo(data) {
  return request({
    url: `/vdm-statistic/index/alarmInfo`,
    method: 'get',
    params: data
  })
}

//近30天上线率
export async function apiGetAlarmAndGoOnline(data) {
  return request({
    url: `/vdm-statistic/index/alarmAndGoOnline`,
    method: 'get',
    params: data
  })
}

//车辆状态
export async function apiGetVehiclestatistic(data) {
  return request({
    url: `/vdm-statistic/index/vehicleState`,
    method: 'get',
    params: data
  })
}
// 监控一览 车辆运行状态
export async function apiGetVehicleRunningState(data) {
  return request({
    url: `/vdm-statistic/monitor/vehicleRunningState`,
    method: 'get',
    params: data
  })
}

//车辆告警列表
export async function apiPostSearchlist(data) {
  return request({
    url: `/security-wrapper/securitymanagement/realtimealarmlist`,
    method: 'get',
    params: data
  })
}

// 首页车辆运行情况
export async function apiPostIndexStatVehRunningState(data) {
  return request({
    url: `/vdm-statistic/index/statVehRunningState?current=${data.current}&size=${data.size}`,
    method: 'post',
    data
  })
}


// 监控一览告警排行
export async function apiPostStatVehRunningState(data) {
  return request({
    url: `/vdm-statistic/monitor/statAlarmSortToday?current=${data.current}&size=${data.size}`,
    method: 'get',
    data
  })
}


// 监控一览-车辆运行状况
export async function apiPostVehicleInfo(data) {
  return request({
    url: `/vdm-statistic/monitor/vehicleInfo`,
    method: 'get',
    params: data
  })
}

// 监控一览-告警情况
export async function apiPostMonitorAlarmInfo(data) {
  return request({
    url: `/vdm-statistic/monitor/alarmInfoToday`,
    method: 'get',
    params: data
  })
}

// 监控一览-7日安全告警
export async function apiGetAlarmAndHandleCount(data) {
  return request({
    url: `/vdm-statistic/monitor/alarmAndHandleCount`,
    method: 'get',
    params: data
  })
}

// 监控一览-运输证到期车辆数
export async function apiPostCertExpiredCount(data) {
  return request({
    url: `/vdm-statistic/monitor/certExpiredCount`,
    method: 'get',
    params: data
  })
}

// 监控一览-当日告警情况--分小时
export async function apiPostAlarmAndHandleCountHour(data) {
  return request({
    url: `/vdm-statistic/monitor/alarmAndHandleCountHour`,
    method: 'get',
    params: data
  })
}
// 监控一览-重点告警
export async function apiPostImportAlarm(data) {
  return request({
    url: `/vdm-statistic/monitor/importAlarm`,
    method: 'get',
    params: data
  })
}

// 监控一览-统计每小时车辆上线数和上线率
export async function apiPostGoOnlineRateHour(data) {
  return request({
    url: `/vdm-statistic/monitor/onlineHourCount`,
    method: 'get',
    params: data
  })
}

// 监控一览-重点和实时告警初始化数据
export async function apiPostRealTimeUnHandleAlarm(data) {
  return request({
    url: `/vdm-statistic/monitor/realTimeUnHandleAlarm`,
    method: 'get',
    params: data
  })
}
// 车牌模糊搜索
export async function apiGetVehicleMatch(data){
  return request({
    url: `/baseinfo-wrapper/baseinfo/vehicle/match`,
    method: 'get',
    params: data
  })
}

// 临时位置跟踪控制
export async function apiPostTrackcontrol(data){
  return request({
    url: `/monitorcars-wrapper/monitorcars/vehicle/trackcontrol`,
    method: 'post',
    data: JSONbig.stringify(data),
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 查询驾驶员和车辆信息状态
export async function apiGetDriverNameAndVehicleState(params) {
  return request({
    url: '/vdm-statistic/bt/statistics/driver/driverAndVehicleState',
    method: 'get',
    params
  })
}
