import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
const JSONbig = require('json-bigint');

/**
 * 通过token获取authCode
 * @param {AlarmSetting} queryParams
 * @returns {Promise<Result>}
 */
export function getAuthCode() {
  return new Promise((resolve, reject) => {
    request.get('/vdm-websocket/authWS/authCode').then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 终端状态
 * @param {Object} queryParams
 * @param {Number} [queryParams.deviceType] 终端类型
 * @param {Array} [queryParams.deviceIds] 终端ID
 * @returns {Promise<PaginationResult>}
 */
export function terminalstates (queryParams) {
  queryParams.sort = undefined;
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/vehiclestates', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData || [],
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 通过token获取authCode
 * @param {AlarmSetting} queryParams
 * @param {Number} [queryParams.deviceType] 终端类型
 * @param {Number} [queryParams.deviceId] 终端ID
 * @param {Number} [queryParams.expire] 总上报时间
 * @param {Number} [queryParams.duration] 上报频率(秒)
 * @returns {Promise<Result>}
 */
export function trackcontrol(queryParams) {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/trackcontrol', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}


export default { getAuthCode, terminalstates };
