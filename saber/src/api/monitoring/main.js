import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
const JSONbig = require('json-bigint');

/**
 * 获取首页数据
 * @typedef {{code: Number, msg: String, data: String}} Result 其中,data为文件完整下载路径
 * @returns {Promise<Result>}
 */
export function terminalCount() {

  return new Promise((resolve, reject) => {
    request.get('/vdm-base-info/home/<USER>/count').then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}


export function getTerminalLocation(deviceType) {
  return new Promise((resolve, reject) => {
    request.get('/vdm-base-info/home/<USER>/real/location', {
      params: {
        deviceType
      }
    }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

// 查询终端位置（新）
export function queryTerminalLocation(queryParams) {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/realpos', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

// 最后推送的十条数据
export function lastStatusData(params) {
  return new Promise((resolve, reject) => {
    request.get('/vdm-statistic/statistic/lastDevStatus/list', { params: params}).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

// 获取各类终端接入量、上线数(新)
export function queryTerminalCount(params) {
  return new Promise((resolve, reject) => {
    request.get('/vdm-base-info/home/<USER>/v2/countNew', { params: params }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

// 获取旧终端接入量、上线数
export function queryOldTerminalCount(params) {
  return new Promise((resolve, reject) => {
    request.get('/vdm-base-info/home/<USER>/v2/countOldDevice', { params: params }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

// 获取特殊终端接入量、上线数
export function querySpecialTerminalCount(params) {
  return new Promise((resolve, reject) => {
    request.get('/vdm-base-info/home/<USER>/countSpcDevice', { params: params }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

// 查询行政区划
export function queryAllRegion(params) {
  return new Promise((resolve, reject) => {
    request.get('/blade-system/region/select', { params: params }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

// 根据产业板块过滤组织
export function industryQueryDept(params) {
  return new Promise((resolve, reject) => {
    request.get('/vdm-base-info/gnProd/treeWith', { params: params }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}
