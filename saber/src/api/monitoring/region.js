import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';

/**
 * 轨迹查询
 * @param {Object} queryParams
 * @param {Number} [queryParams.department] 车组
 * @param {Number} [queryParams.startTime] 开始时间时间戳（秒）
 * @param {Number} [queryParams.endTime] 结束时间时间戳（秒）
 * @param {Object} [queryParams.bounds] 查询区域
 * @param {Array} [queryParams.status] 车辆状态
 * @param {Number} [queryParams.start] 分页参数 开始计数
 * @param {Number} [queryParams.count] 分页参数 总计数
 * @returns {Promise<PaginationResult>}
 */
export function queryRegion(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/historybounds', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          resData: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 查询区域内的当前车辆位置
 * @param {Object} queryParams
 * @param {Number} [queryParams.department] 车组
 * @param {Number} [queryParams.startTime] 开始时间时间戳（秒）
 * @param {Number} [queryParams.endTime] 结束时间时间戳（秒）
 * @param {Object} [queryParams.bounds] 查询区域
 * @param {Array} [queryParams.status] 车辆状态
 * @param {Number} [queryParams.start] 分页参数 开始计数
 * @param {Number} [queryParams.count] 分页参数 总计数
 * @returns {Promise<PaginationResult>}
 */
export function queryrealTimeRegion(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/searchvehiclesinarea', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          resData: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}
