import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
const JSONbig = require('json-bigint');

/**
 * 轨迹查询
 * @param {Object} queryParams
 * @param {Number} [queryParams.licencePlate] 车牌号码
 * @param {Number} [queryParams.startTime] 开始时间时间戳（秒）
 * @param {Number} [queryParams.endTime] 结束时间时间戳（秒）
 * @returns {Promise<PaginationResult>}
 */
export function queryTracking (queryParams) {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/querytracking', params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 轨迹查询分页
 * @param {Object} queryParams
 * @param {Number} [queryParams.licencePlate] 车牌号码
 * @param {Number} [queryParams.startTime] 开始时间时间戳（秒）
 * @param {Number} [queryParams.endTime] 结束时间时间戳（秒）
 * @returns {Promise<PaginationResult>}
 */
export function queryTrackingByPage (queryParams) {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/querytrackingByPage', params).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 轨迹查询分页 带停车点停车时长
 * @param {Object} queryParams
 * @param {Number} [queryParams.licencePlate] 车牌号码
 * @param {Number} [queryParams.startTime] 开始时间时间戳（秒）
 * @param {Number} [queryParams.endTime] 结束时间时间戳（秒）
 * @returns {Promise<PaginationResult>}
 */
export function queryTrackingByPageWithStopPoint (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/vdm-big-data/bd/locationWithStopByPage', { params: params }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 轨迹查询异常点绘制
 * @param {Object} queryParams
 * @param {Number} [queryParams.deviceId] 终端id
 * @param {Number} [queryParams.startTime] 开始时间时间戳（秒）
 * @param {Number} [queryParams.endTime] 结束时间时间戳（秒）
 * @returns {Promise<PaginationResult>}
 *
 */
export function queryTrackingUnablePoint(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/vdm-big-data/bd/exceptionLocationListWithStopPoint', { params: params }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}



/**
 * 轨迹分段查询(查询某段时间内的所有轨迹分段)
 * @param {Object} queryParams
 * @param {Number} [queryParams.deviceId] 设备id
 * @param {Number} [queryParams.startTime] 开始时间时间戳（秒）
 * @param {Number} [queryParams.endTime] 结束时间时间戳（秒）
 * @param {Boolean} [queryParams.byInterval] 按类型搜索分段(为true时，按相邻时间点的间隔大小分段后再按平均速度分段，为false时，先查询终端上下线记录进行分段后再按平均速度分段)
 * @returns {Promise<PaginationResult>}
 */
export function trackingByParagraph (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/vdm-big-data/bd/trackSegments', { params: params }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}


/**
 * 导出查询
 * @param {Object} queryParams
 * @param {Number} [queryParams.licencePlate] 车牌号码
 * @param {Number} [queryParams.startTime] 开始时间时间戳（秒）
 * @param {Number} [queryParams.endTime] 结束时间时间戳（秒）
 * @returns {Promise<PaginationResult>}
 */
export function exportTracking (queryParams) {
  let params = JSONbig.stringify(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/vdm-big-data/bd/export', params, {timeout: 300000}).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 查询车辆状态信息
 * @param {Object} queryParams
 * @param {Number} [queryParams.licencePlate] 车牌号码
 * @param {Number} [queryParams.id] 车辆id
 * @returns {Promise<PaginationResult>}
 */
export function queryVehicleState (queryParams) {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/queryvehiclestate', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 轨迹日期查询
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: Array.<District>}} Result
 * @returns {Promise<Result>}
 */

export function trackDateSearch (queryParams) {
  return new Promise((resolve, reject) => {
    request.get('/vdm-statistic/track/calendar/list', {params: queryParams}).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 轨迹完整率计算
 * @param {Object} queryParams
 * @returns {Promise<Result>}
 */
export function trackRatio (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('/monitorcars-wrapper/monitorcars/vehicle/statisticstrack', {params: params}).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 视频回放日期查询
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: Array.<District>}} Result
 * @returns {Promise<Result>}
 */

export function videoDateSearch (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/queryvideorecordcalendar', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 逆地理编码(批量)
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: Array.<District>}} Result
 * @returns {Promise<Result>}
 */

export function batchAddr (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/batchaddr', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}
