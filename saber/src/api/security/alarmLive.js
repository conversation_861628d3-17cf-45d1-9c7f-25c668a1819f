import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import formatPaginationParam from '@/utils/helper/formatPaginationParam';
// import moment from 'moment';
// import autoInsertParamDeptId from '@/utils/helper/autoInsertParamDeptId';

/**
 *  实时告警分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @param {String} [queryParams.blurry] 模糊搜索
 * @typedef {{total: Number, content: Array.<Job>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination (queryParams) {
  // queryParams = autoInsertParamDeptId(queryParams);
  queryParams = formatPaginationParam(queryParams);
  // queryParams.dealStatus = queryParams.dealStatus !== '' ? queryParams.dealStatus : undefined;
  // queryParams.alarmType = queryParams.alarmType !== '' ? queryParams.alarmType : undefined;
  // queryParams.startTime = queryParams.startTime ? moment(queryParams.startTime).unix() : undefined;
  // queryParams.endTime = queryParams.endTime ? moment(queryParams.endTime).unix() : undefined;
  queryParams.sort = undefined;
  let params = jsonToUnderline(queryParams);
  params['alarm_type'] === null && delete params['alarm_type'];
  return new Promise((resolve, reject) => {
    request.post('securitymanagement/realtimealarm/searchlist', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 实时告警统计--类型
 *
 * @param {Object} queryParams
 * @param {Int} [queryParams.deptId] 车组id
 * @typedef {{code: Number, msg: String, data: Object}}
 * @returns {Promise<Result>}
 */
export function statisticByType (queryParams) {
  // queryParams = autoInsertParamDeptId(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/securitymanagement/realtimealarm/statisticsbytype', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 实时告警统计--车辆
 *
 * @param {Object} queryParams
 * @param {Int} [queryParams.deptId] 车组id
 * @typedef {{code: Number, msg: String, data: Object}}
 * @returns {Promise<Result>}
 */
export function statisticByPlate (queryParams) {
  // queryParams = autoInsertParamDeptId(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/securitymanagement/realtimealarm/statisticsbyplate', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 单个告警的信息
 * @param {Object} queryParams
 * @param {Int} id 告警id id的值用key代替
 * @typedef {{code: Number, msg: String, data: Object}}
 * @returns {Promise<Result>}
 */
export function singleAlarmDetail (queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/securitymanagement/realtimealarm/detail',
      queryParams
    ).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 导出
 * @param {Object} queryParams
 * @param {Int} [queryParams.alarmType] 告警类型
 * @param {Int} [queryParams.startTime] 开始时间时间戳
 * @param {Int} [queryParams.endTime] 开始时间时间戳
 * @typedef {{code: Number, msg: String, data: String}} Result 其中,data为文件完整下载路径
 * @returns {Promise<Result>}
 */
export function exportAll (queryParams) {
  queryParams = formatPaginationParam(queryParams);
  queryParams.sort = undefined;
  let params = jsonToUnderline(queryParams);
  params['alarm_type'] === null && delete params['alarm_type'];
  return new Promise((resolve, reject) => {
    request.post('/securitymanagement/realtimealarm/export', params).then(res => {
      resolve(res.data);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
*  实时告警单个处理
*
* @param {Object} params
* @param {Int} [params.id] 告警id
* @param {String} [params.dealMan] 处理人
*/
export function dealoneAlarm (params) {
  params = jsonToUnderline(params);
  return new Promise((resolve, reject) => {
    request.post('/securitymanagement/realtimealarm/dealsinglealarm',
      params
    ).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
*  实时告警批量处理
*
* @param {Object} params
* @param {Int} [params.id] 告警id
* @param {String} [params.dealMan] 处理人
*/
export function dealAlarms (params) {
  params = jsonToUnderline(params);
  return new Promise((resolve, reject) => {
    request.post('/securitymanagement/realtimealarm/dealalarms',
      params
    ).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { pagination, exportAll, statisticByType, statisticByPlate, dealoneAlarm, dealAlarms };
