import request from "@/router/axios";
export const domain = () => {
  return request({
    url: "/vdm-statistic/screen/base/info/rdss/domain",
    method: "get",
  });
};

export const terminalYear = () => {
  return request({
    url: "/vdm-statistic/screen/base/info/terminal/year",
    method: "get",
  });
};
export const rnssGnssMode = () => {
  return request({
    url: "/vdm-statistic/screen/base/info/rnss/gnssMode",
    method: "get",
  });
};
export const terminalDept = () => {
  return request({
    url: "/vdm-statistic/screen/base/info/terminal/dept",
    method: "get",
  });
};
export const highPrecision = () => {
  return request({
    url: "/vdm-statistic/screen/base/info/high/precision",
    method: "get",
  });
};

export default {
  domain,
  terminalYear,
  rnssGnssMode,
  terminalDept,
  highPrecision,
};
