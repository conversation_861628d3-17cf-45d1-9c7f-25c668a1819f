import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';

/**
 *  车辆上线情况抽查
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @param {String} [queryParams.blurry] 模糊搜索
 * @typedef {{total: Number, content: Array.<Job>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination(queryParams) {
  queryParams.sort = undefined;
  queryParams.current = queryParams.page + 1;
  queryParams.page = undefined;
  if (queryParams.startTime) {
    queryParams.startTime = Math.floor(queryParams.startTime / 1000);
  }
  if (queryParams.endTime) {
    queryParams.endTime = Math.floor(queryParams.endTime / 1000);
  }
  return new Promise((resolve, reject) => {
    request.post(`/vdm-statistic/static/locations/list?size=${queryParams.size}&current=${queryParams.current}`, queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.records,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 车辆上线情况抽查导出
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中,data为文件完整下载路径
 * @returns {Promise<Result>}
 */
export function exportAll(queryParams) {
  queryParams.sort = undefined;
  return new Promise((resolve, reject) => {
    request.post('/vdm-statistic/bt/statistics/vehicleOnline/export', { ...queryParams }).then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 查询默认赋码编号
 * @param {Object} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中,data为文件完整下载路径
 * @returns {Promise<Result>}
 */
export function defaultDeviceNum () {
  return new Promise((resolve, reject) => {
    request.get('/vdm-statistic/static/locations/defaultDeviceNum').then(res => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default {
  pagination,
  exportAll,
  defaultDeviceNum
};
