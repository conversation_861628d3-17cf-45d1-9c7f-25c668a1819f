import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import formatPaginationParam from '@/utils/helper/formatPaginationParam';
import DictCodeEnum from '@/enumerate/dictCode/DictCode';

/**
 * @typedef DictDetail 字典取值
 * @property {String} name 字典取值名称
 * @property {Int} enabled 状态 1使用,0不使用
 * @property {Int} sort 排序
 * @property {Int} dept 部门
 * @property {String} updateTime 更新时间
 * @property {String} createTime 创建时间
 */

/**
 * 查询某个字典的字典取值
 * @description 这个函数优先读取后台固定的字典值，由前端段自行在后台配置的字典值，最后
 * @param {Number} dictName 字典的属性
 * @typedef {{total: Number, content: Array.<DictDetail>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function get(dictName) {
  // console.log(dictName, typeof DictCodeEnum[dictName]);
  // 先从后台枚举中检索是否有固定的内容
  if (DictCodeEnum[dictName]) {
    if (typeof DictCodeEnum[dictName] !== 'function') {
      // let oldData = LocalStorageUtil.getParamByVersion(dictName);
      // if (oldData) { // 如果已经有了同个版本的缓存，则忽略
      //   return new Promise((resolve, reject) => {
      //     // console.log('后台字典旧数据-->', dictName, DictCodeEnum[dictName], oldData);
      //     resolve(oldData);
      //   });
      // } else {
      return new Promise((resolve, reject) => {
        request.get('/blade-system/dict-biz/getDictTreeByCodeCache?code=' + DictCodeEnum[dictName]).then(res => {
          let resHump = jsonToHump(res);
          // console.log('后台字典新数据-->', dictName, DictCodeEnum[dictName], formatLabelValue(resHump.data));
          let result = {
            code: resHump.code,
            msg: resHump.msg,
            data: {
              content: formatLabelValue(resHump.data),
              total: resHump.data.length
            }
          };
          // LocalStorageUtil.setParamByVersion(result, dictName);
          resolve(result);
        }).catch((error) => {
          reject(error);
        });
      });
      // }
    } else if (typeof DictCodeEnum[dictName] === 'function') {
      let content = DictCodeEnum[dictName]();
      return new Promise((resolve, reject) => {
        let result = {
          code: 0,
          msg: '前端枚举: @see DictCode',
          data: {
            content: content,
            total: content.length
          }
        };
        resolve(result);
      });
    }
  } else {
    const queryParams = {
      dictName: dictName,
      start: 0,
      count: 9999
    };
    let params = jsonToUnderline(queryParams);
    return new Promise((resolve, reject) => {
      request.get('systemsetting/dictDetail', { params: params }).then(res => {
        let resHump = jsonToHump(res);
        let result = {
          code: resHump.code,
          msg: resHump.msg,
          data: {
            content: formatLabelValue(resHump.data),
            total: resHump.data.total
          }
        };
        resolve(result);
      }).catch((error) => {
        reject(error);
      });
    });
  }
}

/**
 * 格式化
 * @param {Array} array
 * @param {Int|String} array[].dictCode
 * @param {String} array[].dictName
 * @return {Array.<{value: Number, label: String}>}
 */
function formatLabelValue(array) {
  if (array && array.length) {
    // let hasZero = false;
    for (let i = 0; i < array.length; i++) {
      let item = array[i];
      if (item.dictCode !== undefined) {
        item.value = item.dictCode;
      }
      if (item.dictName !== undefined) {
        item.label = item.dictName;
      }
      // if (item.value === 0) {
      //   hasZero = true;
      // }
    }
    // FIXME 打补丁：如果后台没有给0值，为了规避go语言会将null改为0的特性，所有的int型枚举都额外添加一个请选择的选项
    // if (!hasZero) {
    //   array.unshift({
    //     value: 0,
    //     label: '请选择'
    //   });
    // }
    return array;
  }
}

/**
 * 字典取值分页
 *
 * @param {Object} queryParams
 * @param {Number} [queryParams.page=0] 页码
 * @param {Number} [queryParams.size=10] 每页多少项
 * @param {String} [queryParams.sort] 排序方式，格式“a,b”，a-按照字段排序,b-排序策略(desc-降序，aesc-生序)，如id,desc
 * @param {String} [queryParams.blurry] 模糊搜索
 * @typedef {{total: Number, content: Array.<DictDetail>}} PaginationArray
 * @typedef {{code: Number, msg: String, data: PaginationArray}} PaginationResult
 * @returns {Promise<PaginationResult>}
 */
export function pagination(queryParams) {
  queryParams = formatPaginationParam(queryParams);
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.get('systemsetting/dictDetail', { params: params }).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: {
          content: resHump.data.resData,
          total: resHump.data.total
        }
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 字典取值新增
 * @param {DictDetail} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function add(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('systemsetting/dictDetail', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 字典取值删除
 * @param {Int} id 字典ID
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function del(id) {
  return new Promise((resolve, reject) => {
    request({
      url: 'systemsetting/dictDetail/' + id,
      method: 'delete'
    }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 字典取值编辑
 * @param {DictDetail} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function edit(queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.put('systemsetting/dictDetail', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

export default { add, edit, del, pagination };
