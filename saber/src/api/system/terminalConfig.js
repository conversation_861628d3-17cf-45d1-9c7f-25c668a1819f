import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
const JSONbig = require('json-bigint');

/**
 * 指定终端参数查询
 * @param {Object} queryParams
 * @param {String} [queryParams.phone] 终端手机号码
 * @param {Array} [queryParams.paramIds] 查询指令
 * @returns {Promise<PaginationResult>}
 */
export function queryTerminalParam (queryParams) {
  // let params = jsonToUnderline(queryParams);
  let params = JSONbig.stringify(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/queryterminalspecparam', params).then(res => {
      // let resHump = jsonToHump(res);
      let result = {
        code: res.code,
        msg: res.msg,
        data: res.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 终端参数设置
 * @param {Object} queryParams
 * @param {String} [queryParams.?] 终端参数太多了，具体参考参数设置页面
 * @returns {Promise<PaginationResult>}
 * 注意：该接口先不采用驼峰样式，沿用后台下划线命名
 */
export function setTerminalParam (queryParams) {
  // let params = jsonToUnderline(queryParams);
  let params = JSONbig.stringify(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/setterminalparam', params).then(res => {
      // let resHump = jsonToHump(res);
      let result = {
        code: res.code,
        msg: res.msg,
        data: res.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 终端控制FTP升级
 * @param {Object} queryParams
 * @param {String} [queryParams.?] 终端控制FTP升级
 * @returns {Promise<PaginationResult>}
 * 注意：该接口先不采用驼峰样式，沿用后台下划线命名
 */
export function terminalControl (queryParams) {
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/terminalcontrol', queryParams).then(res => {
      // let resHump = jsonToHump(res);
      let result = {
        code: res.code,
        msg: res.msg
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 查询FTP远程终端升级详情
 * @param {Object} queryParams
 * @param {String} [queryParams.?] 查询FTP远程终端升级详情
 * @returns {Promise<PaginationResult>}
 * 注意：该接口先不采用驼峰样式，沿用后台下划线命名
 */
export function queryTerminalUpdate (queryParams) {
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/queryterminalattr', queryParams).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 苏标告警参数查询post
 * @param {Object} queryParams
 * @param {String} [queryParams.phone] 终端手机号码
 * @param {Array} [queryParams.paramIds] 查询指令
 * @returns {Promise<PaginationResult>}
 */
export function querysuparam (queryParams) {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/querysuparam', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 苏标告警参数设置post
 * @param {Object} queryParams
 * @param {String} [queryParams.?] 终端参数太多了，具体参考参数设置页面
 * @returns {Promise<PaginationResult>}
 */
export function setterminalsuparam (queryParams) {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/setterminalsuparam', params).then(res => {
      // let resHump = jsonToHump(res);
      let result = {
        code: res.code,
        msg: res.msg,
        data: res.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 音视频参数设置post
 * @param {Object} queryParams
 * @param {String} [queryParams.?] 终端参数太多了，具体参考参数设置页面
 * @returns {Promise<PaginationResult>}
 */
export function setterminalavparam (queryParams) {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/setterminalavparam', params).then(res => {
      let result = {
        code: res.code,
        msg: res.msg,
        data: res.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}
/**
 * 重启设置
 * @param {Object} queryParams
 * @param {String} [queryParams.?] 终端参数太多了，具体参考参数设置页面
 * @returns {Promise<PaginationResult>}
 * 注意：该接口先不采用驼峰样式，沿用后台下划线命名
 */
export function restartTerminal (queryParams) {
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/terminalcontrol', queryParams).then(res => {
      // let resHump = jsonToHump(res);
      let result = {
        code: res.code,
        msg: res.msg,
        data: res.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * ic卡查询设置
 * @param {Object} queryParams
 * @param {String} [queryParams.?] 终端参数太多了，具体参考参数设置页面
 * @returns {Promise<PaginationResult>}
 * 注意：该接口先不采用驼峰样式，沿用后台下划线命名
 */
export function queryIcdriverinfo (queryParams) {
  // let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/security-wrapper/securitymanagement/icdriverinfo', queryParams).then(res => {
      // let resHump = jsonToHump(res);
      let result = {
        code: res.code,
        msg: res.msg,
        data: res.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 音视频查询
 * @param {Object} queryParams
 * @param {String} [queryParams.?]
 * @returns {Promise<PaginationResult>}
 */
export function queryaudiovideoattr (queryParams) {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/queryaudiovideoattr', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 终端参数查询设置
 * @param {Object} queryParams
 * @param {String} [queryParams.?] 终端参数太多了，具体参考参数设置页面
 * @returns {Promise<PaginationResult>}
 * 注意：该接口先不采用驼峰样式，沿用后台下划线命名
 */
export function queryterminalattr (queryParams) {
  // let params = jsonToUnderline(queryParams);
  let params = JSONbig.stringify(queryParams);
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/queryterminalattr', params).then(res => {
      // let resHump = jsonToHump(res);
      let result = {
        code: res.code,
        msg: res.msg,
        data: res.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}



/**
 * 获取车辆终端协议版本号
 * @param queryParams
 * @returns {Promise<unknown>}
 */
export function getterminalversion (queryParams) {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  console.log('-> params', params)
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/getterminalversion', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 终端控制
 * @param {Object} queryParams
 * @param {String} [queryParams.cmd] 命令字
 * @param {Array} [queryParams.phones] 终端手机号数组
 * @returns {Promise<PaginationResult>}
 */
export function terminalcontrol (queryParams) {
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/terminalcontrol', queryParams).then(res => {
      let result = {
        code: res.code,
        msg: res.msg,
        data: res.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 终端控制
 * @param {Object} queryParams
 * @param {String} [queryParams.type] 告警类型
 * @param {String} [queryParams.vehicle_id] 车辆id
 * @returns {Promise<PaginationResult>}
 */
export function confirmAlarm (queryParams) {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/confirmalarm', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 频率控制
 * @param {Object} queryParams
 * @param {String} [queryParams.deviceId] 设备id
 * @returns {Promise<PaginationResult>}
 */
export function frequencyConfig (queryParams) {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/vdm-mqtt-official/mqtt/send/frequency', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.res
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 数据下发
 * @param {Object} queryParams
 * @param {String} [queryParams.deviceId] 设备id
 * @returns {Promise<PaginationResult>}
 */
export function dataSend (queryParams) {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/vdm-mqtt-official/mqtt/send/data', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 手表配置服务器地址
 * @param {Object} queryParams
 * @param {String} [queryParams.deviceId] 设备id
 * @returns {Promise<PaginationResult>}
 */
export function mqttControl (queryParams) {
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/vdm-mqtt-official/mqtt/send/cmdcontrol', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}