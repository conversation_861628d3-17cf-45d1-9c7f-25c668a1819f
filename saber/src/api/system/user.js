import request from '@/router/axios';
import btRequest from '../utils/request';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import jsonToHump from '@/utils/helper/jsonToHump';


export const getList = (current, size, params, deptId) => {
  return request({
    url: '/blade-user/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
      deptId,
    }
  });
};

export const remove = (ids) => {
  return request({
    url: '/blade-user/remove',
    method: 'post',
    params: {
      ids,
    }
  });
};

export const add = (row) => {
  return request({
    url: '/blade-user/submit',
    method: 'post',
    data: row,
    params: {
      monitDeptId: row.monitDeptId
    }
  });
};

export const update = (row) => {
  return request({
    url: '/blade-user/update',
    method: 'post',
    data: row,
    params: {
      monitDeptId: row.monitDeptId
    }
  });
};

export const updatePlatform = (userId, userType, userExt) => {
  return request({
    url: '/blade-user/update-platform',
    method: 'post',
    params: {
      userId,
      userType,
      userExt,
    }
  });
};

export const getUser = (id) => {
  return request({
    url: '/blade-user/detail',
    method: 'get',
    params: {
      id,
    }
  });
};

export const getUserPlatform = (id) => {
  return request({
    url: '/blade-user/platform-detail',
    method: 'get',
    params: {
      id,
    }
  });
};

export const getUserInfo = () => {
  return request({
    url: '/blade-user/info',
    method: 'get',
  });
};

export const resetPassword = (userIds) => {
  return request({
    url: '/blade-user/reset-password',
    method: 'post',
    params: {
      userIds,
    }
  });
};

export const updatePassword = (oldPassword, newPassword, newPassword1) => {
  return request({
    url: '/blade-user/update-password',
    method: 'post',
    params: {
      oldPassword,
      newPassword,
      newPassword1,
    }
  });
};

export const updateInfo = (row) => {
  return request({
    url: '/blade-user/update-info',
    method: 'post',
    data: row
  });
};

export const grant = (userIds, roleIds) => {
  return request({
    url: '/blade-user/grant',
    method: 'post',
    params: {
      userIds,
      roleIds,
    }
  });
};

export const lock = (userIds) => {
  return request({
    url: 'blade-user/disableUser',
    method: 'get',
    params: {
      userIds,
    }
  });
};

export const unlock = (userIds) => {
  return request({
    url: '/blade-user/unlock',
    method: 'post',
    params: {
      userIds,
    }
  });
};

/**
 * 获取用户绑定的车辆
 * @param {Object} queryParams
 * @param {Int} queryParams.userId 用户ID
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function userBindVehicleDetail (queryParams) {
  queryParams.size = undefined;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    btRequest.get(`/baseinfo-wrapper/baseinfo/user/bindvehiclelist`, { params: params }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 获取用户未绑定的车辆
 * @param {Object} queryParams
 * @param {Int} queryParams.userId 用户ID
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function userUnbindVehicleDetail (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    btRequest.get(`/baseinfo-wrapper/baseinfo/user/unbindvehiclelist`, { params: params }).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 用户绑定车辆
 * @param {Object} queryParams
 * @param {Int} queryParams.userId 用户ID
 * @param {Array.<Int>} queryParams.vehicleIds 车辆ID
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function userBindVehicleEdit (queryParams) {
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
    btRequest.post('/baseinfo-wrapper/baseinfo/user/bindvehicle', params).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}
