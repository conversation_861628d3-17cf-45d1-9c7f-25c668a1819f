import request from './request';
import qs from 'qs';
import formatPaginationParam from '@/utils/helper/formatPaginationParam';

export function initData (url, params) {
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  });
}

export function download (url, params) {
  params = formatPaginationParam(params);
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get',
    responseType: 'blob'
  });
}
