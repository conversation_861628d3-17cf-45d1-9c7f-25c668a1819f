import request from '@/router/axios';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import defaultValue from '@/Modules/Core/defaultValue';

/**
 * 开始点播某通道视频(GB28181)
 *
 * @description
 *  RTMP 为VideoPlayerRTMP播放器专用，
 *  RTSP 为VideoPlayerJanus播放器播放RTSP over udp（ip camera或GB28181实时流）
 *  RTP 为VideoPlayerJanus播放器播放RTSP over tcp时的折中方案，此时，流媒体会将RTP推送到指定的端口号上
 * @param {String} [streamType=WS_FLV] RTMP/RTSP/RTP/FLV/WS_FLV
 * @param {Object} queryParams
 * @param {Number} [queryParams.channel=1] 视频通道
 * @param {Number} [queryParams.serial] 设备编号
 * @param {Number} [queryParams.timeout] 拉流超时(秒)
 *
 * @returns {Promise<String>}
 */
export const startGBChannel = (queryParams, streamType) => {
    const url = queryParams.url;
    const prefix = queryParams.prefix;
    queryParams.url = undefined;
    queryParams.prefix = undefined;
    queryParams.token = localStorage.getItem('QS_TOKEN') || undefined;
    let params = jsonToUnderline(queryParams);
    streamType = defaultValue(streamType, 'WS_FLV');
    return new Promise((resolve, reject) => {
        axios({
            url: `${url}/v1/stream/start`,
            method: 'get',
            params: params
          }).then(res => {
            const { data, status } = res;
            if (status === 200) {
                let videoUrl = '';
                if (data[streamType]) {
                  const content = data[streamType].split('sms')[1];
                  if (streamType === 'FLV') {
                    videoUrl = prefix + content;
                  } else if (streamType === 'WS_FLV') {
                    videoUrl = prefix + content;
                  }
                }
                resolve(videoUrl);
            } else {
                reject('获取流地址失败');
            }
          }).catch((error) => {
            reject('获取流地址失败');
          });
      });
  }

/**
 * 获取青柿平台token
 * @param {Vehicle} queryParams
 * @typedef {{code: Number, msg: String, data: String}} Result 其中，data包含了一些说明的字符，不用于程序
 * @returns {Promise<Result>}
 */
export function refreshQSToken () {
    return new Promise((resolve, reject) => {
      request({
        url: '/blade-auth/liveGBS/gbsToken',
        method: 'get',
        authorization: false
      }).then(res => {
        let resHump = jsonToHump(res);
        resolve(resHump);
      }).catch((error) => {
        reject(error);
      });
    });
  }

// 获取青柿平台通道编号
export const channellist = (queryParams) => {
  const url = queryParams.url;
  queryParams.url = undefined;
  queryParams.token = localStorage.getItem('QS_TOKEN') || undefined;
  let params = jsonToUnderline(queryParams);
  return new Promise((resolve, reject) => {
      axios({
          url: `${url}/v1/device/channellist`,
          method: 'get',
          params: params
        }).then(res => {
          const { data, status } = res;
          if (status === 200) {
              resolve(data);
          } else {
              reject('获取通道编号失败');
          }
        }).catch((error) => {
          reject('获取通道编号失败');
        });
    });
}