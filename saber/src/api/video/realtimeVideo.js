import request from '../utils/request';
import jsonToHump from '@/utils/helper/jsonToHump';
import jsonToUnderline from '@/utils/helper/jsonToUnderline';
import defaultValue from '@/Modules/Core/defaultValue';
const JSONbig = require('json-bigint');

/**
 * 实时视频开始播放
 * @param {Object} queryParams
 * @param {String} [queryParams.licencePlate] 车牌号码
 * @param {Number} [queryParams.channel=1] 视频通道
 * @param {Number} [queryParams.dataType=0] 数据类型
 * @param {Number} [queryParams.streamType=0] 码流类型
 * @returns {Promise<PaginationResult>}
 */
export function playRealtimeVideo (queryParams) {
  queryParams.deviceId = BigInt(queryParams.deviceId);
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/querypushvideo', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data,
        rtmpUrl: resHump.rtmpUrl,
        flvUrl: resHump.flvUrl
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 实时视频停止播放
 * @param {Object} queryParams
 * @param {String} [queryParams.licencePlate] 车牌号码
 * @param {Number} [queryParams.channel=1] 视频通道
 * @param {Number} [queryParams.cmd] 控制指令
 * @param {Number} [queryParams.closeType=0] 关闭音视频类型
 * @param {Number} [queryParams.streamType=0] 码流类型
 * @returns {Promise<PaginationResult>}
 */
export function stopRealtimeVideo (queryParams) {
  queryParams.deviceId = BigInt(queryParams.deviceId);
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/quitpushvideo', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data,
        result: resHump.result
      };
      resolve(result);
    }).catch((error) => {
      reject(error);
    });
  });
}

/**
 * 开始点播某通道视频
 *
 * @description
 *  RTMP 为VideoPlayerRTMP播放器专用，
 *  RTSP 为VideoPlayerJanus播放器播放RTSP over udp（ip camera或GB28181实时流）
 *  RTP 为VideoPlayerJanus播放器播放RTSP over tcp时的折中方案，此时，流媒体会将RTP推送到指定的端口号上
 * @param {String} [streamType=WS_FLV] RTMP/RTSP/RTP/FLV/WS_FLV
 * @param {Object} queryParams
 * @param {String} [queryParams.licencePlate] 车牌号码
 * @param {Number} [queryParams.channel=1] 视频通道
 * @param {Number} [queryParams.dataType=0] 数据类型 0：音视频，1：视频，2：双向对讲，3：监听，4：中心广播，5：透传
 * @param {Number} [queryParams.streamType=1] 码流类型  0：主码流，1：子码流 对于兵通项目来说，默认使用子码流
 *
 * @returns {Promise<String>}
 */
export function startChannel (queryParams, streamType, changeApi) {
  // console.log('单路点播：');
  // console.log(requiredData);
  queryParams.streamType = defaultValue(queryParams.streamType, 1);
  queryParams.deviceId = BigInt(queryParams.deviceId);
  let params = JSONbig.stringify(jsonToUnderline(queryParams));

  streamType = defaultValue(streamType, 'WS_FLV');
  let url = changeApi ? '/monitorcars-wrapper/monitorcars/vehicle/quitpushvideo' : '/monitorcars-wrapper/monitorcars/vehicle/querypushvideo'; // 切换码流时改变url
  // FIXME 此处临时测试修改为直接生成ws地址进行推流 省略querypushvideo接口调用步骤
  // request.post(url, params).then(res => {
  //   console.log(res)
  // })
  // return new Promise(resolve => {
  //   // setTimeout(()=> {
  //     resolve(`ws://58.248.84.102:20180/sub/${queryParams.phone}_${queryParams.channel}.flv`)
  //     // resolve(`/test/sub/${queryParams.phone}_${queryParams.channel}.flv`)
  //   // }, 5000)
  // })
  return new Promise((resolve, reject) => {
    request.post(url, params).then(res => {
      let resHump = jsonToHump(res);
      let videoUrl;
      if (streamType === 'RTMP') {
        videoUrl = resHump.data.rtmpUrl;
      } else if (streamType === 'FLV') {
        videoUrl = resHump.data.flvUrl;
        resolve(videoUrl);
      } else if (streamType === 'WS_FLV') {
        videoUrl = resHump.data.wsUrl || resHump.data.flvUrl;
        resolve(videoUrl);
      } else if (streamType === 'NGINX_FLV_URL') {
        videoUrl = resHump.data.nginxFlvUrl;
        resolve(videoUrl);
      }
      resolve(videoUrl);
    }).catch((error) => {
      reject(error.msg);
    });
  });
}

/**
 * 停止播放某通道的视频
 * @param {Object} queryParams
 * @param {String} [queryParams.licencePlate] 车牌号码
 * @param {Number} queryParams.channel 视频通道 可选项1,2,3,4
 * @param {Number} [queryParams.cmd=0] 控制指令 控制指令0：关闭音视频传输指令，1：切换码流（增加暂停和继续），2：暂停该通道所有流的发送 ，3：恢复暂停前流的发送，与暂停前流的发送一致，4：关闭双向对讲
 * @param {Number} [queryParams.closeType=0] 关闭音视频类型 关闭音视频类型 0：关闭该通道有关的音视频数据，1：只关闭该通道有关的音频，保留该通道有关视频 ，2：只关闭该通道有关的视频，保留该通道有关音频
 * @param {Number} queryParams.streamType 码流类型 码流类型（默认0） 0：主码流，1：子码流
 * @returns {Promise<*>}
 */
export function stopChannel (queryParams) {
  console.log('停止推流')
  queryParams.closeType = defaultValue(queryParams.closeType, 0);
  queryParams.cmd = defaultValue(queryParams.cmd, 0);
  queryParams.deviceId = BigInt(queryParams.deviceId);
  let params = JSONbig.stringify(jsonToUnderline(queryParams));
  return new Promise((resolve, reject) => {
    request.post('/monitorcars-wrapper/monitorcars/vehicle/quitpushvideo', params).then(res => {
      let resHump = jsonToHump(res);
      let result = {
        code: resHump.code,
        msg: resHump.msg,
        data: resHump.data,
        result: resHump.result
      };
      resolve(result);
    }).catch((error) => {
      reject(error.msg);
    });
  });
}

/**
 * 获取终端信息
 * @param {Vehicle} id
 * @returns {Promise<Result>}
 */
export function terminalDetails (id) {
  return new Promise((resolve, reject) => {
    request.get(`/baseinfo-wrapper/baseinfo/device/getdevice?id=${id}`).then(res => {
      let resHump = jsonToHump(res);
      resolve(resHump);
    }).catch((error) => {
      reject(error);
    });
  });
}


export default {
  startChannel,
  stopChannel,
  terminalDetails
};
