@import 'variables';

.xh-form-inline.el-form--inline .el-form-item__content {
  width: 215px;
}

//.xh-form-inline .el-autocomplete, .xh-form-inline .el-input {
//  width: 220px!important;
//}

.xh-form-inline .el-divider--horizontal{
  margin-top: @xhSpacingBase;
  margin-bottom: @xhSpacingLarge;
}

// 考虑直接全局设置 TODO
.xh-workspace-no-modal-dialog{
  .el-form-item--mini.el-form-item, .el-form-item--small.el-form-item {
    // margin-bottom: @xhSpacingBase;
  }
}
.el-form{
  .el-select, .el-cascader, .el-date-editor{
    width: 100% !important;
  }
  .el-form-item__label {
    font-weight: 500;
    color: #3c3c3c;
  }
  .el-form-item {
    margin-bottom: 8px !important;
  }
  .el-textarea.is-disabled .el-textarea__inner {
    color: inherit !important;
  }
}
.el-dialog__body{
  max-height: 80vh;
  overflow: auto;
  .el-form{
    .el-row{
      width: 100%;
      flex-wrap: wrap;
    }
    .el-input.is-disabled .el-input__inner, .el-input.is-disabled .el-input__icon, .el-textarea.is-disabled .el-textarea__inner, .el-radio__input.is-disabled .el-radio__inner, .el-radio__input.is-disabled + span.el-radio__label, .el-radio__input.is-disabled .el-radio__inner::after, .el-switch.is-disabled .el-switch__core, .el-switch.is-disabled .el-switch__label {
      cursor: default;
    }
  }
}

// .el-form.rewriting-form-disable{
//   .vue-treeselect--disabled .vue-treeselect__control, .el-select .el-input.is-disabled .el-input__inner:hover, .el-input.is-disabled .el-input__inner{
//     border: 1px solid #DCDFE6;
//     background-color: #FFFFFF;
//     color: #606266;
//   }
//   .tree-disabled .vue-treeselect__value-container {
//     border: none;
//     .vue-treeselect__single-value {
//       background-color: #FFFFFF;
//       color: #606266;
//     }
//   }
// }

