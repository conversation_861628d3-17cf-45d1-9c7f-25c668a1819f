@import 'variables';

.xh-table-cell-same-line{
  white-space:nowrap;
  overflow:hidden;
  text-overflow:ellipsis;
}
.el-table {
  position: relative;
  overflow: hidden;
  box-sizing: border-box;

  flex: 1;
  width: 100%;
  max-width: 100%;
  font-size: 14px;
  color: #606266;
  display: flex;
  flex-direction: column;
}

.el-table__footer-wrapper, .el-table__header-wrapper {
  overflow: hidden;
  flex-shrink: 0;
}

.el-table__body-wrapper {
  /*overflow: hidden;*/
  position: relative;
  flex: 1!important;
  flex-basis: auto!important;
  overflow-y: auto!important;
  display: block!important;
}
.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell{
  background: #F6F6F6;
}
.el-table .el-table__row td{
  text-align: left !important;
}
.el-table thead th{
  font-weight: 400;
}
.el-table td {
  padding: 6px 0 !important;
  color: #666666;
  height: 54px !important;
}
.el-table th {
  text-align: left !important;
  padding: 6px 0 !important;
  color: #333333;
  height: 54px !important;
  background-color: #E4E4E4 !important;
}
.table-date-td {
  color: #999999;
}
.el-table__header tr {
  height: 54px !important;
}
