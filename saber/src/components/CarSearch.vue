<template>
  <el-autocomplete
    ref="cautocomplete"
    v-model="CarIDObj"
    :size="size"
    placeholder="请输入车牌号码"
    :clearable="clearable"
    :fetch-suggestions="querySearch"
    :highlight-first-item="true"
    value-key="label"
    @select="handleSelect"
    @clear="clearSelect"
  />
</template>

<script>
import {searchVehicle} from '@/api/base/vehicle.js';
export default {
  name: 'CarSearch',
  components: {},
  props: {
    clearable: {
      type: Boolean,
      default: true
    },
    car: {
      type: [String, Number, Object],
      default: ''
    },
    size: {
      type: String,
      default: 'mini'
    }
  },
  data () {
    return {
      CarIDObj: '',
      CarIDOpts: [],
      externalCar: undefined
    };
  },
  watch: {
    CarIDObj (_val) {
      this.$emit('update:CarID', _val);
    },
    car (val) {
      console.log('c', val);
      this.CarIDObj = val;
    },
    externalCar: {
      handler (newValue) {
        newValue && this.handleSelect({
          value: newValue.id,
          label: newValue.licencePlate
        });
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    async getOptions (_val) {
      let optionsReturn = [];
      let parme = {
        licence_plate: _val + ''
      };
      await searchVehicle(parme, 'id').then((req) => {
        let opts = [];
        for (let i = 0; i < req.length && i <= 20; i++) {
          const element = req[i];
          opts.push({
            value: element.id,
            label: element.licencePlate,
            licenceColor: element.licenceColor
          });
        }
        optionsReturn = opts;
      });
      return optionsReturn;
    },
    querySearch (_val, _callback) {
      this.getOptions(_val).then(req => {
        _callback(req);
      });
    },
    handleSelect (item) {
      this.CarIDObj = item.label;
      this.$emit('carSelected', {
        id: item.value,
        licencePlate: item.label,
        licenceColor: item.licenceColor
      });
    },
    clearSelect () {
      // document.activeElement.blur();
      this.$refs.cautocomplete.activated = true;
      this.CarIDObj = '';
      this.$emit('carSelected', this.CarIDObj);
    }
  }
};
</script>
