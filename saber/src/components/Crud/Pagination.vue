<!--分页-->
<template>
  <div class="xh-pagination">
    <el-pagination
      background
      :page-size.sync="page.size"
      :total="page.total"
      :current-page.sync="page.page"
      :page-sizes="[10, 20, 30, 40, 50, 100]"
      :pager-count="5"
      :layout="layout"
      @size-change="crud.sizeChangeHandler($event)"
      @current-change="crud.pageChangeHandler"
    />
  </div>
</template>
<script>
import { pagination } from './crud';
export default {
  mixins: [pagination()],
  props: {
    layout: {
      type: String,
      default: 'total, prev, pager, next, sizes'
    }
  }
};
</script>
<style lang="less" scoped>
.xh-pagination{
  padding: 0 0 4px;
  text-align: right;

  ::v-deep .el-pagination__sizes {
    margin-right: 0;
  }
}
</style>
