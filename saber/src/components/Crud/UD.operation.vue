<template>
  <div class="operation gn-button-group">
    <slot name="left" />
    <el-button
      v-if="!hideEdit"
      v-permission="permission.edit"
      :disabled="disabledEdit"
      class="table-button-edit"
      size="small"
      type="text"
      @click.native.stop="toEdit(data)"
    >
      {{ editName }}
    </el-button>
    <el-button
      v-if="!hideDel"
      v-permission="permission.del"
      :loading="crud.delAllLoading"
      :disabled="disabledDle"
      class="table-button-del"
      type="text"
      size="small"
      @click.native.stop="toDelete(data)"
    >
      删除
    </el-button>
    <slot name="right" />
  </div>
</template>
<script>
import CRUD, { crud } from './crud';
export default {
  mixins: [crud()],
  props: {
    data: {
      type: Object,
      required: true
    },
    permission: {
      type: Object,
      required: true
    },
    disabledEdit: {
      type: Boolean,
      default: false
    },
    disabledDle: {
      type: Boolean,
      default: false
    },
    editName: {
      type: String,
      default: '编辑'
    },
    delMsg: {
      type: String,
      default: '确定将选择数据删除?'
    },
    icon: {
      type: String,
      default: 'el-icon-edit'
    },
    hideEdit: {
      type: Boolean,
      default: false
    },
    hideDel: {
      type: Boolean,
      default: false
    },
  },
  data () {
    return {
    };
  },
  methods: {
    toDelete (data) {
      let datas = [];
      datas.push(data);
      this.$confirm(`${this.delMsg}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.crud.delAllLoading = true;
        this.crud.doDelete(datas);
      }).catch(() => {
      });
    },
    // 公告详情不使用弹窗模板
    toEdit (data) {
      if (this.$route.path === '/center/announcement/index') {
        this.$emit('announcement', data);
      } else {
        this.crud.toEdit(data);
      }
    }
  }
};
</script>
<style lang="less">
 .operation {
   .el-button {
     padding: 0;
     i {
       font-size: 16px;
     }
   }
 }
</style>
