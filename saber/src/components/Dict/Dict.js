import Vue from 'vue';
import { get as getDictDetail } from '@/api/system/dictDetail';
import Store from '@/store';

export default class Dict {
  constructor(dict) {
    this.dict = dict;
  }

  async init(names, completeCallback) {
    if (names === undefined || name === null) {
      throw new Error('need Dict names');
    }
    const ps = [];
    names.forEach(key => {
      const n = key.split('___')[0];
      const extra = key.split('___')[1];
      Vue.set(this.dict.dict, n, {});
      Vue.set(this.dict.label, n, {});
      Vue.set(this.dict, n, []);

      const cacheDictItem = Store.state.common.dictCache[n];
      if(cacheDictItem) {
        const content = cacheDictItem;
        this.dict[n].splice(0, 0, ...content);
        let list = extra ? this.handleExtra(content, extra) : this.flattenList(content);
        list.forEach(d => {
          Vue.set(this.dict.dict[n], d.value, d);
          Vue.set(this.dict.label[n], d.value, d.label);
        });
      } else {
        ps.push(getDictDetail(n).then(data => {
          if (data.data.content) {
            Store.commit('SET_DICT_CACHE', {
              key: n,
              value: data.data.content
            });
            this.dict[n].splice(0, 0, ...data.data.content);
            let list = extra ? this.handleExtra(data.data.content, extra) : this.flattenList(data.data.content);
            list.forEach(d => {
              Vue.set(this.dict.dict[n], d.value, d);
              Vue.set(this.dict.label[n], d.value, d.label);
            });
          }
        }));
      }
    });
    await Promise.all(ps);
    completeCallback();
  }
  flattenList(data) {
    var result = [];
    data.forEach((item)=> {
      result.push({ label: item.label, value: item.value });
      if (item.children && item.children.length > 0) {
        var childItems = this.flattenList(item.children);
        result = result.concat(childItems);
      }
    });
    return result;
  }
  handleExtra(data, key) {
    let list = [];
    switch (key) {
    case 'onlyChild':
      list = data.reduce((list, item) => {
        return [...list, ...item.children];
      }, []);
      break;

    default:
      break;
    }
    return list;
  }
}
