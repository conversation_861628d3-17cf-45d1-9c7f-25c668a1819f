<template>
  <div>
    <div
      ref="VideoPlayerGalleryContainer"
      class="xh-VideoPlayerGalleryComponent-container"
    />
  </div>
</template>

<script>
import VideoPlayerGallery from '../../Modules/Widgets/VideoPlayerGallery/VideoPlayerGallery';
import StreamTypeEnum from '@/enumerate/video/streamType';
export default {
  name: 'VideoPlayerGalleryBase',
  props: {
    defaultMode: {
      type: Object,
      default: () => {
        return {
          videoNumber: 4,
          templateName: 'videoPlayer-template-4balance-leftTop',
          name: '4balance-leftTop',
          displayName: '4分屏'
        };
      }
    },
    maxVideoCount: {
      type: Number,
      default: () => 16
    },
    ignoreGb28181ForJanusWebRTC: {
      type: Boolean,
      default: () => true
    },
    defaultStream: {
      type: String,
      default: () => '第三码流'
    },
    defaultStreamType: {
      type: Number,
      default: () => StreamTypeEnum.SUB_STREAM
    },
    playerType: {
      type: String,
      default: 'VideoPlayerNodePlayer'
    },
    forceUseVideoPlayer: {
      type: String,
      default: () => ''
    },
    subscribeClickSwitchPlayerEvent: {
      type: Boolean,
      default: () => false
    },
    subscribeClickHighlightPlayerEvent: {
      type: Boolean,
      default: () => false
    },
    forPlayback: {
      type: Boolean,
      default: () => false
    },
    customSetup: {
      type: Object,
      default: () => {
        return {};
      }
    }

  },
  data () {
    return {
      newVideoPlayerGallery: null
    };
  },

  watch: {
    // 监听播放器是否推流结束 关闭
    'newVideoPlayerGallery': {
      handler (newVal) {
        for (let key in newVal['_videoPlayers']['_hash']) {
          let value = newVal['_videoPlayers']['_hash'][key];
          if (value.pushTime === 0) {
            this.$parent.closeSingleChannel(value);
          }
          // 点击了视频窗口的关闭按钮时
          if (value.isCloseVideo) {
            this.$emit('closeVideoClick', value);
          }
        }
      },
      deep: true
      // immediate: true
    }
  },
  beforeDestroy () {
    this.destroyVideoPlayerGallery();
  },
  activated () {
    this.activateVideoPlayerGallery();
  },
  deactivated () {
  },
  methods: {
    /**
     * 分屏模式切换
     * @param {Object} mode 模式
     * @param {String} mode.name 名字
     */
    switchMode (mode) {
      if (this.newVideoPlayerGallery) {
        this.newVideoPlayerGallery.switchMode(mode);
      }
    },

    /**
     * 获取视频组合器
     * @return {VideoPlayerGallery|*}
     * @return Promise<VideoPlayerGallery>
     */
    getVideoPlayerGallery () {
      return new Promise((resolve, reject) => {
        if (this.newVideoPlayerGallery) {
          resolve(this.newVideoPlayerGallery);
        } else {
          let temp = setInterval(() => {
            if (this.newVideoPlayerGallery) {
              resolve(this.newVideoPlayerGallery);
              clearInterval(temp);
            }
          }, 200);
        }
      });
    },

    /**
     * 获取追踪器对象
     * @return {VideoTrackModeHelper|*}
     * @return Promise<VideoTrackModeHelper>
     */
    getVideoTrackModeHelper () {
      return new Promise((resolve, reject) => {
        if (this._videoTrackModeHelper) {
          resolve(this._videoTrackModeHelper);
        } else {
          let temp = setInterval(() => {
            if (this._videoTrackModeHelper) {
              resolve(this._videoTrackModeHelper);
              clearInterval(temp);
            }
          }, 200);
        }
      });
    },

    /**
     * 激活播放器
     */
    activateVideoPlayerGallery () {
      if (this.playerType === 'VideoPlayerRTMP') {
        if (!this.newVideoPlayerGallery) {
          let param = {
            container: this.$refs['VideoPlayerGalleryContainer'],
            subscribeDoubleClickFullScreenEvent: true,
            subscribeClickSwitchPlayerEvent: this.subscribeClickSwitchPlayerEvent,
            subscribeClickHighlightPlayerEvent: this.subscribeClickHighlightPlayerEvent,
            maxVideoCount: this.maxVideoCount,
            defaultMode: this.defaultMode,
            playerType: this.playerType, // 'VideoPlayerRTSP' 'VideoPlayerRTMP' 'VideoPlayerFLV'
            defaultStreamType: this.defaultStreamType,
            ignoreGb28181ForJanusWebRTC: this.ignoreGb28181ForJanusWebRTC,
            forceUseVideoPlayer: this.forceUseVideoPlayer,
            defaultStream: this.defaultStream,
            forPlayback: this.forPlayback,
            customSetup: this.customSetup
          };
            // 视频播放器
          this.newVideoPlayerGallery = new VideoPlayerGallery(param);
          // console.log(this.newVideoPlayerGallery);
        }
      }   else if (this.playerType === 'VideoPlayerNodePlayer') {
        console.log('-> VideoPlayerNodePlayer激活')
        if (!this.newVideoPlayerGallery) {
          let param = {
            container: this.$refs['VideoPlayerGalleryContainer'],
            subscribeDoubleClickFullScreenEvent: true,
            subscribeClickSwitchPlayerEvent: this.subscribeClickSwitchPlayerEvent,
            subscribeClickHighlightPlayerEvent: this.subscribeClickHighlightPlayerEvent,
            maxVideoCount: this.maxVideoCount,
            defaultMode: this.defaultMode,
            playerType: this.playerType, // 'VideoPlayerRTSP' 'VideoPlayerRTMP' 'VideoPlayerFLV'
            defaultStreamType: this.defaultStreamType,
            ignoreGb28181ForJanusWebRTC: this.ignoreGb28181ForJanusWebRTC,
            forceUseVideoPlayer: this.forceUseVideoPlayer,
            defaultStream: this.defaultStream,
            forPlayback: this.forPlayback,
            customSetup: this.customSetup
          };
          // 视频播放器
          this.newVideoPlayerGallery = new VideoPlayerGallery(param);
          // console.log(this.newVideoPlayerGallery);
        }
        this.getVideoPlayerGallery().then(videoPlayerGallery => {
          videoPlayerGallery.playAll();
        }).catch(msg => { console.log(msg); });
      } else {
        if (!this.newVideoPlayerGallery) {
          let param = {
            container: this.$refs['VideoPlayerGalleryContainer'],
            subscribeDoubleClickFullScreenEvent: true,
            subscribeClickSwitchPlayerEvent: this.subscribeClickSwitchPlayerEvent,
            subscribeClickHighlightPlayerEvent: this.subscribeClickHighlightPlayerEvent,
            maxVideoCount: this.maxVideoCount,
            defaultMode: this.defaultMode,
            playerType: this.playerType, // 'VideoPlayerRTSP' 'VideoPlayerRTMP' 'VideoPlayerFLV'
            defaultStreamType: this.defaultStreamType,
            ignoreGb28181ForJanusWebRTC: this.ignoreGb28181ForJanusWebRTC,
            forceUseVideoPlayer: this.forceUseVideoPlayer,
            defaultStream: this.defaultStream,
            forPlayback: this.forPlayback,
            customSetup: this.customSetup
          };
            // 视频播放器
          this.newVideoPlayerGallery = new VideoPlayerGallery(param);
          // console.log(this.newVideoPlayerGallery);
        }
        this.getVideoPlayerGallery().then(videoPlayerGallery => {
          videoPlayerGallery.playAll();
        }).catch(msg => { console.log(msg); });
      }
    },

    /**
     * 使失活播放器
     */
    deactivateVideoPlayerGallery () {
      // this.getVideoPlayerGallery().then(videoPlayerGallery => {
      //   videoPlayerGallery.clearAll();
      // }).catch(msg => { console.log(msg); });
    },

    /**
     * 销毁播放器
     */
    destroyVideoPlayerGallery () {
      this.getVideoPlayerGallery().then(videoPlayerGallery => {
        videoPlayerGallery.destroy();
      }).catch(msg => { console.log(msg); });
    }

  }
};
</script>

<style scoped>
  .xh-VideoPlayerGalleryComponent-container{
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
</style>
