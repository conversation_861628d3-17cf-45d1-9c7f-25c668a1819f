<template>
  <div>
    <div class="xh-VideoPlayerGalleryModeControl-container">
      <div class="xh-VideoPlayerGalleryModeControl-button" @click="set1">单屏</div>
      <div class="xh-VideoPlayerGalleryModeControl-button" @click="set4">四屏</div>
      <div class="xh-VideoPlayerGalleryModeControl-button" @click="set8">八屏</div>
    </div>
  </div>
</template>

<script>
  export default {
    name: "VideoPlayerGalleryModeControl",
    mounted() {
      this.$nextTick(()=>{

      })
    },
    beforeDestroy(){

    },
    methods: {
      /**
       * 设置单屏
       */
      set1(){
        if(this._videoPlayerGallery){
          this._videoPlayerGallery.switchMode({
            videoNumber: 1,
            templateName: 'videoPlayer-template-1big-full',
            name: '1big-full',
            displayName: '单主屏'
          })
        }
      },
      /**
       * 设置4分屏
       */
      set4(){
        console.log('设置4分屏')
        console.log(this._videoPlayerGallery)
        if(this._videoPlayerGallery){
          this._videoPlayerGallery.switchMode({
            videoNumber: 4,
            templateName: 'videoPlayer-template-4balance-leftTop',
            name: '4balance-leftTop',
            displayName: '4分屏'
          })
        }
      },
      /**
       * 设置8分屏
       */
      set8(){
        if(this._videoPlayerGallery){
          this._videoPlayerGallery.switchMode({
            videoNumber: 8,
            templateName: 'videoPlayer-template-1big7small-leftTop',
            name: '1big7small-leftTop',
            displayName: '8分屏'
          })
        }
      },
      /**
       * 绑定视频播放器
       * @param {VideoPlayerGallery} videoPlayerGallery
       */
      bingVideoPlayerGallery(videoPlayerGallery){
        this._videoPlayerGallery = videoPlayerGallery;
      }
    }
  }
</script>

<style scoped>
  .xh-VideoPlayerGalleryModeControl-container{
    padding-top: 10px;
    padding-bottom: 10px;
    text-align: center;
  }
  .xh-VideoPlayerGalleryModeControl-button{
    background: rgb(77, 77, 77);
    display: inline-block;
    padding: 5px 10px;
    color: white;
    text-align: center;
    margin: 0 5px;
    cursor: pointer;
    border-radius: 3px;
  }
  .xh-VideoPlayerGalleryModeControl-button:hover{
    background: rgb(19, 149, 219);
  }
  .xh-VideoPlayerGalleryModeControl-button-active{
    background: rgb(19, 149, 219);
  }
</style>
