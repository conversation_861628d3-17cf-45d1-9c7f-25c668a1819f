<template>
  <div class="layout">
    <el-cascader
      ref="cascaderRef"
      v-model="selectedType"
      :props="config"
      :options="deptOptions"
      :show-all-levels="false"
      size="small"
      clearable
      filterable
      placeholder="请输入告警类型"
      collapse-tags
      @change="change"
    />
  </div>
</template>

<script>
import {getDictDetail} from '@/api/security/alarmHistory';
export default {
  name: 'DeptSingleSelect',
  props: {
    value: {
      type: [String, Array, Object, Number],
      default: null
    },
    alarmType: {
      type: [String, Array, Object, Number],
      default: null
    }
  },
  data () {
    return {
      loading: false,
      selectedType: [],
      deptOptions: [],
      config: {
        value: 'value',
        label: 'label',
        checkStrictly: false,
        multiple: false
      }
    };
  },
  watch: {
    selectedType (array) {
      console.log('arrayarrayarray', array);

      if (!array) {
        return;
      }
      let arr = array[1];
      this.$emit('input', arr);
    },
    alarmType: {
      handler (value) {
        if (value) {
          this.deptOptions = this.ruleData(value);
        }
      },
      immediate: true
    },
    value: {
      handler (v) {
        if (!v) {
          this.selectedType = null;
        } else {
          this.$nextTick(() => {
            this.getSelectedType(v);
          });
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted () {
    // this.$nextTick(() => {
    //   this.getDict();
    // });
  },
  methods: {
    getSelectedType (value) {
      let arr = this.alarmType || this.deptOptions;
      arr && arr.forEach(item => {
        item.children && item.children.forEach(v => {
          if (v.value === value) {
            this.selectedType = [item.value, value];
          }
        });
      });
    },
    getDict () {
      getDictDetail('alarm_type').then(res => {
        this.deptOptions = this.ruleData(res.data);
      });
    },
    ruleData (data) {
      data.forEach(item => {
        if (!item.children || item.children.length === 0) {
          delete item.children;
        } else {
          item.children = this.ruleData(item.children);
        }
      });

      return data;
    },
    getArr (data) {
      let arr = [];
      data.forEach(item => {
        let obj = {
          value: item.dictCode,
          label: item.dictName
        };
        if (item.children && item.children.length > 0) {
          item.children = this.getArr(item.children, item.children);
          obj.children = item.children;
        }
        arr.push(obj);
      });
      return arr;
    },
    /**
     * 监听选中节点
     */
    change (value) {
      let nodeInfo = this.$refs.cascaderRef.getCheckedNodes();
      this.$emit('input', nodeInfo);
    }
  }
};
</script>
<style lang="less" scoped>
.el-cascader-panel{
  height: 420px;
}
// .layout ::v-deep.el-cascader{
//   width: 325px;
// }
</style>
