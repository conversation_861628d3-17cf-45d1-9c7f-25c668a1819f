<template>
  <div class="layout">
    <div
      class="input_like"
      @click="treeOpen"
    >
      <div class="input_content">
        {{ valueDisplay }}
        <span
          v-show="!valueDisplay"
          class="input_like_hold"
        >{{ '请输入行业类型' }}</span>
      </div>
      <i
        v-show="valueDisplay"
        class="el-icon-circle-close input_icon"
        @click="iconClear"
      />
    </div>
    <div
      v-show="treeShow"
      class="input_layout"
      @click="treeHandle"
    >
      <el-input
        ref="filterText"
        v-model="filterText"
        class="input_search"
        placeholder="搜索行业类型"
        size="small"
        clearable
      />
      <div class="allChange">
        <el-checkbox
          v-model="allChecked"
          @change="allChange"
        >
          全选
        </el-checkbox>
      </div>
      <el-tree
        ref="tree"
        class="input_tree"
        :data="treeData"
        :props="defaultProps"
        node-key="value"
        show-checkbox
        :filter-node-method="filterNode"
        @node-click="handleNodeClick"
        @check-change="handleCheckChange"
      />
    </div>
  </div>
</template>
<script>

export default {
  name: 'VehicleUseType',
  props: {
    value: {
      type: [String, Array, Object, Number],
      default: null
    },
    treeData: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data () {
    return {
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      filterText: '',
      valueDisplay: '',
      treeShow: false
    };
  },
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val);
    },
    value (v) {
      if (!v) {
        this.valueDisplay = '';
        this.$refs.tree.setCheckedKeys([]);
      }
    }
  },
  mounted () {
  },
  methods: {
    handleNodeClick (data) {
      // console.log(data);
    },
    // 搜索筛选
    filterNode (value, data, node) {
      // console.log('value, data', value, data, node);
      if (!value) return true;
      if (data.label.indexOf(value) !== -1) {
        return true;
      } else {
        return this.recursionHandle(value, node);
      }
      // return false;
    },
    // 递归判断父级
    recursionHandle (value, node) {
      if (!node.parent.parent) {
        return false;
      } else if (node.parent.data.label.indexOf(value) !== -1) {
        return true;
      } else {
        return this.recursionHandle(value, node.parent);
      }
    },
    // 选择后触发
    handleCheckChange (data, checked, indeterminate) {
      this.sendNodeNow();
    },
    // 传值
    sendNodeNow () {
      let arr = this.$refs.tree.getCheckedNodes(true, false);
      let result = arr.map(item => {
        return item.value;
      });
      let str = '';
      arr.forEach(item => {
        str = str ? str + '；' + item.label : item.label;
      });
      this.valueDisplay = str;
      this.$emit('input', result.length > 0 ? result : null);
      // 勾选全选
      let num = this.treeData.length;
      this.treeData.forEach(item => {
        if (item.children) {
          num += item.children.length;
        }
      });
      if (this.$refs.tree.getCheckedNodes() && this.$refs.tree.getCheckedNodes().length === num) {
        this.allChecked = true;
      } else {
        this.allChecked = false;
      }
    },
    // 关闭监听
    treeClose () {
      if (this.treeShow) this.treeShow = false;
      document.removeEventListener('click', this.treeClose);
    },
    // 点击关闭弹框
    treeOpen (e) {
      e.stopPropagation();
      this.treeShow = !this.treeShow;
      // this.$refs.tree.setCheckedKeys([]);
      document.addEventListener('click', this.treeClose);
      this.$nextTick(() => {
        this.$refs.filterText.focus();
      });
    },
    treeHandle (e) {
      e.stopPropagation();
    },
    // 图标清除
    iconClear (e) {
      e.stopPropagation();
      this.valueDisplay = '';
      this.$emit('input', null);
      this.$refs.tree.setCheckedKeys([]);
    },
    // 全选
    allChange (value) {
      if (value) {
        this.$refs.tree.setCheckedNodes(this.treeData);
      } else {
        this.$refs.tree.setCheckedNodes([]);
      }
    }
  }
};
</script>
<style lang="less" scoped>
.layout{
  width: 180px;
  display: inline-block;
  position: relative;
}
.input_like{
  position: relative;
  -webkit-appearance: none;
  background-color: #FFF;
  background-image: none;
  border-radius: 4px;
  border: 1px solid #BFBFBF;
  box-sizing: border-box;
  color: #606266;
  display: inline-block;
  height: 32px;
  line-height: 28px;
  outline: 0;
  padding: 0 15px;
  -webkit-transition: border-color .2s cubic-bezier(.645,.045,.355,1);
  transition: border-color .2s cubic-bezier(.645,.045,.355,1);
  width: 100%;
  cursor: pointer;
}
.input_content{
  width: 109px;
  overflow: hidden;
  white-space: nowrap;
}
.input_like_hold{
  color: #C0C4CC;
}
.input_icon{
  position: absolute;
  top: 0;
  right: 5px;
  line-height: 30px;
  color: #C0C4CC;
}
.input_layout{
  position: absolute;
  left: 0;
  top: 32px;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 8px 0px;
  z-index: 99999;
  width: 260px;
}
.input_search{
  width: 100%;
}
.input_search ::v-deep .el-input__inner {
  border-radius: 0;
}
.input_tree{
  width: 100%;
  height: 350px;
  overflow-y: scroll;
  // padding-top: 10px;
}
.allChange{
  padding-left: 6px;
  background-color: #fff;
}
</style>
