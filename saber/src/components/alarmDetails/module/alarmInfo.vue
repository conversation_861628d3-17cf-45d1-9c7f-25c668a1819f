<template>
  <div class="right-box">
    <div class="right-item">
      <div
        class="right-title"
        style="background: rgb(243, 163, 40);"
      >
        告警信息
      </div>
      <div
        class="right-info"
        style="background: rgb(254, 233, 174);"
      >
        <div>
          <div class="label">违规内容：</div>
          <div class="alarm-type">
            {{ alarmInfo.alarmType }}
          </div>
        </div>
        <div
          v-if="alarmInfo.roadName"
        >
          <div class="label">路段名称：</div>
          <div>{{ alarmInfo.roadName }}</div>
        </div>
        <div>
          <div class="label">开始时间：</div>
          <div>{{ alarmInfo.startTime }}</div>
        </div>
        <div v-if="['超速告警', '超速预警', '分段限速告警(平台)', '夜间限速告警(平台)', '道路限速告警(平台)', '超速告警(终端)'].includes(alarmInfo.alarmType)">
          <div class="label">速度：</div>
          <div>{{ alarmInfo.speed }}km/h</div>
        </div>
        <div>
          <div class="label">结束时间：</div>
          <div>{{ alarmInfo.alarmComplete === 1 ? alarmInfo.endTime : '持续中' }}</div>
        </div>
        <div>
          <div class="label">开始地点：</div>
          <div>{{ alarmInfo.startAddr }}</div>
        </div>
        <div>
          <div class="label">结束地点：</div>
          <div>{{ alarmInfo.alarmComplete === 1 ? alarmInfo.endAddr : '定位中' }}</div>
        </div>
        <div v-if="alarmInfo.fatigueStartTime">
          <div class="label">疲劳驾驶开始时间：</div>
          <div>{{ alarmInfo.fatigueStartTime }}</div>
        </div>
        <div v-if="alarmInfo.fatigueEndTime">
          <div class="label">疲劳驾驶结束时间：</div>
          <div>{{ alarmInfo.fatigueEndTime }}</div>
        </div>
        <div>
          <!-- 服务商 -->
          <el-button
            class="alarm-btn"
            size="medium"
            type="danger"
            icon="el-icon-edit"
            :disabled="!['未处理', '待处理'].includes(alarmInfo.handleState)"
            @click="alarmHandle"
          >处理</el-button>
          <!-- 误报 -->
          <el-button
            v-if="alarmInfo.isWrong === 0"
            class="operation-btn"
            size="medium"
            type="warning"
            icon="el-icon-thumb"
            @click="misinformationAll"
          >误报</el-button>
        </div>
      </div>
    </div>
    <div class="right-item">
      <div
        class="right-title"
        style="background: #1f71ff;"
      >
        监控对象
      </div>
      <div
        class="right-info"
        style="background: rgb(213, 235, 255);"
      >
        <div>
          <div class="label">监控对象：</div>
          <div>{{ alarmInfo.targetName }}</div>
        </div>
        <div>
          <div class="label">终端类别：</div>
          <div>{{ alarmInfo.deviceTypeName }}</div>
        </div>
        <div>
          <div class="label">终端类型：</div>
          <div>{{ alarmInfo.deviceCateName }}</div>
        </div>
        <div>
          <div class="label">赋码编号：</div>
          <div>{{ alarmInfo.deviceNum }}</div>
        </div>
        <div>
          <div class="label">序列号：</div>
          <div>{{ alarmInfo.uniqueId }}</div>
        </div>
      </div>
    </div>
    <div
      class="right-item"
    >
      <div
        class="right-title"
        style="background: rgb(103, 194, 58);"
      >
        告警附件
      </div>
      <div
        class="right-info right-file"
        style="background: rgb(172, 253, 132);"
      >
        <div v-if="accessory.alarmVideo">
          <video
            width="100%"
            :src="accessory.alarmVideo"
            controls
          />
        </div>
        <div v-if="accessory.imgList.length > 0">
          <el-image
            v-for="(item, i) in accessory.imgList"
            :key="i"
            :src="item"
            class="image-item"
            :preview-src-list="accessory.imgList"
          />
        </div>
        <div v-if="!accessory.alarmVideo && accessory.imgList.length === 0">
          暂无附件
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { parseTime } from '@/api/utils/share';
export default {
  props:{
    alarmInfo: {
      type: Object,
      default: ()=>{return {};}
    },
    dict: {
      type: Object,
      default: ()=>{return {};}
    },
    isMisinformation: {
      type: Boolean,
      default: false
    }
  },
  data(){
    return{
      accessory: {
        imgList: [],
        alarmVideo: '',
      }
    };
  },
  methods:{
    clearAccessory(){
      this.accessory = {
        imgList: [],
        alarmVideo: ''
      };
    },
    accessoryHandle(data){
      this.accessory.alarmVideo = data.mp4;
      let list = [];
      for (let index = 1; index < 4; index++) {
        if (data['img' + index]) {
          list.push(data['img' + index]);
        }
      }
      this.accessory.imgList = list;
    },
    // 误报
    misinformationAll() {
      const list = [this.alarmInfo];
      this.$emit('misinformationAll', list);
    },
    // 操作事件(处理)
    alarmHandle(){
      const list = [this.alarmInfo];
      this.$emit('alarmHandle', list);
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    parseTime
  }
};
</script>
<style lang="less" scoped>
.right-box{
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 5px;
    box-shadow: 0 0 4px #ccc;
    overflow-y: scroll;
}
.right-item{
    width: 100%;
    padding: 10px;
    display: flex;
    align-items: center;
    color: #333;
    font-size: 13px;
    position: relative;
}
.right-title{
    padding-top: 3px;
    width: 24px;
    height: 92px;
    text-align: center;
    color: #fff;
    position: absolute;
    left: 0;
    font-size: 14px;
}
.right-info{
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 10px 10px 10px 20px;
}
.alarm-type{
  background: red;
  text-align: center;
  color: #fff;
  padding: 2px 10px;
  border-radius: 3px;
  flex-grow: 1;
  text-align: center!important;
}
.right-info>div{
  width: 100%;
  display: flex;
  margin-bottom: 5px;
}
.info-item{
  width: 50% !important;
}
.operation-btn{
  width: 100%;
  height: 50px;
  margin-left: -10px;
}
.alarm-btn{
  width: 100%;
  height: 50px;
  margin-left: -10px;
  background-color: #ec4e4e;
}
.label{
  // width: 150px;
  white-space: nowrap;
}
.image-item{
  width: 100px;
  height: 100px;
}
.right-file{
  min-height: 92px;
}
</style>
