<template>
  <div class="footer">
    <div class="tab">
      <div
        class="tab-item"
        :class="{'active-item': activeItem}"
      >
        <el-badge
          :value="alarmTable.length ? alarmTable.length : null"
          :max="99"
          class="item"
        >
          <i class="el-icon-s-custom"/>
          <div>处理记录</div>
        </el-badge>
      </div>
      <!-- <div
        class="tab-item"
        :class="{'active-item': !activeItem}"
        @click="activeItem = !activeItem"
      >
        <el-badge
          :value="null"
          :max="99"
          class="item"
        >
          <i class="el-icon-message-solid"/>
          <div>微信提醒</div>
        </el-badge>
      </div> -->
    </div>
    <div class="tab-container">
      <div
        v-if="activeItem"
        class="table"
      >
        <el-table
          ref="table"
          :data="alarmTable"
          style="width: 100%; height: 100%"
        >
          <el-table-column
            type="index"
            label="#"
          />
          <el-table-column
            label="处理措施"
            prop="dealMeasures"
            show-overflow-tooltip
            min-width="180"
            :resizable="false"
          />
          <el-table-column
            label="处理内容"
            prop="dealContent"
            show-overflow-tooltip
            min-width="120"
            :resizable="false"
          />
          <el-table-column
            label="处理时间"
            prop="createTime"
            show-overflow-tooltip
            min-width="180"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.createTime }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="处理人员"
            prop="createUserName"
            show-overflow-tooltip
            min-width="100"
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <div
        v-else
        class="table"
      >
        <el-table
          ref="table"
          :data="data"
          style="width: 100%; height: 100%"
        >
          <el-table-column
            type="index"
            label="#"
          />
          <el-table-column
            label="类型"
            prop="type"
            show-overflow-tooltip
            min-width="100"
          />
          <el-table-column
            label="告警等级"
            prop="level"
            show-overflow-tooltip
            min-width="120"
          />
          <el-table-column
            label="告警时长"
            prop="time"
            show-overflow-tooltip
            min-width="150"
          />
          <el-table-column
            label="提醒内容"
            prop="content"
            show-overflow-tooltip
            min-width="260"
          />
          <el-table-column
            label="告警时间"
            prop="time"
            show-overflow-tooltip
            min-width="180"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.time }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="接收账号"
            prop="user"
            show-overflow-tooltip
            min-width="120"
          />
          <el-empty slot="empty" :image="require('@/assets/images/nodata.png')" />
  <el-empty slot="empty" :image="require('@/assets/images/nodata.png')" />
</el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { parseTime } from '@/api/utils/share';
export default {
  props:{
    alarmTable: {
      type: Array,
      default: ()=>{return [];}
    },
    dict: {
      type: Object,
      default: ()=>{return {};}
    },
    // 用于判断展示不同服务角色对应的处理人员name
    serviceRole: {
      type: Number,
    }
  },
  data(){
    return{
      activeItem: true
    };
  },
  methods:{
    parseTime,
    getServerState (dictName, val) {
      const valList = val.split(',');
      let labelList = valList.map(element => {
        if (this.dict.dict[dictName] && this.dict.dict[dictName][element]) {
          return this.dict.dict[dictName][element].label;
        }
      });
      let str = labelList.toString();
      return str;
    }
  }
};
</script>

<style lang="less" scoped>
.footer{
  width: calc(100% - 310px);
  height: 200px;
  display: flex;
  padding: 10px;
  background: #fff;
  position: absolute;
  bottom: 0;
  left: 0;
  box-shadow: 0 0 5px #ccc;
  .tab{
    width: 50px;
    background: #f5f5f5;
    height: 100%;
    align-items: center;
    .tab-item{
      color: #777676;
      height: 50%;
      text-align: center;
      font-size: 14px;
      padding: 0 5px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      border-radius: 5px;
      vertical-align: middle;
      position: relative;
      cursor: pointer;
      i{
        font-size: 20px;
      }
    }
    .active-item{
      background: #3885f2;
      color: #fff;
    }
  }
  .tab-container{
    width: calc(100% - 50px);
    padding: 0 10px;
    .table{
      height: 100%;
      ::v-deep .el-table .el-table__cell{
        padding: 5px 0;
      }
    }
  }
}
</style>
