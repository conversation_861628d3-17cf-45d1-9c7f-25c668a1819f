<template>
  <div class="right-box">
    <div class="right-time">
      <div class="right-time-item">
        <span>开始时间：</span>
        <el-date-picker
          v-model="startTime"
          type="datetime"
          size="mini"
          :clearable="false"
          placeholder="选择日期时间"
          value-format="timestamp"
          @blur="judge"
        />
      </div>
      <div class="right-time-item">
        <span>结束时间：</span>
        <el-date-picker
          v-model="endTime"
          type="datetime"
          size="mini"
          :clearable="false"
          placeholder="选择日期时间"
          value-format="timestamp"
          @blur="judge"
        />
      </div>
      <div class="right-time-item">
        <span>查询定位：</span>
        <el-checkbox-group
          v-model="isFilter"
          class="location-checkbox"
        >
          <el-checkbox label="invalid">无效</el-checkbox>
          <el-checkbox label="batch">补报</el-checkbox>
          <el-checkbox label="posSystem">LBS定位</el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="right-time-item check-box">
        <el-checkbox
          v-model="showLocationPoint"
          size="mini"
          @change="pointHandle"
        >
          显示轨迹点位
        </el-checkbox>
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          @click="exportHandle"
        >点位下载</el-button>
      </div>
      <div class="right-time-item right-time-btn">
        <el-button
          type="primary"
          size="mini"
          @click="searchHandle"
        >查询轨迹</el-button>
      </div>
      <div class="right-time-tip">
        （查询时间跨度最大为{{ maxSearchDay }}天）
      </div>
    </div>
    <div class="right-item">
      <div
        class="right-title"
        style="background: rgb(243, 163, 40);"
      >
        定位信息
      </div>
      <div
        class="right-info"
        style="background: rgb(254, 233, 174);"
      >
        <div>
          <span class="label">开始定位时间</span>
          <span class="value">{{ alarmData.startTime || alarmData.alarmTime }}</span>
        </div>
        <div>
          <span class="label">开始地理位置</span>
          <span class="value">{{ alarmData.startAddr || alarmData.startAddress }}</span>
        </div>
        <div>
          <span class="label">结束定位时间</span>
          <span class="value">{{ alarmData.endTime || alarmData.alarmEndTime }}</span>
        </div>
        <div>
          <span class="label">结束地理位置</span>
          <span class="value">{{ alarmData.endAddr || alarmData.endAddress }}</span>
        </div>
      </div>
    </div>
    <div class="right-item">
      <div
        class="right-title"
        style="background: #1f71ff;"
      >
        轨迹日历
      </div>
      <div
        class="right-info"
        style="background: rgb(213, 235, 255);"
      >
        <div class="right-calendar-item">
          <span>月份：</span>
          <el-date-picker
            v-model="month"
            size="mini"
            type="month"
            placeholder="选择月"
          />
        </div>
        <div>
          <el-calendar
            :key="calendarKey"
            v-model="day"
            size="mini"
          >
            <template
              slot="dateCell"
              slot-scope="{data}"
            >
              <div
                slot="reference"
                class="calendar-item"
                :class="{'is-hasData': editCalendar(data.day), 'is-select': data.isSelected}"
                @click="handleCalendar(data)"
              >
                <p>
                  {{ data.day.split('-').slice(2).join() }}
                </p>
              </div>
            </template>
          </el-calendar>
        </div>
      </div>
    </div>
    <!-- <div class="right-item">
      <div
        class="right-title"
        style="background: #1f71ff;"
      >
        车辆信息
      </div>
      <div
        class="right-info"
        style="background: rgb(213, 235, 255);"
      >
        <div>
          <span class="label">车牌号码</span>
          <span class="value">{{ alarmData.licencePlate }}</span>
        </div>
        <div>
          <span class="label">所属公司</span>
          <span class="value">
            石河子市新长运旅客运输有限公司
          </span>
        </div>
        <div>
          <span class="label">车牌颜色</span>
          <span class="value">{{ getEnumDictLabel("licenceColor", alarmData.licenceColor) }}</span>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import { trackDateSearch } from '@/api/monitoring/track.js';
export default {
  props: {
    alarmData: {
      type: Object,
      default: ()=>{return{};}
    },
    dict: {
      type: Object,
      default: ()=>{return{};}
    },
    maxSearchDay: {
      type: Number,
      default: 3
    }
  },
  data(){
    return{
      startTime: null,
      endTime: null,
      showLocationPoint: false,
      month: new Date(),
      day: new Date(),
      dateList: [],
      isFilter: ['invalid'],
    };
  },
  watch: {
    month: {
      handler (newVal) {
        if (newVal) {
          this.day = newVal;
          this.trackHandle();
        }
      }
    }
  },
  methods:{
    clearData () {
      this.showLocationPoint = false;
    },
    trackHandle () {
      const month = this.$moment(this.month).format('YYYY-MM');
      let query = {
        deviceId: this.alarmData.deviceId,
        deviceType: this.alarmData.deviceType,
        targetId: this.alarmData.targetId,
        targetType: this.alarmData.targetType,
        month: month
      };
      trackDateSearch(query).then(res => {
        if (res.data) {
          this.dateList = this.checkDaysWithData(month, res.data);
        } else {
          this.dateList = [];
        }
      });
    },
    checkDaysWithData(month, num) {
      let dateList = [];
      // 将数字转换为二进制字符串，并去掉'0b'前缀
      let binaryStr = num.toString(2);
      // 删除二进制字符串的最后一个字符
      binaryStr = binaryStr.slice(0, -1);
      // 假设num代表的天数不超过31天，因为二进制最多只能表示31天（从第1天到第31天）
      for (let day = 1; day <= binaryStr.length; day++) {
        // 根据二进制字符串的每一位判断当天是否有数据
        if (binaryStr[binaryStr.length - day] === '1') {
          let dayStr = day < 10 ? `0${day}` : day;
          dateList.push(`${month}-${dayStr}`);
        }
      }
      return dateList;
    },
    judge () {
      if (this.startTime) {
        if ( this.startTime > this.endTime ) {
          this.$message({
            type: 'warning',
            message: '开始日期时间要小于结束日期时间'
          });
        }
        if (this.endTime > this.startTime + this.maxSearchDay * 24 * 60 * 60 * 1000) {
          this.$message({
            type: 'warning',
            message: `查询时间跨度最大为${this.maxSearchDay}天`
          });
        }
      }
    },
    // 导出轨迹点位
    exportHandle () {
      if (this.endTime > this.startTime + this.maxSearchDay * 24 * 60 * 60 * 1000) {
        this.$message({
          type: 'warning',
          message: `查询时间跨度最大为${this.maxSearchDay}天`
        });
        return;
      }
      let qurey = {
        deviceId: this.alarmData.deviceId,
        startTime: this.startTime,
        endTime: this.endTime,
        deviceType: this.alarmData.deviceType,
        targetId: this.alarmData.targetId,
        targetType: this.alarmData.targetType,
        isFilter: this.isFilter
      };
      this.$emit('exportHandle', qurey);
    },
    // 显示/隐藏轨迹点
    pointHandle () {
      this.$emit('pointHandle', this.showLocationPoint);
    },
    // 查询轨迹
    searchHandle () {
      if (!this.startTime || !this.endTime) {
        this.$message.warning('请选择查询时间范围');
        return;
      }
      if (this.endTime > this.startTime + this.maxSearchDay * 24 * 60 * 60 * 1000) {
        this.$message({
          type: 'warning',
          message: `查询时间跨度最大为${this.maxSearchDay}天`
        });
        return;
      }
      let qurey = {
        deviceId: this.alarmData.deviceId,
        startTime: this.startTime,
        endTime: this.endTime,
        deviceType: this.alarmData.deviceType,
        targetId: this.alarmData.targetId,
        targetType: this.alarmData.targetType,
        isFilter: this.isFilter
      };
      this.$emit('getTrajectoryData', qurey);
    },
    // 时间赋值
    initValue () {
      this.startTime = this.alarmData.startTime ? this.$moment(this.alarmData.startTime).valueOf() : this.$moment(this.alarmData.alarmTime).valueOf();
      this.endTime = this.alarmData.endTime ? this.$moment(this.alarmData.endTime).valueOf() : this.$moment(this.alarmData.alarmEndTime).valueOf();
      this.isFilter = this.alarmData.isFilter;
    },
    editCalendar (date) {
      let isHas = this.dateList && this.dateList.some(item => item === date);
      return isHas;
    },
    handleCalendar(data){
      this.startTime = this.$moment(data.day).startOf('day').valueOf();
      this.endTime = this.$moment(data.day).endOf('day').valueOf();
      this.searchHandle();
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    }
  }
};
</script>
<style lang="less" scoped>
.right-box{
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 5px;
    box-shadow: 0 0 4px #ccc;
}
.right-item{
    width: 100%;
    padding: 10px;
    display: flex;
    align-items: center;
    color: #333;
    font-size: 12px;
    position: relative;
}
.right-title{
    padding-top: 3px;
    width: 24px;
    height: 92px;
    text-align: center;
    color: #fff;
    position: absolute;
    left: 0;
    font-size: 14px;
}
.right-info{
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 10px 10px 10px 20px;
}
.right-info>div{
  width: 100%;
  display: flex;
  margin-bottom: 5px;
  justify-content: space-between;
}
.right-info .label{
    white-space: nowrap;
    margin-right: 10px;
}
.right-info .value{
    text-align: right;
}
.info-item{
  width: 50% !important;
}
.right-time{
    width: 100%;
    padding: 10px;
    color: #333;
    font-size: 14px;
    &-item{
        display: flex;
        margin-bottom: 10px;
        span{
            white-space: nowrap;
            line-height: 2;
        }
    }
}
.check-box, .right-time-btn{
    display: flex;
    justify-content: space-between;
}
.right-time-btn{
    .el-button{
        width: 100%;
    }
}
.right-time-tip{
    font-size: 12px;
    color: #666;
    text-align: right;
}
.right-calendar-item{
    display: flex;
    span{
        white-space: nowrap;
        line-height: 2;
    }
}
::v-deep .el-calendar__header{
    display: none;
}
::v-deep .el-calendar-table .el-calendar-day{
    height: 40px;
    padding: 0;
    text-align: center;
    line-height: 40px;
}
::v-deep .el-calendar-table:not(.is-range) td.prev{
    visibility: hidden;
}
 ::v-deep .el-calendar-table:not(.is-range) td.next{
    display: none;
 }
 ::v-deep .el-calendar__body{
    padding: 0;
 }
 .is-hasData{
    color: #409EFF;
    font-weight: 700;
    background-color: rgba(255, 0, 0, 0.2);
  }
  .is-select{
    background-color: #409EFF !important;
    font-weight: 700;
    color: white !important;
  }
.location-checkbox {
  .el-checkbox {
    margin-right: 10px;
  }
}
</style>
