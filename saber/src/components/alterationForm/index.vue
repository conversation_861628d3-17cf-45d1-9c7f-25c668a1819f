<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    title="变更"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="140px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 监管目标标识 -->
          <el-form-item
            prop="targetFlag"
          >
            <template #label>
              <div class="form-tips">
                <span>{{ getLabel('targetFlag') }}</span>
                <el-tooltip>
                  <div slot="content">
                    监管车辆时填写公安交通管理部门颁发的机动车号牌
                    <br>
                    铁路货车填写车厢编号
                    <br>
                    集装箱时填写集装箱编号
                    <br>
                    人员填写人员编号(系统唯一)
                    <br>
                    其他类型监管目标时填写终端标识号(终端ID)
                    <br>
                    默认值为终端ID
                    <br>
                  </div>
                  <i class="el-icon-info"/>
                </el-tooltip>
              </div>
            </template>
            <div
              class="mask-operate"
              @click="targetFlagHandle"
            />
            <el-input
              v-model="form.targetFlag"
              readonly
              :placeholder="getPlaceholder('targetFlag')"
            >
              <el-button
                slot="append"
                icon="el-icon-search"
                style="cursor: pointer;"
                type="info"
              />
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 监管目标类型 -->
          <el-form-item
            :label="getLabel('targetModel')"
            prop="targetModel"
          >
            <xh-select
              v-model="form.targetModel"
              :placeholder="getPlaceholder('targetModel')"
              disabled
            >
              <el-option
                v-for="item in dict.targetModel"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="$emit('update:dialogVisible', false)"
      >
        取消
      </el-button>
      <el-button
        :loading="loading"
        type="primary"
        size="small"
        @click="handleSubmit"
      >
        确认
      </el-button>
    </div>
    <BusinessList
      :is-show-table.sync="isShowTable"
      :target-model="dict.targetModel"
      @rowClick="rowClick"
    />
  </el-dialog>
</template>

<script>
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import { edit } from '@/api/base/awaitAllot';
import BusinessList from '@/components/businessList/businessList.vue';

export default {
  components: { BusinessList },
  props: {
    dict: {
      type: Object,
      required: true
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    alterationData: {
      type: Object,
      default: () => { return {}; }
    }
  },
  data () {
    return {
      rules: {
        targetFlag: { required: true, message: '请输入监管目标标识', trigger: 'blur' } // 监管目标标识
      },
      form: {
        targetFlag: null,
        targetModel: null,
        deviceId: null,
        deviceType: null
      },
      loading: false,
      isShowTable: false
    };
  },
  watch: {
    dialogVisible: {
      handler(val) {
        if (val) {
          this.form = { ...this.form, ...this.alterationData };
        }
      }
    }
  },
  methods: {
    rowClick (row) {
      this.form.targetFlag = row.targetFlag;
      this.form.targetModel = row.targetModel;
    },
    targetFlagHandle () {
      this.isShowTable = true;
    },
    handleSubmit () {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const params = JSON.parse(JSON.stringify(this.form));
          this.loading = true;
          edit(params).then(res => {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.$emit('refresh');
            this.$emit('update:dialogVisible', false);
          }).finally(() => {
            this.loading = false;
          });
        }
      });
    },
    // 监听关闭事件
    closed () {
      this.$emit('update:dialogVisible', false);
      this.$refs?.form?.resetFields();
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('AwaitAllot', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('AwaitAllot', value);
    }
  }
};
</script>

<style lang="less" scoped>
.form-tips {
  display: inline-block;
  .el-icon-info {
    color: #409eff;
    font-size: 16px;
    position: relative;
    top: 2px;
  }
}
.mask-operate {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  z-index: 1;
}
</style>
