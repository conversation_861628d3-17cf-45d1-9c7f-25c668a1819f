<template>
  <div class="table-container">
    <el-table
      ref="multipleTable"
      v-loading="loading"
      :data="tableData"
      tooltip-effect="dark"
      style="height: 450px"
      border
      highlight-current-row
      @row-click="rowClick"
    >
      <el-table-column
        v-for="(item) in heads"
        :key="item.columnName"
        :prop="item.columnName"
        :label="item.columnTitle"
        align="center"
        :width="item.width || undefined"
        :show-overflow-tooltip="true"
      />
    </el-table>
    <div class="bottom">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-size="query.size"
        :page-sizes="[10, 20, 30, 40, 50, 100]"
        :current-page="query.current"
        :total="totalPage"
        @current-change="pageChange"
        @size-change="sizeChangeHandler"
      />
    </div>
  </div>
</template>
<script>
export default {
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    heads: {
      type: Array,
      default: () => []
    },
    isShowTable: {
      type: Boolean,
      default: false
    },
    totalPage: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      query: {
        size: 10,
        current: 1,
      },
      loading: false
    };
  },
  watch: {
    isShowTable(val) {
      if(!val) {
        this.$refs.multipleTable?.clearSelection();
      }
    }
  },
  methods: {
    rowClick (row) {
      this.$emit('rowClick', row);
    },
    pageChange (val) {
      this.query.current = val;
      this.$emit('getData');
    },
    sizeChangeHandler (val) {
      this.query.size = val;
      this.$emit('getData');
    },
  }
};
</script>

<style lang="less" scoped>
.table-container {
  /deep/ .el-table__row.current-row > td {
    background-color: rgba(var(--gn-color-rgb), .4) !important;
  }
}
</style>