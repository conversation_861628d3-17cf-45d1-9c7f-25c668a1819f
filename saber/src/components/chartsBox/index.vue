<template>
  <div ref="chartEl"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "chartsBox",
  props: {
    options: {
      type: "Object",
      default: () => {},
    },
    lazyUpdate: {
      type: Boolean,
      default: false
    },
    onlyUpDateSeries: {
        type: Boolean,
        default: false
    }
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    options: {
      handler(newValue, oldValue) {
        this.$nextTick(() => {
          if(!this.onlyUpDateSeries) {
            // 调用 clear 方法清空图表中的数据,解决echars数据发生变化图表没变化的问题
            this.chart.clear();
            this.chart.setOption(newValue);
          } else {
            this.chart.setOption(
              {
                series: newValue.series,
                xAxis: newValue.xAxis
              },
              {
                lazyUpdate: this.lazyUpdate
              }
            );
          }
        });
      },
      deep: true,
    },
  },
  mounted() {
    this.chart = echarts.init(this.$refs.chartEl);
    this.chart.setOption(this.options);
    window.addEventListener('resize', () => {
      this.chart.resize()
    })
  },
  methods: {},
};
</script>

<style>
</style>
