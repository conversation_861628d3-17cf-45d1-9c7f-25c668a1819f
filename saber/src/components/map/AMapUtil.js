'use strict';

/**
 * 高德地图工具
 * @exports AmapUtil
 * @alias AmapUtil
 */
let AmapUtil = {};

/**
 * 加载高德地图
 * @param {Function} callback 回调函数
 * @param {Object} [options] 其他的加载参数
 */
AmapUtil.loadAMap = function (callback, options) {
  // 安全密钥
  window._AMapSecurityConfig = {
    securityJsCode: "c7cefc9b1e83956e47f1462a7b7d104c",
  };
  // console.log('callback', callback, options);
  // key来自余立能的高德账号
  const MAP_URL = 'https://webapi.amap.com/maps?v=2.0&key=43a47c5fa142b49247674a9553232e2b'; // 余立能账号
  if (window.AMap) {
    callback(window.AMap);
  } else {
    let script = document.createElement('script');
    script.type = 'text/javascript';
    script.async = true;
    script.src = MAP_URL;
    // script.onerror = reject;
    document.head.appendChild(script);
    setTimeout(() => {
      AmapUtil.loadAMap(callback, options);
    }, 500);
  }
};

export default AmapUtil;
