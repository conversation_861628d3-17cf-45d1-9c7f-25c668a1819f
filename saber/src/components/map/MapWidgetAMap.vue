<template>
  <div
    ref="mapContainer"
    class="map"
  >
    <!-- 工具栏 -->
    <div class="map-tool-bar">
      <div class="map-tool-bar-search">
        <SearchMap
          v-if="toolConfig.searchToolShow"
          ref="SearchMap"
        >
          <div class="search-icon">
            <i
              class="el-icon-search"
            />
          </div>
        </SearchMap>
      </div>
      <div class="map-tool-map-control">
        <div
          v-for="item in btnNeedShowData"
          :key="item.name"
          class="map-control-text"
          @click="handleClick(item)"
        >
          <i
            v-if="item.icon"
            :class="item.icon"
          />{{ item.name }}
        </div>
        <el-dropdown
          v-if="toolConfig.mapToolsShow"
          @command="handleCommand"
        >
          <span class="el-dropdown-link">
            <i class="tool" />工具<i class="el-icon-arrow-down el-icon--right" />
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="onRuleClick">
              测距
            </el-dropdown-item>
            <el-dropdown-item command="onMeasureAreaClick">
              测面
            </el-dropdown-item>
            <el-dropdown-item command="onCloseClick">
              清除
            </el-dropdown-item>
            <!-- <el-dropdown-item
              v-for="item in btnNeedShowData"
              :key="item.name"
              :icon="item.icon"
              @click.native="handleClick(item)"
            >{{ item.name }}
            </el-dropdown-item> -->
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <LayerSelect
      v-show="false"
      ref="LayerSelect"
      :class="{selectedColor:isShowLayerSelect}"
      @changeLayerSelect="changeLayerSelect"
    />
    <DrawMarker
      v-show="false"
      ref="DrawMarker"
      :class="{selectedColor:isShowDrawMarker}"
      @changeDrawMarker="changeDrawMarker"
      @setFitView="setFitView"
      @setMarkerCenter="setMarkerCenter"
      @setZoomAndCenter="setZoomAndCenter"
      v-on="$listeners"
    />
    <MapTools
      v-show="false"
      ref="MapTools"
      :class="{selectedColor:isShowMapTools}"
      @changeMapTools="changeMapTools"
    />
    <!-- 搜索框 -->
    <!-- <div
      v-show="showFold"
      class="mapWidgetAMap"
    >
      <img
        src="@/assets/images/arrow.png"
        @click="clickArrow"
      >
      <SearchMap ref="SearchMap" />
    </div> -->
    <!-- 区域规则
      v-show="showZoneTool"-->
    <div
      v-show="false"
      class="mapWidgetAMap"
    >
      <img
        src="@/assets/images/arrow.png"
        class="planelSpan"
        @click="clickZoneTool"
      >
      <RectangleEditor
        ref="RectangleEditor"
        :class="{selectedColor:isShowRectangle}"
        @changeRectangle="changeRectangle"
      />
      <CircleEditor
        ref="CircleEditor"
        :class="{selectedColor:isShowCircle}"
        @changeCircle="changeCircle"
      />
      <PolygonEditor
        ref="PolygonEditor"
        :class="{selectedColor:isShowPolygon}"
        @changePolygon="changePolygon"
      />
      <PolylineEditor
        ref="PolylineEditor"
        :class="{selectedColor:isShowPolyline}"
        @changePolyline="changePolyline"
      />
    </div>

     <!-- 信息浮窗 -->
    <div>
      <PlaybackInformation
        ref="PlaybackInformation"
        :currentData="currentInfoData"
        v-bind="$attrs"
        v-on="$listeners"
      />
    </div>

    <!-- 视频页地图折叠按钮 -->
    <!--    <div-->
    <!--      v-show="showFoldButton"-->
    <!--      class="xh-select-component-toggle-button videoMapContent"-->
    <!--      @click="hideMapClick"-->
    <!--    >-->
    <!--      >>-->
    <!--    </div>-->
    <!-- 地图Marker管理 -->
    <CarMarkerManage
      ref="CarMarkerManage"
      v-bind="$attrs"
      @setZoomAndCenter="setZoomAndCenter"
      @onPathWatchClick="onPathWatchClick"
      v-on="$listeners"
    />
    <!-- 历史轨迹检索 -->
    <CarHistorySearch
      v-show="CarHistorySearchVisible"
      ref="carHistorySearch"
      class="pathHistory"
      @onCarHistorySearch="onCarHistorySearch"
      @onCarHistoryCancel="onCarHistoryCancel"
    />
    <!-- 历史轨迹回放面板 -->
    <CarHistoryPlanel
      v-show="CarHistoryPlanelVisible"
      ref="carHistoryPlanel"
      class="CarHistoryPlanel"
      @onCarHistoryPlanelCancel="onCarHistoryPlanelCancel"
      @setTrackCarMarker="setTrackCarMarker"
    />
  </div>
</template>

<script>
/**
 * 地图组件
 */
import PlaybackInformation from './track/PlaybackInformation';
import AMapUtil from './AMapUtil';
import StringUtil from '@/utils/helper/StringUtil';
import configMap from '@/config/configMap';
import RectangleEditor from './tools/RectangleEditor';
import CircleEditor from './tools/CircleEditor';
import PolygonEditor from './tools/PolygonEditor';
import PolylineEditor from './tools/PolylineEditor';
// import SetMapCenter from './tools/SetMapCenter';
// import DrivingLine from './tools/DrivingLine';
import LayerSelect from './tools/LayerSelect';
import DrawMarker from './tools/DrawMarker';
import SearchMap from './tools/SearchMap';
import MapTools from './tools/MapTools';
import CarMarkerManage from './track/CarMarkerManage';
import CarHistorySearch from './track/CarHistorySearch';
import CarHistoryPlanel from './track/CarHistoryPlanel';
import { queryTracking, queryTrackingByPage ,queryTrackingUnablePoint ,queryTrackingByPageWithStopPoint } from '@/api/monitoring/track.js';
import {getCarStatus} from '@/utils/getCarStatus';
import { pagination } from '@/api/base/point.js';
import {mapIp} from '@/api/home/<USER>';

// 图资尺寸 : 用户偏移位置
const ICON_SIZE = 32;
export default {
  name: 'MapWidgetAMap',
  components: {
    PolygonEditor,
    RectangleEditor,
    CircleEditor,
    PolylineEditor,
    // SetMapCenter,
    // DrivingLine,
    LayerSelect,
    DrawMarker,
    SearchMap,
    MapTools,
    CarMarkerManage,
    CarHistorySearch,
    PlaybackInformation,
    CarHistoryPlanel
  },
  props: {
    // 配置需要的工具栏
    toolConfig: {
      type: Object,
      default: () => {
        return {
          routeRegionEdit: false, // 跳转区域编辑
          routePolylineEdit: false, // 跳转路线编辑
          routePointEdit: false, // 跳转标注点编辑
          drawMarkerShow: true, // 标注点
          polylineEditorShow: true, // 绘制直线
          showZoneToolShow: true, // 绘制区域
          searchToolShow: true, // 搜索
          clearBtnShow: true, // 清除按钮
          returnBtnShow: true, // 回到中心
          setCenterShow: true, // 设置中心
          trafficLayerShow: true, // 路况
          layerSelectShow: true, // 卫星图
          drivingLineShow: true, // 路径规划
          mapToolsShow: true, // 工具栏
          closePointerDefault: false, // 默认显示所有预设点
          regionShow: false, // 电子围栏
          showMapLabel: true // 地图标注
        };
      }
    },
    showTools: {
      type: Boolean,
      default: true
    },
    showFoldButton: {
      type: Boolean,
      default: false
    },
    fromQueryRegion: {
      type: Boolean,
      default: false
    },
    showRightContext: {
      type: Boolean,
      default: true
    },
    isClearMap: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      currentInfoData:null,
      btnData: [
        {
          name: '线路',
          path: '/base/routeEditWorkspace',
          show: this.toolConfig.routePolylineEdit
        },
        {
          name: '标注点',
          path: '/base/pointEditWorkspace',
          show: this.toolConfig.drawMarkerShow
        },
        {
          name: '区域',
          // path: '/security/alarm/regionAlarmEditWorkspace',
          path: '/base/regionEditWorkspace',
          show: this.toolConfig.routeRegionEdit
        },
        {
          name: '电子围栏',
          clickHandle: this.handleRegion,
          show: this.toolConfig.regionShow
        },
        {
          name: '路况',
          icon: 'traffic',
          clickHandle: this.showTrafficLayer,
          show: this.toolConfig.trafficLayerShow
        },
        {
          name: '卫星',
          icon: 'satellite',
          clickHandle: this.showSatellite,
          show: this.toolConfig.layerSelectShow
        }
      ], // 按钮数组
      /** 地图对象 */
      map: null,
      /** 高德地图 : 方便调用 API */
      AMap: null,
      /** 随机一个id给高德地图 */
      mapId: '',
      /** 鼠标工具 */
      mousetool: null,
      /** 地图 OverlayGroup 对象 */
      carsClusterer: null,
      /** 信息窗体 */
      carInfoWindow: null,
      config: null,
      satelliteLayer: null,
      trafficLayer: null,
      loaded: false,
      isShowTrafficLayer: true,
      showFold: false,
      showZoneTool: false,
      /** 标签颜色 */
      isShowLayerSelect: false,
      isShowDrivingLine: false,
      isShowMapTools: false,
      isShowMapCenter: false,
      isShowPolyline: false,
      isShowPolygon: false,
      isShowRectangle: false,
      isShowCircle: false,
      isShowDrawMarker: false,
      CarHistorySearchVisible: false,
      CarHistoryPlanelVisible: false,
      Player: {
        index: 0,
        carsData: [],
        car: null,
        coors: [],
        speed: 1000
      },
      driving: null,
      startPoint: null,
      endPoint: null,
      startMarker: [],
      endMarker: [],
      CarMarkers: null,
      // 暂存marker集合
      pointMarkers: [],
      startEndMarker: [], // 起点终点marker
      stopPointMarker:[],// 停靠点marker
      stopPointCoors: [], // 停靠点集合
      unablePointLines:[], // 轨迹回放无效点路线list
      showWatchCard: false,
      trackInfo: null,
      baseTrackSubSize: 5000, // 轨迹回放首次加载的数据长度
      otherTrackSubSize: 10000, // 轨迹回放后续加载的数据长度
      pageFlag: false, // 分页加载开始标识
    };
  },
  computed: {
    btnNeedShowData () {
      return this.btnData.filter(item => {
        return item.show;
      });
    }
  },
  watch: {
    'startMarker': function (val) {
      if (val && val.length > 0) {
        this.createDriving();
      }
    }
  },
  activated () {
    this.mapReload();
  },
  mounted () {
    this.$nextTick(() => {
      // console.log('------------>初始化地图');
      this.init();
      if (this.fromQueryRegion) {
        this.showFold = true;
        this.showZoneTool = false;
      }
    });
  },
  methods: {
    mapReload () {
      let canvas = document.getElementsByClassName('amap-layer')[0];
      if (canvas) {
        console.log(canvas); // 打印绘制的canvas
        let canvasContent = canvas.getContext('webgl'); // 因为高德地图是通过webGl来绘制的，所以通过getContext(‘webgl’)才能获取到内容
        console.log(canvasContent);
        if (canvasContent?.drawingBufferHeight < 10 && canvasContent?.drawingBufferWidth < 10) {
          this.init();
          this.$emit('mapReload');
        }
      }
    },
    /**
     * 初始化
     */
    init () {
      AMapUtil.loadAMap(AMap => {
        this.AMap = AMap;
        this.mapId = StringUtil.generateGuid();
        this.$refs.mapContainer.id = this.mapId;
        this.config = configMap.default;
        this.$nextTick(() => {
          let satelliteLayer = new AMap.TileLayer.Satellite();
          // mapIp({ip: window.location.hostname}).then(ipLocationRes => { // TODO
          let center = [this.config.position.longitude, this.config.position.latitude];
          // if (ipLocationRes.data.longitude && ipLocationRes.data.latitude) {
          //   center = [ipLocationRes.data.longitude, ipLocationRes.data.latitude];
          // } else {
          //   center = [this.config.position.longitude, this.config.position.latitude];
          // }
          this.map = new this.AMap.Map(this.mapId, {
            resizeEnable: true, // 是否监控地图容器尺寸变化
            zoom: this.config.zoom, // 初始化地图层级
            center: center, // 初始化地图中心点
            // center: [this.config.position.longitude, this.config.position.latitude], // 初始化地图中心点
            expandZoomRange: true,
            //最大缩放级别设置成26导致地图缩放到极致10cm 同一经纬度绘制的线路有微小偏差
            zooms: [2, 22],
            animateEnable: false,
            showLabel: this.toolConfig.showMapLabel,
            features: ['bg', 'point', 'road']
            // center: [this.config.position.longitude, this.config.position.latitude] // 初始化地图中心点
            // mapStyle: 'amap://styles/79a81ba37c4c4af76716982dc136b2d1' // 设置自定义地图的显示样式 // 余立能账号
          });
          this.AMap.plugin([
            'AMap.Scale',
            'AMap.ToolBar',
            'AMap.MouseTool',
            'AMap.MarkerCluster',
            'AMap.MapType',
            'AMap.RangingTool',
            'AMap.RectangleEditor',
            'AMap.CircleEditor',
            'AMap.PolygonEditor',
            'AMap.PolylineEditor',
            'AMap.AutoComplete',
            'AMap.PlaceSearch',
            'AMap.InfoWindow',
            'AMap.ContextMenu',
            'AMap.MassMarks',
            'AMap.LabelsLayer',
            'AMap.LabelMarker',
            'AMap.Geocoder',
            'AMap.MoveAnimation',
            'AMap.Driving'
          ], () => {
            // 工具条
            this.toolBar = new this.AMap.ToolBar({
              position: 'RB',
              offset: new this.AMap.Pixel(10, 50),
              locate: false,
              ruler: false
            });
            this.map.addControl(this.toolBar);
            this.map.addControl(new this.AMap.Scale());
            // 鼠标工具
            this.mousetool = new this.AMap.MouseTool(this.map);
            this.carsClusterer = new this.AMap.MarkerCluster(
              this.map,
              [],
              {
                gridSize: 80,
                minClusterSize: 3,
                maxZoom: 16
              }
            );
            this.trafficLayer = new this.AMap.TileLayer.Traffic();
            this.satelliteLayer = satelliteLayer;
            this.loaded = true;
            this.CarHistoryPathPass = this.getPath([], {
            });
            this.DisContPathPass = this.getPath([], {
              strokeColor: '#ff2d51',
              strokeOpacity: 1
            });

            var contextMenu = new this.AMap.ContextMenu();
            // 添加右键菜单内容项
            contextMenu.addItem('放大', () => {
              this.map.zoomIn();
            }, 0);
            contextMenu.addItem('缩小', () => {
              this.map.zoomOut();
            }, 1);
            // contextMenu.addItem('设置起点', (e) => {
            //   this.startMarker = [];
            //   let marker = new this.AMap.Marker({
            //     map: this.map,
            //     position: this.contextMenuPositon // 基点位置
            //   });
            //   this.startMarker.push(this.contextMenuPositon);
            // }, 2);
            // contextMenu.addItem('设置终点', (e) => {
            //   this.endMarker = [];
            //   let marker = new this.AMap.Marker({
            //     map: this.map,
            //     position: this.contextMenuPositon // 基点位置
            //   });
            //   this.endMarker.push(this.contextMenuPositon);
            //   this.createDriving();
            // }, 3);
            contextMenu.addItem('清除', () => {
              this.startMarker = [];
              this.endMarker = [];
              this.mousetool.close(true);
              if (this.isClearMap) {
                this.map.clearMap();
              }
              this.$emit('handleClear');
              contextMenu.close(); // 第一次点击时不会自动关闭, 因此手动调用关闭
            }, 4);
            // 监听鼠标右击事件
            if (this.showRightContext) {
              this.map.on('rightclick', (e) => {
                this.contextMenuPositon = e.lnglat;
                contextMenu.open(this.map, e.lnglat);
              });
            }
            // this.map.setMapStyle('amap://styles/blue');
            this.map.add(this.CarHistoryPathPass);
            this.map.add(this.DisContPathPass);
            /**
             * 初始化地图搜索工具
             */
            if (this.$refs.SearchMap) {
              this.$refs.SearchMap.init(this);
            }
            /**
             * 初始化矩形编辑工具
             */
            this.$refs.RectangleEditor.init(this);
            /**
             * 初始化圆形编辑工具
             */
            this.$refs.CircleEditor.init(this);
            /**
             * 初始化多边形编辑工具
             */
            this.$refs.PolygonEditor.init(this);
            /**
             * 初始化折线编辑工具
             */
            this.$refs.PolylineEditor.init(this);
            /**
             * 初始化标注点工具
             */
            this.$refs.DrawMarker.init(this);
            // /**
            //  * 初始化位置设置工具
            //  */
            // this.$refs.SetMapCenter.init(this);
            // /**
            //  * 初始化路径规划工具
            //  */
            // this.$refs.DrivingLine.init(this);
            /**
             * 初始化卫星图层切换工具
             */
            this.$refs.LayerSelect.init(this);
            /**
             * 初始化地图工具
             */
            this.$refs.MapTools.init(this);
          });
          this.initMouseLeftClickEvent();

          // 地图图块加载完成后触发
          this.map.on('complete', () => {
            this.$emit('mapLoaded', this);
            // 默认绘制所有预设点
            !this.toolConfig.closePointerDefault && this.drawPointerDefault();
          });
          // 地图缩放事件[添加/移除点]
          this.map.on('zoomend', (ev) => {
            let zoom = this.map.getZoom();
            this.pointMarkers.forEach(item => {
              if (item.mapLevel > zoom) {
                this.map.remove(item);
              } else {
                this.map.add(item);
              }
            });
          });
          // }).catch(error => {
          //   console.log(error);
          // });
        });
        // 检测高德地图是否绘制成功, 不成功重新初始化
        this.$nextTick(() => {
          let canvas = document.getElementsByClassName('amap-layer')[0];
          let canvasContent = canvas?.getContext('webgl');
          console.log('canvasContent.drawingBufferHeight', canvasContent?.drawingBufferHeight);
          if (!canvasContent?.drawingBufferHeight) {
            setTimeout(() => {
              this.init();
            }, 10);
          }
        });
      });
    },
    /**
     * 初始化鼠标左键单击事件
     * @emits event
     * @emits {Number} event.pixel.x 像素坐标x
     * @emits {Number} event.pixel.y 像素坐标y
     * @emits {Number} event.coordinate.longitude 经度
     * @emits {Number} event.coordinate.latitude 纬度
     */
    initMouseLeftClickEvent () {
      this.map.on('click', event => {
        /* let contextmenu = new this.AMap.ContextMenu();
        contextmenu.open(this.map, event.lnglat); */
        // // 触发事件的对象
        // let target = event.target;
        // // 触发事件的地理坐标，AMap.LngLat 类型
        // let lnglat = event.lnglat;
        // // 触发事件的像素坐标，AMap.Pixel 类型
        // let pixel = event.pixel;
        // // 触发事件类型
        // let type = event.type;
        console.log('-> event', event);
        this.$emit('leftClickEvent', {
          pixel: {
            x: event.pixel.x,
            y: event.pixel.y
          },
          coordinate: {
            longitude: event.lnglat.lng,
            latitude: event.lnglat.lat
          }
        });
      });
    },
    /**
     * 设置地图中心的位置
     * @param {Object} [options]
     * @param {Number} [options.longitude] 经度
     * @param {Number} [options.latitude] 纬度
     * @param {Number} [options.zoom=18] 地图层级
     */
    setZoomAndCenter (options) {
      if (options) {
        let zoom = options.zoom || 18;
        this.map.setZoomAndCenter(zoom, [options.longitude, options.latitude]);
      }
    },
    /**
     * 调整到最佳视角
     * @param [obj] 高德地图覆盖物
     */
    setFitView (_obj) {
      if (!this.loaded) {
        setTimeout(() => {
          this.setFitView(_obj);
        }, 200);
      } else {
        let obj = _obj || undefined;
        this.map.setFitView(obj);
      }
    },
    // 设置中心点|最后点击的marker
    setMarkerCenter (LngLat) {
      let position = new this.AMap.LngLat(LngLat[0], LngLat[1]);
      this.map.setCenter(position);
    },
    /**
     * 处理标签类型是否可见
     * @param {Object} labelTypeItem
     * @param {Boolean} labelTypeItem.visible 设置成是否可见
     * @param {Number} labelTypeItem.labelTypeId 标签类型ID
     * @param {String} labelTypeItem.labelTypeName 标签类型名称
     */
    setLabelTypeVisible (labelTypeItem) {
      if (labelTypeItem.visible) {
        this.markerCollection[labelTypeItem.labelTypeId].cluster.setMarkers(this.markerCollection[labelTypeItem.labelTypeId].markers);
      } else {
        this.markerCollection[labelTypeItem.labelTypeId].cluster.clearMarkers();
      }
    },
    getMapInit () {
      return {
        AMap: this.AMap,
        map: this.map,
        mousetool: this.mousetool
      };
    },
    /**
     * 视频页面中标注车辆Marker
     */
    setMarkerFromVideo (carInfo) {
      let options = {
        longitude: carInfo.stateObj.longitude || this.config.position.longitude,
        latitude: carInfo.stateObj.latitude || this.config.position.latitude,
        icon: ''
      };
      this.$refs.DrawMarker.drawTempMarker(options);
    },
    /**
     * 视频页面中删除车辆Marker
     */
    deleteMarkerFromVideo () {
      this.$refs.DrawMarker.removeTempMarker();
    },
    /**
     * 显示电子围栏
     */
    handleRegion () {
      if (!this.loaded) {
        setTimeout(() => {
          this.handleRegion();
        }, 200);
      } else {
        this.$emit('handleRegion');
      }
    },
    /**
     * 显示路况图层
     */
    showTrafficLayer () {
      if (!this.loaded) {
        setTimeout(() => {
          this.showTrafficLayer();
        }, 200);
      } else {
        if (this.trafficLayer && this.isShowTrafficLayer) {
          this.trafficLayer.setMap(this.map);
          this.trafficLayer.show();
          this.isShowTrafficLayer = false;
        } else {
          this.trafficLayer.setMap(null);
          this.trafficLayer.hide();
          this.isShowTrafficLayer = true;
        }
      }
    },
    createDriving () {
      if (this.startMarker.length > 0 && this.endMarker.length > 0) {
        let startPoint = [this.startMarker[0].lng, this.startMarker[0].lat];
        let endPoint = [this.endMarker[0].lng, this.endMarker[0].lat];
        // 路径规划
        if (this.driving) {
          this.driving.clear();
        }
        this.driving = new this.AMap.Driving({
          map: this.map
        });
        this.driving.search(startPoint, endPoint, (status, result) => {
          if (status === 'complete') {
            console.log('路径规划成功');
          } else {
            console.log(result);
          }
        });
      }
    },
    /**
     * 回到中心
     */
    goMapCenter () {
      let option = {
        zoom: this.config.zoom,
        longitude: this.config.position.longitude,
        latitude: this.config.position.latitude
      };
      this.setZoomAndCenter(option);
    },
    /**
     * 关闭消息窗口
     */
    closeInfoWindow () {
      if (this._infoWindow) {
        this._infoWindow.close();
      }
    },
    /**
     * 清除地图标注等
     */
    clearAll () {
      if (this.map) {
        this.map.clearMap();
        // 预设点不能被删除的
        !this.toolConfig.closePointerDefault && this.drawPointerDefault();
      } else {
        setTimeout(() => {
          this.clearAll();
        }, 200);
      }
    },
    /**
     * 切换折叠状态
     */
    clickArrow () {
      this.showFold = !this.showFold;
      if (!this.showFold) {
        this.clearSearchResult();
      }
    },
    /**
     * 清空输入框查询
     */
    clearSearchResult () {
      this.$refs?.SearchMap.clearSearchResult();
    },
    /**
     * 设置区域规划控件显示与否
     * @param {Boolean} [value] 这个值为空时取反
     */
    clickZoneTool (value) {
      if (value === true) {
        this.showZoneTool = value;
      } else {
        this.showZoneTool = !this.showZoneTool;
      }
    },
    /**
     * 隐藏地图部分
     */
    hideMapClick () {
      this.$emit('changeMap');
    },
    setFixInfo(row){
      this.currentInfoData = row[0];
    },
    changeLayerSelect (status) {
      this.isShowLayerSelect = status;
    },
    changeDrivingLine (status) {
      this.isShowDrivingLine = status;
    },
    changeMapTools (status) {
      this.isShowMapTools = status;
    },
    changeMapCenter (status) {
      this.isShowMapCenter = status;
    },
    changePolyline (status) {
      this.isShowPolyline = status;
    },
    changePolygon (status) {
      this.isShowPolygon = status;
    },
    changeRectangle (status) {
      this.isShowRectangle = status;
    },
    changeCircle (status) {
      this.isShowCircle = status;
    },
    changeDrawMarker (status) {
      this.isShowDrawMarker = status;
    },
    /**
     * 绘制临时的单个标签
     * <AUTHOR>
     * @param options
     * @param {Number} options.longitude 经度
     * @param {Number} options.latitude 纬度
     * @param {String} [options.icon] 图标url或base64
     */
    drawTempMarker (options) {
      this.$refs.DrawMarker.drawTempMarker(options);
    },
    // 绘制设备图标(新)
    setDeviceMarker(val) {
      // 切换车辆时,清除未关闭的窗口
      this.$refs.CarMarkerManage.dialogVisible = false;
      // 清除地图车辆Marker和打开的Marker窗口
      if (!val) {
        this.$refs.CarMarkerManage.clearOldMarker();
        this.$refs.CarMarkerManage.closeDialog();
        return;
      }
      /**
       * 绘制地图中的车辆Marker
       */
      this.$refs.CarMarkerManage.setDeviceMarkerInit(this, val);
    },
    getPath (_coors, _opts) {
      // 解析 coors
      let opts = _opts || {};
      let coors = _coors || [];
      var polylinePath = [];
      for (let i = 0; i < coors.length; i++) {
        let lnglat = new this.AMap.LngLat(coors[i][0], coors[i][1]);
        polylinePath.push(lnglat);
      }

      // 构建轨迹对象
      let linePath = new this.AMap.Polyline({
        path: polylinePath,
        strokeColor: opts.strokeColor || '#073169',
        strokeOpacity: opts.strokeOpacity || 0.8,
        strokeWeight: 5,
        showDir: opts.showDir !== false,
        zIndex: opts.zIndex || 50,
        lineJoin: 'round'
      });
      // 返回对象
      return linePath;
    },
    onPathWatchClick (carInfo) {
      this.$refs.carHistorySearch.setName(carInfo.licencePlate);
      this.$refs.carHistorySearch.resetTime(carInfo.licencePlate);
      this.CarHistorySearchVisible = true;
      this.$refs.carHistorySearch.resetTrackHandle();
      this.$nextTick(() => {
        this.$refs.carHistorySearch.trackHandle();
      });
      // 关闭监控面板
      this.$emit('showCarList', false);
    },
    onCarHistorySearch () {
      let carSearch = this.$refs.carHistorySearch.getSearchInfo();
      let parme = {
        licence_plate: carSearch.name,
        start_time: carSearch.start,
        end_time: carSearch.end,
        is_filter: carSearch.isFilter
      };
      const loading = this.$loading({
        text: '正在加载',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      queryTracking(parme).then(res => {
        loading.close();
        // console.log(res);
        // 检查路线
        if (!res.data || !res.data.locations) {
          this.$message({
            message: '无路线数据，请重新查找',
            type: 'warning'
          });
          return;
        }
        // 车辆图标清理
        this.$refs.CarMarkerManage.clearOldMarker();
        // 关闭轨迹搜索
        this.CarHistorySearchVisible = false;
        // 关闭轨迹面板
        this.$emit('showCarList', false);
        // 清理轨迹
        this.onCarHistoryPlanelCancel();

        /** 面板显示 */
        this.CarHistorySearchVisible = false;
        this.CarHistoryPlanelVisible = true;
        // 绘制路线
        let coors = this.formatPathData(res.data);
        this.Player.coors = Object.freeze(coors);
        this.Player.path = Object.freeze(coors);
        this.Player.carsData = Object.freeze(res.data);
        let status = getCarStatus();
        // const running = res.data[0].speed > 0 ? 1 : 0;
        this.Player.car = this.getTrackCarMarker({
          status: status[5 + res.data[0].running],
          position: this.Player.path[0],
          label: res.data[0].licencePlate,
          angle: res.data[0].bearing
        });
        // 设置相关元素进面板
        this.$refs.carHistoryPlanel.setTrackElement({
          map: this.map,
          amap: this.AMap,
          car: this.Player.car,
          track: res.data,
          path: this.CarHistoryPathPass,
          disCont: this.DisContPathPass
        });
        // 地图添加与缩放
        this.map.add(this.Player.car);
        // this.map.setFitView();
        this.map.panTo(coors[0]);
        // 面板更新
        let historyInfo = this.Player.carsData[this.Player.index];
        historyInfo.index = this.Player.index;
        historyInfo.total = this.Player.path.length - 1;
        // historyInfo.status = status[historyInfo.locState].text;
        historyInfo.timeStart = carSearch.start;
        historyInfo.timeEnd = carSearch.end;
        this.$refs.carHistoryPlanel.setInfoData(historyInfo);
        // 表单绘制
        this.$refs.carHistoryPlanel.setChartData(res.data);
        // 获取轨迹完整率数据
        this.$refs.carHistoryPlanel.getIntegrityData(parme);
      }).catch(() => {
        loading.close();
        this.$message({
          message: '轨迹查询失败',
          type: 'error'
        });
      });
    },
    // 设备查询轨迹
    onDeviceAllTrackSearch (carSearch) {
      let parme = {
        startTime: carSearch.start,
        endTime: carSearch.end,
        deviceId: carSearch.deviceId ? BigInt(carSearch.deviceId) : undefined,
        deviceType: carSearch.deviceType ? carSearch.deviceType : undefined,
        targetId: carSearch.targetId ? BigInt(carSearch.targetId) : undefined,
        targetType: carSearch.targetType ? carSearch.targetType : undefined
      };
      if (carSearch.isFilter?.length) {
        for (let index = 0; index < carSearch.isFilter.length; index++) {
          const element = carSearch.isFilter[index];
          parme[element] = 1;
        }
      }
      const loading = this.$loading({
        text: '正在加载',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      queryTracking(parme).then(res => {
        loading.close();
        // 检查路线
        if (!res.data?.locations?.length) {
          this.$message({
            message: '无路线数据，请重新查找',
            type: 'warning'
          });
          // 隐藏已绘制的轨迹
          if (this.CarHistoryPathPass) this.CarHistoryPathPass.hide();
          return;
        }
        for (let index = 0; index < res.data.locations.length; index++) {
          const element = res.data.locations[index];
          element.targetType = res.data.targetType;
          element.targetId = res.data.targetId;
          element.targetName = res.data.targetName;
          element.deviceModel = res.data.deviceModel;
          element.deviceNum = res.data.deviceNum;
          element.deviceType = res.data.deviceType;
          element.deviceCategory = res.data.deviceCategory;
          element.deviceId = res.data.deviceId;
          element.deviceUniqueId = res.data.deviceUniqueId;
          element.longitudeTable = element.longitude;
          element.latitudeTable = element.latitude;
        }
        const locationData = this.$utils.wgs84togcj02Batch(res.data.locations);
        // 车辆图标清理
        this.$refs.CarMarkerManage.clearOldMarker();
        // 关闭窗口
        this.$refs.CarMarkerManage.closeDialog();
        // 绘制路线
        let coors = this.formatPathData(locationData);
        this.Player.coors = Object.freeze(coors);
        this.Player.path = Object.freeze(coors);
        const labelTypeData = {
          iconUrl: this.$refs.CarMarkerManage.judgeTerminalIcon({ treeCategory: String(res.data.deviceCategory) }),
          iconWidth: 50,
          iconHeight: 50
        };
        this.Player.car = this.getTrackDeviceMarker({
          position: this.Player.path[0],
          angle: locationData[0].bearing,
          labelTypeData: labelTypeData
        });
        // 设置相关元素进面板
        this.$emit('setTrackElement', {
          map: this.map,
          amap: this.AMap,
          car: this.Player.car,
          track: locationData,
          path: this.CarHistoryPathPass,
          disCont: this.DisContPathPass,
          labelTypeData: labelTypeData
        });
        // 地图添加与缩放
        this.map.add(this.Player.car);
        this.map.setZoomAndCenter(14, coors[0]);
        // 面板更新
        let historyInfo = res.data.locations[this.Player.index];
        historyInfo.index = this.Player.index;
        historyInfo.total = this.Player.path.length - 1;
        historyInfo.timeStart = carSearch.start;
        historyInfo.timeEnd = carSearch.end;
        this.$emit('setInfoData', historyInfo);
        this.$emit('setTableData', {
          data: locationData,
          pageFlag: true
        });
        this.$emit('setTrackPath', {
          data: coors,
          pageFlag: true
        });
      }).catch(err => {
        loading.close();
        console.log(err);
      });
    },
    formatTrackToGCJ02Coordinates(data) {
      if (!Array.isArray(data)) return [];
      return data.map(group => {
        return this.formatPathData(this.$utils.wgs84togcj02Batch(group))
      });
    },
    //设置异常点路径解决拉直线问题
    queryUnablePointLine(carSearch){
      let parme = {
        startTime: carSearch.start,
        endTime: carSearch.end,
        deviceId: carSearch.deviceId ? BigInt(carSearch.deviceId) : undefined,
        deviceType: carSearch.deviceType ? carSearch.deviceType : undefined
      };
      if (carSearch.isFilter?.length) {
        for (let index = 0; index < carSearch.isFilter.length; index++) {
          const element = carSearch.isFilter[index];
          parme[element] = 1;
        }
      }
      queryTrackingUnablePoint(parme).then(res => {
        const trackData = this.formatTrackToGCJ02Coordinates(res.data)
        console.log(trackData,'trackData')
        this.setUnablePointLine(trackData)
      }).catch(err => {
        console.log(err);
      });
    },
    setMarkerLabel(val){
      return `<div class="custom-content-marker"><img src="//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-red.png"><div class="close-btn">${val}</div></div>`
    },
    // 绘制无效点拉直线路线
    setUnablePointLine(trackData) {
      if (this.unablePointLines) {
          this.unablePointLines.forEach(item => {
            this.map.remove(item);
          });
      }
      this.unablePointLines = [];
      // 绘制每条路线
      trackData.forEach((points, index) => {
        const path = points.map(point => new this.AMap.LngLat(point[0], point[1]));
        const polyline = new this.AMap.Polyline({
          path: path,
          showDir: true,
     	    dirColor:'#FFF',
          strokeColor: '#FF0000',
          strokeOpacity: 0.5,
          strokeWeight: 6,
          zIndex:  55,
          lineJoin: 'round'
        });
        this.map.add(polyline);
        this.unablePointLines.push(polyline);
        // 在路线起点添加标记
        const startMarker = new this.AMap.Marker({
          position: path[0],
          content: this.setMarkerLabel('止'),
          offset: new this.AMap.Pixel(0, 0),
          anchor: "center", //设置锚点方位
        });
        this.unablePointLines.push(startMarker);
        this.map.add(startMarker);

        // 在路线终点添加标记
        const endMarker = new this.AMap.Marker({
          position: path[path.length - 1],
          content: this.setMarkerLabel('起'),
          offset: new this.AMap.Pixel(0, 0),
          anchor: "center", //设置锚点方位
        });
        this.map.add(endMarker);
        this.unablePointLines.push(endMarker);
      });
    },
    //查询轨迹分页(接口带停车点停车时长)
    onDeviceTrackSearch(carSearch) {
      //清除地图上的起点终点停靠点
      this.startEndMarker.forEach(item => {
        this.map.remove(item);
      })
      this.stopPointMarker.forEach((item,index) => {
        console.log(item,'item',index)
        this.map.remove(item);
      })
      this.stopPointMarker = [];
      this.baseTrackSubSize = 5000;
      this.setFixInfo([])
      let parme = {
        startTime: carSearch.start,
        endTime: carSearch.end,
        deviceId: carSearch.deviceId ? BigInt(carSearch.deviceId) : undefined,
        deviceType: carSearch.deviceType ? carSearch.deviceType : undefined,
        targetId: carSearch.targetId ? BigInt(carSearch.targetId) : undefined,
        targetType: carSearch.targetType ? carSearch.targetType : undefined,
        startIndex: 0,
        endIndex: this.baseTrackSubSize
      };
      if (carSearch.isFilter?.length) {
        for (let index = 0; index < carSearch.isFilter.length; index++) {
          const element = carSearch.isFilter[index];
          parme[element] = 1;
        }
      }
      const loading = this.$loading({
        text: '正在加载',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      queryTrackingByPageWithStopPoint(parme).then(res => {
        loading.close();
        // 检查路线
        if (!res.data?.locations?.length) {
          this.$message({
            message: '无路线数据，请重新查找',
            type: 'warning'
          });
          // 隐藏已绘制的轨迹
          if (this.CarHistoryPathPass) this.CarHistoryPathPass.hide();
          if (this.unablePointLines) {
              this.unablePointLines.forEach(item => {
                this.map.remove(item);
              });
          }
          this.unablePointLines = [];
          return;
        }
        for (let index = 0; index < res.data.locations.length; index++) {
          const element = res.data.locations[index];
          element.targetType = res.data.targetType;
          element.targetId = res.data.targetId;
          element.targetName = res.data.targetName;
          element.deviceModel = res.data.deviceModel;
          element.deviceNum = res.data.deviceNum;
          element.deviceType = res.data.deviceType;
          element.deviceCategory = res.data.deviceCategory;
          element.deviceId = res.data.deviceId;
          element.deviceUniqueId = res.data.deviceUniqueId;
          element.longitudeTable = element.longitude;
          element.latitudeTable = element.latitude;
        }
        this.setTrackDataFirst(carSearch, Object.freeze(res));
        this.queryUnablePointLine(carSearch)
      }).catch(err => {
        loading.close();
        console.log(err);
      });
    },
    setTrackDataFirst(carSearch, res) {
      // 设置第一页数据
      this.pageFlag = true;
      const locationData = this.$utils.wgs84togcj02Batch(res.data.locations);
      let stopPointsList = [];
      this.stopPointCoors = [];
      locationData.filter(item => {
        if (item.isStopPoint === true) {
          stopPointsList.push(item);
        }
      })
      this.stopPointCoors = stopPointsList;
      // 车辆图标清理
      this.$refs.CarMarkerManage.clearOldMarker();
      // 关闭窗口
      this.$refs.CarMarkerManage.closeDialog();
      // 绘制路线
      let coors = this.formatPathData(locationData);
      this.Player.coors = Object.freeze(coors);
      this.Player.path = Object.freeze(coors);
      this.Player.carsData = Object.freeze(res.data);
      const labelTypeData = {
        iconUrl: this.$refs.CarMarkerManage.judgeTerminalIcon({ treeCategory: String(res.data.deviceCategory) }),
        iconWidth: 50,
        iconHeight: 50
      };
      this.Player.car = this.getTrackDeviceMarker({
        position: this.Player.path[0],
        angle: locationData[0].bearing,
        labelTypeData: labelTypeData
      });
      // 设置相关元素进面板
      this.$emit('setTrackElement', {
        map: this.map,
        amap: this.AMap,
        car: this.Player.car,
        track: locationData,
        path: this.CarHistoryPathPass,
        disCont: this.DisContPathPass,
        labelTypeData: labelTypeData
      });
      // 地图添加与缩放
      this.map.add(this.Player.car);
      this.map.setZoomAndCenter(14, coors[0]);
      // 面板更新
      let historyInfo = this.Player.carsData.locations[this.Player.index];
      historyInfo.index = this.Player.index;
      historyInfo.total = this.Player.path.length - 1;
      historyInfo.timeStart = carSearch.start;
      historyInfo.timeEnd = carSearch.end;
      this.$emit('setInfoData', historyInfo);
      this.$emit('setTableData', {
        data: locationData,
        pageFlag: this.pageFlag
      });
       this.setFixInfo(locationData)
      this.$emit('setTrackPath', {
        data: coors,
        pageFlag: this.pageFlag
      });
      if (res.data.locations.length < res.data.total && res.data.total > this.baseTrackSubSize) {
        this.setIconStartEnd('s', coors[0]);
        let lastLocationData= locationData[locationData.length - 1];
        this.setTrackDataOther(carSearch,lastLocationData);
      } else {
        this.pageFlag = false;
        // 只有第一段的路径添加起点标识和终点标识
        this.setIconStartEnd('s', coors[0]);
        this.setIconStartEnd('e',coors[coors.length - 1]);
        // 只有第一段的路径添加停靠点标识
        console.log(this.stopPointCoors);
        this.setStopPointIcon(this.stopPointCoors);
      }
    },
    setStopPointIcon(coors) {
      for (let index = 0; index < coors.length; index++) {
        let position = new this.AMap.LngLat(coors[index].longitude, coors[index].latitude);
        let pointMarker = new this.AMap.LabelMarker({
        position: position,
        icon: {
          image:require('@/assets/images/car/track-stop2.svg'),
          anchor: 'bottom-center',
          size: [28, 28],
          zIndex:66
        }
      });
      this.stopPointMarker.push(pointMarker);
      pointMarker.on('click', (e) => {
        this.$emit('stopPointClick', coors[index]);
        });
      }
      this.map.add(this.stopPointMarker);
    },
    setIconStartEnd (i,currentData) {
      let icon;
      if (i === 's') {
        icon = require('@/assets/images/car/track-start.png');
      } else if (i === 'e') {
        icon = require('@/assets/images/car/track-end.png');
      }
      let position = new this.AMap.LngLat(currentData[0], currentData[1]);
      let pointMarker = new this.AMap.LabelMarker({
        position: position,
        icon: {
          image: icon,
          anchor: 'bottom-center',
          size: [28, 36]
        }
      });
      this.startEndMarker.push(pointMarker);
      this.map.add(pointMarker);
    },
    setTrackDataOther(carSearch,lastLocationData) {
      this.pageFlag = false;
      const startIndex = this.baseTrackSubSize;
      const endIndex = this.baseTrackSubSize +=this.otherTrackSubSize
      let parme = {
        startTime: carSearch.start,
        endTime: carSearch.end,
        deviceId: carSearch.deviceId ? BigInt(carSearch.deviceId) : undefined,
        deviceType: carSearch.deviceType ? carSearch.deviceType : undefined,
        targetId: carSearch.targetId ? BigInt(carSearch.targetId) : undefined,
        targetType: carSearch.targetType ? carSearch.targetType : undefined,
        startIndex,
        endIndex
      };
      if (carSearch.isFilter?.length) {
        for (let index = 0; index < carSearch.isFilter.length; index++) {
          const element = carSearch.isFilter[index];
          parme[element] = 1;
        }
      }
      queryTrackingByPageWithStopPoint(parme).then(res => {
        // 判断上一页的最后一项和新接口中的第一项经纬度是不是一样 如果一样删除新数据的第一项
        if (res.data.locations && lastLocationData.longitude === res.data.locations[0].longitude && lastLocationData.latitude === res.data.locations[0].latitude) {
          console.log('删除第一项length',res.data.locations.length,'===>' ,res.data.locations[0]  ,lastLocationData);
          res.data.locations.shift();
          console.log('删除后的数据length', res.data.locations.length);
        }
        for (let index = 0; index < res.data.locations.length; index++) {
          const element = res.data.locations[index];
          element.targetType = res.data.targetType;
          element.targetId = res.data.targetId;
          element.targetName = res.data.targetName;
          element.deviceModel = res.data.deviceModel;
          element.deviceNum = res.data.deviceNum;
          element.deviceType = res.data.deviceType;
          element.deviceCategory = res.data.deviceCategory;
          element.deviceId = res.data.deviceId;
          element.deviceUniqueId = res.data.deviceUniqueId;
          element.longitudeTable = element.longitude;
          element.latitudeTable = element.latitude;
        }
        const locationData = this.$utils.wgs84togcj02Batch(res.data.locations);
        let stopPointsList = [];
        locationData.filter(item => {
          if (item.isStopPoint === true) {
            stopPointsList.push(item);
          }
        })
        this.stopPointCoors = [...this.stopPointCoors,...stopPointsList] ;
        let coors = this.formatPathData(locationData);
        // this.Player.coors = Object.freeze([...this.Player.coors, ...coors]);
        // this.Player.path = Object.freeze([...this.Player.path, ...coors]);
        this.Player.coors = Object.freeze(coors);
        this.Player.path = Object.freeze(coors);
        this.Player.carsData = Object.freeze({
          ...this.Player.carsData,
          locations: [...this.Player.carsData.locations, ...res.data.locations]
        });
        // 设置相关元素进面板
        this.$emit('setTrackElement', {
          track: locationData,
          otherUpdate: true
        });
        // 地图添加与缩放
        this.map.add(this.Player.car);
        // 面板更新
        let historyInfo = this.Player.carsData.locations[this.Player.index];
        historyInfo.index = this.Player.index;
        historyInfo.total = this.Player.path.length - 1;
        historyInfo.timeStart = carSearch.start;
        historyInfo.timeEnd = carSearch.end;
        this.$emit('setTableData', {
          data: locationData,
          pageFlag: this.pageFlag
        });
        this.$emit('setInfoData', historyInfo);
        this.$emit('setTrackPath', {
          data: coors,
          pageFlag: this.pageFlag
        });
        // 判断是否还有下一页
        if (endIndex < res.data.total) {
          let lastLocationData= locationData[locationData.length - 1];
          this.setTrackDataOther(carSearch,lastLocationData);
        }else{
         // 添加终点标识
          this.pageFlag = false
          this.setIconStartEnd('e',coors[coors.length - 1]);
          //不再请求后绘制所有的停靠点
          this.setStopPointIcon(this.stopPointCoors);
        }
      });
    },
    // 终端查询轨迹(公务车)
    terminalTrackSearch(carSearch) {
      let parme = {
        startTime: carSearch.start,
        endTime: carSearch.end,
        deviceId: carSearch.deviceId ? BigInt(carSearch.deviceId) : undefined,
        deviceType: carSearch.deviceType ? carSearch.deviceType : undefined,
        targetId: carSearch.targetId ? BigInt(carSearch.targetId) : undefined,
        targetType: carSearch.targetType ? carSearch.targetType : undefined
      };
      if (carSearch.isFilter?.length) {
        for (let index = 0; index < carSearch.isFilter.length; index++) {
          const element = carSearch.isFilter[index];
          parme[element] = 1;
        }
      }
      const loading = this.$loading({
        text: '正在加载',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      queryTracking(parme).then(res => {
        loading.close();
        // 检查路线
        if (!res.data?.locations?.length) {
          this.$message({
            message: '无路线数据，请重新查找',
            type: 'warning'
          });
          // 隐藏已绘制的轨迹
          if (this.CarHistoryPathPass) this.CarHistoryPathPass.hide();
          return;
        }
        for (let index = 0; index < res.data.locations.length; index++) {
          const element = res.data.locations[index];
          element.targetType = res.data.targetType;
          element.targetId = res.data.targetId;
          element.targetName = res.data.targetName;
          element.deviceModel = res.data.deviceModel;
          element.deviceNum = res.data.deviceNum;
          element.deviceType = res.data.deviceType;
          element.deviceCategory = res.data.deviceCategory;
          element.deviceId = res.data.deviceId;
          element.deviceUniqueId = res.data.deviceUniqueId;
          element.longitudeTable = element.longitude;
          element.latitudeTable = element.latitude;
        }
        const locationData = this.$utils.wgs84togcj02Batch(res.data.locations);
        // 绘制路线
        let coors = this.formatPathData(locationData);
        this.Player.coors = Object.freeze(coors);
        this.Player.path = Object.freeze(coors);
        this.Player.carsData = Object.freeze(locationData);
        const labelTypeData = {
          iconUrl: '/bdsplatform/static/images/pic/vehicle.png',
          bgUrl: '/bdsplatform/static/images/pic/static.png',
          iconWidth: 50,
          iconHeight: 50
        };
        this.Player.car = this.getTrackDeviceMarker({
          position: this.Player.path[0],
          angle: locationData[0].bearing,
          labelTypeData: labelTypeData
        });
        // 设置相关元素进面板
        this.$emit('setTrackElement', {
          map: this.map,
          amap: this.AMap,
          car: this.Player.car,
          track: locationData,
          path: this.CarHistoryPathPass,
          disCont: this.DisContPathPass,
          labelTypeData: labelTypeData
        });
        // 地图添加与缩放
        this.map.add(this.Player.car);
        // this.map.setFitView();
        this.map.panTo(coors[0]);
        // 面板更新
        let historyInfo = this.Player.carsData[this.Player.index];
        historyInfo.index = this.Player.index;
        historyInfo.total = this.Player.path.length - 1;
        historyInfo.timeStart = carSearch.start;
        historyInfo.timeEnd = carSearch.end;
        this.$emit('setInfoData', historyInfo);
        this.$emit('setTableData', locationData);
        this.$emit('setTrackPath', this.Player.coors);
      }).catch(err => {
        loading.close();
        console.log(err);
      });
    },
    /**
     * 获取轨迹状态的车辆覆盖物
     */
    getTrackCarMarker (_opts) {
      let opts = _opts || {};
      let marker = new this.AMap.Marker({
        icon: opts.status.icon,
        position: opts.position,
        offset: new this.AMap.Pixel(-ICON_SIZE / 2, -ICON_SIZE / 2),
        angle: opts.angle
      });
      return marker;
    },
    /**
     * 获取轨迹状态的终端覆盖物
     */
    getTrackDeviceMarker (_opts) {
      let opts = _opts || {};
      let marker = new this.AMap.Marker({
        position: opts.position,
        offset: new this.AMap.Pixel(-50 / 2, -50 / 2),
        content: `<div style="position: relative;">
                      <div class="follow-marker-bg" style="position: absolute; width: 100%; height: 100%; background-image: url('/bdsplatform/static/images/pic/static.png'); background-size: 100%;"></div>
                      <img src="${opts.labelTypeData.iconUrl}" style="display: block; width: ${opts.labelTypeData.iconWidth}px; height: ${opts.labelTypeData.iconHeight}px; padding: 3px; position: inherit;">
                    </div>`
      });
      marker.dom.classList.add("follow-marker");
      return marker;
    },
    onCarHistoryPlanelCancel () {
      this.CarHistoryPlanelVisible = false;
      if (!this.Player.car) {
        return;
      }
      this.Player.car.stopMove();
      this.map.remove(this.Player.car);
      this.$refs.carHistoryPlanel.onPlayerStop();
    },
    onTrackPlanelCancel () {
      if (!this.Player.car) {
        return;
      }
      this.Player.car.stopMove();
      this.map.remove(this.Player.car);
      this.$emit('onTrackStop');
    },
    formatPathData (_data) {
      if (!_data) {
        return [];
      }
      let coors = [];
      for (let i = 0; i < _data.length; i++) {
        const element = _data[i];
        let coor = [Number(element.longitude), Number(element.latitude)];
        coors.push(coor);
      }
      return coors;
    },
    setTrackCarMarker (_opts) {
      let opts = _opts || {};
      let marker = _opts.car;
      marker.setIcon(opts.status.icon);
      marker.setAngle(opts.angle);
      if (!opts.speed || opts.speed === 0) {
        marker.setPosition(opts.position);
      } else {
        marker.moveTo(opts.position, opts.speed);
      }
    },
    setPath (_coors) {
      let coors = this.formatPathData(_coors);
      // 无坐标点过滤
      if (coors.length === 0) {
        console.warn('无轨迹数据');
        return;
      }
      this.Player.coors = Object.freeze(coors);
      // 地图添加与缩放
      this.map.setFitView();
      this.map.panTo(coors[0]);
    },
    // 历史查询取消按钮点击
    onCarHistoryCancel () {
      this.CarHistorySearchVisible = false;
      // 打开车辆列表
      this.$emit('showCarList', true);
    },
    // 绘制聚合marker
    drawMarkers (data) {
      const list = [];
      // 初始化Marker工具，且不显示轨迹回放按钮
      this.$refs.CarMarkerManage.init(this, false);
      data.forEach((item)=>{
        const marker = this.$refs.CarMarkerManage.batchCarMarker(item);
        list.push(marker);
      });
      this.updateCarMarkers(list);
    },
    async drawRegionMarkers (carsInMap, isOffset) {
      // 初始化Marker工具，且不显示轨迹回放按钮
      this.$refs.CarMarkerManage.init(this, false);
      this.$refs.CarMarkerManage.closeDialog();
      this.map.clearMap();
      this.$nextTick(() => {
        this.$refs.CarMarkerManage.getDeviceMarkers(carsInMap, isOffset);
      });
    },
    // 更新车辆图标 : 使用聚合工具批量操作
    updateCarMarkers (_carsMarkerData) {
      // 添加新车辆图标
      this.carsClusterer.setData(_carsMarkerData);
    },
    clearClustererMarkers () {
      this.carsClusterer.setData([]);
    },
    /**
     * 跳转路由方法
     */
    goPath (routeUrl) {
      this.$router.push({
        path: routeUrl,
        query: {
          isRouter: this.$route.fullPath
        }
      });
    },
    // 点击工具栏事件
    handleClick (item) {
      if (item.path) {
        this.goPath(item.path);
      }
      if (item.clickHandle) {
        item.clickHandle();
      }
    },
    // 卫星显示
    showSatellite () {
      this.$refs.LayerSelect.showSelect();
    },
    // 工具栏切换
    handleCommand (e) {
      if (e) {
        this.$refs.MapTools[e]();
      }
    },
    // 绘制预设点
    drawPointerDefault () {
      // 请求预设点
      // pagination().then(res => {
      //   res.code === 0 && res.data.content.forEach(item => {
      //     let zoom = this.map.getZoom();
      //     if (!item.iconUrl) {
      //       item.iconUrl = require('@/assets/images/poi-marker-default.png');
      //       item.iconWidth = 26;
      //       item.iconHeight = 34;
      //     }
      //     item.callback = (marker) => {
      //       marker.setLabel({
      //         offset: new this.AMap.Pixel(0, 2), // 设置文本标注偏移量
      //         content: `<div class="point-for-moniter">${item.pointName || '未命名'}</div>`, // 设置文本标注内容
      //         direction: 'top' // 设置文本标注方位
      //       });
      //       marker.mapLevel = item.mapLevel;
      //       // 添加预设点
      //       if (item.mapLevel <= zoom) this.map.add(marker);
      //       this.pointMarkers.push(marker);
      //     };
      //     this.$refs.DrawMarker.drawMarker(item);
      //   });
      // });
    },
    // 自定义函数 暂时没用
    customHandle (fnc) {
      fnc(this);
    }
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style >
  .amap-logo{
    display: none!important;
  }
  .amap-marker-label{
    border: 0;
    background-color: transparent;
  }
  .amap-sug-result {
    z-index: 5000;
  }
</style>

<style lang="less" scoped>
  @import "../../assets/less/selectComponent.less";
  .map{
    width: 100%;
    height: 100%;
    // position: relative;
    .map-tool-bar{
      position: absolute;
      right: 15px;
      top: 15px;
      // width: 500px;
      height: 44px;
      z-index: 1;
      display: flex;
      cursor: pointer;
      ::v-deep .searchResultsBox{
        background: none;
        padding: 0;
        border-radius: 4px;
        box-shadow: 0px 0px 7px 2px rgba(83,83,83,0.20);
        .el-input--mini .el-input__inner{
          height: 44px;
          line-height: 44px;
        }
      }
      .search-icon{
        width: 47px;
        height: 44px;
        background: var(--gn-color);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #FFFFFF;
        .search-img{
          display: flex;
          width: 22px;
          height: 18px;
          background: url('../../assets/images/monitor/monitor_icon_search.png') center no-repeat;
          background-size: 100% 100%;
        }
      }
      .map-tool-map-control{
        margin-left: 11px;
        // width: 564px;
        height: 44px;
        opacity: 1;
        background: #ffffff;
        border-radius: 4px;
        box-shadow: 0px 0px 7px 2px rgba(83,83,83,0.20);
        display: flex;
        align-items: center;
        .map-control-text{
          padding: 14px 19px;
          font-size: 12px;
          color: #6d6d6d;
          display: flex;
          align-items: center;
          &:hover{
            background: #E3F4FF;
          }
        }
        ::v-deep .el-dropdown{
          font-size: 12px;
          .el-dropdown-link{
            display: flex;
            align-items: center;
            padding: 15px 19px;
          }
        }
        .traffic{
          display: flex;
          width: 22px;
          height: 18px;
          background: url('../../assets/images/monitor/monitor_icon_traffic.png') center no-repeat;
          background-size: 100% 100%;
        }
        .satellite{
          display: flex;
          width: 22px;
          height: 18px;
          background: url('../../assets/images/monitor/monitor_icon_satellite.png') center no-repeat;
          background-size: 100% 100%;
        }
        .tool{
          display: flex;
          width: 22px;
          height: 18px;
          background: url('../../assets/images/monitor/monitor_icon_tool.png') center no-repeat;
          background-size: 100% 100%;
        }
      }
    }
  }
  .mapWidgetAMap{
    display: flex;
    flex-direction: row;
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 1000;
    padding: 10px;
    background-color: #fff;
    font-size: 14px;
    border:1px;
    border-radius:5px;
    box-shadow: 0px 0px 5px #888888;
    span{
      line-height: 28px;
    }
    .search-box{
      display: flex;
      align-items: center;
      ::v-deep .searchResultsBox{
        position: relative;
        padding: 0;
        right: 3px;
        margin-right: 10px;
      }
    }
  }
  .videoMapContent{
    position: absolute;
    right: 0px;
    z-index: 1000;
  }
  .planelSpan{
    margin-right: 10px;
    cursor: pointer;
  }
  .selectedColor{
    color: red
  }
  .pathHistory{
    position: absolute;
    width: 420px;
    height: 160px;
    left:0;
    top:0;
    right:0;
    bottom: 0;
    margin: auto;
    z-index: 1202;
  }
  .CarHistoryPlanel{
    position: absolute;
    width: 100%;
    height: 200px;
    left:0;
    right:0;
    bottom: 0;
    margin: auto;
    z-index: 1202;
  }
  ::v-deep .point-for-moniter{
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 6px 0 rgb(114 124 245 / 50%);
    border-radius: .25rem;
    padding: 0.25rem;
  }
  ::v-deep .amap-copyright{
    display: none !important; // 隐藏高德版本
  }
  ::v-deep .amap-toolbar{
    position: absolute;
    bottom: 0 !important; // 地图控制栏
  }

::v-deep .custom-content-marker {
  position: relative;
  width: 22px;
  height: 32px;
}

::v-deep .custom-content-marker img {
    width: 100%;
    height: 100%;
}
::v-deep .custom-content-marker .close-btn {
  position: absolute;
  top: 3px;
  right: 5px;
  font-size: 12px;
  color: #fff;
}



</style>
