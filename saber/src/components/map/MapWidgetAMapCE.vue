<template>
  <div
    id="ce-map"
    ref="mapContainer"
    class="map"
  >
    <!-- 工具栏 -->
    <div class="map-tool-bar">
      <div class="map-tool-bar-search">
        <SearchMap
          v-if="toolConfig.searchToolShow"
          ref="SearchMap"
        >
          <div class="search-icon">
            <i
              class="el-icon-search"
            />
          </div>
        </SearchMap>
      </div>
      <div class="map-tool-map-control">
        <div
          v-for="item in btnNeedShowData"
          :key="item.name"
          class="map-control-text"
          @click="handleClick(item)"
        >
          <i
            v-if="item.icon"
            :class="item.icon"
          />{{ item.name }}
        </div>
        <el-dropdown
          v-if="toolConfig.mapToolsShow"
          @command="handleCommand"
        >
          <span class="el-dropdown-link">
            <i class="tool"/>工具<i class="el-icon-arrow-down el-icon--right"/>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="onRuleClick">
              测距
            </el-dropdown-item>
            <el-dropdown-item command="onMeasureAreaClick">
              测面
            </el-dropdown-item>
            <el-dropdown-item command="onCancelClick">
              取消
            </el-dropdown-item>
            <el-dropdown-item command="onCloseClick">
              清除
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <LayerSelect
      v-show="false"
      ref="LayerSelect"
      :class="{selectedColor:isShowLayerSelect}"
      @changeLayerSelect="changeLayerSelect"
    />
    <DrawMarker
      v-show="false"
      ref="DrawMarker"
      :class="{selectedColor:isShowDrawMarker}"
      @changeDrawMarker="changeDrawMarker"
      @setFitView="setFitView"
      @setMarkerCenter="setMarkerCenter"
      @setZoomAndCenter="setZoomAndCenter"
      v-on="$listeners"
    />
    <MapTools
      v-show="false"
      ref="MapTools"
      :class="{selectedColor:isShowMapTools}"
      @changeMapTools="changeMapTools"
    />
    <div
      v-show="false"
      class="mapWidgetAMap"
    >
      <img
        src="@/assets/images/arrow.png"
        class="planelSpan"
        @click="clickZoneTool"
      >
      <RectangleEditor
        ref="RectangleEditor"
        :class="{selectedColor:isShowRectangle}"
        @changeRectangle="changeRectangle"
      />
      <CircleEditor
        ref="CircleEditor"
        :class="{selectedColor:isShowCircle}"
        @changeCircle="changeCircle"
      />
      <PolygonEditor
        ref="PolygonEditor"
        :class="{selectedColor:isShowPolygon}"
        @changePolygon="changePolygon"
      />
      <PolylineEditor
        ref="PolylineEditor"
        :class="{selectedColor:isShowPolyline}"
        @changePolyline="changePolyline"
      />
    </div>
    <!-- 地图Marker管理 -->
    <CarMarkerManage
      ref="CarMarkerManage"
      v-bind="$attrs"
      @setZoomAndCenter="setZoomAndCenter"
      @onPathWatchClick="onPathWatchClick"
      v-on="$listeners"
    />
  </div>
</template>

<script>
/**
 * 地图组件
 */
import RectangleEditor from './tools/RectangleEditor';
import CircleEditor from './tools/CircleEditor';
import PolygonEditor from './tools/PolygonEditor';
import PolylineEditor from './tools/PolylineEditor';
import LayerSelect from './tools/LayerSelectCE';
import DrawMarker from './tools/DrawMarkerCE';
import SearchMap from './tools/SearchMapCE';
import MapTools from './tools/MapToolsCE';
import CarMarkerManage from './track/CarMarkerManageCE';
import { queryTracking, queryTrackingByPage, queryTrackingUnablePoint } from '@/api/monitoring/track.js';
import { MapFactory } from '@map-library/core';
import { MayMapAdapter } from '@map-library/maymap';

MapFactory.register('maymap', MayMapAdapter);
// 图资尺寸 : 用户偏移位置
const ICON_SIZE = 32;
export default {
  name: 'MapWidgetAMap',
  components: {
    PolygonEditor,
    RectangleEditor,
    CircleEditor,
    PolylineEditor,
    LayerSelect,
    DrawMarker,
    SearchMap,
    MapTools,
    CarMarkerManage
  },
  props: {
    // 配置需要的工具栏
    toolConfig: {
      type: Object,
      default: () => {
        return {
          routeRegionEdit: false, // 跳转区域编辑
          routePolylineEdit: false, // 跳转路线编辑
          routePointEdit: false, // 跳转标注点编辑
          drawMarkerShow: true, // 标注点
          polylineEditorShow: true, // 绘制直线
          showZoneToolShow: true, // 绘制区域
          searchToolShow: true, // 搜索
          clearBtnShow: true, // 清除按钮
          returnBtnShow: true, // 回到中心
          setCenterShow: true, // 设置中心
          trafficLayerShow: true, // 路况
          layerSelectShow: true, // 卫星图
          drivingLineShow: true, // 路径规划
          mapToolsShow: true, // 工具栏
          closePointerDefault: false, // 默认显示所有预设点
          regionShow: false, // 电子围栏
          showMapLabel: true // 地图标注
        };
      }
    },
    showTools: {
      type: Boolean,
      default: true
    },
    showFoldButton: {
      type: Boolean,
      default: false
    },
    fromQueryRegion: {
      type: Boolean,
      default: false
    },
    showRightContext: {
      type: Boolean,
      default: true
    },
    isClearMap: {
      type: Boolean,
      default: true
    },
    // 不初始化默认的右键菜单
    notInitRightContextmenu: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      alarmSource: null,
      alarmLayer: null,
      btnData: [
        {
          name: '线路',
          path: '/base/routeEditWorkspace',
          show: this.toolConfig.routePolylineEdit
        },
        {
          name: '标注点',
          path: '/base/pointEditWorkspace',
          show: this.toolConfig.drawMarkerShow
        },
        {
          name: '区域',
          // path: '/security/alarm/regionAlarmEditWorkspace',
          path: '/base/regionEditWorkspace',
          show: this.toolConfig.routeRegionEdit
        },
        {
          name: '电子围栏',
          clickHandle: this.handleRegion,
          show: this.toolConfig.regionShow
        },
        {
          name: '卫星',
          icon: 'satellite',
          clickHandle: this.showSatellite,
          show: this.toolConfig.layerSelectShow
        }
      ], // 按钮数组
      /** 地图对象 */
      map: null,
      /** 国能地图 : 方便调用 API */
      AMap: null,
      /** 地图 OverlayGroup 对象 */
      carsClusterer: null,
      /** 信息窗体 */
      carInfoWindow: null,
      config: null,
      trafficLayer: null,
      loaded: false,
      isShowTrafficLayer: true,
      showFold: false,
      showZoneTool: false,
      /** 标签颜色 */
      isShowLayerSelect: false,
      isShowDrivingLine: false,
      isShowMapTools: false,
      isShowMapCenter: false,
      isShowPolyline: false,
      isShowPolygon: false,
      isShowRectangle: false,
      isShowCircle: false,
      isShowDrawMarker: false,
      Player: {
        index: 0,
        carsData: [],
        car: null,
        coors: [],
        speed: 1000
      },
      driving: null,
      startPoint: null,
      endPoint: null,
      startMarker: [],
      endMarker: [],
      CarMarkers: null,
      unablePointLines: [], // 轨迹回放无效点路线list
      // 暂存marker集合
      pointMarkers: [],
      showWatchCard: false,
      trackInfo: null,
      mapInstance: null,
      vectorBaseMap: null,
      vectorLayer: null,
      satelliteLayer: null,
      satelliteAnnotationLayer: null,
      Measure: null, // 测距测面对象
      pathLayer: null, // 国能地图轨迹矢量图层
      Track: null, // 国能地图轨迹对象
      baseTrackSubSize: 5000, // 轨迹回放首次加载的数据长度
      otherTrackSubSize: 10000, // 轨迹回放后续加载的数据长度
      cachePageData: [], // 分页数据缓存
      LabelsLayer: null,
      contextmenuMarker: null // 右键菜单要素
    };
  },
  computed: {
    btnNeedShowData() {
      return this.btnData.filter(item => {
        return item.show;
      });
    }
  },
  watch: {
    'startMarker': function (val) {
      if (val && val.length > 0) {
        this.createDriving();
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      // console.log('------------>初始化地图');
      this.init();
      if (this.fromQueryRegion) {
        this.showFold = true;
        this.showZoneTool = false;
      }
    });
  },
  methods: {
    /**
     * 初始化
     */
    async init() {
      const config = {
        container: 'ce-map',
        config: {
          key: 'b4f0d3a1-2c5e-4a8f-9b3d-5e7f3c2b1a4d',
          longitude: 110.552500,
          latitude: 38.822700,
          zoom: 4,
          maxZoom: 20,
          minZoom: 4
        }
      };
      const mapInstance = MapFactory.createMap('maymap', config);
      this.map = await mapInstance.initialize();
      this.mapInstance = mapInstance;
      this.AMap = window.MayMap;
      this.LabelsLayer = MayMap.TileLayer.LabelsLayer({ zIndex: 1 });
      this.LabelsLayer.setStyle((feature, resolution) => {
        return new MayMap.ol.style.Style({
          //边框色
          stroke: new MayMap.ol.style.Stroke({
            color: '#3ca2fa',
            width: 2
          }),
          //矢量圆点
          image: new MayMap.ol.style.Circle({
            radius: 6,
            fill: new MayMap.ol.style.Fill({
              color: 'rgb(23,145,252,0.4)'
            })
          }),
          //填充色
          fill: new MayMap.ol.style.Fill({
            color: 'rgb(23,145,252,0.4)'
          })
        });
      });
      this.map.addLayer(this.LabelsLayer);
      this.addCELayer();
      // 初始化轨迹报警图层
      this.alarmSource = new this.AMap.ol.source.Vector();
      this.alarmLayer = new this.AMap.ol.layer.Vector({
        source: this.alarmSource,
        zIndex: 15
      });
      this.map.addLayer(this.alarmLayer);
      this.Measure = new MayMap.Measure(this.LabelsLayer);
      this.map.addControl({
        type: 'Zoom'
      });
      this.map.addControl({
        type: 'ScaleLine'
      });
      if (!this.notInitRightContextmenu) {
        const Element = document.createElement('div');
        Element.className = 'right-contextmenu';
        Element.innerHTML = `
      <ul>
        <li class="item1">放大</li>
        <li class="item2">缩小</li>
        <li class="item3">清除</li>
      </ul>`;
        this.contextmenuMarker = new MayMap.ol.Overlay({
          element: Element
        });
        this.map.olMap.addOverlay(this.contextmenuMarker);
        this.$nextTick(() => {
          document.querySelector(".right-contextmenu").addEventListener("click", (e) => {
            const className = e.target.className;
            if (className === 'item1') {
              this.mapZoomIn();
            } else if (className === 'item2') {
              this.mapZoomOut();
            } else if (className === 'item3') {
              this.clearMap();
            }
            this.contextmenuMarker.setPosition(null);
          });
        });
        this.map.olMap.getViewport().addEventListener('contextmenu', (event) => {
          event.preventDefault(); // 阻止默认的右键菜单弹出
          const coordinate = this.map.olMap.getEventCoordinate(event); // 获取鼠标点击的地理坐标
          this.contextmenuMarker.setPosition(coordinate);
        });
      }
      const CE_MAP = document.getElementById('ce-map');
      CE_MAP.addEventListener('contextmenu', function (event) {
        event.preventDefault();
      });
      const resizeObserver = new ResizeObserver(entries => {
        for (let entry of entries) {
          // 调用地图的 updateSize 方法
          if (this.map) {
            this.map.olMap.updateSize();
          }
        }
      });
      // 开始监听元素
      resizeObserver.observe(CE_MAP);
      let vectorBaseMap = this.AMap.TileLayer.VectorBaseMap();
      let vectorLayer = this.AMap.TileLayer.VectorAnnotation();
      let satelliteLayer = this.AMap.TileLayer.Satellite();
      let satelliteAnnotationLayer = this.AMap.TileLayer.SatelliteAnnotation();
      this.vectorBaseMap = vectorBaseMap;
      this.vectorLayer = vectorLayer;
      this.satelliteLayer = satelliteLayer;
      this.satelliteAnnotationLayer = satelliteAnnotationLayer;
      this.loaded = true;
      /**
       * 初始化地图搜索工具
       */
      if (this.$refs.SearchMap) {
        this.$refs.SearchMap.init(this);
      }
      /**
       * 初始化矩形编辑工具
       */
      this.$refs.RectangleEditor.init(this);
      /**
       * 初始化圆形编辑工具
       */
      this.$refs.CircleEditor.init(this);
      /**
       * 初始化多边形编辑工具
       */
      this.$refs.PolygonEditor.init(this);
      /**
       * 初始化折线编辑工具
       */
      this.$refs.PolylineEditor.init(this);
      /**
       * 初始化标注点工具
       */
      this.$refs.DrawMarker.init(this);
      /**
       * 初始化卫星图层切换工具
       */
      this.$refs.LayerSelect.init(this);
      /**
       * 初始化地图工具
       */
      this.$refs.MapTools.init(this);
      this.initMouseLeftClickEvent();
      // 地图图块加载完成后触发
      this.map.on('postrender', () => {
        this.$emit('mapLoaded', this);
      });
    },
    mapZoomIn() {
      this.map.zoomIn();
    },
    mapZoomOut() {
      this.map.zoomOut();
    },
    clearMap() {
      this.LabelsLayer.getSource().clear();
      this.$emit('clearMap');
      if (this.Measure) {
        this.Measure.clearMeasure();
      }
    },
    addCELayer() {
      // 国能要求 加载国能厂区地图 暂时一个 后续加入权限控制
      let ceLayer = new MayMap.ol.layer.Tile({
        title: '天地图矢量注记图层',
        source: new MayMap.ol.source.XYZ({
          url: '/bdsplatform/cegis/ws/w184/imgtile/5e4de8dd-9efa-4b3c-8abf-86ffd7a06968/101/{z}/{x}/{y}.png?org=10017863',
          tileLoadFunction: function (imageTile, src) {
            src = src.replace('CEkey=b4f0d3a1-2c5e-4a8f-9b3d-5e7f3c2b1a4d', 'CEkey=e0084286');
            const xhr = new XMLHttpRequest();
            xhr.responseType = 'blob';
            xhr.onload = function () {
              const blob = xhr.response;
              imageTile.getImage().src = URL.createObjectURL(blob);
            };
            // xhr.open('GET', "http://localhost:1888/cegis/ws/w184/imgtile/5e4de8dd-9efa-4b3c-8abf-86ffd7a06968/101/14/13211/6246.png?org=10017863", true);
            xhr.open('GET', `${src}`, true);
            // xhr.open('GET', src, true);
            xhr.setRequestHeader('loginuser', 'e0084286');
            xhr.send();
          }
        }),
        extent: [
          12276756.752656179,
          4756215.321352308,
          12284677.906583384,
          4763084.807176925
        ] //加载范围
      });
      this.map.addLayer(ceLayer);
    },
    /**
     * 初始化鼠标左键单击事件
     * @emits event
     * @emits {Number} event.pixel.x 像素坐标x
     * @emits {Number} event.pixel.y 像素坐标y
     * @emits {Number} event.coordinate.longitude 经度
     * @emits {Number} event.coordinate.latitude 纬度
     */
    initMouseLeftClickEvent() {
      this.map.on('click', event => {
        this.$emit('leftClickEvent', {
          pixel: {
            x: event.event.originalEvent.x,
            y: event.event.originalEvent.y
          },
          coordinate: event.coordinate,
          feature: event.feature
        });
        if (this.contextmenuMarker) {
          this.contextmenuMarker.setVisible(false);
        }
      });
    },
    /**
     * 设置地图中心的位置
     * @param {Object} [options]
     * @param {Number} [options.longitude] 经度
     * @param {Number} [options.latitude] 纬度
     * @param {Number} [options.zoom=18] 地图层级
     */
    setZoomAndCenter(options) {
      if (options) {
        let zoom = options.zoom || 18;
        this.map.setZoomAndCenter([
          options.longitude,
          options.latitude
        ], zoom);
      }
    },
    /**
     * 调整到最佳视角
     * @param [_obj] 地图覆盖物
     */
    setFitView(_obj ,padding) {
      if (!this.loaded) {
        setTimeout(() => {
          this.setFitView(_obj);
        }, 200);
      }
      else {
        let obj = _obj || [];
        this.mapInstance.setFitView(obj, padding);
      }
    },
    // 设置中心点|最后点击的marker
    setMarkerCenter(LngLat) {
      let position = [
        LngLat[0],
        LngLat[1]
      ];
      this.map.setCenter(position);
    },
    /**
     * 视频页面中标注车辆Marker
     */
    setMarkerFromVideo(carInfo) {
      let options = {
        longitude: carInfo.stateObj.longitude || this.config.position.longitude,
        latitude: carInfo.stateObj.latitude || this.config.position.latitude,
        icon: ''
      };
      this.$refs.DrawMarker.drawTempMarker(options);
    },
    /**
     * 视频页面中删除车辆Marker
     */
    deleteMarkerFromVideo() {
      this.$refs.DrawMarker.removeTempMarker();
    },
    /**
     * 显示电子围栏
     */
    handleRegion() {
      if (!this.loaded) {
        setTimeout(() => {
          this.handleRegion();
        }, 200);
      }
      else {
        this.$emit('handleRegion');
      }
    },
    createDriving() {
      if (this.startMarker.length > 0 && this.endMarker.length > 0) {
        let startPoint = [
          this.startMarker[0].lng,
          this.startMarker[0].lat
        ];
        let endPoint = [
          this.endMarker[0].lng,
          this.endMarker[0].lat
        ];
        // 路径规划
        if (this.driving) {
          this.driving.clear();
        }
        this.driving = new this.AMap.Driving({
          map: this.map
        });
        this.driving.search(startPoint, endPoint, (status, result) => {
          if (status === 'complete') {
            console.log('路径规划成功');
          }
          else {
            console.log(result);
          }
        });
      }
    },
    /**
     * 关闭消息窗口
     */
    closeInfoWindow() {
      if (this._infoWindow) {
        this._infoWindow.close();
      }
    },
    /**
     * 清除地图标注等
     */
    clearAll() {
      if (this.map) {
        this.map.clearMap();
      }
      else {
        setTimeout(() => {
          this.clearAll();
        }, 200);
      }
    },
    /**
     * 切换折叠状态
     */
    clickArrow() {
      this.showFold = !this.showFold;
      if (!this.showFold) {
        this.clearSearchResult();
      }
    },
    /**
     * 清空输入框查询
     */
    clearSearchResult() {
      this.$refs?.SearchMap.clearSearchResult();
    },
    /**
     * 设置区域规划控件显示与否
     * @param {Boolean} [value] 这个值为空时取反
     */
    clickZoneTool(value) {
      if (value === true) {
        this.showZoneTool = value;
      }
      else {
        this.showZoneTool = !this.showZoneTool;
      }
    },
    /**
     * 改变标签颜色
     */
    changeLayerSelect(status) {
      this.isShowLayerSelect = status;
    },
    changeDrivingLine(status) {
      this.isShowDrivingLine = status;
    },
    changeMapTools(status) {
      this.isShowMapTools = status;
    },
    changeMapCenter(status) {
      this.isShowMapCenter = status;
    },
    changePolyline(status) {
      this.isShowPolyline = status;
    },
    changePolygon(status) {
      this.isShowPolygon = status;
    },
    changeRectangle(status) {
      this.isShowRectangle = status;
    },
    changeCircle(status) {
      this.isShowCircle = status;
    },
    changeDrawMarker(status) {
      this.isShowDrawMarker = status;
    },
    /**
     * 绘制临时的单个标签
     * <AUTHOR>
     * @param options
     * @param {Number} options.longitude 经度
     * @param {Number} options.latitude 纬度
     * @param {String} [options.icon] 图标url或base64
     */
    drawTempMarker(options) {
      this.$refs.DrawMarker.drawTempMarker(options);
    },
    // 绘制设备图标(新)
    setDeviceMarker(val) {
      // 切换车辆时,清除未关闭的窗口
      this.$refs.CarMarkerManage.dialogVisible = false;
      // 清除地图车辆Marker和打开的Marker窗口
      if (!val) {
        this.$refs.CarMarkerManage.clearOldMarker();
        this.$refs.CarMarkerManage.closeDialog();
        return;
      }
      /**
       * 绘制地图中的车辆Marker
       */
      this.$refs.CarMarkerManage.setDeviceMarkerInit(this, val);
    },
    // todo 构建轨迹
    getPath(_coors) {
      // 解析 coors
      let coors = _coors || [];
      let polylinePath = [];
      for (let i = 0; i < coors.length; i++) {
        let lnglat = [
          coors[i][0],
          coors[i][1]
        ];
        polylinePath.push(lnglat);
      }
      if (!this.lineLayer) {
        this.lineLayer = MayMap.TileLayer.LabelsLayer();
        this.map.addLayer(this.lineLayer);
      }
      if (polylinePath.length === 0) {
        polylinePath = [[]];
      }
      this.TrackObj = this.mapInstance.initTrack({
        layer: this.lineLayer, //图层
        data: polylinePath //数据
        // interval: 4,  //两点更新的时间间隔S
        // multiple: 6,  //播放速度
        // imgUrl: "../data/img/综合.png", //自定义图标地址 默认汽车
        // imgScale: 0.8, //自定义图标大小 默认1倍
        // imgAnchor: [0.51, 0.4], //自定义图标位置 默认[0.45,0.55]
        // imgRotation: 180, //自定义图标旋转角 默认0
      });
      return this.TrackObj;
    },
    removeTrack() {
      this.mapInstance.removeLayer(this.lineLayer);
      this.lineLayer = null;
      this.TrackObj = null;
    },
    onPathWatchClick() {
      this.$nextTick(() => {
        this.$refs.carHistorySearch.trackHandle();
      });
      // 关闭监控面板
      this.$emit('showCarList', false);
    },
    // 设备查询轨迹
    onDeviceAllTrackSearch (carSearch) {
      console.log('CE-查询轨迹');
      let parme = {
        startTime: carSearch.start,
        endTime: carSearch.end,
        deviceId: carSearch.deviceId ? BigInt(carSearch.deviceId) : undefined,
        deviceType: carSearch.deviceType ? carSearch.deviceType : undefined,
        targetId: carSearch.targetId ? BigInt(carSearch.targetId) : undefined,
        targetType: carSearch.targetType ? carSearch.targetType : undefined
      };
      if (carSearch.isFilter?.length) {
        for (let index = 0; index < carSearch.isFilter.length; index++) {
          const element = carSearch.isFilter[index];
          parme[element] = 1;
        }
      }
      const loading = this.$loading({
        text: '正在加载',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      queryTracking(parme)
        .then((res) => {
          loading.close();
          // 检查路线
          if (!res.data.locations || res.data.locations.length === 0) {
            this.$message({
              message: "无路线数据，请重新查找",
              type: "error",
            });
            // 隐藏已绘制的轨迹
            if (this.CarHistoryPathPass) this.CarHistoryPathPass.hide();
            this.alarmSource.clear();
            return;
          }
          for (let index = 0; index < res.data.locations.length; index++) {
            const element = res.data.locations[index];
            element.targetType = res.data.targetType;
            element.targetId = res.data.targetId;
            element.targetName = res.data.targetName;
            element.deviceModel = res.data.deviceModel;
            element.deviceNum = res.data.deviceNum;
            element.deviceType = res.data.deviceType;
            element.deviceCategory = res.data.deviceCategory;
            element.deviceId = res.data.deviceId;
            element.deviceUniqueId = res.data.deviceUniqueId;
            element.longitudeTable = element.longitude;
            element.latitudeTable = element.latitude;
          }
          // 车辆图标清理
          this.$refs.CarMarkerManage.clearOldMarker();
          // 关闭窗口
          this.$refs.CarMarkerManage.closeDialog();
          // 绘制路线
          console.time("冻结数据");
          let coors = this.formatPathData(res.data.locations);
          this.Player.coors = Object.freeze(coors);
          this.Player.path = Object.freeze(coors);
          console.timeEnd("冻结数据");
          const labelTypeData = {
            iconUrl: this.$refs.CarMarkerManage.judgeTerminalIcon({
              treeCategory: String(res.data.deviceCategory),
            }),
            iconWidth: 50,
            iconHeight: 50,
          };
          this.Player.car = this.getTrackDeviceMarker({
            position: this.Player.path[0],
            angle: res.data.locations[0].bearing,
            labelTypeData: labelTypeData,
          });
          // 地图添加与缩放
          this.map.olMap.addOverlay(this.Player.car);
          this.map.setZoomAndCenter(coors[0], 10);
          // 设置相关元素进面板
          this.$emit("setTrackElement", {
            map: this.map,
            amap: this.AMap,
            car: this.Player.car,
            track: res.data.locations,
            path: this.CarHistoryPathPass,
            disCont: this.DisContPathPass,
            labelTypeData: labelTypeData,
          });
          // 面板更新
          let historyInfo = res.data.locations[this.Player.index];
          historyInfo.index = this.Player.index;
          historyInfo.total = this.Player.path.length - 1;
          historyInfo.timeStart = carSearch.start;
          historyInfo.timeEnd = carSearch.end;
          console.time("提交数据");
          this.$emit("setInfoData", historyInfo);
          this.$emit("setTableData", res.data.locations);
          this.$emit("setTrackPath", this.Player.coors);
          console.timeEnd("提交数据");
        })
        .catch((err) => {
          loading.close();
          console.log(err);
        });
    },
    formatTrackToGCJ02Coordinates(data) {
      if (!Array.isArray(data)) return [];
      return data.map(group => {
        console.log(this.formatPathData(group),9999,group)
        return this.formatPathData(group)
      });
    },
    //设置异常点路径解决拉直线问题
    queryUnablePointLine(carSearch) {
      let parme = {
        startTime: carSearch.start,
        endTime: carSearch.end,
        deviceId: carSearch.deviceId ? BigInt(carSearch.deviceId) : undefined,
        deviceType: carSearch.deviceType ? carSearch.deviceType : undefined,
      };
      if (carSearch.isFilter?.length) {
        for (let index = 0; index < carSearch.isFilter.length; index++) {
          const element = carSearch.isFilter[index];
          parme[element] = 1;
        }
      }
      queryTrackingUnablePoint(parme)
        .then((res) => {
          // 检查路线
          if (!res.data.locations || res.data.locations.length === 0) {
            this.$message({
              message: "无路线数据，请重新查找",
              type: "warning",
            });
            // 隐藏已绘制的轨迹
            this.alarmSource.clear();
          }
          const trackData = this.formatTrackToGCJ02Coordinates(res.data)
          this.setUnablePointLine(trackData)
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 绘制无效点拉直线路线
    setUnablePointLine (trackData) {
      if (!trackData || !trackData.length) return;
      // 清除之前的要素
      this.alarmSource.clear();
      // 绘制每条路线
      trackData.forEach((points) => {
        const lineList = [];
        points.forEach((point) => {
          const coordinates =  MayMap.ol.proj.transform([
            point[0],
            point[1]
          ], 'EPSG:4326', 'EPSG:3857');
          lineList.push(coordinates);
        });
        // 创建线要素
        const lineFeature = new MayMap.ol.Feature({
          geometry: new MayMap.ol.geom.LineString(lineList)
        });

        // 设置线的样式
        lineFeature.setStyle(new MayMap.ol.style.Style({
          stroke: new MayMap.ol.style.Stroke({
            color: 'red',
            width: 6,
          })
        }));
        // 添加线要素
        this.alarmSource.addFeature(lineFeature);
        // 添加起点标记
        const startPoint = lineList[0];
        const startFeature = new MayMap.ol.Feature({
          geometry: new MayMap.ol.geom.Point(startPoint)
        });
        // 设置起点标记的样式 水滴带一个止字
        startFeature.setStyle(new MayMap.ol.style.Style({
          // 水滴图标
          image: new MayMap.ol.style.Icon({
            width: 50,
            height: 70,
            src: '//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-red.png', // 水滴图标路径
            scale: 0.6, // 图标缩放比例
            anchor: [0.5, 1]
          }),
          // 止字文本
          text: new MayMap.ol.style.Text({
            text: '止',
            font: 'bold 12px sans-serif',
            fill: new MayMap.ol.style.Fill({
              color: '#FFFFFF', // 白色文字
            }),
            offsetY: -27,
            // 确保文本水平居中
            textAlign: 'center',
            textBaseline: 'middle'
          })
        }));
        // 添加起点标记要素
        this.alarmSource.addFeature(startFeature);
         // 添加异常终点标记
        const endPoint = lineList[lineList.length - 1];
        const endFeature = new MayMap.ol.Feature({
          geometry: new MayMap.ol.geom.Point(endPoint)
        });
        // 设置异常终点标记的样式
        endFeature.setStyle(new MayMap.ol.style.Style({
          // 水滴图标
          image: new MayMap.ol.style.Icon({
            width: 50,
            height: 70,
            src: '//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png', // 水滴图标路径
            scale: 0.6, // 图标缩放比例
            anchor: [0.5, 1], // 锚点设置为底部中心
          }),
          // 止字文本
          text: new MayMap.ol.style.Text({
            text: '起',
            font: 'bold 12px sans-serif',
            fill: new MayMap.ol.style.Fill({
              color: '#FFFFFF', // 白色文字
            }),
            offsetY: -27,
            // 确保文本水平居中
            textAlign: 'center',
            textBaseline: 'middle'
          })
        }));
        //添加异常终点标记到图层
        this.alarmSource.addFeature(endFeature);
      });
    },
    /**
     * 设备查询轨迹(分页加载)
     * @param carSearch
     */
    onDeviceTrackSearch(carSearch) {
      console.log('CE-查询轨迹分页加载');
      let parme = {
        startTime: carSearch.start,
        endTime: carSearch.end,
        deviceId: carSearch.deviceId ? BigInt(carSearch.deviceId) : undefined,
        deviceType: carSearch.deviceType ? carSearch.deviceType : undefined,
        targetId: carSearch.targetId ? BigInt(carSearch.targetId) : undefined,
        targetType: carSearch.targetType ? carSearch.targetType : undefined,
        startIndex: 0,
        endIndex: this.baseTrackSubSize + 1
      };
      if (carSearch.isFilter?.length) {
        for (let index = 0; index < carSearch.isFilter.length; index++) {
          const element = carSearch.isFilter[index];
          parme[element] = 1;
        }
      }
      // 重置分页数据缓存
      this.cachePageData = [];
      const loading = this.$loading({
        text: '正在加载',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      queryTrackingByPage(parme).then(res => {
        loading.close();
        // 检查路线
        if (!res.data.locations || res.data.locations.length === 0) {
          this.$message({
            message: '无路线数据，请重新查找',
            type: 'error'
          });
          // 隐藏已绘制的轨迹
          if (this.CarHistoryPathPass) this.CarHistoryPathPass.hide();
          this.alarmSource.clear();
          return;
        }
        for (let index = 0; index < res.data.locations.length; index++) {
          const element = res.data.locations[index];
          element.targetType = res.data.targetType;
          element.targetId = res.data.targetId;
          element.targetName = res.data.targetName;
          element.deviceModel = res.data.deviceModel;
          element.deviceNum = res.data.deviceNum;
          element.deviceType = res.data.deviceType;
          element.deviceCategory = res.data.deviceCategory;
          element.deviceId = res.data.deviceId;
          element.deviceUniqueId = res.data.deviceUniqueId;
          element.longitudeTable = element.longitude;
          element.latitudeTable = element.latitude;
        }
        // 首次设置分页轨迹数据
        this.setTrackDataFirst(carSearch, Object.freeze(res));
        this.queryUnablePointLine(carSearch)

      }).catch(err => {
        loading.close();
        console.log(err);
      });
    },
    setTrackDataFirst(carSearch, res) {
      // 设置第一页数据
      this.cachePageData = res.data.locations;
      // 车辆图标清理
      this.$refs.CarMarkerManage.clearOldMarker();
      // 关闭窗口
      this.$refs.CarMarkerManage.closeDialog();
      // 绘制路线
      console.time('冻结数据');
      let coors = this.formatPathData(this.cachePageData);
      this.Player.coors = Object.freeze(coors);
      this.Player.path = Object.freeze(coors);
      this.Player.carsData = this.cachePageData;
      console.timeEnd('冻结数据');
      const labelTypeData = {
        iconUrl: this.$refs.CarMarkerManage.judgeTerminalIcon({ treeCategory: String(res.data.deviceCategory) }),
        iconWidth: 50,
        iconHeight: 50
      };
      this.Player.car = this.getTrackDeviceMarker({
        position: this.Player.path[0],
        angle: this.cachePageData[0].bearing,
        labelTypeData: labelTypeData
      });
      // 地图添加与缩放
      this.map.olMap.addOverlay(this.Player.car);
      this.map.setZoomAndCenter(coors[0], 10);
      // 设置相关元素进面板
      this.$emit('setTrackElement', {
        map: this.map,
        amap: this.AMap,
        car: this.Player.car,
        track: this.cachePageData,
        path: this.CarHistoryPathPass,
        disCont: this.DisContPathPass,
        labelTypeData: labelTypeData
      });
      // 面板更新
      let historyInfo = this.Player.carsData[this.Player.index];
      historyInfo.index = this.Player.index;
      historyInfo.total = this.Player.path.length - 1;
      historyInfo.timeStart = carSearch.start;
      historyInfo.timeEnd = carSearch.end;
      console.time('提交数据');
      this.$emit('setInfoData', historyInfo);
      this.$emit('setTableData', this.cachePageData);
      this.$emit('setTrackPath', this.Player.coors);
      console.timeEnd('提交数据');
      if (this.cachePageData.length < res.data.total) {
        this.setTrackDataOther(carSearch);
      }
    },
    setTrackDataOther(carSearch) {
      let parme = {
        startTime: carSearch.start,
        endTime: carSearch.end,
        deviceId: carSearch.deviceId ? BigInt(carSearch.deviceId) : undefined,
        deviceType: carSearch.deviceType ? carSearch.deviceType : undefined,
        targetId: carSearch.targetId ? BigInt(carSearch.targetId) : undefined,
        targetType: carSearch.targetType ? carSearch.targetType : undefined,
        startIndex: this.cachePageData.length,
        endIndex: this.cachePageData.length + this.otherTrackSubSize
      };
      if (carSearch.isFilter?.length) {
        for (let index = 0; index < carSearch.isFilter.length; index++) {
          const element = carSearch.isFilter[index];
          parme[element] = 1;
        }
      }
      queryTrackingByPage(parme).then(res => {
        // 分页数据设置到缓存中
        for (let index = 0; index < res.data.locations.length; index++) {
          const element = res.data.locations[index];
          element.targetType = res.data.targetType;
          element.targetId = res.data.targetId;
          element.targetName = res.data.targetName;
          element.deviceModel = res.data.deviceModel;
          element.deviceNum = res.data.deviceNum;
          element.deviceType = res.data.deviceType;
          element.deviceCategory = res.data.deviceCategory;
          element.deviceId = res.data.deviceId;
          element.deviceUniqueId = res.data.deviceUniqueId;
          element.longitudeTable = element.longitude;
          element.latitudeTable = element.latitude;
        }
        console.time('冻结分页设置数据');
        this.cachePageData = Object.freeze([
          ...this.cachePageData,
          ...res.data.locations
        ]);
        let coors = this.formatPathData(this.cachePageData);
        this.Player.coors = Object.freeze(coors);
        this.Player.path = Object.freeze(coors);
        console.timeEnd('冻结分页设置数据');
        // 判断是否还有下一页 以及下一页的起始位置
        this.$emit('setTableData', this.cachePageData);
        this.$emit('setTrackPath', this.Player.coors);
        if (this.cachePageData.length < res.data.total) {
          this.setTrackDataOther(carSearch);
        }
      });
    },
    // 终端查询轨迹(公务车)
    terminalTrackSearch(carSearch) {
      let parme = {
        startTime: carSearch.start,
        endTime: carSearch.end,
        deviceId: carSearch.deviceId ? BigInt(carSearch.deviceId) : undefined,
        deviceType: carSearch.deviceType ? carSearch.deviceType : undefined,
        targetId: carSearch.targetId ? BigInt(carSearch.targetId) : undefined,
        targetType: carSearch.targetType ? carSearch.targetType : undefined,
        // 数据起始索引
        startIndex: 0,
        // 数据结束索引
        endIndex: this.baseTrackSubSize
      };
      if (carSearch.isFilter?.length) {
        for (let index = 0; index < carSearch.isFilter.length; index++) {
          const element = carSearch.isFilter[index];
          parme[element] = 1;
        }
      }
      const loading = this.$loading({
        text: '正在加载',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      queryTracking(parme).then(res => {
        loading.close();
        // 检查路线
        if (!res.data.locations || res.data.locations.length === 0) {
          this.$message({
            message: '无路线数据，请重新查找',
            type: 'error'
          });
          // 隐藏已绘制的轨迹
          if (this.CarHistoryPathPass) this.CarHistoryPathPass.hide();
          return;
        }
        for (let index = 0; index < res.data.locations.length; index++) {
          const element = res.data.locations[index];
          element.targetType = res.data.targetType;
          element.targetId = res.data.targetId;
          element.targetName = res.data.targetName;
          element.deviceModel = res.data.deviceModel;
          element.deviceNum = res.data.deviceNum;
          element.deviceType = res.data.deviceType;
          element.deviceCategory = res.data.deviceCategory;
          element.deviceId = res.data.deviceId;
          element.deviceUniqueId = res.data.deviceUniqueId;
          element.longitudeTable = element.longitude;
          element.latitudeTable = element.latitude;
        }
        const locationData = this.$utils.wgs84togcj02Batch(res.data.locations);
        // 绘制路线
        let coors = this.formatPathData(locationData);
        this.Player.coors = Object.freeze(coors);
        this.Player.path = Object.freeze(coors);
        this.Player.carsData = Object.freeze(locationData);
        const labelTypeData = {
          iconUrl: '/bdsplatform/static/images/pic/vehicle.png',
          bgUrl: '/bdsplatform/static/images/pic/static.png',
          iconWidth: 50,
          iconHeight: 50
        };
        this.Player.car = this.getTrackDeviceMarker({
          position: this.Player.path[0],
          angle: locationData[0].bearing,
          labelTypeData: labelTypeData
        });
        // 设置相关元素进面板
        this.$emit('setTrackElement', {
          map: this.map,
          amap: this.AMap,
          car: this.Player.car,
          track: locationData,
          path: this.CarHistoryPathPass,
          disCont: this.DisContPathPass,
          labelTypeData: labelTypeData
        });
        // 地图添加与缩放
        this.map.olMap.addOverlay(this.Player.car);
        // this.map.setFitView();
        this.map.panTo(coors[0]);
        // 面板更新
        let historyInfo = this.Player.carsData[this.Player.index];
        historyInfo.index = this.Player.index;
        historyInfo.total = this.Player.path.length - 1;
        historyInfo.timeStart = carSearch.start;
        historyInfo.timeEnd = carSearch.end;
        this.$emit('setInfoData', historyInfo);
        this.$emit('setTableData', locationData);
        this.$emit('setTrackPath', this.Player.coors);
      }).catch(err => {
        loading.close();
        console.log(err);
      });
    },
    /**
     * 获取轨迹状态的车辆覆盖物
     */
    getTrackCarMarker(_opts) {
      let opts = _opts || {};
      let marker = new this.AMap.Marker({
        icon: opts.status.icon,
        position: opts.position,
        offset: new this.AMap.Pixel(-ICON_SIZE / 2, -ICON_SIZE / 2),
        angle: opts.angle
      });
      return marker;
    },
    /**
     * 获取轨迹状态的终端覆盖物
     */
    getTrackDeviceMarker(_opts) {
      let opts = _opts || {};
      const element = document.createElement('div');
      // FIXME 暂时屏蔽
      element.innerHTML = `<div style="position: relative;display: none">
                      <div class="follow-marker-bg" style="position: absolute; width: 100%; height: 100%; background-image: url('/bdsplatform/static/images/pic/static.png'); background-size: 100%;"></div>
                      <img src="${opts.labelTypeData.iconUrl}" style="display: block; width: ${opts.labelTypeData.iconWidth}px; height: ${opts.labelTypeData.iconHeight}px; padding: 3px; position: inherit;">
                    </div>`;
      let marker = new this.AMap.ol.Overlay({
        element,
        stopEvent: false,
        position: this.AMap.ol.proj.transform([
          opts.position[0],
          opts.position[1]
        ], 'EPSG:4326', 'EPSG:3857'),
        positioning: 'center-center',
        autoPan: true
      });
      return marker;
    },
    onTrackPlanelCancel() {
      if (!this.Player.car) {
        return;
      }
      // todo CE的
      // this.Player.car.stopMove();
      // this.map.remove(this.Player.car);
      this.$emit('onTrackStop');
    },
    formatPathData(_data) {
      if (!_data) {
        return [];
      }
      let coors = [];
      for (let i = 0; i < _data.length; i++) {
        const element = _data[i];
        let coor = [
          Number(element.longitude),
          Number(element.latitude)
        ];
        coors.push(coor);
      }
      return coors;
    },
    setPath(_coors) {
      let coors = this.formatPathData(_coors);
      // 无坐标点过滤
      if (coors.length === 0) {
        console.warn('无轨迹数据');
        return;
      }
      this.Player.coors = Object.freeze(coors);
      // 地图添加与缩放
      this.map.setFitView();
      this.map.panTo(coors[0]);
    },
    // 绘制聚合marker
    drawMarkers(data) {
      const list = [];
      // 初始化Marker工具，且不显示轨迹回放按钮
      this.$refs.CarMarkerManage.init(this, false);
      data.forEach((item) => {
        const marker = this.$refs.CarMarkerManage.batchCarMarker(item);
        list.push(marker);
      });
      this.updateCarMarkers(list);
    },
    async drawRegionMarkers(carsInMap, isOffset) {
      // 初始化Marker工具，且不显示轨迹回放按钮
      this.$refs.CarMarkerManage.init(this, false);
      this.$refs.CarMarkerManage.closeDialog();
      this.map.olMap.getOverlays().clear();
      this.$nextTick(() => {
        this.$refs.CarMarkerManage.getDeviceMarkers(carsInMap, isOffset);
      });
    },
    // 更新车辆图标 : 使用聚合工具批量操作
    updateCarMarkers(_carsMarkerData) {
      // 添加新车辆图标
      this.carsClusterer.setData(_carsMarkerData);
    },
    clearClustererMarkers() {
      this.carsClusterer.setData([]);
    },
    /**
     * 跳转路由方法
     */
    goPath(routeUrl) {
      this.$router.push({
        path: routeUrl,
        query: {
          isRouter: this.$route.fullPath
        }
      });
    },
    // 点击工具栏事件
    handleClick(item) {
      if (item.path) {
        this.goPath(item.path);
      }
      if (item.clickHandle) {
        item.clickHandle();
      }
    },
    // 卫星显示
    showSatellite() {
      this.$refs.LayerSelect.showSelect();
    },
    // 工具栏切换
    handleCommand(e) {
      if (e) {
        this.$refs.MapTools[e]();
      }
    }
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style>
.amap-logo {
  display: none !important;
}

.amap-marker-label {
  border: 0;
  background-color: transparent;
}

.amap-sug-result {
  z-index: 5000;
}
</style>

<style lang="less" scoped>
@import "../../assets/less/selectComponent.less";

.map {
  width: 100%;
  height: 100%;
  // position: relative;
  .map-tool-bar {
    position: absolute;
    right: 15px;
    top: 15px;
    // width: 500px;
    height: 44px;
    z-index: 1;
    display: flex;
    cursor: pointer;

    ::v-deep .searchResultsBox {
      background-color: transparent;
      padding: 0;
      border-radius: 4px;
      box-shadow: 0px 0px 7px 2px rgba(83, 83, 83, 0.20);

      .el-input--mini .el-input__inner {
        height: 44px;
        line-height: 44px;
      }
    }

    .search-icon {
      width: 44px;
      height: 44px;
      background: var(--gn-color);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;
      border-radius: 0 4px 4px 0;
      position: relative;
      //left: -2px;

      .search-img {
        display: flex;
        width: 22px;
        height: 18px;
        background: url('../../assets/images/monitor/monitor_icon_search.png') center no-repeat;
        background-size: 100% 100%;
      }
    }

    .map-tool-map-control {
      margin-left: 11px;
      // width: 564px;
      height: 44px;
      opacity: 1;
      background: #ffffff;
      border-radius: 4px;
      box-shadow: 0px 0px 7px 2px rgba(83, 83, 83, 0.20);
      display: flex;
      align-items: center;

      .map-control-text {
        padding: 14px 19px;
        font-size: 12px;
        color: #6d6d6d;
        display: flex;
        align-items: center;

        &:hover {
          background: #e3f4ff;
        }
      }

      ::v-deep .el-dropdown {
        font-size: 12px;

        .el-dropdown-link {
          display: flex;
          align-items: center;
          padding: 15px 19px;
        }
      }

      .traffic {
        display: flex;
        width: 22px;
        height: 18px;
        background: url('../../assets/images/monitor/monitor_icon_traffic.png') center no-repeat;
        background-size: 100% 100%;
      }

      .satellite {
        display: flex;
        width: 22px;
        height: 18px;
        background: url('../../assets/images/monitor/monitor_icon_satellite.png') center no-repeat;
        background-size: 100% 100%;
      }

      .tool {
        display: flex;
        width: 22px;
        height: 18px;
        background: url('../../assets/images/monitor/monitor_icon_tool.png') center no-repeat;
        background-size: 100% 100%;
      }
    }
  }
}

.mapWidgetAMap {
  display: flex;
  flex-direction: row;
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 1000;
  padding: 10px;
  background-color: #ffffff;
  font-size: 14px;
  border: 1px;
  border-radius: 5px;
  box-shadow: 0px 0px 5px #888888;

  span {
    line-height: 28px;
  }

  .search-box {
    display: flex;
    align-items: center;

    ::v-deep .searchResultsBox {
      position: relative;
      padding: 0;
      right: 3px;
      margin-right: 10px;
    }
  }
}

.videoMapContent {
  position: absolute;
  right: 0px;
  z-index: 1000;
}

.planelSpan {
  margin-right: 10px;
  cursor: pointer;
}

.selectedColor {
  color: red
}

::v-deep .point-for-moniter {
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 6px 0 rgb(114 124 245 / 50%);
  border-radius: .25rem;
  padding: 0.25rem;
}

::v-deep .amap-copyright {
  display: none !important; // 隐藏高德版本
}

::v-deep .amap-toolbar {
  position: absolute;
  bottom: 0 !important; // 地图控制栏
}
</style>
<style lang="scss">
.right-contextmenu ul {
  background-color: #ffffff;
  list-style-type: none;
  box-shadow: 0 2px 2px 0 #cdcdcd;
  padding: 0;
  border-radius: 4px;
  & li {
    list-style-type: none;
    cursor: pointer;
    width: 60px;
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
  }
}
#ce-map {
  position: relative;
}
</style>
