<template>
  <div class="vehicle-container">
    <div class="container-header">
      <p class="container-header-title">{{ vehicleData.targetName }}</p>
      <div class="container-header-content">
        <div v-show="operationButton && vehicleData.trackable" style="flex: 1;">
          <el-checkbox
            v-model="followOpen"
            class="follow-check"
            size="small"
            title="跟踪终端"
            @change="followChange"
          >
            跟踪终端
          </el-checkbox>
        </div>
        <div class="container-header-info">
          <div
            v-if="vehicleData.charge !== 0"
            class="battery-icon"
          >
            <div class="battery-shell">
              <div
                class="battery-level"
                :style="{ width: vehicleData.charge + '%',backgroundColor: getChargeBg(vehicleData.charge) }"
              />
            </div>
            <div
              class="battery-percentage"
            >
              {{ vehicleData.charge }}
            </div>
          </div>
          <el-badge
            :value="vehicleData.gnssNum"
            class="badge-item"
            type="primary"
          >
            <img
              class="signal-icon"
              :src="`/bdsplatform/static/images/signal/signal-100.png`"
              alt="定位信号"
            >
          </el-badge>
          <el-tooltip :content="`通信信号: ${vehicleData.wireless}`">
            <div class="wireless-signal">
              <span
                v-for="item in 4"
                :key="item"
                class="wireless-signal-item"
                :style="{height: `${(item + 1) * 20}%`, backgroundColor: getWirelessColor(vehicleData.wireless, item)}"
              />
            </div>
          </el-tooltip>
        </div>
      </div>
      <div
        class="container-header-btn"
        @click="closeHandle"
      >
        <i class="el-icon-circle-close"/>
      </div>
    </div>
    <div class="vehicle-state">
      <div class="state-container">
        <!-- 设备详情 -->
        <div class="state-content-bottom">
          <div class="state-content-bottom-sign">
            <span class="state-content-label">序列号</span>
            <span class="state-content-value">{{ vehicleData.uniqueId }}</span>
          </div>
          <div class="state-content-bottom-sign">
            <!-- <span class="state-content-label">终端类别</span>
            <span class="state-content-value">{{ getEnumDictLabel('bdmDeviceType', vehicleData.deviceType) }}</span> -->
            <span class="state-content-label">速度</span>
            <span class="state-content-value">{{ vehicleData.speed }}km/h</span>
          </div>
          <div class="state-content-bottom-sign">
            <span class="state-content-label">终端类型</span>
            <span class="state-content-value">{{ getEnumDictLabel('bdmDeviceType', vehicleData.deviceCategory) }}</span>
          </div>
          <div class="state-content-bottom-sign">
            <span class="state-content-label">赋码编号</span>
            <span class="state-content-value">{{ vehicleData.deviceNum }}</span>
          </div>
          <div class="state-content-bottom-sign">
            <span class="state-content-label">使用单位</span>
            <span class="state-content-value">{{ vehicleData.deptName }}</span>
          </div>
          <div class="state-content-bottom-sign">
            <span class="state-content-label">定位时间</span>
            <span class="state-content-value">{{ vehicleData.time ? parseTime(vehicleData.time) : '-' }}</span>
          </div>
          <div
            class="state-content-bottom-sign"
            style="padding: 4px 8px;"
          >
            <!--            <span class="state-content-label">当前位置：</span>-->
            <span class="state-content-value">{{ position }}</span>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="operationButton"
      class="vehicle-btn"
    >
      <el-button
        size="mini"
        @click="gotoTrack"
      >轨迹回放
      </el-button>
      <el-button
        v-if="vehicleData.videoable && !isOfficial"
        size="mini"
        @click="gotoVideo"
      >实时视频
      </el-button>
    </div>
  </div>
</template>

<script>
import { parseTime } from '@/api/utils/share';
import getLabel from '@/utils/getLabel';
import { fourDimensionalAddress, gnAddress, vehicleAddress } from '@/api/monitoring/info.js';

export default {
  components: {},
  props: {
    vehicleData: {
      type: Object,
      default: () => {
        return {};
      }
    },
    followCar: { // 跟踪车辆集合
      type: Array,
      default: () => {
        return [];
      }
    },
    operationButton: {
      type: Boolean,
      default: true
    },
    isOfficial: {
      type: Boolean,
      default: false
    }
  },
  dicts: [
    'bdmDeviceType'
  ],
  data() {
    return {
      followOpen: false, // 开启跟踪
      position: '' // 当前位置
    };
  },
  computed: {
    getChargeBg() {
      return (val) => {
        if (val >= 50) {
          return '#2ebd32';
        }
        else if (val >= 20) {
          return '#d5a52c';
        }
        else {
          return '#ed5c5c';
        }
      };
    },
    getWirelessColor() {
      return (source, order) => {
        const itemVal = order * 25;
        let flag;
        if(source === 0) {
          flag = 0;
        }
        else if (source < 6) {
          flag = 25;
        }
        else if (source < 16) {
          flag = 50;
        }
        else if (source < 26) {
          flag = 75;
        }
        else {
          flag = 100;
        }
        if (itemVal <= flag) {
          return '#0c88dc';
        }
        else {
          return '#b8b8b8';
        }
      };
    },
    getGnssNumBg() {
      return (source) => {
        const val = (source / 69) * 100;
        if (val === 0) {
          return '0';
        }
        else if (val <= 25) {
          return '25';
        }
        else if (val <= 50) {
          return '50';
        }
        else if (val <= 75) {
          return '75';
        }
        else if (val <= 100) {
          return '100';
        }
      };
    }
  },
  watch: {
    vehicleData: {
      handler(newVal) {
        this.position = newVal.position;
        this.setFollowState();
        if (!this.position) {
          this.getAddress(newVal);
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 四维逆地理编码(后台接口)
    async getAddress(data) {
      const query = {
        lon: Number(data.longitude),
        lat: Number(data.latitude)
      };
      await vehicleAddress(query).then(res => {
        if (res.code === 200) {
          this.position = res.data;
          this.vehicleData.position = res.data;
        }
      });
    },
    // 国能逆地理编码
    getGNAddress(data) {
      const query = {
        postStr: {
          lon: data.longitude,
          lat: data.latitude,
          ver: 1
        },
        type: 'geocode'
      };
      gnAddress(query).then(res => {
        if (res.status === 200) {
          let {
            formatted_address
          } = res.data.result;
          if (Number(res.data.status) === 0) {
            this.position = formatted_address;
            this.vehicleData.position = formatted_address;
          }
        }
      });
    },
    // 点击跟踪车辆按钮
    followChange(v) {
      this.$emit('followHandle', {
        open: v,
        ...JSON.parse(JSON.stringify(this.vehicleData)),
        deviceId: this.vehicleData.deviceIdStr,
        position: [
          this.vehicleData.longitude,
          this.vehicleData.latitude
        ]
      });
    },
    // 设置跟踪车辆按钮
    setFollowState() {
      this.followOpen = false;
      this.followCar.forEach(item => {
        if (this.vehicleData.deviceIdStr === item.deviceIdStr) {
          this.followOpen = item.open;
        }
      });
    },
    /**
     * 视频
     */
    gotoVideo() {
      this.$router.push({
        path: '/monitoring/videoLiveRTVS/index',
        query: {
          deviceId: this.vehicleData.deviceIdStr,
          deviceType: this.vehicleData.deviceType,
          targetName: this.vehicleData.targetName,
          isRouter: this.$route.fullPath
        }
      });
    },
    /**
     * 轨迹
     */
    gotoTrack() {
      const trackInfo = {
        deviceId: this.vehicleData.deviceIdStr,
        deviceType: this.vehicleData.deviceType,
        targetType: this.vehicleData.targetType,
        targetId: this.vehicleData.targetId,
        targetName: this.vehicleData.targetName,
        id: this.vehicleData.deviceIdStr
      };
      if (this.isOfficial) {
        this.$emit('handleTrack', trackInfo);
      }
      else {
        localStorage.setItem('TRACK_INFO', JSON.stringify(trackInfo));
        this.$router.push({
          path: '/monitoring/trackInfo/indexCE',
          query: {
            isRouter: this.$route.fullPath
          }
        });
      }
    },
    closeHandle() {
      this.$emit('update:dialogVisible', false);
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('Vehicle', value);
    },
    parseTime
  }
};
</script>

<style lang="less" scoped>
.vehicle-container.vehicle-info {
  width: 360px;
  overflow: hidden;
  box-shadow: 4px 4px 12px rgba(0, 0, 0, 0.15);
}

.container-header {
  height: 36px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgb(216, 236, 254);
  padding: 0 8px;

  &-title {
    color: rgb(51, 51, 51);
    font-size: 16px;
    font-weight: 600;
  }

  &-content {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 10px 0 10px;

    .container-header-info {
      height: 100%;
      display: flex;
      align-items: center;
    }
  }

  &-btn {
    font-size: 20px;
    cursor: pointer;
  }
}

.state-container {
  width: 100%;
  background: rgb(255, 255, 255);
  display: flex;
  flex-direction: column;
  padding: 0 0 4px;
}

.vehicle-state {
  // padding: 10px 10px;
  // width: 100%;
  max-height: 320px;
  overflow: auto;
}

.state-content-label {
  display: inline-block;
  width: 66px;
  font-weight: 500;
  white-space: nowrap;
  border-right: 0.5px #e8e8e8 solid;
  margin-right: 10px;
  padding: 3px 0;
  color: #686868;
}

.state-content-value {
  padding: 3px 0;
}

.state-content-bottom {
  &-sign {
    padding: 0 8px;
    border-bottom: 0.5px #e8e8e8 solid;
  }
}

.basics {
  width: 100%;
  display: flex;
  background: linear-gradient(270deg, #2d46c7, #ffffff) 0 0 no-repeat, linear-gradient(270deg, #ffffff, #2d46c7) 100% 0 no-repeat, linear-gradient(270deg, #2d46c7, #ffffff) 0 100% no-repeat, linear-gradient(270deg, #ffffff, #2d46c7) 100% 100% no-repeat;
  background-size: 51% 3px, 51% 3px;
  padding: 10px;

  &-info {
    flex: 2;
    height: 100%;

    &-left > div {
      padding-top: 6px;
    }
  }

  &-image {
    flex: 1;

    ::v-deep .el-image {
      height: 110px;
    }
  }
}

.vehicle-btn {
  display: flex;
  justify-content: center;
  padding-bottom: 4px;
}

.more-button {
  color: #2398ff;
  padding: @xhSpacingBase @xhSpacingBase;
  margin-right: @xhSpacingBase;
  border-radius: @xhBorderRadiusBase;
  cursor: pointer;
  // margin-left: 285px;
}

.battery-icon {
  display: inline-flex;
  align-items: center;
  position: relative;
  width: 24px;
  height: 14px;
  margin-right: 8px;
  user-select: none;

  &::after {
    content: '';
    position: absolute;
    right: -2px;
    width: 2px;
    height: 8px;
    top: 3px;
    border-radius: 0 2px 2px 0;
    border-right: 1px solid #333333;
    border-top: 1px solid #333333;
    border-bottom: 1px solid #333333;
  }
}

.battery-shell {
  border: 1px solid #333333;
  border-radius: 4px;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.battery-level {
  height: 100%;
}

.battery-percentage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 10px;
  line-height: 10px;
  color: #333333;
  font-weight: bold;
}

.signal-icon {
  width: 18px;
  margin-right: 10px;
  user-select: none;
  position: relative;
  top: 3px;
}

.badge-item {
  //margin-top: 5px;
  //margin-right: 6px;
  /deep/ .el-badge__content {
    top: 3px;
    right: 19px;
    font-size: 10px;
    line-height: 17px;
  }
}

.wireless-signal {
  height: 16px;
  display: flex;
  align-items: flex-end;
  user-select: none;

  .wireless-signal-item {
    background-color: #0c88dc;
    width: 3px;
    margin-right: 2px;
  }
}

.follow-check {
  ::v-deep .el-checkbox__label {
    font-size: 12px !important;
    margin-left: -4px !important;
  }
}

// 不同分辨率媒体查询样式

@media screen and (max-width: 1500px) {
  // 底部表格
  .vehicle-state {
    //max-height: 150px !important;
  }
}
</style>
