<template>
  <div>
    <el-popover
      placement="bottom"
      trigger="click"
      @show="showCircle"
      @hide="hideCircle"
    >
      <el-button
        size="small"
        @click="startEditCircle"
      >
        开始绘制
      </el-button>
      <el-button
        size="small"
        @click="clearCircle"
      >
        清除
      </el-button>
      <el-button
        size="small"
        @click="editCircle"
      >
        编辑
      </el-button>
      <el-button
        size="small"
        @click="endEditCircle"
      >
        结束编辑
      </el-button>
      <a
        slot="reference"
        ref="toggleButton"
        class="planel_button planelSpan"
      >圆形</a>
    </el-popover>
  </div>
</template>

<script>
import defaultValue from '@/utils/core/defaultValue';
export default {
  name: 'CircleEditor',
  components: {
  },
  props: {
  },
  data () {
    return {
      /** 地图对象 */
      map: null,
      /** 高德地图 : 方便调用 API */
      AMap: null,
      /** 高德地图鼠标工具 */
      mousetool: null,
      loaded: false,
      toolParam: {
        isEditingCircle: true
      },
      showCircleEditor: false
    };
  },
  methods: {
    /**
     * 初始化
     * @description 地图相关的对象从外部传入
     * @param {Component} mapWidget 地图组件
     * @param {Object} mapWidget.AMap 高德地图
     * @param {Object} mapWidget.map 地图实例
     * @param {Object} mapWidget.mousetool 地图工具
     * @param {Boolean} mapWidget.loaded 是否加载完毕
     */
    init (mapWidget) {
      this._mapWidget = mapWidget;
      this.AMap = this._mapWidget.AMap;
      this.map = this._mapWidget.map;
      this.mousetool = this._mapWidget.mousetool;
      this.loaded = this._mapWidget.loaded;
    },
    /**
     * 开始绘制圆形
     */
    startEditCircle () {
      console.log(this.loaded);
      if (!this.loaded) {
        setTimeout(() => {
          this.startEditCircle();
        }, 200);
      } else {
        this.toolParam.isEditingCircle = true;
        this.clearCircle();
        // 用鼠标工具画圆形
        this.mousetool.circle();
        // 添加事件
        this.mousetool.on('draw', (e) => {
          this.drawCircle = e.obj;
          this.mousetool.close();
          console.log('坐标点路径为', e.obj);// 获取路径范围
        });
      }
    },
    /**
     * 清除圆
     */
    clearCircle () {
      if (this.circleEditor) {
        this.circleEditor.close();
        this.circleEditor = undefined;
      }
      if (this.drawCircle) {
        this.map.remove(this.drawCircle);
        this.drawCircle = undefined;
      }
    },
    /**
     * 编辑圆
     */
    editCircle () {
      if (!this.circleEditor) {
        // 实例化圆编辑器，传入地图实例和要进行编辑的圆实例
        this.circleEditor = new this.AMap.CircleEditor(this.map, this.drawCircle);
        // 开启编辑模式
        this.circleEditor.open();
      }
      this.toolParam.isEditingCircle = true;
    },
    /**
     * 结束编辑圆
     */
    endEditCircle () {
      if (this.circleEditor) {
        this.circleEditor.close();
        this.circleEditor = undefined;
      }
    },
    /**
     * 获取圆的数据
     * @return {String}
     */
    getCirclePath () {
      if (this.drawCircle) {
        return this.drawCircle.getPath();
      } else {
        return '';
      }
    },
    /**
     * 获取圆的数据
     * @return {String}
     * @return {{longitude, latitude, radius: Number}}
     */
    getCircleParams () {
      if (this.drawCircle) {
        let center = this.drawCircle.getCenter();
        return {
          longitude: center.lng,
          latitude: center.lat,
          radius: this.drawCircle.getRadius()
        };
      } else {
        return '';
      }
    },
    /**
     * 初始化编辑的圆
     * @param {Array} param
     * @param {Number} param.longitude 经度
     * @param {Number} param.latitude 纬度
     * @param {Number} param.radius 半径
     * @param {String} [param.fillColor='#00b0ff'] 填充颜色
     * @param {String} [param.strokeColor='#80d8ff'] 线条颜色
     * @param {String} [param.borderWeight=1] 线条宽度
     */
    setEditCircle (param) {
      console.log(param, 'param');
      if (!this.loaded) {
        setTimeout(() => {
          this.setEditCircle(param);
        }, 200);
      } else {
        this.clearCircle();
        console.log('setEditCircle-->', param);
        this.drawCircle = new this.AMap.Circle({
          center: new this.AMap.LngLat(param.longitude, param.latitude),
          radius: param.radius,
          fillColor: defaultValue(param.fillColor, '#00b0ff'),
          strokeColor: defaultValue(param.strokeColor, '#80d8ff'),
          borderWeight: defaultValue(param.borderWeight, 2)
        });
        this.map.add(this.drawCircle);
        this.editCircle();
      }
    },
    /**
     * 打开圆绘制面板
     */
    openEditCircle () {
      this.showCircleEditor = !this.showCircleEditor;
      this.clearCircle();
    },
    /**
     * 显示园
     */
    showCircle () {
      this.$emit('changeCircle', true);
    },
    /**
     * 隐藏园
     */
    hideCircle () {
      this.$emit('changeCircle', false);
    },
    /**
     * 开启el-popover的编辑弹窗
     */
    toggleShow () {
      this.$refs.toggleButton.click();
    }
  }
};
</script>

<style scoped>
  .planelSpan{
    margin-right: 10px;
  }
</style>
