<template>
  <div>
    <!-- <a class="planel_button planelSpan" @click="showDialog">标注点</a>
    <div class="drawMarker" v-show="showDrawMarker">
      <el-button @click="startDrawMarker" size="small">创建标注点</el-button>
      <el-button @click="removeTempMarker" size="small">清除标注点</el-button>
    </div> -->
    <el-popover
      placement="bottom"
      trigger="click"
    >
      <el-button
        size="small"
        @click="startDrawMarker"
      >
        创建标注点
      </el-button>
      <el-button
        size="small"
        @click="removeTempMarker"
      >
        清除标注点
      </el-button>
      <a
        slot="reference"
        ref="toggleButton"
        class="planel_button planelSpan"
      >标注点</a>
    </el-popover>
    <div style="display:none;">
      <CarDetailWindow
        ref="carinfo"
        :info="detailInfo"
        :map="map"
        :amap="AMap"
        @onCloseInfoWindow="onCloseInfoWindow"
      />
    </div>
  </div>
</template>
<script>
import defaultValue from '@/utils/core/defaultValue';
import CarDetailWindow from '../track/CarDetailWindow';
import configMap from '@/config/configMap';
export default {
  name: 'DrawMarker',
  components: {
    CarDetailWindow
  },
  // 数据字典
  dicts: [
    'alarmType', 'bdmDeviceType'
  ],
  props: {
  },
  data () {
    return {
      map: null,
      AMap: null,
      loaded: false,
      /** 高德地图鼠标工具 */
      mousetool: null,
      showDrawMarker: false,
      clickListener: null,
      // 控制车辆轮询时其窗口是否自动打开
      clickState: false,
      carDetailWindows: null,
      detailInfo: [],
      markerCollection: {},
      alarmColor: ['#85ce61', '#409eff', '#f7b742', '#B25A05', '#f56c6c', '#710303'],
      followTerminal: {}
    };
  },
  mounted () {
  },
  methods: {
    init (_mapObj) {
      this.map = _mapObj.map;
      this.AMap = _mapObj.AMap;
      this.loaded = _mapObj.loaded;
      this.mousetool = _mapObj.mousetool;
    },
    showDialog () {
      this.showDrawMarker = !this.showDrawMarker;
      if (!this.showDrawMarker) {
        this.removeTempMarker();
      }
    },
    startDrawMarker () {
      if (this.clickListener) {
        this.map.off('click', this.handleMapClick);
      }
      this.clickListener = this.map.on('click', this.handleMapClick);
    },
    // 地图点击事件
    handleMapClick (e) {
      let options = {
        longitude: e.lnglat.lng,
        latitude: e.lnglat.lat
      };
      this.drawTempMarker(options);
    },
    // 创建位置跟踪marker
    drawFollowMarker (data) {
      const str = data.deviceType + '-' + data.deviceId;
      let speed = (data.speed || data.speed === 0) ? Math.floor(data.speed * 10) / 10 : '-';
      this.followTerminal[str] = new this.AMap.Marker({
        map: this.map,
        position: [data.longitudeGcj, data.latitudeGcj],
        offset: new this.AMap.Pixel(-(data.iconWidth / 2), -(data.iconHeight / 2)),
        content: `<div style="position: relative;">
                      <div class="follow-marker-bg" style="position: absolute; width: 100%; height: 100%; background-image: url(${data.bgUrl}); background-size: 100%; transform: rotate(${data.bearing}deg);"></div>
                      <img src="${data.iconUrl}" style="display: block; width: ${data.iconWidth}px; height: ${data.iconHeight}px; padding: 3px; position: inherit;">
                      <div class="follow-marker-label" style="position: absolute; lineHeight: 18px; width: max-content; display: block; border: 2px solid #4096d1; color: #4096d1; background-color: #FFF; padding: 1px 2px; font-size: 12px; text-align: center; transform: translateX(-25%);">
                        ${data.targetName} ${speed}km/h
                      </div>
                    </div>`,
        // 这里extData保存的是原始wgs坐标
        extData: data
      });
      this.followTerminal[str].dom.classList.add("follow-marker");
      this.followTerminal[str].on('click', ()=>{
        this.$emit('toVehicleInfo', data.deviceIdStr || data.deviceId);
      });
      this.$emit('setZoomAndCenter', {
        longitude: data.longitudeGcj,
        latitude: data.latitudeGcj
      });
    },
    // 移除位置跟踪marker
    removeFollowMarker (id) {
      if (this.followTerminal[id]) {
        this.map.remove(this.followTerminal[id]);
        delete this.followTerminal[id];
      }
    },
    /**
     * 绘制临时的单个标签
     * @param options
     * @param {Number} options.longitude 经度
     * @param {Number} options.latitude 纬度
     * @param {String} [options.icon] 图标url或base64
     */
    drawTempMarker (options) {
      if (!options) {
        return;
      }
      if (!this.loaded) {
        setTimeout(() => {
          this.drawTempMarker(options);
        }, 200);
      } else {
        if (options && options.longitude && options.latitude) {
          if (this._tempMarker) {
            this.map.remove(this._tempMarker);
          }
          let icon = defaultValue(options.icon, 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png');
          this._tempMarker = new this.AMap.Marker({
            icon: icon,
            position: [options.longitude, options.latitude]
          });
          this.map.add([this._tempMarker]);
          this.$emit('setFitView', this._tempMarker);
        }
      }
    },
    /**
     * 删除绘制的临时标签
     */
    removeTempMarker () {
      if (this._tempMarker) {
        this.map.remove(this._tempMarker);
        this.map.off('click', this.handleMapClick);
      }
    },
    /**
     * 获取绘制的临时标签的坐标点
     * @return {{longitude, latitude: Number}|undefined}
     */
    getTempMarkerPosition () {
      if (this._tempMarker) {
        let position = this._tempMarker.getPosition();
        return {
          longitude: position.lng,
          latitude: position.lat
        };
      }
    },
    /**
     * 绘制单个标签
     * @param options
     * @param {Number} options.longitude 经度
     * @param {Number} options.latitude 纬度
     * @param {String} [options.iconUrl] 图标地址
     * @param {Number} [options.iconWidth=35] 图标宽度
     * @param {Number} [options.iconHeight=35] 图标高度
     * @param {Element|String} [options.content] 详情内容
     * @param {Function} [options.callback] 回调函数中将marker对象返回
     */
    drawMarker (options) {
      if (!options) {
        return;
      }
      if (!this.loaded) {
        setTimeout(() => {
          this.drawMarker(options);
        }, 200);
      } else {
        let iconWidth = defaultValue(options.iconWidth, 35);
        let iconHeight = defaultValue(options.iconHeight, 35);
        let size = new this.AMap.Size(iconWidth, iconHeight);
        let icon = new this.AMap.Icon({
          size: size, // 图标尺寸
          image: options.iconUrl, // Icon的图像
          imageSize: size // 根据所设置的大小拉伸或压缩图片
        });

        let marker = new this.AMap.Marker({
          position: [options.longitude, options.latitude],
          // content:opts.content,
          icon: icon,
          offset: new this.AMap.Pixel(-iconWidth / 2, -iconHeight),
          clickable: true
          // extData: ''
          // content: options.content
        });

        let clickHandle = marker.on('click', (e) => {
          // console.log(JSON.parse(JSON.stringify(e.target.getExtData())));
          let detailDom = options.content;
          // 得到的数据
          this._infoWindow = new this.AMap.InfoWindow({
            isCustom: true, // 使用自定义窗体
            content: detailDom,
            offset: new this.AMap.Pixel(0, -iconHeight)
          });
          this._infoWindow.open(this.map, [options.longitude, options.latitude]);
        });

        if (options.callback) {
          options.callback(marker);
        } else {
          this.map.add(marker);
        }
      }
    },
    /**
     * 绘制一类标签
     * @param {Array} markerClusterRenderParam
     * @param {String} markerClusterRenderParam[].labelId 标签ID，可作为删除的依据
     * @param {String} markerClusterRenderParam[].labelTypeId 标签类型ID，可作为分类的依据
     * @param {String} markerClusterRenderParam[].labelTypeName 标签名称
     * @param {Number} markerClusterRenderParam[].longitude 经度
     * @param {Number} markerClusterRenderParam[].latitude 纬度
     * @param {String} [markerClusterRenderParam[].iconUrl] 图标地址
     * @param {Number} [markerClusterRenderParam[].iconWidth=35] 图标宽度
     * @param {Number} [markerClusterRenderParam[].iconHeight=35] 图标高度
     * @param {Element|String} [markerClusterRenderParam[].detailDomElement] 详情内容
     * @param {Boolean} [markerClusterRenderParam[].showWarn=false] 是否需要显示告警
     */
    drawMarkersCluster (markerClusterRenderParam) {
      if (!this.loaded) {
        setTimeout(() => {
          this.drawMarkersCluster(markerClusterRenderParam);
        }, 200);
      } else {
        // 渲染参数
        const padding = 3;
        const triangleHeight = 8;

        let markers = [];
        let markerCollection = {};
        for (let i = 0; i < markerClusterRenderParam.length; i++) {
          let markerRenderParam = markerClusterRenderParam[i];
          let iconWidth = defaultValue(markerRenderParam.iconWidth, 35);
          let iconHeight = defaultValue(markerRenderParam.iconHeight, 35);
          let waterDropDomData = this.getWaterDropDomData(markerRenderParam);
          let marker = new this.AMap.Marker({
            position: [markerRenderParam.longitude, markerRenderParam.latitude],
            content: waterDropDomData.content,
            offset: waterDropDomData.offset,
            clickable: true,
            extData: {
              labelTypeId: markerRenderParam.labelTypeId,
              labelId: markerRenderParam.labelId,
              showWarn: markerRenderParam.showWarn,
              originalData: markerRenderParam,
              vehicleId: markerRenderParam.vehicleId
            }
          });

          let fnc = () => {
            let detailDom = markerRenderParam.detailDomElement;
            if (!detailDom) {
              return;
            }
            let removeBtn = detailDom.getElementsByClassName('xh-mapWidget-drawMarker-infoWindow-closeButton')[0];
            removeBtn && removeBtn.remove();
            this.clickState = true;
            let closeButton = document.createElement('div');
            closeButton.innerHTML = `
                <div class="xh-mapWidget-drawMarker-infoWindow-closeButton">
                    ×
                </div>`;
            detailDom.appendChild(closeButton);
            if (markerRenderParam.updateFunction) { // 针对marker执行更新函数
              markerRenderParam.updateFunction();
            }
            // 得到的数据
            this._infoWindow = new this.AMap.InfoWindow({
              isCustom: true, // 使用自定义窗体
              content: detailDom,
              offset: new this.AMap.Pixel(0, -iconHeight / 2 - padding * 2),
              closeWhenClickMap: false
            });
            closeButton.addEventListener('click', () => {
              this.clickState = false;
              this.closeInfoWindow();
            });

            this._infoWindow.open(this.map, [markerRenderParam.longitude, markerRenderParam.latitude]);
          };
          fnc();
          // 单击事件
          let clickHandle = marker.on('click', fnc);
          markers.push(marker);

          // 分类
          if (markerCollection[markerRenderParam.labelTypeId]) {
            markerCollection[markerRenderParam.labelTypeId].markers.push(marker);
          } else {
            markerCollection[markerRenderParam.labelTypeId] = {
              markers: [marker],
              iconUrl: markerRenderParam.iconUrl,
              iconWidth: markerRenderParam.iconWidth,
              iconHeight: markerRenderParam.iconHeight,
              cluster: null
            };
          }
          // 地图最佳视角
          // this.$emit('setFitView', markerCollection[markerRenderParam.labelTypeId].markers);
          this.$emit('setMarkerCenter', [markerClusterRenderParam[markerClusterRenderParam.length - 1].longitude, markerClusterRenderParam[markerClusterRenderParam.length - 1].latitude]);
        }

        for (let labelTypeId in markerCollection) {
          let cluster = new this.AMap.MarkerCluster(this.map, markerCollection[labelTypeId].markers, {
            maxZoom: 18,
            renderClusterMarker: (context) => {
              // 水滴
              let halfIconHeight = markerCollection[labelTypeId].iconHeight / 2;
              let radiusHeight = halfIconHeight + padding;
              let totalHeight = radiusHeight * 2;
              let container = document.createElement('div');
              container.style.backgroundColor = '#ffffff';
              container.style.borderRadius = radiusHeight + 'px';
              container.style.height = totalHeight + 'px';
              container.style.display = 'inline-flex';
              let imgDom = document.createElement('img');
              // 判断聚合样式
              imgDom.src = context.count > 1 ? '/bdsplatform/static/images/icons/carCollection.svg' : markerCollection[labelTypeId].iconUrl;
              imgDom.style.borderRadius = '50%';
              imgDom.style.width = markerCollection[labelTypeId].iconWidth + 'px';
              imgDom.style.height = markerCollection[labelTypeId].iconHeight + 'px';
              imgDom.style.padding = padding + 'px';
              container.appendChild(imgDom);
              // 告警数据
              let markers = context.markers; // markers的样式应当是根据聚集数据来定的 let markers = markerCollection[labelTypeId].markers;
              for (let i = 0; i < markers.length; i++) {
                let marker = markers[i];
                let extData = marker.getExtData();
                if (extData.showWarn) {
                  let warnDomHeight = parseInt(markerCollection[labelTypeId].iconHeight / 3);
                  let warnDom = document.createElement('div');
                  warnDom.style.borderRadius = '50%';
                  warnDom.style.width = warnDomHeight + 'px';
                  warnDom.style.height = warnDomHeight + 'px';
                  warnDom.style.backgroundColor = 'red';
                  warnDom.style.border = 'solid 1px #ffffff';
                  warnDom.style.position = 'absolute';
                  container.appendChild(warnDom);
                  break;
                }
              }
              // 计数
              let countDom = document.createElement('div');
              countDom.innerText = context.count;
              countDom.style.display = 'inline-block';
              countDom.style.height = totalHeight + 'px';
              countDom.style.lineHeight = totalHeight + 'px';
              countDom.style.padding = `0 ${radiusHeight / 2 + padding}px 0 ${padding}px`; // 这样将文字缩回去一点会比较好看
              container.appendChild(countDom);
              let triangleDown = document.createElement('div');
              triangleDown.style.width = '0';
              triangleDown.style.height = '0';
              triangleDown.style.borderLeft = `${triangleHeight}px solid transparent`;
              triangleDown.style.borderRight = `${triangleHeight}px solid transparent`;
              triangleDown.style.borderTop = `${triangleHeight}px solid #ffffff`;
              triangleDown.style.position = 'absolute';
              triangleDown.style.top = totalHeight + 'px';
              triangleDown.style.marginLeft = `calc(50% - ${triangleHeight}px)`;
              container.appendChild(triangleDown);
              container.style.marginLeft = '-50%';
              context.marker.setContent(container);
              context.marker.setOffset(new this.AMap.Pixel(0, -totalHeight - triangleHeight));
            }
          });
          markerCollection[labelTypeId].cluster = cluster;
          // // 监听聚合点
          // cluster.on('click', (e) => {
          //   if (this.map.getZoom() >= 18 && e.markers.length > 1) {
          //     var info = [];
          //     e.markers.forEach(val => {
          //       info.push(val.getExtData().originalData.labelTypeName);
          //     });
          //     this.detailInfo = info;
          //     this.carDetailWindows = new this.AMap.InfoWindow({
          //       isCustom: true,
          //       anchor: 'top-left',
          //       content: this.$refs.carinfo.$el
          //     });
          //     this.carDetailWindows.open(this.map, [e.markers[0].getPosition().lng, e.markers[0].getPosition().lat]);
          //   }
          // });
        }
        this.markerCollection = markerCollection;
      }
    },
    // 刷新地图marker
    renewMarkerToMarkersCluster (data, fn0, fn1, labelTypeId) {
      if (this.markerCollection[labelTypeId]) {
        for (let index = 0; index < data.length; index++) {
          const element = data[index];
          let marker = this.markerCollection[labelTypeId].markerObj[element.deviceIdStr || element.deviceId];
          if (marker && marker.originalData.time < element.time) {
            if (marker.originalData.latitude !== element.latitude || marker.originalData.longitude !== element.longitude) { // 修改位置
              marker.originalData.latitude = element.latitude;
              marker.originalData.longitude = element.longitude;
              marker.lnglat = [element.longitude, element.latitude];
            }
            let img = fn0(element);
            let bgImg = fn1(element);
            // 图标、图标方向、速度任意一个不一致时替换新的
            if (marker.originalData.iconUrl !== img || marker.originalData.bgUrl !== bgImg || marker.originalData.bearing !== element.bearing || marker.originalData.speed !== element.speed) {
              marker.originalData.iconUrl = img;
              marker.originalData.bearing = element.bearing;
              marker.originalData.speed = element.speed;
              marker.originalData.bgUrl = bgImg;
            }
          }
        }
        this.markerCollection[labelTypeId].cluster.setData(Object.values(this.markerCollection[labelTypeId].markerObj));
        // if (data?.length) {
        //   this.$emit('setMarkerCenter', [data[data.length - 1].longitude, data[data.length - 1].latitude]);
        // }
      }
    },
    // 删除所有marker
    clearMarkerToMarkersCluster(labelTypeId) {
      this.markerCollection[labelTypeId]?.cluster.setData([]);
      this.markerCollection[labelTypeId].markerObj = {};
    },
    // 滑动marker
    renewMarker(data, passedPolyline, lineArr) {
      const str = data.deviceType + '-' + data.deviceId;
      if (this.followTerminal[str]) {
        let marker = this.followTerminal[str];
        let markerData = this.followTerminal[str].getExtData();
        // markerData和data都是原始wgs坐标
        // lineArr是gcj坐标
        if (markerData.latitude !== data.latitude || markerData.longitude !== data.longitude) {
          if (lineArr.length >= 2) {
            let list = [
              [lineArr[lineArr.length - 2].longitude, lineArr[lineArr.length - 2].latitude],
              [lineArr[lineArr.length - 1].longitude, lineArr[lineArr.length - 1].latitude]
            ];
            marker.moveAlong(list, {
              duration: 3000,
              autoRotation: true
            });
          }
          markerData.latitude = data.latitude;
          markerData.longitude = data.longitude;
          marker.on('moving', (e) => {
            // 在这里使用setContent改变整个dom元素太过频繁, 感觉可能会浪费性能, 因此只修改部分dom元素样式
            const markerDom = e.target.dom;
            let bgDom = markerDom.getElementsByClassName('follow-marker-bg')[0];
            bgDom.style.transform = `rotate(${e.target['_style'].rotate}deg)`;
            // 速度不一致时替换新的
            if (markerData.speed !== data.speed) {
              const bgUrl = data.speed ? '/bdsplatform/static/images/pic/move.png' : '/bdsplatform/static/images/pic/static.png';
              bgDom.style.backgroundImage = `url(${bgUrl})`;
              markerData.speed = data.speed;
              let labelDom = markerDom.getElementsByClassName('follow-marker-label')[0];
              labelDom.innerText = `${markerData.targetName} ${Math.floor(data.speed * 10) / 10}km/h`;
            }
            // 假设lineArr有A点、B点、C点, 这里只是刚准备从B点走到C点, 此时不需要绘制到C点的轨迹线
            // 所以删除C点, 也就是数组最后一个经纬度
            let lineData = lineArr.slice(0, lineArr.length - 1);
            lineData.push({
              longitude: e.passedPath[1].lng,
              latitude: e.passedPath[1].lat
            });
            passedPolyline.setPath(this.formatPathData(lineData));
          });
        }
      }
    },
    formatPathData (_data) {
      if (!_data) {
        return [];
      }
      let coors = [];
      for (let i = 0; i < _data.length; i++) {
        const element = _data[i];
        let coor = [element.longitude, element.latitude];
        coors.push(coor);
      }
      return coors;
    },
    // 适配合适视野范围
    setAllFitView(data) {
      let list = [];
      data.forEach(item => {
        const str = item.deviceType + '-' + item.deviceId;
        if (this.followTerminal[str]) {
          list.push(this.followTerminal[str]);
        }
      });
      this.$emit('setFitView', list);
    },
    /**
     * 绘制标签
     * @param {Array} markerRenderParam
     * @param {Number} markerRenderParam[].labelId 标签ID
     * @param {Number} markerRenderParam[].labelTypeId 标签类型ID，可作为分类的依据
     * @param {String} markerRenderParam[].labelTypeName 标签名称
     * @param {Number} markerRenderParam[].longitude 经度
     * @param {Number} markerRenderParam[].latitude 纬度
     * @param {String} [markerRenderParam[].iconUrl] 图标地址
     * @param {Number} [markerRenderParam[].iconWidth=35] 图标宽度
     * @param {Number} [markerRenderParam[].iconHeight=35] 图标高度
     * @param {Element|String} [markerRenderParam[].detailDomElement] 详情内容
     */
    addMarkerToMarkersCluster (markerRenderParam, setFitView) {
      console.time();
      // 渲染参数
      const padding = 3;
      const triangleHeight = 8; // let triangleHeight = halfIconHeight / 2;
      let markerObj = {};
      for (let index = 0; index < markerRenderParam.length; index++) {
        const element = markerRenderParam[index];
        markerObj[element.vehicleId] = {
          lnglat: [element.longitude, element.latitude],
          labelTypeId: element.labelTypeId,
          vehicleId: element.vehicleId,
          originalData: element
        };
      }
      let markerCollection = this.markerCollection;
      let labelTypeId = markerRenderParam[0].labelTypeId;
      if (markerCollection[labelTypeId]) {
        markerCollection[labelTypeId].cluster.addData(Object.values(markerObj));
        markerCollection[labelTypeId].markerObj = {...markerCollection[labelTypeId].markerObj, ...markerObj};
      } else {
        markerCollection[labelTypeId] = {
          iconWidth: markerRenderParam[0].iconWidth,
          iconHeight: markerRenderParam[0].iconHeight,
          markerObj: markerObj,
          cluster: null
        };
        let cluster = new this.AMap.MarkerCluster(this.map, Object.values(markerCollection[labelTypeId].markerObj), {
          gridSize: 80, // 聚合网格像素大小
          maxZoom: 20, // 大于20不聚合
          renderMarker: this.renderMarker, // 自定义marker
          // renderClusterMarker: (context) => {
          //   let clusterData = context.clusterData; // markers的样式应当是根据聚集数据来定的
          //   // 水滴
          //   let halfIconHeight = markerCollection[labelTypeId].iconHeight / 2;
          //   let radiusHeight = halfIconHeight + padding;
          //   let totalHeight = radiusHeight * 2;
          //   let container = document.createElement('div');
          //   container.style.backgroundColor = '#ffffff';
          //   container.style.borderRadius = radiusHeight + 'px';
          //   container.style.height = totalHeight + 'px';
          //   container.style.display = 'inline-flex';
          //   let imgDom = document.createElement('img');
          //   // 判断聚合样式
          //   if (context.count > 1) {
          //     imgDom.src = '/bdsplatform/static/images/icons/carCollection.svg';
          //   } else {
          //     let markerData = clusterData[0];
          //     imgDom.src = markerData.originalData.iconUrl;
          //   }
          //   imgDom.style.borderRadius = '50%';
          //   imgDom.style.width = markerCollection[labelTypeId].iconWidth + 'px';
          //   imgDom.style.height = markerCollection[labelTypeId].iconHeight + 'px';
          //   imgDom.style.padding = padding + 'px';
          //   container.appendChild(imgDom);
          //   // 告警数据
          //   // for (let i = 0; i < markers.length; i++) {
          //   //   let marker = markers[i];
          //   //   let extData = marker.getExtData();
          //   //   if (extData.showWarn) {
          //   //     let warnDomHeight = parseInt(markerCollection[labelTypeId].iconHeight / 3);
          //   //     let warnDom = document.createElement('div');
          //   //     warnDom.style.borderRadius = '50%';
          //   //     warnDom.style.width = warnDomHeight + 'px';
          //   //     warnDom.style.height = warnDomHeight + 'px';
          //   //     warnDom.style.backgroundColor = 'red';
          //   //     warnDom.style.border = 'solid 1px #ffffff';
          //   //     warnDom.style.position = 'absolute';
          //   //     container.appendChild(warnDom);
          //   //     break;
          //   //   }
          //   // }
          //   // 计数
          //   let countDom = document.createElement('div');
          //   countDom.innerText = context.count;
          //   countDom.style.display = 'inline-block';
          //   countDom.style.height = totalHeight + 'px';
          //   countDom.style.lineHeight = totalHeight + 'px';
          //   countDom.style.padding = `0 ${radiusHeight / 2 + padding}px 0 ${padding}px`; // 这样将文字缩回去一点会比较好看
          //   container.appendChild(countDom);
          //   let triangleDown = document.createElement('div');
          //   triangleDown.style.width = '0';
          //   triangleDown.style.height = '0';
          //   triangleDown.style.borderLeft = `${triangleHeight}px solid transparent`;
          //   triangleDown.style.borderRight = `${triangleHeight}px solid transparent`;
          //   triangleDown.style.borderTop = `${triangleHeight}px solid #ffffff`;
          //   triangleDown.style.position = 'absolute';
          //   triangleDown.style.top = totalHeight + 'px';
          //   triangleDown.style.marginLeft = `calc(50% - ${triangleHeight}px)`;
          //   container.appendChild(triangleDown);
          //   container.style.marginLeft = '-50%';
          //   context.marker.setContent(container);
          //   context.marker.setOffset(new this.AMap.Pixel(0, -totalHeight - triangleHeight));
          // }
        });
        markerCollection[labelTypeId].cluster = cluster;
        // 监听聚合点
        cluster.on('click', (e) => {
          if (this.map.getZoom() >= 18 && e.clusterData.length > 1) {
            var info = [];
            e.clusterData.forEach(val => {
              info.push({
                id: val.vehicleId,
                licencePlate: val.originalData.labelTypeName
              });
            });
            this.detailInfo = info;
            this.carDetailWindows = new this.AMap.InfoWindow({
              isCustom: true,
              anchor: 'top-left',
              content: this.$refs.carinfo.$el
            });
            this.carDetailWindows.open(this.map, [e.clusterData[0].lnglat.lng, e.clusterData[0].lnglat.lat]);
          }
        });
      }
      // 地图最佳视角
      if (setFitView) {
        if (markerRenderParam.length === 1) {
          const data = {
            longitude: markerRenderParam[markerRenderParam.length - 1].longitude,
            latitude: markerRenderParam[markerRenderParam.length - 1].latitude
          };
          this.$emit('setZoomAndCenter', data);
        } else if (markerRenderParam.length > 1) {
          this.$emit('setFitView');
        }
      }
      console.timeEnd();
    },
    /**
     * 绘制marker
     * @param {Object} context marker对象
     */
    renderMarker (context) {
      const { marker, data } = context;
      const originalData = data[0].originalData;
      let speed = (originalData.speed || originalData.speed === 0) ? Math.floor(originalData.speed * 10) / 10 : '-';
      let content = `<div style="position: relative;">
                      <div style="position: absolute; width: 100%; height: 100%; background-image: url(${originalData.bgUrl}); background-size: 100%; transform: rotate(${originalData.bearing}deg);"></div>
                      <img src="${originalData.iconUrl}" style="display: block; width: ${originalData.iconWidth}px; height: ${originalData.iconHeight}px; padding: 3px; position: inherit;">
                      <div style="position: absolute; lineHeight: 18px; width: max-content; display: block; border: 2px solid #4096d1; color: #4096d1; background-color: #FFF; padding: 1px 2px; font-size: 12px; text-align: center; transform: translateX(-25%);" class="briefInfo">
                        ${originalData.labelTypeName} ${speed}km/h
                      </div>
                    </div>`;
      marker.setOffset(new this.AMap.Pixel(-(originalData.iconWidth / 2), -(originalData.iconHeight / 2)));
      marker.setContent(content);
      marker.on('click', ()=>{
        this.$emit('toVehicleInfo', originalData.vehicleId);
      });
    },
    /**
     * 绘制标签
     * @param {Array} markerRenderParam
     * @param {Number} markerRenderParam[].labelId 标签ID
     * @param {Number} markerRenderParam[].labelTypeId 标签类型ID，可作为分类的依据
     * @param {String} markerRenderParam[].labelTypeName 标签名称
     * @param {Number} markerRenderParam[].longitude 经度
     * @param {Number} markerRenderParam[].latitude 纬度
     * @param {String} [markerRenderParam[].iconUrl] 图标地址
     * @param {Number} [markerRenderParam[].iconWidth=35] 图标宽度
     * @param {Number} [markerRenderParam[].iconHeight=35] 图标高度
     * @param {Element|String} [markerRenderParam[].detailDomElement] 详情内容
     */
    addMarkerToMarkersClusterWithoutContent (markerRenderParam, setFitView) {
      console.time();
      // 渲染参数
      const padding = 3;
      const triangleHeight = 8; // let triangleHeight = halfIconHeight / 2;
      let markerObj = {};
      for (let index = 0; index < markerRenderParam.length; index++) {
        const element = markerRenderParam[index];
        // let waterDropDomData = this.getWaterDropDomData(element);
        let speed = (element.speed || element.speed === 0) ? Math.floor(element.speed * 10) / 10 : '-';
        let marker = new this.AMap.Marker({
          position: [element.longitude, element.latitude],
          content: `<div style="position: relative;">
                      <img src="${element.iconUrl}" style="display: block; width: ${element.iconWidth}px; height: ${element.iconHeight}px; padding: 3px; transform: rotate(${element.bearing}deg);">
                    </div>`,
          // icon: icon,
          offset: new this.AMap.Pixel(-60, -(element.iconHeight / 2)),
          clickable: true,
          // angle: element.bearing,
          extData: {
            labelTypeId: element.labelTypeId,
            labelId: element.labelId,
            vehicleId: element.vehicleId,
            originalData: element
          },
          // autoRotation: true
        });
        marker.on('click', ()=>{
          this.$emit('toVehicleInfo', element.vehicleId);
        });
        markerObj[element.vehicleId] = marker;
      }
      let markerCollection = this.markerCollection;
      let labelTypeId = markerRenderParam[0].labelTypeId;
      if (markerCollection[labelTypeId]) {
        markerCollection[labelTypeId].cluster.addMarkers(Object.values(markerObj));
        markerCollection[labelTypeId].markerObj = {...markerCollection[labelTypeId].markerObj, ...markerObj};
      } else {
        markerCollection[labelTypeId] = {
          iconWidth: markerRenderParam[0].iconWidth,
          iconHeight: markerRenderParam[0].iconHeight,
          markerObj: markerObj,
          cluster: null
        };
        let cluster = new this.AMap.MarkerCluster(this.map, Object.values(markerCollection[labelTypeId].markerObj), {
          gridSize: 80, // 聚合网格像素大小
          maxZoom: 20, // 大于20不聚合
          renderClusterMarker: (context) => {
            let markers = context.markers; // markers的样式应当是根据聚集数据来定的
            // 水滴
            let halfIconHeight = markerCollection[labelTypeId].iconHeight / 2;
            let radiusHeight = halfIconHeight + padding;
            let totalHeight = radiusHeight * 2;
            let container = document.createElement('div');
            container.style.backgroundColor = '#ffffff';
            container.style.borderRadius = radiusHeight + 'px';
            container.style.height = totalHeight + 'px';
            container.style.display = 'inline-flex';
            let imgDom = document.createElement('img');
            // 判断聚合样式
            if (context.count > 1) {
              imgDom.src = '/bdsplatform/static/images/icons/carCollection.svg';
            } else {
              let markerData = markers[0].getExtData();
              imgDom.src = markerData.originalData.iconUrl;
            }
            imgDom.style.borderRadius = '50%';
            imgDom.style.width = markerCollection[labelTypeId].iconWidth + 'px';
            imgDom.style.height = markerCollection[labelTypeId].iconHeight + 'px';
            imgDom.style.padding = padding + 'px';
            container.appendChild(imgDom);
            // 告警数据
            for (let i = 0; i < markers.length; i++) {
              let marker = markers[i];
              let extData = marker.getExtData();
              if (extData.showWarn) {
                let warnDomHeight = parseInt(markerCollection[labelTypeId].iconHeight / 3);
                let warnDom = document.createElement('div');
                warnDom.style.borderRadius = '50%';
                warnDom.style.width = warnDomHeight + 'px';
                warnDom.style.height = warnDomHeight + 'px';
                warnDom.style.backgroundColor = 'red';
                warnDom.style.border = 'solid 1px #ffffff';
                warnDom.style.position = 'absolute';
                container.appendChild(warnDom);
                break;
              }
            }
            // 计数
            let countDom = document.createElement('div');
            countDom.innerText = context.count;
            countDom.style.display = 'inline-block';
            countDom.style.height = totalHeight + 'px';
            countDom.style.lineHeight = totalHeight + 'px';
            countDom.style.padding = `0 ${radiusHeight / 2 + padding}px 0 ${padding}px`; // 这样将文字缩回去一点会比较好看
            container.appendChild(countDom);
            let triangleDown = document.createElement('div');
            triangleDown.style.width = '0';
            triangleDown.style.height = '0';
            triangleDown.style.borderLeft = `${triangleHeight}px solid transparent`;
            triangleDown.style.borderRight = `${triangleHeight}px solid transparent`;
            triangleDown.style.borderTop = `${triangleHeight}px solid #ffffff`;
            triangleDown.style.position = 'absolute';
            triangleDown.style.top = totalHeight + 'px';
            triangleDown.style.marginLeft = `calc(50% - ${triangleHeight}px)`;
            container.appendChild(triangleDown);
            container.style.marginLeft = '-50%';
            context.marker.setContent(container);
            context.marker.setOffset(new this.AMap.Pixel(0, -totalHeight - triangleHeight));
          }
        });
        markerCollection[labelTypeId].cluster = cluster;
        // 监听聚合点
        cluster.on('click', (e) => {
          if (this.map.getZoom() >= 18 && e.markers.length > 1) {
            var info = [];
            e.markers.forEach(val => {
              info.push({
                id: val.getExtData().vehicleId,
                licencePlate: val.getExtData().originalData.labelTypeName
              });
            });
            this.detailInfo = info;
            this.carDetailWindows = new this.AMap.InfoWindow({
              isCustom: true,
              anchor: 'top-left',
              content: this.$refs.carinfo.$el
            });
            this.carDetailWindows.open(this.map, [e.markers[0].getPosition().lng, e.markers[0].getPosition().lat]);
          }
        });
      }
      // 地图最佳视角
      if (setFitView) {
        this.$emit('setFitView', Object.values(this.markerCollection[labelTypeId].markerObj));
        this.$emit('setMarkerCenter', [markerRenderParam[markerRenderParam.length - 1].longitude, markerRenderParam[markerRenderParam.length - 1].latitude]);
      }
      console.timeEnd();
    },
    /**
     * 从聚点中删除标签
     * @param {Object} label
     * @param {Number} label.labelId 标签ID
     * @param {Number} label.labelTypeId 标签类型ID
     */
    removeMarkerFromCluster (label, setFitView) {
      if (this.markerCollection[label.labelTypeId]) {
        for (let index = 0; index < label.vehicleId.length; index++) {
          const element = label.vehicleId[index];
          delete this.markerCollection[label.labelTypeId].markerObj[element];
        }
        this.markerCollection[label.labelTypeId].cluster.setData(Object.values(this.markerCollection[label.labelTypeId].markerObj));
      }
      // 地图最佳视角
      if (setFitView) {
        if (this.markerCollection[label.labelTypeId]) {
          let m = this.markerCollection[label.labelTypeId].markerObj;
          let list = Object.values(m);
          if (list && list.length > 0) {
            this.$emit('setMarkerCenter', [list[list.length - 1].originalData.longitude, list[list.length - 1].originalData.latitude]);
          }
        }
      }
    },
    /**
     * 水滴图
     * @param {String} markerRenderParam.labelTypeName 标签类型名称
     * @param {Number} markerRenderParam.longitude 经度
     * @param {Number} markerRenderParam.latitude 纬度
     * @param {String} [markerRenderParam.iconUrl] 图标地址
     * @param {Number} [markerRenderParam.iconWidth=35] 图标宽度
     * @param {Number} [markerRenderParam.iconHeight=35] 图标高度
     * @param {Element|String} [markerRenderParam.content] 详情内容
     * @param {Boolean} [markerRenderParam.showWarn=false] 是否显示告警
     * @return {Object}
     */
    getWaterDropDomData (markerRenderParam) {
      // 水滴
      let padding = 3;
      let halfIconHeight = markerRenderParam.iconHeight / 2;
      let radiusHeight = halfIconHeight + padding;
      let totalHeight = radiusHeight * 2;
      let container = document.createElement('div');
      container.style.width = `120px`;
      let imgDom = document.createElement('img');
      imgDom.src = markerRenderParam.iconUrl;
      // imgDom.style.borderRadius = '50%';
      imgDom.style.display = 'block';
      imgDom.style.width = markerRenderParam.iconWidth + 'px';
      imgDom.style.height = markerRenderParam.iconHeight + 'px';
      imgDom.style.padding = padding + 'px';
      imgDom.style.margin = `0 auto`;
      imgDom.style.transform = `rotate(${markerRenderParam.bearing}deg)`;
      container.appendChild(imgDom);
      // 告警红点
      if (markerRenderParam.showWarn) {
        let warnDomHeight = parseInt(markerRenderParam.iconHeight / 3);
        let warnDom = document.createElement('div');
        warnDom.style.borderRadius = '50%';
        warnDom.style.width = warnDomHeight + 'px';
        warnDom.style.height = warnDomHeight + 'px';
        warnDom.style.backgroundColor = 'red';
        warnDom.style.border = 'solid 1px #ffffff';
        warnDom.style.position = 'absolute';
        container.appendChild(warnDom);
      }
      // 文字
      let briefInfoDom = document.createElement('div');
      let speed = (markerRenderParam.speed || markerRenderParam.speed === 0) ? Math.floor(markerRenderParam.speed * 10) / 10 : '-';
      briefInfoDom.innerText = `${markerRenderParam.labelTypeName} ${speed}km/h`;
      briefInfoDom.style.lineHeight = 18 + 'px';
      briefInfoDom.style.width = 'max-content';
      briefInfoDom.style.display = 'block';
      briefInfoDom.style.border = '2px solid #4096d1';
      briefInfoDom.style.color = '#4096d1';
      briefInfoDom.style.backgroundColor = '#FFF';
      briefInfoDom.style.padding = `1px 2px`;
      briefInfoDom.style.fontSize = `12px`;
      briefInfoDom.className += 'briefInfo';
      briefInfoDom.style.textAlign = `center`;
      briefInfoDom.style.margin = `0 auto`;
      container.appendChild(briefInfoDom);

      return {
        content: container,
        offset: new this.AMap.Pixel(-60, -(markerRenderParam.iconHeight / 2))
      };
    },
    /**
     * 绘制告警图标
     * @param {String} markerRenderParam.labelTypeName 标签类型名称
     * @param {Number} markerRenderParam.longitude 经度
     * @param {Number} markerRenderParam.latitude 纬度
     * @param {String} markerRenderParam.alarmLevel 告警等级
     * @param {String} markerRenderParam.alarmType 告警类型
     * @param {String} [markerRenderParam.iconUrl] 图标地址
     * @param {Number} [markerRenderParam.iconWidth=35] 图标宽度
     * @param {Number} [markerRenderParam.iconHeight=35] 图标高度
     */
    createAlarmMaker (markerRenderParam) {
      let alarmDropDomData = this.getAlarmDropDomData(markerRenderParam);
      let marker = new this.AMap.Marker({
        position: [markerRenderParam.longitude, markerRenderParam.latitude],
        content: alarmDropDomData.content,
        offset: alarmDropDomData.offset
      });
      this.map.add(marker);
      // 5秒后自动清除该marker
      setTimeout(()=>{
        this.map.remove(marker);
        marker = null;
      }, 5000);
    },
    /**
     * 告警内容
     * @param {String} markerRenderParam.labelTypeName 标签类型名称
     * @param {Number} markerRenderParam.longitude 经度
     * @param {Number} markerRenderParam.latitude 纬度
     * @param {String} markerRenderParam.alarmLevel 告警等级
     * @param {String} markerRenderParam.alarmType 告警类型
     * @param {String} [markerRenderParam.iconUrl] 图标地址
     * @param {Number} [markerRenderParam.iconWidth=35] 图标宽度
     * @param {Number} [markerRenderParam.iconHeight=35] 图标高度
     */
    getAlarmDropDomData (markerRenderParam) {
      let padding = 3;
      let container = document.createElement('div');
      container.style.width = `120px`;
      // 图标
      let imgDom = document.createElement('img');
      imgDom.src = markerRenderParam.iconUrl;
      // imgDom.style.borderRadius = '50%';
      imgDom.style.display = 'block';
      imgDom.style.width = markerRenderParam.iconWidth + 'px';
      imgDom.style.height = markerRenderParam.iconHeight + 'px';
      imgDom.style.padding = padding + 'px';
      imgDom.style.margin = `0 auto`;
      container.appendChild(imgDom);
      // 告警
      let alarmInfoDom = document.createElement('div');
      let labelDom = document.createElement('p');
      let addressDom = document.createElement('p');
      labelDom.innerText = `${this.getEnumDictLabel('bdmDeviceType', markerRenderParam.deviceType)} ${this.getEnumDictLabel('alarmType', markerRenderParam.alarmType)} ${markerRenderParam.alarmTime}`;
      addressDom.innerText = `${markerRenderParam.startAddress}`;
      alarmInfoDom.style.width = 'max-content';
      alarmInfoDom.style.display = 'block';
      const color = this.alarmColor[markerRenderParam.alarmLevel];
      alarmInfoDom.style.border = `2px solid ${color}`;
      alarmInfoDom.style.color = 'white';
      alarmInfoDom.style.backgroundColor = color;
      alarmInfoDom.style.padding = `3px 5px`;
      alarmInfoDom.style.fontSize = `12px`;
      alarmInfoDom.className += 'briefInfo';
      alarmInfoDom.style.margin = `0 auto`;
      alarmInfoDom.style.marginTop = `-10px`;
      alarmInfoDom.style.borderRadius = `10px`;
      alarmInfoDom.appendChild(labelDom);
      alarmInfoDom.appendChild(addressDom);
      container.appendChild(alarmInfoDom);
      return {
        content: container,
        offset: new this.AMap.Pixel(-60, -(markerRenderParam.iconHeight / 2))
      };
    },
    /**
     * 开启el-popover的编辑弹窗
     */
    toggleShow () {
      this.$refs.toggleButton.click();
    },
    /**
     * 关闭消息窗口
     */
    closeInfoWindow () {
      if (this._infoWindow) {
        this._infoWindow.close();
      }
    },
    getMarkerCollection () {
      return this.markerCollection;
    },
    /**
     * 删除所有的标记点
     */
    clearAll () {
      if (this.markerCollection[1]) {
        this.markerCollection[1].cluster.clearMarkers();
        this.markerCollection[1].markerObj = {};
        this.closeInfoWindow();
      }
    },
    onCloseInfoWindow () {
      this.carDetailWindows.close();
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
  }
};
</script>
<style lang="less" scoped>
  .spanWidth{
    margin-left: 5px;
  }
  .planelSpan{
    margin-right: 10px;
  }
</style>

<style lang="less">
  @import "../../../assets/less/variables.less";
  .xh-mapWidget-drawMarker-infoWindow-closeButton{
    position: absolute;
    right: @xhSpacingMiddle;
    top: @xhSpacingMiddle;
    z-index: 100;
    cursor: pointer;
    color: @xhTextColor3;
    border-radius: 50%;
    padding: 2px;
    text-align: center;
    font-size: @xhFontSizeTitle2;
  }
  .xh-mapWidget-drawMarker-infoWindow-closeButton:hover{
    color: @xhUIColorMain;
  }
  .follow-marker {
    transform: translate(-25px, -25px) scale(1) !important;
  }
</style>
