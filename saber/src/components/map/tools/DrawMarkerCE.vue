<template>
  <div>
    <!-- <a class="planel_button planelSpan" @click="showDialog">标注点</a>
    <div class="drawMarker" v-show="showDrawMarker">
      <el-button @click="startDrawMarker" size="small">创建标注点</el-button>
      <el-button @click="removeTempMarker" size="small">清除标注点</el-button>
    </div> -->
    <el-popover
      placement="bottom"
      trigger="click"
    >
      <el-button
        size="small"
        @click="startDrawMarker"
      >
        创建标注点
      </el-button>
      <el-button
        size="small"
        @click="removeTempMarker"
      >
        清除标注点
      </el-button>
      <a
        slot="reference"
        ref="toggleButton"
        class="planel_button planelSpan"
      >标注点</a>
    </el-popover>
    <div style="display:none;">
      <CarDetailWindow
        ref="carinfo"
        :info="detailInfo"
        :map="map"
        :amap="AMap"
        @onCloseInfoWindow="onCloseInfoWindow"
      />
    </div>
  </div>
</template>
<script>
import defaultValue from '@/utils/core/defaultValue';
import CarDetailWindow from '../track/CarDetailWindow';

export default {
  name: 'DrawMarker',
  components: {
    CarDetailWindow
  },
  // 数据字典
  dicts: [
    'alarmType',
    'bdmDeviceType'
  ],
  props: {},
  data() {
    return {
      map: null,
      AMap: null,
      loaded: false,
      /** 高德地图鼠标工具 */
      mousetool: null,
      showDrawMarker: false,
      clickListener: null,
      // 控制车辆轮询时其窗口是否自动打开
      clickState: false,
      carDetailWindows: null,
      detailInfo: [],
      markerCollection: {},
      alarmColor: [
        '#85ce61',
        '#409eff',
        '#f7b742',
        '#b25a05',
        '#f56c6c',
        '#710303'
      ],
      followTerminal: {},
      trackLayer: null
    };
  },
  mounted() {
  },
  methods: {
    init(_mapObj) {
      this.map = _mapObj.map;
      this.AMap = _mapObj.AMap;
      this.loaded = _mapObj.loaded;
      this.mousetool = _mapObj.mousetool;
      this.mapInstance = _mapObj.mapInstance;
    },
    showDialog() {
      this.showDrawMarker = !this.showDrawMarker;
      if (!this.showDrawMarker) {
        this.removeTempMarker();
      }
    },
    startDrawMarker() {
      if (this.clickListener) {
        this.map.off('click', this.handleMapClick);
      }
      this.clickListener = this.map.on('click', this.handleMapClick);
    },
    // 地图点击事件
    handleMapClick(e) {
      let options = {
        longitude: e.lnglat.lng,
        latitude: e.lnglat.lat
      };
      this.drawTempMarker(options);
    },
    // 创建位置跟踪marker
    drawFollowMarker(data) {
      const str = data.deviceType + '-' + data.deviceId;
      if (!this.trackLayer) {
        this.trackLayer = MayMap.TileLayer.LabelsLayer({
          zIndex: 99993
        });
        this.map.addLayer(this.trackLayer);
      }
      this.followTerminal[str] = new MayMap.Composite.actualTimeTrack({
        layer: this.trackLayer, //图层
        data: [data.longitude, data.latitude], //数据
        imgUrl: require(`@/assets/images/track/vehicleFollow.png`), //自定义图标地址 默认汽车
        imgScale: 0.18, //自定义图标大小 默认1倍
        // imgAnchor: [0.51, 0.4], //自定义图标位置 默认[0.45,0.55]
        // imgRotation: 180, //自定义图标旋转角 默认0
        // viewport: true, //是否自动定位到当前点 默认false
      });
      this.map.setZoomAndCenter([data.longitude, data.latitude], 18);
    },
    // 移除位置跟踪marker
    removeFollowMarker(id) {
      if (this.followTerminal[id]) {
        this.followTerminal[id].destroy();
        delete this.followTerminal[id];
      }
    },
    /**
     * 绘制临时的单个标签
     * @param options
     * @param {Number} options.longitude 经度
     * @param {Number} options.latitude 纬度
     * @param {String} [options.icon] 图标url或base64
     */
    drawTempMarker(options) {
      if (!options) {
        return;
      }
      if (!this.loaded) {
        setTimeout(() => {
          this.drawTempMarker(options);
        }, 200);
      }
      else {
        if (options && options.longitude && options.latitude) {
          if (this._tempMarker) {
            this.map.remove(this._tempMarker);
          }
          let icon = defaultValue(options.icon, 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png');
          this._tempMarker = new this.AMap.Marker({
            icon: icon,
            position: [
              options.longitude,
              options.latitude
            ]
          });
          this.map.add([this._tempMarker]);
          this.$emit('setFitView', this._tempMarker);
        }
      }
    },
    /**
     * 删除绘制的临时标签
     */
    removeTempMarker() {
      if (this._tempMarker) {
        this.map.remove(this._tempMarker);
        this.map.off('click', this.handleMapClick);
      }
    },
    /**
     * 获取绘制的临时标签的坐标点
     * @return {{longitude, latitude: Number}|undefined}
     */
    getTempMarkerPosition() {
      if (this._tempMarker) {
        let position = this._tempMarker.getPosition();
        return {
          longitude: position.lng,
          latitude: position.lat
        };
      }
    },
    // 刷新地图marker
    renewMarkerToMarkersCluster(data, fn0, fn1, labelTypeId) {
      if (this.markerCollection[labelTypeId]) {
        for (let index = 0; index < data.length; index++) {
          const element = data[index];
          let marker = this.markerCollection[labelTypeId].markerObj[element.deviceIdStr || element.deviceId];
          if (marker && marker.originalData.time < element.time) {
            if (marker.originalData.latitude !== element.latitude || marker.originalData.longitude !== element.longitude) { // 修改位置
              marker.originalData.latitude = element.latitude;
              marker.originalData.longitude = element.longitude;
              marker.lnglat = [
                element.longitude,
                element.latitude
              ];
            }
            let img = fn0(element);
            let bgImg = fn1(element);
            // 图标、图标方向、速度任意一个不一致时替换新的
            if (marker.originalData.iconUrl !== img || marker.originalData.bgUrl !== bgImg || marker.originalData.bearing !== element.bearing || marker.originalData.speed !== element.speed) {
              marker.originalData.iconUrl = img;
              marker.originalData.bearing = element.bearing;
              marker.originalData.speed = element.speed;
              marker.originalData.bgUrl = bgImg;
            }
          }
        }
        this.renderMarker({
          data: Object.values(this.markerCollection[labelTypeId].markerObj)
        });
      }
    },
    // 删除所有marker
    clearMarkerToMarkersCluster(labelTypeId) {
      for (const markerItem of this.markerCollection[labelTypeId].cluster) {
        this.map.olMap.removeOverlay(markerItem);
      }
      this.markerCollection[labelTypeId].markerObj = {};
      this.markerCollection[labelTypeId].cluster = [];
    },
    // 滑动marker
    renewMarker(data, passedPolyline, lineArr) {
      const str = data.deviceType + '-' + data.deviceId;
      if (this.followTerminal[str]) {
        let marker = this.followTerminal[str];
        marker.setPointLocation({
          coordinate: [data.longitude, data.latitude],
          interval: 3
        });
      }
    },
    formatPathData(_data) {
      if (!_data) {
        return [];
      }
      let coors = [];
      for (let i = 0; i < _data.length; i++) {
        const element = _data[i];
        let coor = [
          element.longitude,
          element.latitude
        ];
        coors.push(coor);
      }
      return coors;
    },
    // 适配合适视野范围
    setAllFitView(data) {
      let list = [];
      data.forEach(item => {
        const str = item.deviceType + '-' + item.deviceId;
        if (this.followTerminal[str]) {
          list.push(this.followTerminal[str]);
        }
      });
      this.$emit('setFitView', list);
    },
    /**
     * 绘制标签
     * @param {Array} markerRenderParam
     * @param {Number} markerRenderParam[].labelId 标签ID
     * @param {Number} markerRenderParam[].labelTypeId 标签类型ID，可作为分类的依据
     * @param {String} markerRenderParam[].labelTypeName 标签名称
     * @param {Number} markerRenderParam[].longitude 经度
     * @param {Number} markerRenderParam[].latitude 纬度
     * @param {String} [markerRenderParam[].iconUrl] 图标地址
     * @param {Number} [markerRenderParam[].iconWidth=35] 图标宽度
     * @param {Number} [markerRenderParam[].iconHeight=35] 图标高度
     * @param {Element|String} [markerRenderParam[].detailDomElement] 详情内容
     */
    addMarkerToMarkersCluster(markerRenderParam) {
      let markerObj = {};
      for (let index = 0; index < markerRenderParam.length; index++) {
        const element = markerRenderParam[index];
        markerObj[element.vehicleId] = {
          lnglat: [
            element.longitude,
            element.latitude
          ],
          labelTypeId: element.labelTypeId,
          vehicleId: element.vehicleId,
          originalData: element
        };
      }
      let markerCollection = this.markerCollection;
      let labelTypeId = markerRenderParam[0].labelTypeId;
      if (markerCollection[labelTypeId]) {
        markerCollection[labelTypeId].markerObj = { ...markerCollection[labelTypeId].markerObj, ...markerObj };
        this.renderMarker({
          data: Object.values(markerCollection[labelTypeId].markerObj)
        });
      }
      else {
        markerCollection[labelTypeId] = {
          iconWidth: markerRenderParam[0].iconWidth,
          iconHeight: markerRenderParam[0].iconHeight,
          markerObj: markerObj,
          cluster: []
        };
        this.renderMarker({
          data: Object.values(markerCollection[labelTypeId].markerObj)
        });
      }
    },
    /**
     * 绘制marker
     * @param {Object} context marker对象
     */
    renderMarker(context) {
      const {
        data
      } = context;
      let overlays = [];
      data.forEach(item => {
        if (item.overlay) {
          this.map.olMap.removeOverlay(item.overlay);
        }
        const originalData = item.originalData;
        // let speed = (originalData.speed || originalData.speed === 0) ? Math.floor(originalData.speed * 10) / 10 : '-';
        let content = `<div style="position: relative;cursor: pointer;user-select: none">
                      <div style="position: absolute; width: 100%; height: 100%; background-image: url(${originalData.bgUrl}); background-size: 100%; transform: rotate(${originalData.bearing}deg);"></div>
                      <img src="${originalData.iconUrl}" style="display: block; width: ${originalData.iconWidth}px; height: ${originalData.iconHeight}px; padding: 3px; position: inherit;">
                      <div style="position: absolute;left:50%; lineHeight: 18px; width: max-content; display: block; border: 2px solid #4096d1; color: #4096d1; background-color: #ffffff; padding: 1px 2px; font-size: 12px; text-align: center; transform: translateX(-50%);" class="briefInfo">
                        ${originalData.labelTypeName.split('-')[0]}
                      </div>
                    </div>`;
        const element = document.createElement('div');
        element.innerHTML = content;
        const newMarker = new MayMap.ol.Overlay({
          element: element,
          stopEvent: false,
          position: MayMap.ol.proj.transform([
            originalData.longitude,
            originalData.latitude
          ], 'EPSG:4326', 'EPSG:3857'),
          positioning: 'center-center',
        });
        this.map.olMap.addOverlay(newMarker);
        item.overlay = newMarker;
        this.markerCollection[1].cluster.push(newMarker);
        overlays.push(newMarker);
        // 获取覆盖层的DOM元素
        const markerElement = newMarker.getElement();
        // 为DOM元素添加点击事件监听器
        markerElement.addEventListener('click', (event) => {
          // 阻止事件冒泡到地图上，避免触发地图的其他交互事件
          this.$emit('toVehicleInfo', originalData.vehicleId);
          event.stopPropagation();
        });
      });
      if (data.length === 1) {
        // 单个终端
        this.map.setZoomAndCenter([data[0].originalData.longitude, data[0].originalData.latitude], 18);
      } else if (data.length > 1) {
        // 多个终端的情况下 计算最佳显示
        const positions = overlays.map(overlay => overlay.getPosition());
        const extent = MayMap.ol.extent.boundingExtent(positions.map(position => [position[0], position[1]]));
        // 将视图调整到这个范围
        this.map.olMap.getView().fit(extent, {
          duration: 1000, // 可选：动画持续时间
          padding: [100, 100, 100, 100], // 可选：内边距
        });
      }
    },
    /**
     * 从聚点中删除标签 国能暂无聚合功能
     * @param {Object} label
     * @param setFitView
     * @param {Number} label.labelId 标签ID
     * @param {Number} label.labelTypeId 标签类型ID
     * @param {Number} label.vehicleId 终端ID
     */
    removeMarkerFromCluster(label, setFitView) {
      if (this.markerCollection[label.labelTypeId]) {
        for (let index = 0; index < label.vehicleId.length; index++) {
          const element = label.vehicleId[index];
          let marker = this.markerCollection[label.labelTypeId].markerObj[element];
          if (marker?.overlay) {
            this.map.olMap.removeOverlay(marker.overlay);
            delete marker.overlay;
          }
          delete this.markerCollection[label.labelTypeId].markerObj[element];
        }
      }
      // TODO 地图最佳视角 效果不好 暂时屏蔽
      // if (setFitView) {
      //   if (this.markerCollection[label.labelTypeId]) {
      //     let m = this.markerCollection[label.labelTypeId].markerObj;
      //     let list = Object.values(m);
      //     if (list && list.length > 0) {
      //       console.log(444);
      //       this.$emit('setMarkerCenter', [
      //         list[list.length - 1].originalData.longitude,
      //         list[list.length - 1].originalData.latitude
      //       ]);
      //     }
      //   }
      // }
    },
    /**
     * 水滴图
     * @param {String} markerRenderParam.labelTypeName 标签类型名称
     * @param {Number} markerRenderParam.longitude 经度
     * @param {Number} markerRenderParam.latitude 纬度
     * @param {String} [markerRenderParam.iconUrl] 图标地址
     * @param {Number} [markerRenderParam.iconWidth=35] 图标宽度
     * @param {Number} [markerRenderParam.iconHeight=35] 图标高度
     * @param {Element|String} [markerRenderParam.content] 详情内容
     * @param {Boolean} [markerRenderParam.showWarn=false] 是否显示告警
     * @return {Object}
     */
    getWaterDropDomData(markerRenderParam) {
      // 水滴
      let padding = 3;
      let halfIconHeight = markerRenderParam.iconHeight / 2;
      let radiusHeight = halfIconHeight + padding;
      let totalHeight = radiusHeight * 2;
      let container = document.createElement('div');
      container.style.width = `120px`;
      let imgDom = document.createElement('img');
      imgDom.src = markerRenderParam.iconUrl;
      // imgDom.style.borderRadius = '50%';
      imgDom.style.display = 'block';
      imgDom.style.width = markerRenderParam.iconWidth + 'px';
      imgDom.style.height = markerRenderParam.iconHeight + 'px';
      imgDom.style.padding = padding + 'px';
      imgDom.style.margin = `0 auto`;
      imgDom.style.transform = `rotate(${markerRenderParam.bearing}deg)`;
      container.appendChild(imgDom);
      // 告警红点
      if (markerRenderParam.showWarn) {
        let warnDomHeight = parseInt(markerRenderParam.iconHeight / 3);
        let warnDom = document.createElement('div');
        warnDom.style.borderRadius = '50%';
        warnDom.style.width = warnDomHeight + 'px';
        warnDom.style.height = warnDomHeight + 'px';
        warnDom.style.backgroundColor = 'red';
        warnDom.style.border = 'solid 1px #ffffff';
        warnDom.style.position = 'absolute';
        container.appendChild(warnDom);
      }
      // 文字
      let briefInfoDom = document.createElement('div');
      let speed = (markerRenderParam.speed || markerRenderParam.speed === 0) ? Math.floor(markerRenderParam.speed * 10) / 10 : '-';
      briefInfoDom.innerText = `${markerRenderParam.labelTypeName} ${speed}km/h`;
      briefInfoDom.style.lineHeight = 18 + 'px';
      briefInfoDom.style.width = 'max-content';
      briefInfoDom.style.display = 'block';
      briefInfoDom.style.border = '2px solid #4096d1';
      briefInfoDom.style.color = '#4096d1';
      briefInfoDom.style.backgroundColor = '#ffffff';
      briefInfoDom.style.padding = `1px 2px`;
      briefInfoDom.style.fontSize = `12px`;
      briefInfoDom.className += 'briefInfo';
      briefInfoDom.style.textAlign = `center`;
      briefInfoDom.style.margin = `0 auto`;
      container.appendChild(briefInfoDom);

      return {
        content: container,
        offset: [
          -60,
          -(markerRenderParam.iconHeight / 2)
        ]
      };
    },
    /**
     * 绘制告警图标
     * @param {String} markerRenderParam.labelTypeName 标签类型名称
     * @param {Number} markerRenderParam.longitude 经度
     * @param {Number} markerRenderParam.latitude 纬度
     * @param {String} markerRenderParam.alarmLevel 告警等级
     * @param {String} markerRenderParam.alarmType 告警类型
     * @param {String} [markerRenderParam.iconUrl] 图标地址
     * @param {Number} [markerRenderParam.iconWidth=35] 图标宽度
     * @param {Number} [markerRenderParam.iconHeight=35] 图标高度
     */
    createAlarmMaker(markerRenderParam) {
      let alarmDropDomData = this.getAlarmDropDomData(markerRenderParam);
      let marker = new this.AMap.ol.Overlay({
        element: alarmDropDomData.content,
        stopEvent: false,
        position: MayMap.ol.proj.transform([
          markerRenderParam.longitude,
          markerRenderParam.latitude
        ], 'EPSG:4326', 'EPSG:3857'),
        positioning: 'center-center',
        autoPan: true
      });
      this.map.olMap.addOverlay(marker);
      // 5秒后自动清除该marker
      setTimeout(() => {
        this.map.olMap.removeOverlay(marker);
        marker = null;
      }, 5000);
    },
    /**
     * 告警内容
     * @param {String} markerRenderParam.labelTypeName 标签类型名称
     * @param {Number} markerRenderParam.longitude 经度
     * @param {Number} markerRenderParam.latitude 纬度
     * @param {String} markerRenderParam.alarmLevel 告警等级
     * @param {String} markerRenderParam.alarmType 告警类型
     * @param {String} [markerRenderParam.iconUrl] 图标地址
     * @param {Number} [markerRenderParam.iconWidth=35] 图标宽度
     * @param {Number} [markerRenderParam.iconHeight=35] 图标高度
     */
    getAlarmDropDomData(markerRenderParam) {
      let padding = 3;
      let container = document.createElement('div');
      container.style.width = `120px`;
      // 图标
      let imgDom = document.createElement('img');
      imgDom.src = markerRenderParam.iconUrl;
      // imgDom.style.borderRadius = '50%';
      imgDom.style.display = 'block';
      imgDom.style.width = markerRenderParam.iconWidth + 'px';
      imgDom.style.height = markerRenderParam.iconHeight + 'px';
      imgDom.style.padding = padding + 'px';
      imgDom.style.margin = `0 auto`;
      container.appendChild(imgDom);
      // 告警
      let alarmInfoDom = document.createElement('div');
      let labelDom = document.createElement('p');
      let addressDom = document.createElement('p');
      labelDom.innerText = `${this.getEnumDictLabel('bdmDeviceType', markerRenderParam.deviceType)} ${this.getEnumDictLabel('alarmType', markerRenderParam.alarmType)} ${markerRenderParam.alarmTime}`;
      addressDom.innerText = `${markerRenderParam.startAddress}`;
      alarmInfoDom.style.width = 'max-content';
      alarmInfoDom.style.display = 'block';
      const color = this.alarmColor[markerRenderParam.alarmLevel];
      alarmInfoDom.style.border = `2px solid ${color}`;
      alarmInfoDom.style.color = 'white';
      alarmInfoDom.style.backgroundColor = color;
      alarmInfoDom.style.padding = `3px 5px`;
      alarmInfoDom.style.fontSize = `12px`;
      alarmInfoDom.className += 'briefInfo';
      alarmInfoDom.style.margin = `0 auto`;
      alarmInfoDom.style.marginTop = `-10px`;
      alarmInfoDom.style.borderRadius = `10px`;
      alarmInfoDom.appendChild(labelDom);
      alarmInfoDom.appendChild(addressDom);
      container.appendChild(alarmInfoDom);
      return {
        content: container,
        // offset: new this.AMap.Pixel(-60, -(markerRenderParam.iconHeight / 2))
      };
    },
    /**
     * 开启el-popover的编辑弹窗
     */
    toggleShow() {
      this.$refs.toggleButton.click();
    },
    /**
     * 关闭消息窗口
     */
    closeInfoWindow() {
      if (this._infoWindow) {
        this._infoWindow.close();
      }
    },
    getMarkerCollection() {
      return this.markerCollection;
    },
    /**
     * 删除所有的标记点
     */
    clearAll() {
      if (this.markerCollection[1]) {
        for (const markerItem of this.markerCollection[1].cluster) {
          this.map.olMap.removeOverlay(markerItem);
        }
        this.markerCollection[1].cluster = [];
        this.markerCollection[1].markerObj = {};
        this.closeInfoWindow();
      }
    },
    onCloseInfoWindow() {
      this.carDetailWindows.close();
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    }
  }
};
</script>
<style lang="less" scoped>
.spanWidth {
  margin-left: 5px;
}

.planelSpan {
  margin-right: 10px;
}
</style>

<style lang="less">
@import "../../../assets/less/variables.less";

.xh-mapWidget-drawMarker-infoWindow-closeButton {
  position: absolute;
  right: @xhSpacingMiddle;
  top: @xhSpacingMiddle;
  z-index: 100;
  cursor: pointer;
  color: @xhTextColor3;
  border-radius: 50%;
  padding: 2px;
  text-align: center;
  font-size: @xhFontSizeTitle2;
}

.xh-mapWidget-drawMarker-infoWindow-closeButton:hover {
  color: @xhUIColorMain;
}

.follow-marker {
  transform: translate(-25px, -25px) scale(1) !important;
}
</style>
