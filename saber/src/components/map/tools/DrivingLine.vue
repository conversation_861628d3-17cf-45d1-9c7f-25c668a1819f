<template>
  <div>
    <el-popover placement="bottom" trigger="click" @show="showDrivingLine" @hide="hideDrivingLine">
      <div style="display: flex; flex-direction: row;">
        <div>
          <div style="display: flex; flex-direction: column;">
            <el-input v-model="startLine" placeholder="请输入起点地名"  size="small" style="width: 250px" clearable></el-input>
            <el-input v-model="endLine" placeholder="请输入终点地名"  size="small" style="width: 250px; margin-top: 5px" clearable></el-input>
          </div>
        </div>
        <div>
          <el-button icon="el-icon-position" size="small" class="spanWidth" @click="drivingLine"></el-button>
        </div>
      </div>
      <a class="planel_button planelSpan" slot="reference">路径规划</a>
    </el-popover>
  </div>
</template>
<script>
export default {
  name: 'DrivingLine',
  props: {
  },
  data () {
    return{
      map: null,
      AMap: null,
      driver: null,
      startLine: '',
      endLine: '',
      loaded: false,
    }
  },
  mounted () {
  },
  methods: {
    init(_mapObj){
      this.map = _mapObj.map
      this.AMap = _mapObj.AMap
      this.loaded = _mapObj.loaded
    },
    drivingLine(){
      if(this.driving){
        this.driving.clear()
      }
      this.driving = new this.AMap.Driving({
        map: this.map,
      });
      this.driving.search([{
        keyword: this.startLine,
        city: ''
      }, {
        keyword: this.endLine,
        city: ''
      }], (status, result) => {
        if(status === 'complete'){
          console.log('路径规划成功')
        } else {
          console.log(result)
        }
      })
    },
    clearDirving(){
      if(this.driving){
        this.driving.clear()
      }
    },
    showDrivingLine(){
      this.$emit('changeDrivingLine', true)
    },
    hideDrivingLine(){
      this.$emit('changeDrivingLine', false)
    },
  },
}
</script>
<style lang="less" scoped>
  .spanWidth{
    margin-left: 5px;
    height: 70px;
  }
  .planelSpan{
    margin-right: 10px;
  }
  .el-popover ::v-deep {
    border: 1px solid #0f89f5;
  }
</style>
