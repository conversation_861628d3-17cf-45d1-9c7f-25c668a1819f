<template>
  <div>
    <a class="planel_button planelSpan" @click="showSelect">卫星</a>
    <!-- 设置地图中心 -->
    <!-- <div class="layerSelect" v-show="showLayerSelect">
      <el-radio-group v-model="layers">
        <el-radio :label="1" @click.native.prevent="selectRoadNet(1)">路网</el-radio>
      </el-radio-group>
    </div> -->
  </div>
</template>
<script>
export default {
  name: 'LayerSelect',
  props: {
  },
  data () {
    return{
      map: null,
      AMap: null,
      loaded: false,
      satelliteLayer: null,
      showLayerSelect: false,
      layers: 1
    }
  },
  methods: {
    init(_mapObj){
      this.map = _mapObj.map
      this.AMap = _mapObj.AMap
      this.loaded = _mapObj.loaded
      this.satelliteLayer = _mapObj.satelliteLayer
    },
    showSelect(){
      this.showLayerSelect = !this.showLayerSelect
      if(this.showLayerSelect) {
        this.showSatelliteLayer()
      } else {
        this.hideSatelliteLayer()
      }
      this.$emit('changeLayerSelect', this.showLayerSelect)
    },
    /**
     * 显示卫星图图层
     */
    showSatelliteLayer(){
      if(!this.loaded){
        setTimeout(()=>{
          this.showSatelliteLayer();
        }, 200)
      }else{
        if(this.satelliteLayer){
          this.satelliteLayer.setMap(this.map);
          this.satelliteLayer.show();
        }
      }
    },
    /**
     * 隐藏卫星图图层
     */
    hideSatelliteLayer(){
      if(!this.loaded){
        setTimeout(()=>{
          this.hideSatelliteLayer();
        }, 200)
      }else{
        if(this.satelliteLayer){
          this.satelliteLayer.setMap(null);
          this.satelliteLayer.hide();
        }
      }
    },
    /**
     * 路网图层交互逻辑还不完善
     * 如果有需求后期待完善
     */
    selectRoadNet(e){
      e === this.layers ? this.layers = '' : this.layers = e
      // this.$emit('selectRoadLayer', this.layers)
      if(showLayer === 1) {
        this.hideRoadNet()
      } else {
        this.showRoadNet()
      }
    },
    /**
     * 显示路网
     */
    showRoadNet(){
      if(!this.loaded){
        setTimeout(()=>{
          this.showRoadNet();
        }, 200)
      }else{
        // if(this.roadNet){
        //   this.roadNet.show();
        // }else{
        //   this.roadNet = new this.AMap.TileLayer.RoadNet();
        //   this.roadNet.setMap(this.map);
        // }
      }
    },
    /**
     * 隐藏路网
     */
    hideRoadNet(){
      if(!this.loaded){
        setTimeout(()=>{
          this.hideRoadNet();
        }, 200)
      }else{
        // if(this.roadNet){
        //   this.roadNet.hide();
        // }
      }
    }
  },
}
</script>
<style lang="less" scoped>
  .planelSpan{
    margin-right: 10px;
  }
</style>