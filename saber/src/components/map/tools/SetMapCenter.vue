<template>
  <div>
    <el-popover placement="bottom" trigger="click" @show="showMapCenter" @hide="hideMapCenter">
      中心点:
      <el-input v-model="mapCenter" placeholder="请输入中心点经纬度(格式：经度,纬度)"  size="small" style="width: 250px" clearable></el-input>
      <el-button icon="el-icon-search" size="small" class="spanWidth" @click="setCenter"></el-button>
      <a class="planel_button planelSpan" slot="reference">设置中心</a>
    </el-popover>
  </div>
</template>
<script>
export default {
  name: 'SetMapCenter',
  props: {
  },
  data () {
    return{
      map: null,
      AMap: null,
      loaded: false,
      mapCenter: ''
    }
  },
  mounted () {
  },
  methods: {
    init(_mapObj){
      this.map = _mapObj.map
      this.AMap = _mapObj.AMap
      this.loaded = _mapObj.loaded
    },
    setCenter(){
      // this.mapCenter ? this.$emit('setNewCenter', this.mapCenter) : ''
      let option = {
        longitude: parseFloat(this.mapCenter.split(",")[0]),
        latitude: parseFloat(this.mapCenter.split(",")[1]),
      }
      this.$emit("setZoomAndCenter", option)
    },
    showMapCenter(){
      this.$emit('changeMapCenter', true)
    },
    hideMapCenter(){
      this.$emit('changeMapCenter', false)
    },
  },
}
</script>
<style lang="less" scoped>
  .spanWidth{
    margin-left: 5px;
  }
</style>