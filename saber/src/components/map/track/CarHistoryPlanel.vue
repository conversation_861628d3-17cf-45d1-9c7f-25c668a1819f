<template>
  <div class="CarHistoryPlanel">
    <div class="planel_a flex_box">
      <div class="planel_text">
        <div>车牌号码：<span>{{ carInfo.name }}</span></div>
        <!-- <div>车属公司：<span></span></div> -->
        <div>开始时间：<span>{{ carInfo.timeStart }}</span></div>
        <div>截止时间：<span>{{ carInfo.timeEnd }}</span></div>
        <div>定位时间：<span>{{ carInfo.timeCurrent }}</span></div>
        <div>定位时速：<span>{{ carInfo.speed }} 公里/时</span></div>
        <div>车辆状态：<span>{{ carInfo.status }}</span></div>
        <div>播放速度：<span>{{ TimerSpeedString }}</span></div>
        <div>
          轨迹完整率：<span>{{ integrityRatio }}</span>
          <el-checkbox
            v-model="integrityChecked"
            style="margin-left: 5px;"
          />
        </div>
        <div>
          漂移次数：<span>{{ driftCounts }}</span>
          <el-checkbox
            v-model="driftChecked"
            style="margin-left: 5px;"
          />
        </div>
        <!-- <div>提示信息：<span>{{Player.index}}</span></div> -->
        <div>
          <button
            class="planel_button"
            @click="onExportData"
          >
            导出轨迹数据
          </button>
        </div>
      </div>
      <div class="planel_ab">
        <div>
          <el-tooltip
            class="item"
            content="降低播放速度"
            placement="top"
          >
            <a
              class="icon_player_fast_back"
              @click="onPlayerFastBack"
            />
          </el-tooltip>
        </div>
        <div>
          <el-tooltip
            v-if="IsPlaying"
            class="item"
            content="暂停播放"
            placement="top"
          >
            <a
              class="icon_player_pause"
              @click="onPlayerPause"
            />
          </el-tooltip>
          <el-tooltip
            v-else
            class="item"
            content="播放"
            placement="top"
          >
            <a
              class="icon_player_play"
              @click="onPlayerPlay"
            />
          </el-tooltip>
        </div>
        <div>
          <el-tooltip
            class="item"
            content="增加播放速度"
            placement="top"
          >
            <a
              class="icon_player_fast_front"
              @click="onPlayerFastFront"
            />
          </el-tooltip>
        </div>
        <div>
          <el-tooltip
            class="item"
            content="停止播放"
            placement="top"
          >
            <a
              class="icon_player_stop"
              @click="onPlayerStop"
            />
          </el-tooltip>
        </div>
      </div>
    </div>
    <div class="planel_b flex_box">
      <div class="planel_chart">
        <div
          id="chart"
          ref="chart_speed"
        />
      </div>
      <div class="planel_ab">
        <x-range
          class="history_range"
          :value="HistoryRange"
          @update:value="onRangeMove"
        />
      </div>
    </div>
    <div class="planel_c">
      <div
        class="planelCancel"
        @click="onCarHistoryPlanelCancel"
      >
        X
      </div>
    </div>
  </div>
</template>

<script>
import xRange from '@/components/xarms/range';
import * as echarts from 'echarts/core';
import {exportTracking, trackRatio} from '@/api/monitoring/track.js';
import {getCarStatus} from '@/utils/getCarStatus';
import {
  TooltipComponent,
  LegendComponent,
  TitleComponent,
  GridComponent
} from 'echarts/components';
import {
  LineChart
} from 'echarts/charts';
import {
  CanvasRenderer
} from 'echarts/renderers';
echarts.use(
  [TitleComponent, TooltipComponent, GridComponent, LegendComponent, LineChart, CanvasRenderer]
);
export default {
  name: 'CarHistoryPlanel',
  components: {
    'x-range': xRange
  },
  props: {
    // Player: Object
  },
  data () {
    return {
      HistoryRange: 0,
      chart: null,
      IsPlaying: false,
      carInfo: {
        name: '',
        timeStart: '',
        timeEnd: '',
        timeCurrent: '',
        status: '',
        else: '暂无'
      },
      // 速度数组 ， 用于显示速度图表
      SpeedsArray: [],
      // 轨迹播放定时器
      Timer: null,
      // 定时器时间间隔 ， 1000 = 1秒
      TimerSpeed: 1000,
      TimerSpeedString: '正常',
      CarMarker: null,
      PathMarker: null,
      DisContPathPass: null,
      Map: null,
      TrackData: null,
      Player: {
        index: 0,
        coors: []
      },
      // 完整率
      integrityRatio: 1,
      // 漂移率
      driftCounts: 0,
      // 是否绘制轨迹不连续数据
      integrityChecked: false,
      // 是否绘制漂移数据
      driftChecked: false,
      // 不连续轨迹点
      trackPath: [],
      // 漂移轨迹点
      driftPath: [],
      // 不连续轨迹层
      trackPathGroups: null,
      // 漂移轨迹点层
      driftPathGroups: null
    };
  },
  watch: {
    integrityChecked (val) {
      console.log(this.trackPath);
      if (val) {
        // 绘制轨迹不连续数据
        this.setDisCont(this.trackPath);
        this.$parent.showWatchCard = true;
      } else {
        this.$parent.showWatchCard = false;
        if (this.trackPathGroups) {
          this.Map.remove(this.trackPathGroups);
        }
      }
    },
    driftChecked (val) {
      console.log(this.driftPath);
      if (val) {
        // 绘制轨迹漂移数据
        this.setDrift(this.driftPath);
        this.$parent.showWatchCard = true;
      } else {
        this.$parent.showWatchCard = false;
        if (this.driftPathGroups) {
          this.Map.remove(this.driftPathGroups);
        }
      }
    }
  },
  mounted () {
    let chartelement = this.$refs.chart_speed;
    this.chart = echarts.init(chartelement);
    let opts = {
      xAxis: {
        type: 'time'
      },
      yAxis: {
        type: 'value',
        // interval:75,
        minInterval: 60,
        maxInterval: 100,
        name: '时速'
      },
      grid: {
        top: 40,
        bottom: 40,
        left: 30,
        right: 20
      },
      textStyle: {
        color: '#303133'
      },
      series: [{
        data: [],
        type: 'line',
        // type: 'bar',
        // barMaxWidth: 4,
        lineStyle: {
          color: '#118de3'
        },
        itemStyle: {
          color: '#118de3'
        },
        // 图表上显示点
        showSymbol: false
      }]
    };
    this.chart.setOption(opts);
    this.onPlayerStop();
  },
  methods: {
    formatChartData (_data) {
      let data = _data || [];
      let chartData = [];
      for (let i = 0; i < data.length; i++) {
        const element = data[i];
        let item = {
          value: [element.locTime * 1000, element.speed]
        };
        if (i > 0 && data[i].locTime - data[i - 1].locTime > 1800) {
          item.value[1] = 0;
        }
        if (i < data.length - 1 && data[i + 1].locTime - data[i].locTime > 1800) {
          item.value[1] = 0;
        }
        chartData.push(item);
      }
      return chartData;
    },
    formatPathData (_data) {
      if (!_data) {
        return [];
      }
      let coors = [];
      for (let i = 0; i < _data.length; i++) {
        const element = _data[i];
        let coor = [element.longitude, element.latitude];
        coors.push(coor);
      }
      return coors;
    },
    setChartData (_data) {
      let data = this.formatChartData(_data);
      this.SpeedsArray = data;
      this.chart.setOption({
        series: [{
          data: data
        }]
      });
      setTimeout(() => {
        this.chart.resize();
      }, 100);
    },
    setInfoData (_data) {
      let data = _data || {};
      let date = new Date();
      date.setTime(data.locTime * 1000);
      let timeCurrent = this.$utils.formatDate(date, 'yyyy-MM-dd hh:mm:ss');
      this.carInfo.name = data.licencePlate;
      this.carInfo.status = data.running === 0 ? '停驶' : '行驶';
      this.carInfo.total = data.total;
      this.carInfo.timeCurrent = timeCurrent;
      this.carInfo.speed = data.speed;
      this.carInfo.playerSpeed = data.playerSpeed;
      //  只有第一次才有起始时间与终止时间数据
      if (data.timeStart) {
        date.setTime(data.timeStart * 1000);
        let timeStart = this.$utils.formatDate(date, 'yyyy-MM-dd hh:mm:ss');
        date.setTime(data.timeEnd * 1000);
        let timeEnd = this.$utils.formatDate(date, 'yyyy-MM-dd hh:mm:ss');
        this.carInfo.timeStart = timeStart;
        this.carInfo.timeEnd = timeEnd;

        // 记录下开始结束时间的时间戳，用于数据导出
        this.carInfo.timeStartCode = data.timeStart;
        this.carInfo.timeEndCode = data.timeEnd;
      }
      this.updateRange(data.locTime * 1000);
      if (data.index === data.total) {
        this.setPlayerStatus(false);
      };
    },
    resetInfo () {
      this.SpeedsArray = [];
      /** 速度初始化 */
      this.TimerSpeed = 1000;
      this.switchTimerSpeedString();
    },
    setPlayerStatus (_bool) {
      this.IsPlaying = _bool || false;
    },
    /**
     * 开始时间，结束时间，当前时间
     */
    updateRange (_timeCurrent) {
      if (!this.SpeedsArray.length) {
        return;
      }
      let timeStart = this.SpeedsArray[0].value[0];
      let timeEnd = this.SpeedsArray[this.SpeedsArray.length - 1].value[0];
      let timeDistance = timeEnd - timeStart;
      let indexTime = _timeCurrent - timeStart;
      let range = indexTime / timeDistance;
      // range *= 100;
      // console.info('--> updateRange',range );
      this.HistoryRange = range;
    },
    /**
     * 设置车辆图标
     * 设置地图
     */
    setTrackElement (_opts) {
      let opts = _opts || {};
      this.Map = opts.map;
      this.AMap = opts.amap;
      this.CarMarker = opts.car;
      this.PathMarker = opts.path;
      this.TrackData = opts.track;
      this.Player.coors = this.formatPathData(opts.track);
      this.DisContPathPass = opts.disCont;
      console.log(this.TrackData);
    },
    /**
     * 设置轨迹不连续的路段
     */
    setDisCont (points) {
      let markerCollection = [];
      // this.DisContPathPass.setPath(path);
      for (let i = 0; i < points.length; i++) {
        let midNum = parseInt(points[i].length / 2);
        let linePath = new this.AMap.Polyline({
          path: points[i],
          strokeColor: '#ff2d51',
          strokeOpacity: 1,
          strokeWeight: 5,
          showDir: true,
          zIndex: 50,
          bubble: true
        });
        let lineText = new this.AMap.Text({
          position: points[i][midNum],
          title: points[i][midNum],
          // text: points[i][midNum],
          zIndex: 50
        });
        linePath.on('mouseover', (e) => {
          console.log(e.lnglat);
        });
        markerCollection.push([linePath, lineText]);
      }
      this.trackPathGroups = new this.AMap.OverlayGroup(markerCollection);
      this.Map.add(this.trackPathGroups);
    },
    /**
     * 设置轨迹漂移的路段
     */
    setDrift (points) {
      let markerCollection = [];
      for (let i = 0; i < points.length; i++) {
        let midNum = parseInt(points[i].length / 2);
        let linePath = new this.AMap.Polyline({
          path: points[i],
          strokeColor: '#ffd700',
          strokeOpacity: 1,
          strokeWeight: 5,
          showDir: true,
          zIndex: 50,
          bubble: true
        });
        let lineText = new this.AMap.Text({
          position: points[i][midNum],
          title: points[i][midNum],
          // text: points[i][midNum],
          zIndex: 50
        });
        markerCollection.push([linePath, lineText]);
      }
      this.driftPathGroups = new this.AMap.OverlayGroup(markerCollection);
      this.Map.add(this.driftPathGroups);
    },
    /**
     * 设置已经过的路线
     */
    setPassPath (_coor, _index) {
      let path = Array.from(this.Player.coors);
      path.length = _index;
      path.push(_coor);
      this.PathMarker.setPath(path);
    },
    /**
     *  小车移动
     */
    carMoving (_index) {
      if (!this.TrackData) {
        return;
      }
      let _this = this;
      // 找到当前位置
      let trackIndex = this.TrackData[_index];
      let lnglat = [trackIndex.longitude, trackIndex.latitude];
      let historyInfo = this.TrackData[_index];
      let status = getCarStatus();
      let angle = historyInfo.bearing;
      // 设置小车位置
      /* this.Map.setTrackCarMarker(this.CarMarker,{
        status:status[5 + historyInfo.running],
        position:lnglat,
        speed:0,
        angle:angle
      }); */
      this.$emit('setTrackCarMarker', {
        status: status[5 + historyInfo.running],
        position: lnglat,
        speed: 0,
        angle: angle,
        car: _this.CarMarker
      });
      // 更新轨迹
      this.setPassPath(lnglat, _index);
      /* if (this.TrackData[_index].locTime >= 1637300793 && this.TrackData[_index].locTime <= 1637301363) {
        // 设置轨迹不连续的路段
        this.setDisCont(lnglat, _index);
      } */
      // 设置地图位置
      if (!this.isInBound(lnglat)) {
        this.Map.panTo(lnglat);
      }
      // 更新信息
      historyInfo.index = _index;
      historyInfo.total = this.TrackData.length - 1;
      historyInfo.status = status[historyInfo.locState].text;
      // historyInfo.playerSpeed = this.Player.speed;
      this.setInfoData(historyInfo);
    },
    /**
     * 判断是否在地图范围内
     * 在 返回 true
     */
    isInBound (_lnglat) {
      const bounds = this.Map.getBounds();
      const NorthEast = bounds.getNorthEast();
      const SouthWest = bounds.getSouthWest();
      const SouthEast = [NorthEast.lng, SouthWest.lat];
      const NorthWest = [SouthWest.lng, NorthEast.lat];
      const path = [[NorthEast.lng, NorthEast.lat], SouthEast, [SouthWest.lng, SouthWest.lat], NorthWest];
      const isPointInRing = this.AMap.GeometryUtil.isPointInRing(_lnglat, path);
      return isPointInRing;
    },
    timerRunning () {
      // 索引越界判断
      if (this.Player.index >= this.TrackData.length) {
        // this.onPlayerStop();
        this.onPlayerPause();
        // this.timeStop();
        return;
      }
      this.Timer = setTimeout(() => {
        this.carMoving(this.Player.index++);
        this.timerRunning();
      }, this.TimerSpeed);
    },
    timeStop () {
      clearTimeout(this.Timer);
    },
    switchTimerSpeedString () {
      switch (this.TimerSpeed) {
        case 100:
          this.TimerSpeedString = '特快';
          break;
        case 400:
          this.TimerSpeedString = '较快';
          break;
        case 700:
          this.TimerSpeedString = '快';
          break;
        case 1000:
          this.TimerSpeedString = '正常';
          break;
        case 1300:
          this.TimerSpeedString = '慢';
          break;
        case 1600:
          this.TimerSpeedString = '较慢';
          break;
        case 1900:
          this.TimerSpeedString = '特慢';
          break;
        default:
          break;
      }
    },
    onCarHistoryPlanelCancel () {
      this.clearOverlays();
      this.$emit('onCarHistoryPlanelCancel');
    },
    onCarHistoryPlanelShow () {
      this.$emit('onCarHistoryPlanelShow');
    },
    // 播放暂停
    onPlayerPause () {
      // this.chart.resize();
      this.setPlayerStatus(false);
      // this.$emit('onPlayerPause');
      this.timeStop();
    },
    // 播放减速
    onPlayerFastBack () {
      // 最低速度限制
      if (this.TimerSpeed === 1900) {
        return;
      }
      this.TimerSpeed += 300;
      this.switchTimerSpeedString();
      this.timeStop();
      this.onPlayerPlay();
      // this.timerRunning();
    },
    // 播放开始
    onPlayerPlay () {
      this.setPlayerStatus(true);
      // this.$emit('onPlayerPlay');
      this.timerRunning();
    },
    // 播放加速
    onPlayerFastFront () {
      // 最高速度限制
      if (this.TimerSpeed === 100) {
        return;
      }
      this.TimerSpeed -= 300;
      this.switchTimerSpeedString();
      this.timeStop();
      this.onPlayerPlay();
      // this.timerRunning();
    },
    // 播放停止
    onPlayerStop () {
      this.setPlayerStatus(false);
      // this.$emit('onPlayerStop');
      // 停止计时器
      this.timeStop();
      // 重置数据
      this.carMoving(0);
      this.Player.index = 0;
      this.HistoryRange = 0;
      this.TimerSpeed = 1000;
      this.TimerSpeedString = '正常';
    },
    trackRangeMove (e) {
      // 暂停播放
      this.onPlayerPause();
      // 进度改变
      this.Player.index = e;
      // 车辆直接定位
      this.carMoving(this.Player.index);
      // 轨迹绘制
      this.setPassPath(this.Player.coors[this.Player.index], this.Player.index);
      // 开始播放
      // this.onPlayerPlay();
    },
    onRangeMove (_rangeVal) {
      // console.info('--> onRangeMove',_rangeVal);
      if (!this.SpeedsArray.length) {
        return;
      }
      let range = _rangeVal;
      // 得到位置百分数 算出位置的时间
      let timeStart = this.SpeedsArray[0].value[0];
      let timeEnd = this.SpeedsArray[this.SpeedsArray.length - 1].value[0];
      let timeDistance = timeEnd - timeStart;
      let indexTime = range * timeDistance + timeStart;

      let date = new Date();
      date.setTime(indexTime);
      let indexTimeString = this.$utils.formatDate(date, 'yyyy-MM-dd hh:mm:ss');
      // 根据时间遍历出点
      for (let i = 0; i < this.SpeedsArray.length; i++) {
        const element = this.SpeedsArray[i];
        if (element.value[0] > indexTime) {
          // this.$emit('onRangeMove',i-1);
          let index = i - 1 > 0 ? i - 1 : 0;
          this.trackRangeMove(index);
          return i;
        }
      }
    },
    /** 导出用车信息 */
    onExportData () {
      const data = this.carInfo;
      // 请求路线信息
      let parme = {
        licence_plate: data.name,
        start_time: data.timeStartCode,
        end_time: data.timeEndCode
      };
      this.$message({
        message: '数据正在导出,请稍后。。',
        type: 'success'
      });
      exportTracking(parme).then(res => {
        if (res.data) {
          this.$download(res.data, 'path');
        } else {
          this.$msgbox.alert('导出失败，请重新查询轨迹', '提示', {
            confirmButtonText: '确定',
            type: 'warning'
          });
        }
      }).catch(res => {
        loading.close();
        console.log(res);
      });
    },
    /**
     * 获取完整率数据
     */
    getIntegrityData (val) {
      let parme = {
        st: val.start_time,
        et: val.end_time,
        licence: val.licence_plate
      };
      trackRatio(parme).then(res => {
        let trackInfo = {};
        this.integrityRatio = res.data.ratio;
        trackInfo = {
          totalMile: res.data.totalMile,
          contMile: res.data.contMile,
          disContCnt: res.data.disContCnt,
          ratio: res.data.ratio,
          driftCounts: res.data.drift ? res.data.drift.length : 0
        };
        this.$parent.trackInfo = trackInfo;
        let disCont = res.data.disCont || [];
        let drifts = res.data.drift || [];
        // 保存
        this.trackPath = [];
        this.driftPath = [];
        // 不完整轨迹点
        let trackParagraph = [];
        for (let i = 0; i < disCont.length; i++) {
          let points = disCont[i].pos;
          points.forEach(item => {
            let coor = new this.AMap.LngLat(item.lng, item.lat);
            trackParagraph.push(coor);
          });
        }
        if (trackParagraph && trackParagraph.length > 0) {
          this.trackPath.push(trackParagraph);
        }
        // 漂移轨迹点
        this.driftCounts = drifts.length;
        let driftParagraph = [];
        for (let i = 0; i < drifts.length; i++) {
          let points = drifts[i].pos;
          points.forEach(item => {
            let coor = new this.AMap.LngLat(item.lng, item.lat);
            driftParagraph.push(coor);
          });
        }
        if (driftParagraph && driftParagraph.length > 0) {
          this.driftPath.push(driftParagraph);
        }
        this.$forceUpdate();
        // this.setDisCont(trackPath);
      }).catch(res => {
        console.log(res);
      });
      setTimeout(() => {
        this.onPlayerStop();
      }, 200);
    },
    /**
     * 清除轨迹点
     */
    clearOverlays () {
      this.integrityChecked = false;
      this.driftChecked = false;
      this.$parent.showWatchCard = false;
      this.trackPath = [];
      this.driftPath = [];
      this.integrityRatio = 1;
      this.driftCounts = 0;
      if (this.trackPathGroups) {
        this.Map.remove(this.trackPathGroups);
      }
      if (this.driftPathGroups) {
        this.Map.remove(this.driftPathGroups);
      }
    }
  }
};
</script>

<style lang="less" scoped>

.CarHistoryPlanel{
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  color: #303133;
  font-size: 12px;
  min-height: 230px;
}
.flex_box {
  display: flex;
  flex-direction: column;
}
.planel_a{
  width: 240px;
  .planel_ab{
    div{
      margin-right: 10px;
    }
  }
}
.planel_text{
  margin-left: 30px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  div{
    margin-bottom: 2px;
  }
  span{
    color:#fda23c;
  }
}
.planel_b{
  flex: 1;
}
.planel_chart{
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
.planel_ab{
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.planel_c{
  width: 30px;
  display: flex;
  justify-content: center;
}
.history_range{
  width: 100%;
  margin-left: 30px;
  margin-right: 20px;
}

#chart{
  width: 100%;
  height: 100%;
}
@player_icon_size : 16px;
.icon_player_pause{
  height: @player_icon_size;
  width: @player_icon_size;
  display: inline-block;
  background: no-repeat center  url(../../../assets/images/track/player-pause.png);
  background-size: @player_icon_size;
}
.icon_player_pause:hover{
  filter: contrast(200%);
  cursor: pointer;
}
.icon_player_play{
  height: @player_icon_size;
  width: @player_icon_size;
  display: inline-block;
  background: no-repeat center  url(../../../assets/images/track/player-play.png);
  background-size: @player_icon_size;
}
.icon_player_play:hover{
  filter: contrast(200%);
  cursor: pointer;
}
.icon_player_fast_back{
  height: @player_icon_size;
  width: @player_icon_size;
  display: inline-block;
  background: no-repeat center  url(../../../assets/images/track/player-fast.png);
  transform:scaleX(-1);
  background-size: @player_icon_size;
}
.icon_player_fast_back:hover{
  filter: contrast(200%);
  cursor: pointer;
}
.icon_player_fast_front{
  height: @player_icon_size;
  width: @player_icon_size;
  display: inline-block;
  background: no-repeat center  url(../../../assets/images/track/player-fast.png);
  background-size: @player_icon_size;
}
.icon_player_fast_front:hover{
  filter: contrast(200%);
  cursor: pointer;
}
.icon_player_stop{
  height: @player_icon_size;
  width: @player_icon_size;
  display: inline-block;
  background: no-repeat center  url(../../../assets/images/track/player-stop.png);
  background-size: @player_icon_size;
}
.icon_player_stop:hover{
  filter: contrast(200%);
  cursor: pointer;
}
.planelCancel{
  color: rgba(100, 100, 100, 0.9);
  font-weight: bolder;
  cursor: pointer;
}
.planelCancel:hover{
  color: #118de3;
}

/** range */
.player_range{
  -webkit-appearance: none;
  border-radius:2px;
  width:100%;
  height:4px;
  // background-image:-webkit-linear-gradient(left ,#f22 0%,#f22 20%,#fff 20%, #fff 100%);
  box-shadow:inset #529bec 0 0 5px;
  outline : none;
  transition:.1s;
  outline : none;
}
.player_range::-webkit-slider-thumb{
  -webkit-appearance: none;
  border: 3px solid #fff;
  width:14px;
  height:14px;
  background:#529bec;
  border-radius:50%;
  transition:.1s;
}

.player_range::-webkit-slider-thumb:hover,
.player_range::-webkit-slider-thumb:active{
  width:16px;
  height:16px;
}
/** -----range------- */
</style>
