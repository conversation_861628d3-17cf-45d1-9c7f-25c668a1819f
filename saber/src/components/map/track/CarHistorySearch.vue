<template>
  <div class="CarHistorySearch">
    <div class="seatch_title">
      <div class="icon_logo" />
      <span class="blank" />
      <span>轨迹回放设置</span>
    </div>
    <div class="search_item">
      <span class="search_carname">车牌号码</span>
      <span class="search_form_centent_carid">{{ search.name }}</span>
    </div>
    <div
      class="search_item"
    >
      <el-date-picker
        v-model="search.time"
        size="mini"
        type="datetimerange"
        format="yyyy-MM-dd HH:mm"
        :picker-options="pickerOptions"
        range-separator="至"
        start-placeholder="开始时间"
        :default-time="['00:00:00', '23:59:59']"
        end-placeholder="结束时间"
        popper-class="carHistorySearchDatePicker"
      />
    </div>
    <div>
      <el-checkbox v-model="search.isFilter">
        过滤数据
      </el-checkbox>
    </div>
    <div
      class="seatch_button
      search_item"
    >
      <button
        class="planel_button"
        @click="onCarHistorySearch"
      >
        确认
      </button>
      <span class="blank" />
      <button
        class="planel_button"
        @click="onCarHistoryCancel"
      >
        取消
      </button>
    </div>
  </div>
</template>

<script>
import {trackDateSearch} from '@/api/monitoring/track';
export default {
  name: 'CarHistorySearch',
  props: {
  },
  data () {
    return {
      search: {
        name: '',
        start: 0,
        end: 0,
        time: null,
        isFilter: 0
      },
      pickerOptions: {
        shortcuts: [{
          text: '近2小时',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 2);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: '今天',
          onClick (picker) {
            const end = new Date();
            let start = new Date(end.getFullYear(), end.getMonth(), end.getDate(), 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: '昨天',
          onClick (picker) {
            const now = new Date();
            const start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 0, 0, 0);
            const end = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        },
        {
          text: '最近一周',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }],
        disabledDate: null
      }
    };
  },
  methods: {
    trackHandle () {
      trackDateSearch({licencePlate: this.search.name}).then(res => {
        this.dataNull = res.data;
        res.data = res.data || [];
        // 增加: 当天可操作
        res.data.push(this.$moment().format('YYYYMMDD'));
        if (!res.data || res.data.length === 0) {
          this.pickerOptions.disabledDate = (time) => {
            return true;
          };
        } else {
          this.pickerOptions.disabledDate = (time) => {
            let arr = res.data && res.data.map(v => {
              return v.slice(0, 4) + '-' + v.slice(4, 6) + '-' + v.slice(6);
            });
            let i; let k = 0;
            for (i = 0; i < arr.length; i++) {
              if (time.getTime() >= new Date(`${arr[i]} 00:00:00`) && time.getTime() <= new Date(`${arr[i]} 23:59:59`)) {
                k++;
              }
              if (k > 0) break;
            }
            return !(k > 0);
          };
        }
      });
    },

    resetTrackHandle () {
      this.pickerOptions.disabledDate = null;
    },

    getSearchInfo () {
      let start = new Date(this.search.time[0]);
      let startTime = Date.parse(start) / 1000;
      let end = new Date(this.search.time[1]);
      let endTime = Date.parse(end) / 1000;
      console.log('------------->', this.search.isFilter ? 1 : 0);
      let searchInfo = {
        name: this.search.name,
        start: startTime,
        end: endTime,
        isFilter: this.search.isFilter ? 1 : 0
      };
      return searchInfo;
    },
    setName (_name) {
      this.search.name = _name;
    },
    setTime (_start, _end) {
      this.search.start = _start;
      this.search.end = _end;
    },
    onCarHistorySearch () {
      this.$emit('onCarHistorySearch');
    },
    onCarHistoryCancel () {
      this.$emit('onCarHistoryCancel');
    },
    resetTime () {
      /** 默认最近设置 2 个小时 */
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 2);
      this.search.time = [start, end];
    }
  }
};
</script>
<style lang="less">
.carHistorySearchDatePicker .day_able span{
  background-color: rgb(255, 210, 210);
}
</style>
<style lang="less" scoped>
.CarHistorySearch{
  padding:10px;
  display: flex;
  flex-direction: column;
  color: #303133;
  background-color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px
}
.seatch_title{
  // flex:1;
  display: flex;
  align-items: center;
}
.seatch_button{
  display: flex;
  justify-content: center;
  align-items: center;
}
.search_form_centent_carid{
  color: #118de3;
  font-weight: bolder;
}
.icon_logo{
  width: 6px;
  height: 20px;
  background: #118de3;
}
.blank{
  margin: 4px;
}
.search_item{
  // flex:1;
  margin-top: 10px;
}
.search_carname{
  margin-right: 10px;
}
</style>
