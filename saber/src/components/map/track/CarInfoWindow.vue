<template>
  <div
    v-show="showWindow"
    class="infowindow"
  >
    <div class="info_title">
      <div class="info_title_name">
        <div class="icon_logo" />
        <span class="blank" />
        <span>{{ carInfo.licencePlate }}</span>
      </div>
      <div>
        <!-- <a style="color: #409EFF; margin-right: 20px;">更多</a> -->
        <a
          class="info_title_close"
          @click="onCloseInfoWindow"
        >
          X
        </a>
      </div>
    </div>
    <div class="info_list">
      <div>驾驶员：<span>{{ carInfo.driverName }}</span></div>
      <div>使用状态：<span>{{ carInfo.vehicleUse }}</span></div>
      <div>车组：<span>{{ carInfo.deptName }}</span></div>
      <div>行政区域：<span>{{ carInfo.district }}</span></div>
      <div>行业类型：<span>{{ carInfo.vehicleUseType }}</span></div>
      <div>车辆类型：<span>{{ carInfo.vehicleModel }}</span></div>
      <div>当前速度：<span>{{ carInfo.speed + 'km/h' }}</span></div>
      <div>定位时间：<span>{{ carInfo.time }}</span></div>
      <div>SIM卡号：<span>{{ carInfo.simId }}</span></div>
      <div>终端状态：<span>{{ carInfo.state }}</span></div>
      <div>定位地址：<span>{{ carInfo.position }}</span></div>
      <!-- <a
        v-show="showTrack"
        class="planel_button info_pl"
        @click="onPathWatch"
      >
        轨迹回放
      </a>
      <a
        v-show="showButton"
        class="planel_button info_pl"
        :disabled="false"
        @click="gotoVideo"
      >
        实时视频
      </a>
      <a
        v-show="showButton"
        class="planel_button info_pl"
        @click="gotoInstruction"
      >
        指令
      </a>
      <a
        v-show="showButton"
        class="planel_button info_pl"
        @click="gotoPhoto"
      >
        拍照
      </a>
      <a
        v-show="showButton"
        class="planel_button info_pl"
        @click="onReport"
      >
        点名
      </a> -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'CarInfoWindow',
  dicts: [
    'vehicleModel',
    'vehicleUseType',
    'vehicleState'
  ],
  props: {
    info: {
      type: Object,
      default: () => {
        return {};
      }
    },
    showWindow: {
      type: Boolean,
      default: false
    },
    showTrack: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      carInfo: {},
      showButton: true
    };
  },
  watch: {
    info: function (val) {
      console.log(val);
      this.carInfo = {
        licencePlate: val.licencePlate,
        driverName: val.driverName,
        vehicleUse: this.getEnumDictLabel('vehicleState', val.vehicleUse),
        deptName: val.deptName,
        district: val.district,
        vehicleUseType: this.getEnumDictLabel('vehicleUseType', val.vehicleUseType),
        vehicleModel: this.getEnumDictLabel('vehicleModel', val.vehicleModel),
        speed: val.speed,
        simId: val.simId,
        time: this.$moment(val.time * 1000).format('YYYY-MM-DD HH:mm:ss'),
        state: val.state,
        position: val.position,
        vehicleId: val.vehicleId
      };
    }
  },
  methods: {
    init (showButton) {
      this.showButton = showButton;
    },
    /* onPathWatch () {
      this.$emit('onPathWatch');
    }, */
    onPathWatch () {
      this.$router.push({path: '/monitoring/trackInfo/index', query: {licencePlate: this.carInfo.licencePlate, isRouter: this.$route.fullPath}});
    },
    onReport () {
      this.$emit('onReport', this.carInfo.licencePlate, res => {
        if (res) {
          this.$message({
            message: '点名上报成功!',
            type: 'success'
          });
        }
      });
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      // console.log(dictName, value, this.dict.dict[dictName], this.dict.dict[dictName][value]);
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    gotoInstruction () {
      this.$router.push({path: '/center/instruction/index', query: {licencePlate: this.carInfo.licencePlate, id: this.carInfo.vehicleId, isRouter: this.$route.fullPath}});
    },
    gotoPhoto () {
      this.$router.push({path: '/center/instruction/index', query: {licencePlate: this.carInfo.licencePlate, from: 'photoInstruction', isRouter: this.$route.fullPath}});
    },
    gotoVideo () {
      // this.$router.push({path: '/monitor/vehicle/video/live', query: {licencePlate: this.carInfo.licencePlate}});
      // this.$router.push({name: 'VideoLive', params: {licencePlate: this.carInfo.licencePlate}});
      this.$router.push({path: '/monitoring/videoLiveRTVS/index', query: {licencePlate: this.carInfo.licencePlate, isRouter: this.$route.fullPath}});
    },
    onCloseInfoWindow () {
      this.$emit('onCloseInfoWindow');
    },
    // /**
    //  * 获取某种字典类型的文字[仅适用双层数据]
    //  * @param {String} dictName
    //  * @param {Int|String} value
    //  */
    // getEnumDictLabelDeep (dictName, value) {
    //   if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
    //     return this.dict.dict[dictName][value].label;
    //   } else {
    //     return '';
    //   }
    // }
  }
};
</script>

<style lang="less" scoped>
.infowindow{
  padding:10px;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  span{
    color: #fda244
  }
}
.info_list{
  color: #303133;
  font-size: 12px;
  padding: 10px 10px 0 10px;
  text-align: left;
  // min-width: 415px;
  width: 220px;
  border-collapse: collapse;
  span{
    color: #fda244;
    cursor: pointer;
  }
  button{
    margin-top: 8px;
  }
}
.info_title{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.info_title_name{
  display: flex;
  align-items: center;
}
.icon_logo{
  width: 4px;
  height: 20px;
  background: #118de3;
}
.info_title_close{
  color: rgba(169, 169, 169, 0.4);
  font-weight: bolder;
  cursor: pointer;
}
.info_title_close:hover{
  color: #118de3;
}
.blank{
  margin: 2px;
}
.info_pl{
  margin-left: 5px;
  margin-top: 5px;
}
</style>
