<template>
  <div class="carMarkerManage">
    <VehicleInfo
      v-if="dialogVisible"
      ref="vehicleInfo"
      :dialog-visible.sync="dialogVisible"
      class="vehicle-info"
      :vehicle-data="vehicleData"
      :operation-button="false"
    />
  </div>
</template>
<script>
import VehicleInfo from '../infoWindow/VehicleInfo';
import { getCarStatus } from '@/utils/getCarStatus';
import { queryVehicleState } from '@/api/monitoring/track.js';
import { terminalStates } from '@/api/monitoring/info.js';
// 图资尺寸 : 用户偏移位置
const ICON_SIZE = 52;
export default {
  name: 'CarMarkerManage',
  components: {
    VehicleInfo
  },
  props: {
    operateType: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      /** 地图对象 */
      map: null,
      /** 高德地图 : 方便调用 API */
      AMap: null,
      /** 高德地图鼠标工具 */
      mousetool: null,
      loaded: false,
      CarMarker: null,
      showButton: true,
      marker: null,
      dialogVisible: false,
      vehicleData: {}, // 车辆信息
      isOffset: null, // marker偏移
      deviceData: {}
    };
  },
  methods: {
    closeDialog() {
      this.dialogVisible = false;
    },
    /**
     * 初始化, 绘制设备图标(新)
     * @description 地图相关的对象从外部传入
     * @param {Component} mapWidget 地图组件
     * @param {Object} mapWidget.AMap 高德地图
     * @param {Object} mapWidget.map 地图实例
     * @param {Object} mapWidget.mousetool 地图工具
     * @param {Boolean} mapWidget.loaded 是否加载完毕
     * @param {Array.<String>} carInfo 已选中的车辆
     */
    setDeviceMarkerInit (mapWidget, carInfo) {
      this._mapWidget = mapWidget;
      this.AMap = this._mapWidget.AMap;
      this.map = this._mapWidget.map;
      this.mousetool = this._mapWidget.mousetool;
      this.loaded = this._mapWidget.loaded;
      // 绘制车辆MarKer
      if (carInfo) {
        this.startDeviceMarker(carInfo);
      }
    },
    init (mapWidget, showButton) {
      this._mapWidget = mapWidget;
      this.AMap = this._mapWidget.AMap;
      this.map = this._mapWidget.map;
      this.mousetool = this._mapWidget.mousetool;
      this.loaded = this._mapWidget.loaded;
      this.showButton = showButton;
    },
    /**
     * 在地图中绘制车辆Marker
     */
    startDeviceMarker (carInfo) {
      // 清除旧的图标
      this.clearOldMarker();
      console.log('carInfo', carInfo);
      this.getDeviceMarker(carInfo).then(res => {
        if (!res) {
          return;
        }
        if (this.CarMarker) { // 连续点击时会生成多个车辆图标, 因此在这里二次清除一下
          this.clearOldMarker();
        }
        this.$nextTick(() => {
          this.CarMarker = res;
          this.map.olMap.addOverlay(this.CarMarker);
        });
      });
    },
    batchCarMarker (data) {
      let _this = this;
      this.marker = null;
      const labelTypeData = {
        iconUrl: this.judgeTerminalIcon(data),
        bgUrl: this.judgeBackgroundIcon(data),
        iconWidth: 50,
        iconHeight: 50
      };
      this.marker = new this.AMap.Marker({
        // icon: startIcon,
        position: new this.AMap.LngLat(data.longitude, data.latitude),
        // offset: new this.AMap.Pixel(-ICON_SIZE / 2, -ICON_SIZE),
        label: {
          content: "<div class='car-label'>" + data.targetName + '</div>',
          direction: 'top',
          offset: new this.AMap.Pixel(0, 6)
        },
        content: `<div style="position: relative;">
                      <div style="position: absolute; width: 100%; height: 100%; background-image: url(${labelTypeData.bgUrl}); background-size: 100%; transform: rotate(${data.bearing}deg);"></div>
                      <img src="${labelTypeData.iconUrl}" style="display: block; width: ${labelTypeData.iconWidth}px; height: ${labelTypeData.iconHeight}px; padding: 3px; position: inherit;">
                    </div>`
      });
      if (!data.notCenter) {
        this.$emit('setZoomAndCenter', {longitude: data.longitude, latitude: data.latitude});
        this.handleMarker(data);
      }
      // 车辆点击事件注册
      this.marker.on('click', () => {
        this.handleMarker(data);
        this.$emit('customEvent', data); // 给marker添加其他事件
      });
      return this.marker;
    },
    handleMarker(data) {
      this.vehicleData = data;
      // 窗口开启
      this.dialogVisible = true;
    },
    editVehicleData(data) {
      this.vehicleData = data;
      this.dialogVisible = true;
    },
    // 获取终端数据
    getDeviceData () {
      const labelTypeData = {
        iconUrl: this.judgeTerminalIcon(this.deviceData),
        bgUrl: this.judgeBackgroundIcon(this.deviceData),
        iconWidth: 50,
        iconHeight: 50
      };
      return labelTypeData || {};
    },
    // 构建车辆覆盖物
    async getCarMarker (list) {
      if (!list || !list.length) {
        return;
      }
      let parme = {
        deviceIds: list.map(item => BigInt(item.deviceId))
      };
      await terminalStates(parme).then(res => {
        console.log('构建车辆图标-视频', res.data.content);
        for (let index = 0; index < res.data.content.length; index++) {
          const data = res.data.content[index];
          data.treeCategory = String(data.deviceCategory);
          data.fusionState = data.status;
          const labelTypeData = {
            iconUrl: this.judgeTerminalIcon(data),
            bgUrl: this.judgeBackgroundIcon(data),
            iconWidth: 50,
            iconHeight: 50
          };
          this.handleMarker(data);
          const content = `
                      <div class="car-label" style="position:absolute;top: -14px; left: -18.5px;font-size: 12px;white-space:nowrap;">${data.targetName}</div>
                      <div style="position: relative;width: 50px;height: 50px;">
                       <div style="position: absolute; width: 100%; height: 100%; background-image: url(${labelTypeData.bgUrl}); background-size: 100%; transform: rotate(${data.bearing}deg);"></div>
                      <img src="${labelTypeData.iconUrl}" style="display: block; width: ${labelTypeData.iconWidth}px; height: ${labelTypeData.iconHeight}px; padding: 3px; position: inherit;">
                    </div>`;
          const element = document.createElement('div');
          element.style.position = 'relative';
          element.innerHTML = content;
          const locations = [Number(data.longitude),
            Number(data.latitude)];
          const marker = new this.AMap.ol.Overlay({
            element: element,
            stopEvent: false,
            position: MayMap.ol.proj.transform(locations, 'EPSG:4326', 'EPSG:3857'),
            positioning: 'center-center'
          });
          this.map.setZoomAndCenter(locations, 12);
          this.map.olMap.addOverlay(marker);
        }
      });
    },
    // 构建设备覆盖物
    async getDeviceMarker (_opts) {
      let opts = _opts || {};
      this.marker = null;
      if (opts) {
        let query = {
          deviceIds: opts.deviceId ? [BigInt(opts.deviceId)] : undefined
        };
        await terminalStates(query).then(res => {
          if (this.operateType === 2) {
            return;
          }
          const terminalObj = res.data.content[0] || {};
          console.log('构建车辆图标-ce', terminalObj);
          this.deviceData = {
            ...terminalObj,
            treeCategory: String(terminalObj.deviceCategory),
            fusionState: terminalObj.status,
            locTime: terminalObj.time,
            deviceUniqueId: terminalObj.uniqueId
          };
          const labelTypeData = {
            iconUrl: this.judgeTerminalIcon(this.deviceData),
            bgUrl: this.judgeBackgroundIcon(this.deviceData),
            iconWidth: 50,
            iconHeight: 50
          };
          this.handleMarker(this.deviceData);
          // const content = `
          //             <div class="car-label" style="position:absolute;top: -14px; left: -18.5px;font-size: 12px;white-space:nowrap;">${this.deviceData.targetName}</div>
          //             <div style="position: relative;width: 50px;height: 50px;">
          //              <div style="position: absolute; width: 100%; height: 100%; background-image: url(${labelTypeData.bgUrl}); background-size: 100%; transform: rotate(${this.deviceData.bearing}deg);"></div>
          //             <img src="${labelTypeData.iconUrl}" style="display: block; width: ${labelTypeData.iconWidth}px; height: ${labelTypeData.iconHeight}px; padding: 3px; position: inherit;">
          //           </div>`;
          const content = `
                      <div class="car-label" style="position:absolute;top: -14px; left: -18.5px;font-size: 12px;white-space:nowrap;">${this.deviceData.targetName}</div>
                      <div style="position: relative;width: 50px;height: 50px;">
                       <div style="position: absolute; width: 100%; height: 100%; background-image: url(${labelTypeData.bgUrl}); background-size: 100%; transform: rotate(${this.deviceData.bearing}deg);"></div>
                      <img src="${labelTypeData.iconUrl}" style="display: block; width: ${labelTypeData.iconWidth}px; height: ${labelTypeData.iconHeight}px; padding: 3px; position: inherit;">
                    </div>`;
          const element = document.createElement('div');
          element.style.position = 'relative';
          element.innerHTML = content;
          const locations = [Number(this.deviceData.longitude),
            Number(this.deviceData.latitude)];
          this.marker = new this.AMap.ol.Overlay({
            element: element,
            stopEvent: false,
            position: MayMap.ol.proj.transform(locations, 'EPSG:4326', 'EPSG:3857'),
            positioning: 'center-center'
          });
          this.map.setZoomAndCenter(locations, 12);
          // if (!opts.notCenter) {
          // //   // 实时视频和历史视频的视角要向下移动, TODO 0.001仅临时使用, 后期可通过计算地图的顶点纬度相差, 再乘以车辆详情弹窗在地图dom元素中的占比来计算出要加的纬度
          //   this.$emit('setZoomAndCenter', {longitude: this.deviceData.longitude, latitude: this.isOffset ? Number(this.deviceData.latitude) + 0.001 : this.deviceData.latitude});
          //   this.handleMarker(this.deviceData);
          // }
          // 车辆点击事件注册
          // this.marker.on('click', () => {
          //   this.handleMarker(this.deviceData);
          //   this.$emit('customEvent', this.deviceData); // 给marker添加其他事件
          // });
          if (opts.fusionState !== terminalObj.status) {
            this.$emit('updateStateTerminal', [{
              id: terminalObj.deviceIdStr,
              fusionState: terminalObj.status
            }]);
          }
        });
      }
      return this.marker;
    },
    // 构建车辆覆盖物数组
    async getDeviceMarkers (carData, isOffset) {
      this.isOffset = isOffset;
      this.getCarMarker(carData);
    },
    clearOldMarker () {
      if (this.CarMarker) {
        this.map.olMap.removeOverlay(this.CarMarker);
        this.CarMarker = null;
      }
    },
    // 格式化车辆覆盖物数据
    formatCarMarkers (_data) {
      let data = _data || [];
      let markersData = [];
      for (let i = 0; i < data.length; i++) {
        if (data[i].data) {
          const element = data[i].data;
          let marker = {
            status: element.state || element.veState,
            lnglat: [element.longitude, element.latitude],
            name: element.licencePlate,
            info: element
          };
          markersData.push(marker);
        } else {
          const element = data[i];
          let marker = {
            status: element.stateObj.vehicleState,
            lnglat: [element.stateObj.longitude, element.stateObj.latitude],
            name: element.stateObj.licencePlate,
            info: element
          };
          markersData.push(marker);
        }
      }
      return markersData;
    },
    /**
     * 根据终端类型判断图标
     */
    judgeTerminalIcon (val) {
      const vehicleModel = ['101', '102', '103', '104', '105'];
      const materialsModel = ['106', '108', '109', '111', '112', '114'];
      const personnelModel = ['201', '202', '301', '107', '110', '113'];
      const shortMessageModel = ['302', '303'];
      const timeServiceModel = ['501', '502', '503'];
      const monitorModel = ['401', '402'];
      let vehicleIcon = '';
      if (vehicleModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/vehicle.png`; // 车辆
      } else if (materialsModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/materials.png`; // 物资
      } else if (personnelModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/personnel.png`; // 人员
      } else if (shortMessageModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/shortMessage.png`; // 短报文终端
      } else if (timeServiceModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/timeService.png`; // 授时终端
      } else if (monitorModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/monitor.png`; // 监测终端
      } else if (val.treeCategory === '0') {
        vehicleIcon = `/bdsplatform/static/images/pic/other.png`; // 其他
      }
      return vehicleIcon;
    },
    /**
     * 根据其他类型判断其他图标
     */
    judgeStaffIcon (val) {
      const vehicleModel = ['101', '102', '103', '104', '105'];
      const materialsModel = ['106', '108', '109', '111', '112', '114'];
      const personnelModel = ['201', '202', '301', '107', '110', '113'];
      const shortMessageModel = ['302', '303'];
      const timeServiceModel = ['501', '502', '503'];
      const monitorModel = ['401', '402'];
      let vehicleIcon = '';
      if (vehicleModel.includes(val.treeCategory)) {
        vehicleIcon = this.colorStaffType(val, 'vehicle'); // 车辆
      } else if (materialsModel.includes(val.treeCategory)) {
        vehicleIcon = this.colorStaffType(val, 'materials'); // 物资
      } else if (personnelModel.includes(val.treeCategory)) {
        vehicleIcon = this.colorStaffType(val, 'personnel'); // 人员
      } else if (shortMessageModel.includes(val.treeCategory)) {
        vehicleIcon = this.colorStaffType(val, 'shortMessage'); // 短报文终端
      } else if (timeServiceModel.includes(val.treeCategory)) {
        vehicleIcon = this.colorStaffType(val, 'timeService'); // 授时终端
      } else if (monitorModel.includes(val.treeCategory)) {
        vehicleIcon = this.colorStaffType(val, 'monitor'); // 监测终端
      } else if (val.treeCategory === '0') {
        vehicleIcon = this.colorStaffType(val, 'other'); // 其他
      }
      return vehicleIcon;
    },
    /**
     * 根据车辆类型判断车辆图标
     */
    judgeVehicleIcon (val) {
      console.log(val);
      let passengerModel = ['10', '11', '12', '13', '14', '15', '16', '66']; // 66为公交车, 也用客车图标
      let truckModel = ['20', '21', '22', '23'];
      let vehicleIcon = '';
      if (passengerModel.includes(val.vehicleModel)) {
        vehicleIcon = this.colorType(val, 'passenger');
        // vehicleIcon = val.teState ? '/bdsplatform/static/images/icons/busOnline.svg' : '/bdsplatform/static/images/icons/busOffline.svg';
      } else if (truckModel.includes(val.vehicleModel)) {
        vehicleIcon = this.colorType(val, 'vehicle');
        // vehicleIcon = val.teState ? '/bdsplatform/static/images/icons/truckOnline.svg' : '/bdsplatform/static/images/icons/truckOffline.svg';
      } else if (val.vehicleModel === '73' || val.vehicleModel === '33') {
        vehicleIcon = this.colorType(val, 'coldChain'); // 冷链车
      } else if (val.vehicleModel === '35' || val.vehicleModel === '40') {
        vehicleIcon = this.colorType(val, 'oilTank'); // 油罐车
      } else if (val.vehicleModel === '70' || val.vehicleModel === '71') {
        vehicleIcon = this.colorType(val, 'rentOut'); // 出租车
      } else {
        vehicleIcon = this.colorType(val, 'otherOffline');
        // vehicleIcon = val.teState ? '/bdsplatform/static/images/icons/otherCarOnline.svg' : '/bdsplatform/static/images/icons/otherCarOffline.svg';
      }
      return vehicleIcon;
    },
    judgeBackgroundIcon (val) {
      console.log('-> val', val);
      // 0-离线 1-静止 2-移动
      // 离线(Offline) 静止(Static) 移动(Move)
      let vehicleIcon = '';
      switch (val.fusionState) {
      case 0:
        vehicleIcon = `/bdsplatform/static/images/pic/offline.png`;
        break;
      case 1:
        vehicleIcon = `/bdsplatform/static/images/pic/static.png`;
        break;
      case 2:
        vehicleIcon = `/bdsplatform/static/images/pic/move.png`;
        break;
      }
      console.log('-> vehicleIcon', vehicleIcon);
      return vehicleIcon;
    },
    colorStaffType (val, type) {
      // 0-离线 1-静止 2-移动
      // 离线(Offline) 静止(Static) 移动(Move)
      let vehicleIcon = '';
      switch (val.fusionState) {
      case 0:
        vehicleIcon = `/bdsplatform/static/images/pic/${type}Offline.png`;
        break;
      case 1:
        vehicleIcon = `/bdsplatform/static/images/pic/${type}Static.png`;
        break;
      case 2:
        vehicleIcon = `/bdsplatform/static/images/pic/${type}Move.png`;
        break;
      }
      return vehicleIcon;
    },
    colorType (val, type) {
      // 0-离线 1-行驶 2-停驶-ACC关 3-告警 4-未定位 5-停驶-ACC开
      // 离线(灰色) 行驶(蓝色) 停驶-ACC关(紫色) 告警(红色) 未定位(黄色) 停驶-ACC开(绿色)
      let vehicleIcon = '';
      switch (val.fusionState) {
      case 0:
        vehicleIcon = `/bdsplatform/static/images/pic/${type}.png`;
        break;
      case 1:
        vehicleIcon = `/bdsplatform/static/images/pic/${type}Blue.png`;
        break;
      case 2:
        vehicleIcon = `/bdsplatform/static/images/pic/${type}Purple.png`;
        break;
      case 3:
        vehicleIcon = `/bdsplatform/static/images/pic/${type}Red.png`;
        break;
      case 4:
        vehicleIcon = `/bdsplatform/static/images/pic/${type}Yellow.png`;
        break;
      case 5:
        vehicleIcon = `/bdsplatform/static/images/pic/${type}Green.png`;
        break;
      }
      return vehicleIcon;
    }
  }
};
</script>
<style lang="less" scoped>
  .carMarkerManage{
    display: flex;
  }
  .car-label{
    color: #fffcfc;
    background-color: #333333c5;
    padding: 1px 4px;
  }
  .vehicle-info{
    width: 400px;
    position: absolute;
    top: 80px;
    right: 5px;
    background-color: white;
    z-index: 1000;
    border-radius: 5px;
    box-shadow: rgb(113, 144, 202) 1px 0px;
  }
</style>
