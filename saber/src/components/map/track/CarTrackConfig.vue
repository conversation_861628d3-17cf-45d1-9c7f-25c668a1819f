<template>
  <div class="track-container">
    <el-form
      ref="form"
      :model="query"
      label-width="70px"
      size="mini"
    >
      <el-radio-group
        v-model="tabSearch"
        class="tab-search"
        @change="handleTabClick"
      >
        <el-radio-button :label="1">今天</el-radio-button>
        <el-radio-button :label="2">最近2天</el-radio-button>
        <el-radio-button :label="3">最近3天</el-radio-button>
        <el-radio-button title="提示：高频定位终端查询时间范围过大容易造成浏览器卡顿" :label="10">自定义</el-radio-button>
      </el-radio-group>
      <el-form-item label="开始时间">
        <el-date-picker
          ref="dateStartPicker"
          v-model="query.startTime"
          type="datetime"
          placeholder="开始时间"
          default-time="00:00:00"
          size="mini"
          format="yyyy-MM-dd HH:mm"
          :picker-options="pickerStartOptions"
          popper-class="carHistorySearchDatePicker"
          @change="handleTime('startTime')"
        />
      </el-form-item>
      <el-form-item label="结束时间">
        <el-date-picker
          ref="dateEndPicker"
          v-model="query.endTime"
          type="datetime"
          placeholder="结束时间"
          size="mini"
          format="yyyy-MM-dd HH:mm"
          :picker-options="pickerEndOptions"
          default-time="23:59:59"
          popper-class="carHistorySearchDatePicker"
          @change="handleTime('endTime')"
        />
      </el-form-item>
      <el-form-item label="查询定位">
        <el-checkbox-group
          v-model="query.isFilter"
          class="location-checkbox"
        >
          <el-checkbox label="invalid">无效</el-checkbox>
          <el-checkbox label="batch">补报</el-checkbox>
          <el-checkbox label="posSystem">LBS定位</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <div class="query-container">
        <el-button
          type="primary"
          class="query-btn"
          @click="onTrackSearch"
        >
          查询
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { trackDateSearch, exportTracking } from '@/api/monitoring/track.js';
import { getSystemParam } from '@/api/user';
export default {
  name: 'CarTrackConfig',
  components: {
  },
  props: {
    dict: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      query: {
        startTime: new Date(this.$moment().startOf('day')),
        endTime: new Date(this.$moment().endOf('day')),
        isFilter: ['invalid'],
        targetId: null,
        targetType: null,
        deviceId: null,
        deviceType: null
      },
      pickerStartOptions: {
        cellClassName: null
      },
      pickerEndOptions: {
        cellClassName: null
      },
      maxSearchDay: 3,
      tabSearch: 1
    };
  },
  mounted () {
    this.getTrackMaxDay();
  },
  activated () {
    this.handlePickerWatch('dateStartPicker', 'pickerStartOptions');
    this.handlePickerWatch('dateEndPicker', 'pickerEndOptions');
  },
  methods: {
    handlePickerWatch (picker, option) {
      this.$nextTick(() => {
        let pickerWatch = null;
        let unwatch = null;
        let datePicker = this.$refs[picker]; // 获取组件
        pickerWatch = datePicker.$watch('pickerVisible', (newVal) => {
          if (newVal && this.query.deviceId) {
            // 打开了日期选择面板，获取picker组件
            const picker = datePicker.picker;
            if (picker) {
              this.trackHandle(this.$moment(picker.date).format('YYYY-MM'), option);
              unwatch = picker.$watch('month', (newDate) => {
                this.trackHandle(this.$moment(picker.date).format('YYYY-MM'), option);
              });
            }
          } else {
            unwatch && unwatch();
            unwatch = null;
          }
        });
        this.$once('hook:deactivated', () => {
          pickerWatch && pickerWatch();
          pickerWatch = null;
          datePicker = null;
        });
      });
    },
    editValue(data) {
      data.isFilter = [...new Set([...this.query.isFilter, ...data.isFilter])];
      this.query = { ...this.query, ...data };
    },
    // 选择查询天数
    handleTabClick () {
      if(this.tabSearch !== 10) {
        this.query.startTime = new Date(this.$moment().subtract(this.tabSearch - 1, 'days').startOf('day'));
        this.query.endTime = new Date(this.$moment().endOf('day'));
      }else{
        this.query.startTime = new Date(this.$moment().subtract(this.tabSearch - 1, 'days').startOf('day'));
        this.query.endTime = new Date(this.$moment().endOf('day'));
      }
    },
    // 获取轨迹最大可查询天数
    getTrackMaxDay () {
      getSystemParam('trackSearchDay').then(res => {
        const { code, data } = res?.data;
        if (code === 200) {
          this.maxSearchDay = Number(data);
        }
      }).catch(err => {

      });
    },
    handleTime(time) {
      if (this.query.startTime && this.query.endTime) {
        const days = this.$moment(this.query.endTime).diff(this.$moment(this.query.startTime), 'days');
        if (days >= this.maxSearchDay) {
          this.$message.warning(`查询时间跨度不能超过${this.maxSearchDay}天`);
          this.query[time] = null;
        }
      }
      const query = {
        startTime: this.query.startTime,
        endTime: this.query.endTime
      };
      this.$emit('handleTime', query);
    },
    resetTrackHandle () {
      this.pickerStartOptions.cellClassName = null;
      this.pickerEndOptions.cellClassName = null;
    },
    trackHandle (month, option) {
      this[option].cellClassName = null;
      let query = {
        deviceId: this.query.deviceId,
        deviceType: this.query.deviceType,
        targetId: this.query.targetId,
        targetType: this.query.targetType,
        month: month
      };
      trackDateSearch(query).then(res => {
        if (res.data) {
          const arr = this.checkDaysWithData(month, res.data);
          this[option].cellClassName = (time) => {
            let i; let k = 0;
            for (i = 0; i < arr.length; i++) {
              if (time.getTime() >= new Date(`${arr[i]} 00:00:00`) && time.getTime() <= new Date(`${arr[i]} 23:59:59`)) {
                k++;
              }
              if (k > 0) break;
            }
            return k > 0 ? 'day_able' : '';
          };
        }
      });
    },
    checkDaysWithData(month, num) {
      let dateList = [];
      // 将数字转换为二进制字符串，并去掉'0b'前缀
      let binaryStr = num.toString(2);
      // 删除二进制字符串的最后一个字符
      binaryStr = binaryStr.slice(0, -1);
      // 假设num代表的天数不超过31天，因为二进制最多只能表示31天（从第1天到第31天）
      for (let day = 1; day <= binaryStr.length; day++) {
        // 根据二进制字符串的每一位判断当天是否有数据
        if (binaryStr[binaryStr.length - day] === '1') {
          let dayStr = day < 10 ? `0${day}` : day;
          dateList.push(`${month}-${dayStr}`);
        }
      }
      return dateList;
    },
    onTrackSearch () {
      this.$emit('clearTrackData');
      if (!this.query.deviceId) {
        this.$message({
          message: '请先选择终端！',
          type: 'warning'
        });
        return;
      }
      if (!this.query.startTime || !this.query.endTime) {
        this.$message({
          message: '请先选择时间！',
          type: 'warning'
        });
        return;
      } else if (this.query.startTime.getTime() > this.query.endTime.getTime()) {
        this.$message({
          message: '结束时间要大于开始时间',
          type: 'warning'
        });
        return;
      }
      if (this.tabSearch == 10 && this.$moment(this.query.endTime).diff(this.$moment(this.query.startTime), 'days') > 10) {
        this.$message({
          message: '自定义时间间隔不能超过10天',
          type: 'warning'
        });
        return;
      }
      // this.$emit('onTrackPlanelCancel');
      // return;
      let searchInfo = {
        start: this.$moment(this.query.startTime).unix(),
        end: this.$moment(this.query.endTime).unix(),
        // start:1740972971,
        // end: 1749225599,
        // start:1747670400,
        // end: 1747756799,
        deviceId: this.query.deviceId,
        deviceType: this.query.deviceType,
        targetId: this.query.targetId,
        targetType: this.query.targetType,
        isFilter: this.query.isFilter
      };
      this.$emit('onTrackSearch', searchInfo);
    },
    // 导出设备轨迹
    onExportDeviceData() {
      if (!this.query.deviceId) {
        this.$message({
          message: '请先选择终端！',
          type: 'error'
        });
        return;
      }
      if (!this.query.startTime || !this.query.endTime) {
        this.$message({
          message: '请先选择时间！',
          type: 'warning'
        });
        return;
      } else if (this.query.startTime.getTime() > this.query.endTime.getTime()) {
        this.$message({
          message: '结束时间要大于开始时间',
          type: 'warning'
        });
        return;
      }
      // 请求路线信息
      let parme = {
        startTime: this.$moment(this.query.startTime).unix(),
        endTime: this.$moment(this.query.endTime).unix(),
        deviceId: BigInt(this.query.deviceId),
        deviceType: this.query.deviceType,
        targetId: this.query.targetId ? BigInt(this.query.targetId) : undefined,
        targetType: this.query.targetType ? this.query.targetType : undefined,
      };
      if (this.query.isFilter?.length) {
        for (let index = 0; index < this.query.isFilter.length; index++) {
          const element = this.query.isFilter[index];
          parme[element] = 1;
        }
      }
      this.$message({
        message: '数据正在导出,请稍后。。',
        type: 'success'
      });
      exportTracking(parme).then(res => {
        if (res.data) {
          this.$download(res.data, 'path');
        } else {
          this.$msgbox.alert('导出失败，请重新查询轨迹', '提示', {
            confirmButtonText: '确定',
            type: 'warning'
          });
        }
      }).catch(res => {
        loading.close();
        console.log(res);
      });
    }
  }
};
</script>
<style lang="less">
.carHistorySearchDatePicker .day_able span{
  background-color: rgb(255, 210, 210);
}
</style>

<style lang="less" scoped>
.track-container{
  padding: 10px;
}
.query-container{
  display: flex;
  justify-content: center;
}
.query-btn{
  width: 140px;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
}
/deep/ .el-autocomplete{
  width: 100%;
}
.car-form-item {
  /deep/ .el-form-item__content{
    display: flex;
  }
}
.tab-search {
  display: flex;
  justify-content: center;
  padding-bottom: 10px;
  /deep/ .el-radio-button__inner {
    padding: 7px 13px;
  }
}
.location-checkbox {
  display: flex;
  justify-content: space-between;
  .el-checkbox {
    margin-right: 0;
  }
}

// 不同分辨率媒体查询样式

@media screen and (max-width: 1500px) {
    // 左侧搜索条件
    .track-container {
      padding: 5px;
        /deep/ .el-date-editor {
          .el-input__prefix {
            display: none !important;
          }
          .el-input__inner {
            padding-left: 15px !important;
          }
        }
    }
    .location-checkbox {
      /deep/ .el-checkbox__label {
        padding-left: 0;
      }
    }
}
</style>
