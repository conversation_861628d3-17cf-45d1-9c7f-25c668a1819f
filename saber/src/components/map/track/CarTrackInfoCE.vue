<template>
  <div class="car-track-info">
    <div
      class="info-all"
    >
      <div
        class="row-bg"
      >
        <div class="config-btn">
          <!-- 降低播放速度 -->
          <span
            class="player-icon player-slow"
            title="降低播放速度"
            @click="onPlayerFastBack"
          />
          <!-- 暂停播放 -->
          <span
            v-if="IsPlaying"
            class="player-icon player-pause"
            title="暂停/继续播放"
            @click="onPlayerPause"
          />
          <!-- 播放 -->
          <span
            v-else
            class="player-icon player-play"
            title="播放"
            @click="onPlayerPlay"
          />
          <!-- 增加播放速度 -->
          <span
            class="player-icon player-fast"
            title="增加播放速度"
            @click="onPlayerFastFront"
          />
          <!-- 停止播放 -->
          <span
            class="player-icon player-stop"
            title="停止播放"
            @click="onPlayerStop(true)"
          />
        </div>
        <div class="config-label">
          <span class="search_carname">播放速度：</span>
          <span class="search_form_centent_carid">{{ playSpeedString }}</span>
        </div>
        <div class="config-range">
          <span>播放进度：</span>
          <x-range
            class="history_range"
            :value="HistoryRangeCE"
            :max-num="trackDataMaxNum"
            :disabled="canCheck"
            @update:value="onRangeMove"
            @closeAnimation="closeAnimation"
            @onPlayerPause="onPlayerPause"
            @onPlayerPlay="onPlayerPlay"
          />
        </div>
        <div class="config-check">
          <el-checkbox
            v-model="showLocationPoint"
            :disabled="canCheck"
          >
            定位点
          </el-checkbox>
          <el-checkbox
            v-if="false"
            v-model="showTimeSpeed"
            class="setting-info"
            :disabled="canCheck"
          >
            时间/速度
          </el-checkbox>
          <el-checkbox
            v-if="false"
            v-model="showAddress"
            class="setting-info"
            :disabled="canCheck"
          >
            显示地址
          </el-checkbox>
          <el-checkbox
            v-if="false"
            v-model="integrityChecked"
            class="setting-info"
            :disabled="canCheck"
          >
            轨迹完整率
          </el-checkbox>
          <!-- <el-checkbox
            v-model="driftChecked"
            class="setting-info"
            :disabled="canCheck"
          >
            漂移次数
          </el-checkbox> -->
        </div>
        <div class="config-export">
          <el-button
            size="mini"
            @click="onExportDeviceData"
          >
            导出
          </el-button>
        </div>
      </div>
      <TrackTable
        ref="trackTable"
        :dict="dict"
        @goToPoint="goToPoint"
      />
    </div>
  </div>
</template>

<script>
import xRange from './RangeCE';
import { batchAddr, trackRatio } from '@/api/monitoring/track.js';
import TrackTable from './TrackTableCE';
import CarSearch from '@/components/CarSearch';
// 图资尺寸 : 用户偏移位置
const ICON_SIZE = 32;
// let this.Track = null;
export default {
  name: 'CarTrackInfo',
  components: {
    'x-range': xRange,
    TrackTable,
    CarSearch
  },
  directives: {
    drag(el, bindings, vnode) { // 拖拽
      el.onmousedown = function (e) {
        let y = e.clientY;
        let yel = vnode.context.$parent.$refs.tableContainer;
        let yelDefault = yel.style.height || '220px';
        yelDefault = +yelDefault.substring(0, yelDefault.length - 2);
        yelDefault = yelDefault > 600 ? 600 : yelDefault;
        yelDefault = yelDefault < 230 ? 230 : yelDefault;
        document.onmousemove = function (e) {
          let ylong = yelDefault + y - e.clientY;
          ylong = ylong > 600 ? 600 : ylong;
          ylong = ylong < 230 ? 230 : ylong;
          yel.style.height = ylong + 'px';
        };
        document.onmouseup = function () {
          document.onmousemove = document.onmouseup = null;
        };
      };
    }
  },
  props: {
    selectedCar: {
      type: [
        String,
        Number,
        Object
      ],
      default: ''
    },
    selectedColor: {
      type: String,
      default: ''
    },
    dict: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      HistoryRange: 0,
      HistoryRangeCE: 0,
      trackDataMaxNum: 100,
      IsPlaying: false,
      // 速度数组 ， 用于显示速度图表
      SpeedsArray: [],
      playSpeed: 1, // 播放速度
      playSpeedString: 'X 1',
      CarMarker: null,
      PathMarker: null,
      DisContPathPass: null,
      map: null,
      TrackData: null,
      Player: {
        index: 0,
        coors: []
      },
      // 完整率
      integrityRatio: 1,
      // 漂移率
      driftCounts: 0,
      // 是否绘制轨迹不连续数据
      integrityChecked: false,
      // 是否绘制漂移数据
      driftChecked: false,
      // 不连续轨迹点
      trackPath: [],
      // 漂移轨迹点
      driftPath: [],
      // 不连续轨迹层
      trackPathGroups: null,
      // 漂移轨迹点层
      driftPathGroups: null,
      // 定位点层
      trackPointGroups: null,
      // 时间速度层
      timeSpeedGroups: null,
      // 地址层
      addressGroups: null,
      // 单个点文本层
      infoWindow: null,
      truckData: [],
      search: {
        isFilter: 0,
        licenceColor: null,
        s_time: new Date(this.$moment().startOf('day')),
        e_time: new Date(this.$moment().endOf('day'))
      },
      carID: '',
      showLocationPoint: false,
      showTimeSpeed: false,
      showAddress: false,
      clickData: [],
      canCheck: true,
      headerStyle: {
        'background-color': '#87cefa',
        'line-height': '5px',
        'height': '10px',
        'justify-content': 'center'
      },
      schedule: 0,
      presentLocation: [],
      timer: null,
      timerSpeed: 600,
      // timerSpeed: 0,
      labelTypeData: null,
      pathLayer: null, // 国能轨迹矢量图层
      InfoLayer:null // 弹层标志点图层
    };
  },
  watch: {
    integrityChecked(val) {
      if (val) {
        // 绘制轨迹不连续数据
        this.setDisCont(this.trackPath);
        this.$parent.showWatchCard = true;
      }
      else {
        if (!this.driftChecked) {
          this.$parent.showWatchCard = false;
        }
        if (this.trackPathGroups) {
          this.trackPathGroups.clearOverlays();
          this.map.remove(this.trackPathGroups);
        }
      }
    },
    driftChecked(val) {
      if (val) {
        // 绘制轨迹漂移数据
        this.setDrift(this.driftPath);
        this.$parent.showWatchCard = true;
      }
      else {
        if (!this.integrityChecked) {
          this.$parent.showWatchCard = false;
        }
        if (this.driftPathGroups) {
          this.driftPathGroups.clearOverlays();
          this.map.remove(this.driftPathGroups);
        }
      }
    },
    showLocationPoint(val) {
      if (this.WebGLPointsLayer) {
        this.AMap.TileLayer.setVisible(this.WebGLPointsLayer, val);
      } else if (val) {
        this.setTrackPoint(this.truckData);
      }
    },
    showTimeSpeed(val) {
      if (val) {
        this.timeSpeedGroups ? this.timeSpeedGroups.show() : this.setTimeSpeed(this.truckData);
        this.clearSignalPointText();
      }
      else {
        if (this.timeSpeedGroups) {
          this.timeSpeedGroups.hide();
        }
      }
    },
    showAddress(val) {
      if (val) {
        this.addressGroups ? this.addressGroups.show() : this.setAddress(this.truckData);
        this.clearSignalPointText();
      }
      else {
        if (this.addressGroups) {
          this.addressGroups.hide();
        }
      }
    }
  },
  mounted() {
  },
  methods: {
    closeAnimation() {
      if (this.IsPlaying) {
        if (this.timer) {
          clearTimeout(this.timer);
          this.timer = null;
        }
        // this.CarMarker.stopMove(); // 终止动画
      }
    },
    // 赋值时间
    handleTime(query) {
      this.search.s_time = query.startTime;
      this.search.e_time = query.endTime;
    },
    showDeptBtn() {
      this.$parent.deptSingleShow = !this.$parent.deptSingleShow;
    },
    formatChartData(_data) {
      let data = _data || [];
      let chartData = [];
      for (let i = 0; i < data.length; i++) {
        const element = data[i];
        let item = {
          value: [
            element.time * 1000,
            element.speed
          ]
        };
        if (i > 0 && data[i].time - data[i - 1].time > 1800) {
          item.value[1] = 0;
        }
        if (i < data.length - 1 && data[i + 1].time - data[i].time > 1800) {
          item.value[1] = 0;
        }
        chartData.push(item);
      }
      return chartData;
    },
    formatPathData(_data) {
      if (!_data) {
        return [];
      }
      let coors = [];
      for (let i = 0; i < _data.length; i++) {
        const element = _data[i];
        let coor = [
          Number(element.longitude),
          Number(element.latitude)
        ];
        coors.push(coor);
      }
      return coors;
    },
    setChartData(_data) {
      let data = this.formatChartData(_data);
      this.SpeedsArray = data;
    },
    setInfoData(_data) {
      let data = _data || {};
      // setInfoData比setTableData先执行，所以执行的时候还拿不到SpeedsArray最新值，因此需要nextTick
      this.$nextTick(() => {
        // this.updateRange(data.time * 1000);
      });
      if (data.index === data.total) {
        this.setPlayerStatus(false);
      }
    },
    resetInfo() {
      this.SpeedsArray = [];
      /** 速度初始化 */
      this.playSpeed = 1;
      this.timerSpeed = 600;
      this.switchTimerSpeedString();
    },
    setPlayerStatus(_bool) {
      this.IsPlaying = _bool || false;
    },
    /**
     * 开始时间，结束时间，当前时间
     */
    // updateRange (_timeCurrent) {
    //   if (!this.SpeedsArray.length) {
    //     return;
    //   }
    //   let timeStart = this.SpeedsArray[0].value[0];
    //   let timeEnd = this.SpeedsArray[this.SpeedsArray.length - 1].value[0];
    //   let timeDistance = timeEnd - timeStart;
    //   let indexTime = _timeCurrent - timeStart;
    //   let range = indexTime / timeDistance;
    //   // range *= 100;
    //   // console.info('--> updateRange',range );
    //   this.HistoryRange = range;
    // },
    // 地图鼠标左键单击事件
    leftClickEvent (e) {
      if (e.feature?.length && e.feature[0].get('data')) {
        let currentData = e.feature[0].get('data');
        // console.log(currentData,"leftClickEvent")
        this.$refs.trackTable.$refs.utable.setCurrentRow(currentData);
        // 表格滚动条: top,left -> 距离顶部,左侧距离,不传值默认为0
        // 54为单位格的高度，-3为了将滚动条调整到视图中间位置
        this.$refs.trackTable.$refs.utable.pagingScrollTopLeft((currentData.customizeId - 3) * 30, 0);
        // 处理定位
        this.setSinglePointText(currentData);
        this.setFixInfo([currentData])
      }
    },
    /**
     * 设置车辆图标
     * 设置地图
     */
    setTrackElement(_opts) {
      let opts = _opts || {};
      this.map = opts.map;
      this.AMap = opts.amap;
      this.CarMarker = opts.car;
      this.PathMarker = opts.path;
      console.time('设置TrackData');
      this.TrackData = Object.freeze(opts.track);
      console.timeEnd('设置TrackData');
      console.time('设置Player.coors');
      this.Player.coors = Object.freeze(this.formatPathData(opts.track));
      console.timeEnd('设置Player.coors');
      console.time('设置labelTypeData, DisContPathPass');
      this.labelTypeData = opts.labelTypeData;
      this.DisContPathPass = opts.disCont;
      console.timeEnd('设置labelTypeData, DisContPathPass');
      // 点击地图空白区域，取消单个marker的文字标注
      // this.map.on('click', (e) => {
      //   this.clearSignalPointText();
      // });
      // this.map.on('zoomchange', (e) => {
      //   let zoom = this.map.getZoom();
      // });
      // todo 暂时屏蔽
      // this.CarMarker.on('moving', (e)=>{
      //   const markerDom = e.target.dom;
      //   let bgDom = markerDom.getElementsByClassName('follow-marker-bg')[0];
      //   bgDom.style.transform = `rotate(${e.target['_style'].rotate}deg)`;
      //   this.presentLocation = [e.passedPath[e.passedPath.length - 1].lng, e.passedPath[e.passedPath.length - 1].lat];
      //   // 设置地图位置
      //   if (!this.isInBound(this.presentLocation)) {
      //     this.map.panTo(this.presentLocation);
      //   }
      //   if (this.Player.index !== e.passedPath.length - 2 + this.schedule ) {
      //     this.Player.index = e.passedPath.length - 2 + this.schedule;
      //     this.carMoving(this.Player.index);
      //   }
      // });
      // this.CarMarker.on('movealong', ()=>{
      //   if (this.Player.index !== this.Player.coors.length - 1) {
      //     this.carMoving(this.Player.coors.length - 1);
      //   }
      //   this.setPlayerStatus(false);
      //   this.schedule = 0;
      //   this.Player.index = 0;
      //   this.presentLocation = [];
      //   this.IsPlaying = false;
      // });
    },
    /**
     * 设置轨迹不连续的路段
     */
    // setDisCont (data) {
    //   data && data.forEach(points => {
    //     let markerCollection = [];
    //     // this.DisContPathPass.setPath(path);
    //     for (let i = 0; i < points.length; i++) {
    //       let midNum = parseInt(points[i].length / 2);
    //       let linePath = new this.AMap.Polyline({
    //         path: points[i],
    //         strokeColor: '#ff2d51',
    //         strokeOpacity: 1,
    //         strokeWeight: 5,
    //         showDir: true,
    //         zIndex: 50,
    //         bubble: true
    //       });
    //       let lineText = new this.AMap.Text({
    //         position: points[i][midNum],
    //         title: points[i][midNum],
    //         // text: points[i][midNum],
    //         zIndex: 50
    //       });
    //       linePath.on('mouseover', (e) => {
    //         console.log(e.lnglat);
    //       });
    //       markerCollection.push([linePath, lineText]);
    //     }
    //     this.trackPathGroups = new this.AMap.OverlayGroup(markerCollection);
    //     this.map.add(this.trackPathGroups);
    //   });
    // },
    /**
     * 设置轨迹漂移的路段
     */
    // setDrift (points) {
    //   let markerCollection = [];
    //   for (let i = 0; i < points.length; i++) {
    //     let midNum = parseInt(points[i].length / 2);
    //     let linePath = new this.AMap.Polyline({
    //       path: points[i],
    //       strokeColor: '#ffd700',
    //       strokeOpacity: 1,
    //       strokeWeight: 5,
    //       showDir: true,
    //       zIndex: 50,
    //       bubble: true
    //     });
    //     let lineText = new this.AMap.Text({
    //       position: points[i][midNum],
    //       title: points[i][midNum],
    //       // text: points[i][midNum],
    //       zIndex: 50
    //     });
    //     markerCollection.push([linePath, lineText]);
    //   }
    //   this.driftPathGroups = new this.AMap.OverlayGroup(markerCollection);
    //   this.map.add(this.driftPathGroups);
    // },
    /**
     * 设置已经过的路线
     */
    // setPassPath (_coor, _index) {
    //   let path = Array.from(this.Player.coors);
    //   path.length = _index;
    //   path.push(_coor);
    //   this.PathMarker.setPath(path);
    // },
    /**
     *  小车移动
     */
    // carMoving (_index) {
    //   let historyInfo = this.TrackData[_index];
    //   if (_index && this.TrackData[_index - 1].speed !== historyInfo.speed) {
    //     const markerDom = this.CarMarker.dom;
    //     let bgDom = markerDom.getElementsByClassName('follow-marker-bg')[0];
    //     this.labelTypeData.bgUrl = historyInfo.speed ? '/bdsplatform/static/images/pic/move.png' : '/bdsplatform/static/images/pic/static.png';
    //     bgDom.style.backgroundImage = `url(${this.labelTypeData.bgUrl})`;
    //   }
    //   // 更新信息
    //   historyInfo.index = _index;
    //   historyInfo.total = this.TrackData.length - 1;
    //   // historyInfo.status = status[historyInfo.locState].text;
    //   // historyInfo.playerSpeed = this.Player.speed;
    //   this.setInfoData(historyInfo);
    //   this.$refs.trackTable.$refs.utable.setCurrentRow(this.TrackData[_index]);
    //   // 表格滚动条: top,left -> 距离顶部,左侧距离,不传值默认为0
    //   // 54为单位格的高度，-3为了将滚动条调整到视图下方位置
    //   this.$refs.trackTable.$refs.utable.pagingScrollTopLeft((this.TrackData[_index].customizeId - 3) * 54, 0);
    // },
    /**
     * 判断是否在地图范围内
     * 在 返回 true
     */
    // isInBound (_lnglat) {
    //   const bounds = this.map.getBounds();
    //   const NorthEast = bounds.getNorthEast();
    //   const SouthWest = bounds.getSouthWest();
    //   const SouthEast = [NorthEast.lng, SouthWest.lat];
    //   const NorthWest = [SouthWest.lng, NorthEast.lat];
    //   const path = [[NorthEast.lng, NorthEast.lat], SouthEast, [SouthWest.lng, SouthWest.lat], NorthWest];
    //   const isPointInRing = this.AMap.GeometryUtil.isPointInRing(_lnglat, path);
    //   return isPointInRing;
    // },
    switchTimerSpeedString() {
      switch (this.playSpeed) {
      case 8:
        this.playSpeedString = 'X 8';
        break;
      case 4:
        this.playSpeedString = 'X 4';
        break;
      case 2:
        this.playSpeedString = 'X 2';
        break;
      case 1:
        this.playSpeedString = 'X 1';
        break;
      case 0.5:
        this.playSpeedString = 'X 0.5';
        break;
      case 0.25:
        this.playSpeedString = 'X 0.25';
        break;
      case 0.125:
        this.playSpeedString = 'X 0.125';
        break;
      default:
        break;
      }
    },
    onTrackPlanelCancel(flag) {
      this.$emit('onTrackPlanelCancel');
      this.clearCEPath();
      this,this.clearOverlays();
      // 更换车辆不清除时间
      // if (!flag) {
      //   this.search.s_time = null;
      //   this.search.e_time = null;
      // }
      this.$refs.trackTable.setTableData([]);
    },
    // onCarHistoryPlanelShow () {
    //   this.$emit('onCarHistoryPlanelShow');
    // },
    onRangeMove (value) {
      // console.log('-> onRangeMove', this.TrackData[value]);
      this.setFixInfo([this.TrackData[value]]);
      this.Track.change(value);
    },
    // 暂停
    onPlayerPause() {
      this.setPlayerStatus(false);
      this.Track.suspend();
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      // this.CarMarker.stopMove(); // 终止动画
    },
    // 播放减速
    onPlayerFastBack() {
      if (!this.Track) return;
      if (this.canCheck) {
        return;
      }
      // 最低速度限制
      if (this.playSpeed === 0.125) {
        return;
      }
      this.playSpeed = this.playSpeed / 2;
      console.log('-> this.playSpeed', this.playSpeed);
      this.timerSpeed += 150;
      this.switchTimerSpeedString();
      if (this.IsPlaying) {
        if (this.timer) {
          clearTimeout(this.timer);
          this.timer = null;
        }
        // this.CarMarker.stopMove(); // 终止动画
        // this.$nextTick(() => {
        this.Track.setMultiple(this.playSpeed);
        // });
      }
    },
    // 播放开始
    onPlayerPlay() {
      if (!this.Track) return;
      this.setPlayerStatus(true);
      this.Track.play();
      if ((this.Player.index && this.Player.index !== this.TrackData.length - 1) || this.presentLocation.length) {
        let list;
        this.schedule = this.Player.index;
        if (this.presentLocation.length) { // 手动点击暂停或控制速度时
          list = this.Player.coors.slice(this.Player.index + 1);
          list.unshift(this.presentLocation);
        }
        else { // 滑动进度条时
          list = this.Player.coors.slice(this.Player.index);
        }
        // todo 让marker移动
        // this.$nextTick(() => {
        //   this.CarMarker.moveAlong(list, {
        //     duration: this.playSpeed,
        //     autoRotation: true
        //   });
        // });
      }
      else {
        this.schedule = 0;
        // this.CarMarker.moveAlong(this.Player.coors, {
        //   duration: this.playSpeed,
        //   autoRotation: true
        // });
        // this.carMoving(0);
      }
    },
    // 播放加速
    onPlayerFastFront() {
      if (!this.Track) return;
      if (this.canCheck) {
        return;
      }
      // 最高速度限制
      if (this.playSpeed === 8) {
        return;
      }
      this.playSpeed = this.playSpeed * 2;
      console.log('-> this.playSpeed', this.playSpeed);
      this.timerSpeed -= 150;
      this.switchTimerSpeedString();
      if (this.IsPlaying) {
        // if (this.timer) {
        //   clearTimeout(this.timer);
        //   this.timer = null;
        // }
        // this.CarMarker.stopMove(); // 终止动画
        // this.$nextTick(() => {
        this.Track.setMultiple(this.playSpeed);
        // });
      }
    },
    // 播放停止
    onPlayerStop(type = false) {
      /* if (this.canCheck) {
        return;
      } */
      if (!this.Track) return;
      console.log('-> 停止');
      this.Track.stop();
      this.Track.change(0);
      // if (this.timer) {
      //   clearTimeout(this.timer);
      //   this.timer = null;
      // }
      // // this.CarMarker.stopMove(); // 终止动画
      this.setPlayerStatus(false);
      this.IsPlaying = false;
      // // 重置数据
      // this.carMoving(0);
      this.Player.index = 0;
      this.HistoryRangeCE = 0;
      this.schedule = 0;
      this.presentLocation = [];
      // type为true时才是终止轨迹回放操作，将marker放置初始位置，false代表外部调用，只是用来重置值
      if (type) {
        let trackIndex = this.TrackData[0];
        let lnglat = [trackIndex.longitude, trackIndex.latitude];
        this.map.setZoomAndCenter(lnglat, 10);
      }
    },
    // trackRangeMove (e, type) {
    //   this.presentLocation = [];
    //   // 进度改变
    //   this.Player.index = e;
    //   this.carMoving(this.Player.index);
    //   let trackIndex = this.TrackData[e];
    //   let lnglat = [trackIndex.longitude, trackIndex.latitude];
    //   this.CarMarker.setPosition(lnglat);
    //   this.map.setCenter(lnglat);
    //   // type = range不触发动画
    //   if (this.IsPlaying && type !== 'range') {
    //     this.onPlayerPlay();
    //   }
    // },
    // onRangeMove (_rangeVal, type) {
    // //   console.info('--> onRangeMove',_rangeVal);
    //   if (!this.SpeedsArray.length) {
    //     return;
    //   }
    //   let range = _rangeVal;
    //   // 得到位置百分数 算出位置的时间
    //   let timeStart = this.SpeedsArray[0].value[0];
    //   let timeEnd = this.SpeedsArray[this.SpeedsArray.length - 1].value[0];
    //   let timeDistance = timeEnd - timeStart;
    //   let indexTime = range * timeDistance + timeStart;
    //
    //   let date = new Date();
    //   date.setTime(indexTime);
    //   // 根据时间遍历出点
    //   for (let i = 0; i < this.SpeedsArray.length; i++) {
    //     const element = this.SpeedsArray[i];
    //     if (element.value[0] === indexTime) {
    //       let index = i;
    //       this.trackRangeMove(index, type);
    //       return i;
    //     } else if (element.value[0] > indexTime) {
    //       // this.$emit('onRangeMove',i-1);
    //       let index = i - 1 > 0 ? i - 1 : 0;
    //       this.trackRangeMove(index, type);
    //       return i;
    //     }
    //   }
    // },
    // 导出设备轨迹(新)
    onExportDeviceData() {
      this.$emit('onExportDeviceData');
    },
    // 获取颜色value
    queryColor(val) {
      const data = this.dict.licenceColor.find((item) => item.label === val);
      return data && data.value;
    },
    /**
     * 获取完整率数据
     */
    // getIntegrityData (val) {
    //   let parme = {
    //     st: val.start_time,
    //     et: val.end_time,
    //     licence_plate: val.licence_plate ? val.licence_plate : undefined,
    //     licence_color: val.licence_color ? val.licence_color : undefined,
    //     phone: val.phone ? val.phone : undefined
    //   };
    //   trackRatio(parme).then(res => {
    //     let trackInfo = {};
    //     this.integrityRatio = res.data.ratio;
    //     trackInfo = {
    //       totalMile: res.data.totalMile,
    //       contMile: res.data.contMile,
    //       disContCnt: res.data.disContCnt,
    //       ratio: res.data.ratio,
    //       driftCounts: res.data.drift ? res.data.drift.length : 0,
    //       validTotal: res.data.validTotal,
    //       invalidTotal: res.data.invalidTotal,
    //       repayTotal: res.data.repayTotal,
    //       statisticsDuration: res.data.statisticsDuration,
    //       parkingDuration: res.data.parkingDuration,
    //       maxSpeed: res.data.maxSpeed,
    //       averageSpeed: res.data.averageSpeed
    //     };
    //     this.$parent.trackInfo = trackInfo;
    //     let disCont = res.data.disCont || [];
    //     let drifts = res.data.drift || [];
    //     // 保存
    //     this.trackPath = [];
    //     this.driftPath = [];
    //     // 不完整轨迹点
    //     let trackParagraph = [];
    //     for (let i = 0; i < disCont.length; i++) {
    //       let points = disCont[i].pos;
    //       trackParagraph[i] = [];
    //       points.forEach(item => {
    //         let coor = new this.AMap.LngLat(item.lng, item.lat);
    //         trackParagraph[i].push(coor);
    //       });
    //     }
    //     if (trackParagraph && trackParagraph.length > 0) {
    //       this.trackPath.push(trackParagraph);
    //     }
    //     // 漂移轨迹点
    //     this.driftCounts = drifts.length;
    //     let driftParagraph = [];
    //     for (let i = 0; i < drifts.length; i++) {
    //       let points = drifts[i].pos;
    //       points.forEach(item => {
    //         let coor = new this.AMap.LngLat(item.lng, item.lat);
    //         driftParagraph.push(coor);
    //       });
    //     }
    //     if (driftParagraph && driftParagraph.length > 0) {
    //       this.driftPath.push(driftParagraph);
    //     }
    //     this.$forceUpdate();
    //   }).catch(res => {
    //     console.log(res);
    //   });
    //   // 如果完整率导致的轨迹播放有问题，可在此处处理
    //   /* setTimeout(() => {
    //     this.onPlayerStop();
    //   }, 200); */
    // },
    /**
     * 清除轨迹相关
     */
    clearOverlays() {
      this.showLocationPoint = false;
      this.canCheck = true;
      this.integrityChecked = false;
      this.driftChecked = false;
      this.showTimeSpeed = false;
      this.showAddress = false;
      this.$parent.showWatchCard = false;
      this.trackPath = [];
      this.driftPath = [];
      this.integrityRatio = 1;
      this.driftCounts = 0;
      if (this.PathMarker) {
        // this.map.remove(this.PathMarker);
        this.PathMarker.hide();
        // 清除轨迹
      }
      if (this.trackPathGroups) {
        this.trackPathGroups.clearOverlays();
        this.map.remove(this.trackPathGroups);
      }
      if (this.driftPathGroups) {
        this.driftPathGroups.clearOverlays();
        this.map.remove(this.driftPathGroups);
      }
      if (this.trackPointGroups) {
        // this.$refs.CarSearch.clearSelect();
        this.trackPointGroups.clear();
        // this.trackPointGroups.clearOverlays();
        this.map.remove(this.trackPointGroups);
        this.trackPointGroups = null;
      }
      if (this.timeSpeedGroups) {
        this.timeSpeedGroups.clear();
        // this.timeSpeedGroups.clearOverlays();
        this.map.remove(this.timeSpeedGroups);
        this.timeSpeedGroups = null;
      }
      if (this.addressGroups) {
        this.addressGroups.clear();
        // this.addressGroups.clearOverlays();
        this.map.remove(this.addressGroups);
        this.addressGroups = null;
      }
      this.clearSignalPointText();
    },
    clearCEPath() {
      if (this.Track) {
        this.map.removeLayer(this.pathLayer);
        this.Track = null;
        this.pathLayer = null;
      }
      if (this.WebGLPointsLayer) {
        this.AMap.TileLayer.clearLayer(this.WebGLPointsLayer);
        this.WebGLPointsLayer = null;
      }
      this.infoWindow && this.map.olMap.removeOverlay(this.infoWindow);
    },
    /**
     * 轨迹加载
     */
    setTrackPath(coor) {
      this.clearCEPath();
      console.log('CE-轨迹加载');
      if (!this.pathLayer) {
        this.pathLayer = MayMap.TileLayer.LabelsLayer();
        this.map.addLayer(this.pathLayer);
      }
      console.time('设置轨迹');
      this.Track = new MayMap.Track({
        layer: this.pathLayer, //图层
        data: coor, //数据
        interval: 1, //两点更新的时间间隔
        multiple: this.playSpeed, //播放速度
        // imgUrl: '/bdsplatform/static/images/test/CE-TEST.png', //自定义图标地址 默认汽车
        imgUrl:'/bdsplatform/static/images/pic/carMove.png', //自定义图标地址 默认汽车
        imgScale: 0.18, //自定义图标大小 默认1倍
        // imgAnchor: [0.51, 0.4], //自定义图标位置 默认[0.45,0.55]
        imgRotation: 0, //自定义图标旋转角 默认0
      });
      console.timeEnd('设置轨迹');
      this.canCheck = false;
      this.trackDataMaxNum = coor.length - 1;
      this.Track.moving((index) => {
        this.HistoryRangeCE = index;
        this.setFixInfo([this.TrackData[index]])
        // 表格滚动条: top,left -> 距离顶部,左侧距离,不传值默认为0
        // 30为单位格的高度，-3为了将滚动条调整到视图下方位置
        this.$refs.trackTable.$refs.utable.pagingScrollTopLeft((this.TrackData[index]?.customizeId - 3) * 30, 0);
        this.$refs.trackTable.$refs.utable.setCurrentRow(this.TrackData[index]);
        if (index + 1 === this.trackDataMaxNum) {
          setTimeout(() => {
            this.HistoryRangeCE = index + 1;
            this.IsPlaying = false;
          }, 500);
        }
        // 考虑手动滑动滚动条可能会使HistoryRangeCE等于trackDataMaxNum, 因此添加判断HistoryRangeCE = trackDataMaxNum
        if (this.HistoryRangeCE === this.trackDataMaxNum) {
          this.IsPlaying = false;
        }
      });
      this.showLocationPoint && this.setTrackPoint(this.truckData);
    },
    /**
     * 定位点加载
     */
    setTrackPoint (currentData) {
      const style = {
        symbol: {
          symbolType: 'image',
          src: require(`@/assets/images/track/track-spring.png`),
          size: [
            20,
            20
          ],
          textureCoord: [
            'match',
            [
              'get',
              'type'
            ],
            // 1定位、2停车、3报警
            1, [0.0, 0.0, 0.3333, 1],
            2, [0.3333, 0.0, 0.6666, 1],
            3, [0.6666, 0.0, 1, 1],
            [0.0, 0.0, 0.3333, 1]
          ],
          rotateWithView: true,//禁止旋转
          offset: [
            0,
            10
          ]
        }
      };
      this.WebGLPointsLayer = new this.AMap.TileLayer.WebGLPointsLayer({
        style,
        zIndex: 5
      });
      this.map.addLayer(this.WebGLPointsLayer);
      const points = {
        type: 'FeatureCollection',
        features: (currentData || []).map(item => {
          return {
            geometry: {
              type: 'Point',
              coordinates: [item.longitude, item.latitude]
            },
            properties: { data: item, type: 1 },
            type: 'Feature'
          };
        })
      };
      this.AMap.TileLayer.addGeoJSONs(this.WebGLPointsLayer, points);
    },
    setInfoPoint (currentData) {
      const style = {
        symbol: {
          symbolType: 'image',
          src: require(`@/assets/images/track/track-spring.png`),
          size: [
            20,
            20
          ],
          textureCoord: [
            'match',
            [
              'get',
              'type'
            ],
            // 1定位、2停车、3报警
            1, [0.0, 0.0, 0.3333, 1],
            2, [0.3333, 0.0, 0.6666, 1],
            3, [0.6666, 0.0, 1, 1],
            [0.0, 0.0, 0.3333, 1]
          ],
          rotateWithView: true,//禁止旋转
          offset: [
            0,
            10
          ]
        }
      };
      this.InfoLayer = new this.AMap.TileLayer.WebGLPointsLayer({
        style,
        zIndex: 55
      });
      this.map.addLayer(this.InfoLayer);
      const points = {
        type: 'FeatureCollection',
        features: (currentData || []).map(item => {
          return {
            geometry: {
              type: 'Point',
              coordinates: [item.longitude, item.latitude]
            },
            properties: { data: item, type: 1 },
            type: 'Feature'
          };
        })
      };
      this.AMap.TileLayer.addGeoJSONs(this.InfoLayer, points);
    },
    /**
     * 时间速度加载
     */
    // setTimeSpeed (currentData) {
    //   if (this.timeSpeedGroups) {
    //     this.timeSpeedGroups.hide();
    //     return;
    //   }
    //   this.timeSpeedGroups = new this.AMap.LabelsLayer({
    //     zooms: [2, 26],
    //     collision: false
    //   });
    //   for (let i = 0; i < currentData.length; i++) {
    //     let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
    //     let colorStyle = this.getColor(currentData[i]);
    //     let timeSpeedText = new this.AMap.LabelMarker({
    //       position: position,
    //       text: {
    //         content: this.$moment(currentData[i].time * 1000).format('YYYY-MM-DD HH:mm:ss') + ', ' + currentData[i].speed + 'km/h',
    //         direction: 'right',
    //         offset: [6, -18],
    //         style: {
    //           'fontSize': 12,
    //           'backgroundColor': 'rgba(255, 255, 255, 0.1)',
    //           'fillColor': colorStyle,
    //           'strokeColor': 'white',
    //           'strokeWidth': 4
    //         }
    //       }
    //     });
    //     this.timeSpeedGroups.add(timeSpeedText);
    //   }
    //   this.map.add(this.timeSpeedGroups);
    // },
    /**
     * 地址加载
     */
    // setAddress (currentData) {
    //   if (this.addressGroups) {
    //     this.addressGroups.hide();
    //     return;
    //   }
    //   this.addressGroups = new this.AMap.LabelsLayer({
    //     zooms: [2, 26],
    //     collision: false
    //   });
    //   for (let i = 0; i < currentData.length; i++) {
    //     let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
    //     let colorStyle = this.getColor(currentData[i]);
    //     let addressText = new this.AMap.LabelMarker({
    //       position: position,
    //       text: {
    //         content: currentData[i].locAddr,
    //         title: currentData[i].locAddr,
    //         direction: 'right',
    //         offset: [6, -4],
    //         style: {
    //           'fontSize': 12,
    //           'backgroundColor': 'rgba(255, 255, 255, 0.1)',
    //           'fillColor': colorStyle,
    //           'strokeColor': 'white',
    //           'strokeWidth': 4
    //         }
    //       }
    //     });
    //     this.addressGroups.add(addressText);
    //   }
    //   this.map.add(this.addressGroups);
    // },
    /**
     * 单个marker点文本设置
     */
    async setSinglePointText (currentData) {
      this.clearSignalPointText()
      this.setInfoPoint([currentData])
      const { code, data } = await batchAddr([{
        longitude: Number(currentData.longitude),
        latitude: Number(currentData.latitude),
        id: 0
      }]);
      if (code === 200 && data?.length) {
        currentData.locAddr = data[0].locAddr;
      }
      // 预先引入图片资源
      // const stopIconUrl = require('@/assets/images/car/track-stop2.svg');
      // let info = [];
      // if(currentData.isStopPoint){
      //   info.push("<div style='display: flex; padding:8px 8px 0px 8px;justify-content: space-between;'><h4><img src='" + stopIconUrl + "' style='width:20px;height:20px;margin-right:5px;vertical-align:middle;'>"  + `${currentData.targetName}` + "</h4><h4 class='info-window-head-icon' style='font-size: 18px; cursor: pointer;'>×</h4></div>");
      // }else{
      //   info.push("<div style='display: flex;padding:8px 8px 0px 8px; justify-content: space-between;'><h4>" + `${currentData.targetName}` + "</h4><h4 class='info-window-head-icon' style='font-size: 18px; cursor: pointer;'>×</h4></div>");
      // }
      // info.push("<p class='input-item' style='margin: 5px'>定位信息：" + `${currentData.speed}` + 'km/h,' + `${this.getDirection(currentData.bearing)}`);
      // info.push("<p class='input-item' style='margin: 5px'>经纬度：" + `${this.handlePosition(currentData.longitudeTable)}` + ',' + `${this.handlePosition(currentData.latitudeTable)}`);
      // info.push("<p class='input-item' style='margin: 5px'>总里程：" + `${currentData.mileage}` + 'km');
      // // info.push("<p class='input-item' style='margin: 10px'>状态：" + `${currentData.stateAcc}`);
      // if(currentData.isStopPoint){
      //   info.push("<p class='input-item' style='margin: 5px'>停靠开始时间：" + `${this.parseTimes(currentData.stopStartTime)}`);
      //   info.push("<p class='input-item' style='margin: 5px'>停靠结束时间：" + `${this.parseTimes(currentData.stopEndTime)}`);
      //   info.push("<p class='input-item' style='margin: 5px'>停靠时长：" + `${this.handlerTime(currentData.stopDuration)}`);
      // }else{
      //   info.push("<p class='input-item' style='margin: 5px'>定位时间：" + `${this.parseTimes(currentData.time)}`);
      // }
      // info.push("<p class='input-item' style='max-width:400px;padding: 0px 5px 8px 5px'>地址：" + `${currentData.locAddr || '-'}`);
      // this.infoWindow = MayMap.TileLayer.InfoWindow({
      //   center: [currentData.longitude, currentData.latitude],
      //   html: info.join(''),
      //   offset: [0, -35]
      // });
      // 获取关闭按钮的 DOM 元素
      // const closeButtonDom = document.querySelector('.info-window-head-icon');
      // if (closeButtonDom) {
      //   // 移除点击事件监听器
      //   closeButtonDom.removeEventListener('click', (e) => {
      //     this.infoWindow && this.map.olMap.removeOverlay(this.infoWindow);
      //     this.AMap.TileLayer.clearLayer(this.InfoLayer);
      //   });
      //   // 添加点击事件监听器
      //   closeButtonDom.addEventListener('click', (e) => {
      //     this.infoWindow && this.map.olMap.removeOverlay(this.infoWindow);
      //     this.AMap.TileLayer.clearLayer(this.InfoLayer);
      //   });
      // }
    },
    goToPoint (row) {
      this.setSinglePointText(row);
      this.onRangeMove(row.customizeId-1)
      setTimeout(() => {
        this.map.setZoomAndCenter([row.longitude, row.latitude], 18);
      }, 500);
    },
    setFixInfo(row){
      this.$emit('setFixInfo' , row);
    },
    setTableData(data) {
      this.$refs.trackTable.setTableData(data);
      this.truckData = Object.freeze(data);
      this.SpeedsArray = this.formatChartData(data);
    },
    getColor(item) {
      let color = item.speed ? '#036eb8' : '#f4a11e';
      if (item.alarmType) {
        color = '#f41e1e';
      }
      return color;
    },
    clearSignalPointText() {
      if (this.infoWindow) {
        this.map.olMap.removeOverlay(this.infoWindow);
        this.infoWindow = null;
      }
      if (this.InfoLayer) {
        this.AMap.TileLayer.clearLayer(this.InfoLayer);
        this.map.olMap.removeOverlay(this.InfoLayer);
        this.InfoLayer = null;
      }
    },
    carSelected(selectedCar) {
      // 车牌有多个颜色时不赋值
      if (selectedCar.licenceColor !== 0) {
        this.search.licenceColor = selectedCar.licenceColor;
      }
      this.$parent.selectedRow(selectedCar);
    },
    exitCard() {
      this.driftChecked = false;
      this.integrityChecked = false;
    },
    // 处理时间格式化为"x天x小时x分x秒"
    handlerTime(seconds) {
      if (seconds === 0) return "0秒";
      const duration = this.$moment.duration(seconds, 'seconds');
      const days = duration.days();
      const hours = duration.hours();
      const minutes = duration.minutes();
      const secs = duration.seconds();
      let result = '';
      if (days > 0) result += days + '天';
      if (hours > 0) result += hours + '小时';
      if (minutes > 0) result += minutes + '分';
      if (secs > 0) result += secs + '秒';
      return result;
    },
    parseTimes(time) {
      return this.$moment(time * 1000).format('YYYY-MM-DD HH:mm:ss');
    },
    getDirection(bearing) {
      if (bearing === 0) {
        return '正北';
      }
      if (bearing) {
        switch (true) {
        case bearing === 90:
          return '正东';
        case bearing === 180:
          return '正南';
        case bearing === 270:
          return '正西';
        case bearing > 0 && bearing < 90:
          return '东北';
        case bearing > 90 && bearing < 180:
          return '东南';
        case bearing > 180 && bearing < 270:
          return '西南';
        case bearing > 270 && bearing < 360:
          return '西北';
        default:
          return '无';
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
@import "../../../assets/less/variables.less";

.CarTrackInfo {
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  color: #303133;
  font-size: 12px;
  min-height: 230px;
}

.car-track-info {
  height: 100%;
}

.flex_box {
  display: flex;
  flex-direction: column;
}

.planel_a {
  width: 240px;

  .planel_ab {
    div {
      margin-right: 10px;
    }
  }
}

.planel_text {
  margin-left: 30px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;

  div {
    margin-bottom: 2px;
  }

  span {
    color: #fda23c;
  }
}

.planel_b {
  flex: 1;
}

.planel_chart {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.planel_ab {
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.planel_c {
  width: 30px;
  display: flex;
  justify-content: center;
}

// .history_range{
//   width: 100%;
//   margin-top: 10px;
//   margin-right: 10px;
// }

#chart {
  width: 100%;
  height: 100%;
}

@player_icon_size: 16px;
.icon_player_pause {
  height: @player_icon_size;
  width: @player_icon_size;
  display: inline-block;
  background: no-repeat center url(../../../assets/images/track/player-pause.png);
  background-size: @player_icon_size;
}

.icon_player_pause:hover {
  filter: contrast(200%);
  cursor: pointer;
}

.icon_player_play {
  height: @player_icon_size;
  width: @player_icon_size;
  display: inline-block;
  background: no-repeat center url(../../../assets/images/track/player-play.png);
  background-size: @player_icon_size;
}

.icon_player_play:hover {
  filter: contrast(200%);
  cursor: pointer;
}

.icon_player_fast_back {
  height: @player_icon_size;
  width: @player_icon_size;
  display: inline-block;
  background: no-repeat center url(../../../assets/images/track/player-fast.png);
  transform: scaleX(-1);
  background-size: @player_icon_size;
}

.icon_player_fast_back:hover {
  filter: contrast(200%);
  cursor: pointer;
}

.icon_player_fast_front {
  height: @player_icon_size;
  width: @player_icon_size;
  display: inline-block;
  background: no-repeat center url(../../../assets/images/track/player-fast.png);
  background-size: @player_icon_size;
}

.icon_player_fast_front:hover {
  filter: contrast(200%);
  cursor: pointer;
}

.icon_player_stop {
  height: @player_icon_size;
  width: @player_icon_size;
  display: inline-block;
  background: no-repeat center url(../../../assets/images/track/player-stop.png);
  background-size: @player_icon_size;
}

.icon_player_stop:hover {
  filter: contrast(200%);
  cursor: pointer;
}

.planelCancel {
  color: rgba(100, 100, 100, 0.9);
  font-weight: bolder;
  cursor: pointer;
}

.planelCancel:hover {
  color: #118de3;
}

/** range */
.player_range {
  -webkit-appearance: none;
  border-radius: 2px;
  width: 300px;
  height: 4px;
  // background-image:-webkit-linear-gradient(left ,#f22 0%,#f22 20%,#fff 20%, #fff 100%);
  box-shadow: inset #c0c4cc 0 0 5px;
  outline: none;
  transition: .1s;
  outline: none;
}

.player_range::-webkit-slider-thumb {
  -webkit-appearance: none;
  border: 3px solid #ffffff;
  width: 16px;
  height: 16px;
  background: #176ac0;
  border-radius: 50%;
  transition: .1s;
}

.player_range::-webkit-slider-thumb:hover,
.player_range::-webkit-slider-thumb:active {
  width: 16px;
  height: 16px;
}

/** -----range------- */
.info {
  position: absolute;
  bottom: 0px;
  margin: 5px;
  left: calc(@selectComponentContainerWidth + 2 * @xhSpacingBase);
  width: calc(100% - @selectComponentContainerWidth - 3 * @xhSpacingBase);
  overflow-y: hidden;
  overflow-x: hidden;
  border-radius: @xhBorderRadiusBase;
  background-color: #ffffff;
  padding: 1px;
  z-index: 1000;
  font-size: 12px;
}

.info-all {
  //position: absolute;
  //bottom: 11px;
  // bottom: 0px;
  // margin: 0px calc(@xhSpacingBase);
  // left: calc(@xhSpacingBase);
  //width: calc(100% - @xhSpacingBase);
  height: 100%;
  overflow-y: hidden;
  overflow-x: hidden;
  border-radius: @xhBorderRadiusBase;
  background-color: #ffffff;
  padding: 3px;
  z-index: 1000;
  font-size: 12px;
  display: flex;
  flex-direction: column;
}

.el-col {
  border-radius: 4px;
}

.grid-content {
  display: flex;
  align-content: center;
  border-radius: 4px;
  min-height: 16px;
}

.row-bg {
  height: 30px;
  padding: 0px 5px;
  background-color: white;
  display: flex;
  align-items: center;
  font-size: 14px;
}

.config-btn {
  display: flex;
}

.config-label {
  padding: 0 20px;
}

.config-range {
  flex: 1;
  display: flex;
  align-items: center;
}

.history_range {
  flex: 1;
}
.config-export{
  ::v-deep .el-button--mini{
    padding: 5px 15px;
    margin-bottom: 2px;
  }
}
.config-check {
  padding: 0 25px;
  // /deep/ .el-checkbox__inner{
  //   border: 1px solid #176AC0;
  // }
  // /deep/ .el-checkbox__input.is-checked .el-checkbox__inner{
  //   background-color: #176AC0;
  //   border-color: #176AC0;
  // }
  // /deep/ .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after{
  //   border-color: white;
  // }
}

// .carHistorySearchDatePicker .available span{
//   background-color: rgba(255, 0, 0, 0.2);
// }
.search_carname {
  margin-right: 4px;
  margin-top: 5px;
}

.search_form_centent_carid {
  color: #118de3;
  font-weight: bolder;
  margin-top: 4px;
  // display: inline-block;
}

.tooltip-item {
  margin-right: 4px;
}

.custom-input-card {
  width: 18rem;
}

.custom-input-card .btn:last-child {
  margin-left: 1rem;
}

.content-window-card {
  position: relative;
  width: 23rem;
  padding: 0.75rem 0 0 1.25rem;
  box-shadow: none;
  bottom: 0;
  left: 0;
}

/deep/ .content-window-card p {
  height: 2rem;
}

// 收起弹框
.shrinkOut, .shrinkIn {
  position: absolute;
  display: block;
  width: 24px;
  height: 24px;
  background: #2398ff;
  bottom: 16px;
  right: 30px;
  font-size: 24px;
  color: #ffffff;
  border-radius: 2px;
  z-index: 30000;
  cursor: pointer;
}

.shrinkIn {
  top: 10px;
  right: 26px;
}

.showDeptBtn {
  margin-right: 10px;
}

.player-icon {
  display: inline-block;
  width: 22px;
  height: 23px;
  padding: 1px;
  border: 1px solid #176ac0;
  margin-right: 4px;
  background-size: 100% 100%;
  cursor: pointer;
}

.player-play {
  background-image: url("../../../assets/images/track/player-play.svg");
}

.player-fast {
  background-image: url("../../../assets/images/track/player-fast.svg");
}

.player-pause {
  background-image: url("../../../assets/images/track/player-pause.svg");
}

.player-slow {
  background-image: url("../../../assets/images/track/player-slow.svg");
}

.player-stop {
  background-image: url("../../../assets/images/track/player-stop.svg");
}

.follow-marker {
  transform: translate(-25px, -25px) scale(1) !important;
}

// 不同分辨率媒体查询样式

@media screen and (max-width: 1500px) {
  // 底部表格
  .row-bg {
    .config-label, .config-check {
      padding: 0 5px;
    }

    .config-check /deep/ .el-checkbox {
      margin-right: 5px;
    }

    .config-range {
      width: 280px;
    }
  }
}
</style>
