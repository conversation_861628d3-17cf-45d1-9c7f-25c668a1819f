<template>
  <div
    v-show="visible"
    ref="info-fix"
    class="info-popup"
    :class="{'collapsed': isCollapsed}"
  >
    <div v-if="!isCollapsed" class="info-header">
      <span>{{currentData?.targetName}}</span>
      <i class="el-icon-close" @click="toggleCollapse"></i>
    </div>
    <div v-if="!isCollapsed" class="info-content">
       <p>定位时间：{{this.parseTimes(currentData?.time)}}</p>
       <p>定位信息：{{currentData?.speed}}km/h,{{currentData?.bearing | getDirection()}}</p>
       <p>经纬度：{{this.handlePosition(currentData?.longitudeTable)}},{{this.handlePosition(currentData?.latitudeTable)}}</p>
       <p>总里程：{{currentData?.mileage}}km</p>
       <p>高程：{{currentData?.altitude}}m</p>
       <p>电量：{{currentData?.charge === null?'-':currentData?.charge}}%</p>
       <p>定位卫星：{{currentData?.gnssNum}}</p>
       <p>通信信号：{{currentData?.wireless}}</p>
       <p v-if="currentData?.isStopPoint">停靠开始时间：{{this.parseTimes(currentData?.stopStartTime)}}</p>
       <p v-if="currentData?.isStopPoint">停靠结束时间：{{this.parseTimes(currentData?.stopDuration)}}</p>
       <p v-if="currentData?.isStopPoint">停靠时长:{{this.handlerTime(currentData?.stopDuration)}}</p>
       <p v-if="currentData?.locAddr">地址：{{currentData?.locAddr}}</p>
    </div>
    <!-- 折叠后显示的小球 -->
    <div v-if="isCollapsed" class="info-ball" @click="toggleCollapse">
      <i class="el-icon-data-analysis"></i>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TrackTable',
  components: {
  },
  props: {
    currentData:{
      type: Array,
      required: true
    },
    dict: {
      type: Object,
      required: true
    },
    visible: {
      type: Boolean,
      default: true
    }
  },
  filters: {
    getDirection (bearing) {
      if (bearing === 0) {
        return '正北';
      }
      if (bearing) {
        switch (true) {
          case bearing === 90:
            return '正东';
          case bearing === 180:
            return '正南';
          case bearing === 270:
            return '正西';
          case bearing > 0 && bearing < 90:
            return '东北';
          case bearing > 90 && bearing < 180:
            return '东南';
          case bearing > 180 && bearing < 270:
            return '西南';
          case bearing > 270 && bearing < 360:
            return '西北';
          default:
            return '无';
        }
      }
    }
  },
  data () {
    return {
      isVisible: this.visible,
      isCollapsed: true // 控制是否折叠为小球
    };
  },
  // 枚举类型处理
  computed: {
    EnumerationTypeHandling () {
      return (dictName, val) => {
        if (this.dict.dict[dictName] && this.dict.dict[dictName][val]) {
          return this.dict.dict[dictName][val].label;
        }
      };
    }
  },
  methods: {
    // 隐藏弹窗
    hidePopup() {
      this.isVisible = false;
      this.$emit('update:visible', false);
    },
    // 切换折叠/展开状态
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed;
    },
    // 处理时间格式化为"x天x小时x分x秒"
    handlerTime(seconds) {
      if (seconds === 0) return "0秒";
      const duration = this.$moment.duration(seconds, 'seconds');
      const days = duration.days();
      const hours = duration.hours();
      const minutes = duration.minutes();
      const secs = duration.seconds();
      let result = '';
      if (days > 0) result += days + '天';
      if (hours > 0) result += hours + '小时';
      if (minutes > 0) result += minutes + '分';
      if (secs > 0) result += secs + '秒';
      return result;
    },
    parseTimes (time) {
      if(time === null || time === undefined ) return '';
      return this.$moment(time * 1000).format('YYYY-MM-DD HH:mm:ss');
    }
  }
};
</script>

<style lang="less" scoped>
  .info-popup {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 230px;
    height: 280px;
    opacity: .9;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 9999;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease-in-out;

    &.collapsed {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #409EFF;
      opacity: 1;
    }

    .info-ball {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      cursor: pointer;
      background-color: var(--gn-color) !important;

      i {
        font-size: 20px;
      }
    }

    .info-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px 10px;
      background-color: var(--gn-color) !important;
      color: #fff;

      i {
        cursor: pointer;
        font-size: 14px;
        &:hover {
          opacity: 0.8;
        }
      }
    }

    .info-content {
      flex: 1;
      padding: 5px 10px;
      overflow-y: auto;
      p {
        margin: 3px 0;
        color: #000;
        // color: var(--gn-color) !important;
        font-size: 12px;
      }
    }
  }
</style>
