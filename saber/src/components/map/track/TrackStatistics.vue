<template>
  <div
    v-show="showWatchCard"
    class="watchCard drag_box"
    draggable="true"
    :style="`right:${elRight}px;top:${elTop}px`"
    @dragstart="dragstart($event)"
    @dragend="dragend($event)"
  >
    <el-card :class="reduceCard?'reduce-box-card':'box-card'">
      <div
        slot="header"
        class="clearfix"
      >
        <span>轨迹统计</span>
        <el-button
          style="float: right"
          size="mini"
          type="danger"
          icon="el-icon-close"
          round
          @click="exitCard"
        />
        <el-button
          style="float: right ; margin-right:10px"
          size="mini"
          type="info"
          icon="el-icon-full-screen"
          round
          @click="reduceCardHandle"
        />
      </div>
      <div class="player_room">
        <div class="work_item_box">
          <span class="work_item_title">总里程：</span>
          <span
            v-if="carInfo.totalMile || carInfo.totalMile === 0"
            class="work_item_value"
          >{{ carInfo.totalMile }} km</span>
        </div>
        <div class="work_item_box">
          <span class="work_item_title">连续里程值：</span>
          <span
            v-if="carInfo.contMile || carInfo.contMile === 0"
            class="work_item_value"
          >{{ carInfo.contMile }} km</span>
        </div>
        <div class="work_item_box">
          <span class="work_item_title">不连续次数：</span>
          <span class="work_item_value">{{ carInfo.disContCnt }}</span>
        </div>
        <div class="work_item_box">
          <span class="work_item_title">完整率：</span>
          <span class="work_item_value">{{ carInfo.ratio }}</span>
        </div>
        <div class="work_item_box">
          <span class="work_item_title">漂移次数：</span>
          <span class="work_item_value">{{ carInfo.driftCounts }}</span>
        </div>
        <div class="work_item_box">
          <span class="work_item_title">有效点：</span>
          <span class="work_item_value">{{ carInfo.validTotal }}</span>
        </div>
        <div class="work_item_box">
          <span class="work_item_title">无效点：</span>
          <span class="work_item_value">{{ carInfo.invalidTotal }}</span>
        </div>
        <div class="work_item_box">
          <span class="work_item_title">补报点个数：</span>
          <span class="work_item_value">{{ carInfo.repayTotal }}</span>
        </div>
        <div class="work_item_box">
          <span class="work_item_title">统计时长：</span>
          <el-tooltip
            :content="carInfo.statisticsDuration"
            placement="top"
          >
            <span class="work_item_value">{{ carInfo.statisticsDuration }}</span>
          </el-tooltip>
        </div>
        <div class="work_item_box">
          <span class="work_item_title">停车时长：</span>
          <el-tooltip
            :content="carInfo.parkingDuration"
            placement="top"
          >
            <span class="work_item_value">{{ carInfo.parkingDuration }}</span>
          </el-tooltip>
        </div>
        <div class="work_item_box">
          <span class="work_item_title">最大速度：</span>
          <span
            v-if="carInfo.maxSpeed || carInfo.maxSpeed === 0"
            class="work_item_value"
          >{{ carInfo.maxSpeed }} km/h</span>
        </div>
        <div class="work_item_box">
          <span class="work_item_title">平均速度：</span>
          <span
            v-if="carInfo.averageSpeed || carInfo.averageSpeed === 0"
            class="work_item_value"
          >{{ carInfo.averageSpeed }} km/h</span>
        </div>
      </div>
    </el-card>
  </div>
</template>
<script>
export default {
  name: 'TrackStatistics',
  props: {
    trackInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    showWatchCard: {
      type: Boolean,
      default: false
    },
    initHeight: {
      type: Number,
      default: 0
    },
    initWidth: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      carInfo: {},
      reduceCard: false,
      startclientX: 0, // 元素拖拽前距离浏览器的X轴位置
      startclientY: 0, // 元素拖拽前距离浏览器的Y轴位置
      elRight: 0, // 元素的右偏移量
      elTop: 70 // 元素的上偏移量
    };
  },
  watch: {
    trackInfo: function (val) {
      this.carInfo = {
        totalMile: val.totalMile,
        contMile: val.contMile,
        disContCnt: val.disContCnt,
        ratio: val.ratio * 100 + '%',
        driftCounts: val.driftCounts,
        validTotal: val.validTotal,
        invalidTotal: val.invalidTotal,
        repayTotal: val.repayTotal,
        statisticsDuration: val.statisticsDuration,
        parkingDuration: val.parkingDuration,
        maxSpeed: val.maxSpeed,
        averageSpeed: val.averageSpeed
      };
    }
  },
  methods: {
    exitCard () {
      this.$parent.showWatchCard = false;
      this.$parent.exitCard();
    },
    reduceCardHandle () {
      this.reduceCard = !this.reduceCard;
    },
    // 拖拽开始事件
    dragstart (e) {
      this.startclientX = e.clientX; // 记录拖拽元素初始位置
      this.startclientY = e.clientY;
    },
    // 拖拽完成事件
    dragend (e) {
      let x = e.clientX - this.startclientX; // 计算偏移量
      let y = e.clientY - this.startclientY;
      this.elRight -= x; // 实现拖拽元素随偏移量移动
      this.elTop += y;
    }
  }
};
</script>

<style lang="less" scoped>
  .watchCard {
    position: absolute;
    right: 0;
    top: 70px;
    margin: 10px;
    width: 300px;
    z-index: 1000;
  }
  .player_room {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .work_item_box{
    margin: 2px;
    width: 250px;
    display: flex;
    align-items: center;
  }
  .work_item_title{
    width: 140px;
    font-size: 14px;
    color: #909399;
  }
  .work_item_value{
    color: #303133;
    font-size: 16px;
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
    flex: 1;
  }
  .reduce-box-card ::v-deep .el-card__body{
    height: 180px;
    overflow-y: scroll
  }
  .reduce-box-card ::v-deep .el-card__header{
    padding: 8px;
  }
  .drag_box{
    user-select: none;
  }
</style>
