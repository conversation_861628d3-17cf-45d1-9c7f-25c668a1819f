<template>
  <div
    ref="work_eltable"
    class="work_eltable"
  >
    <template>
      <u-table
        ref="utable"
        :loading="tableLoading"
        use-virtual
        highlight-current-row
        inverse-current-row
        class="offline-car-tab"
        :data="tableData"
        :header-cell-style="headerStyle"
        :beautify-table="true"
        :border="false"
        :row-height="30"
        :height="tableHeight || 234"
        @row-click="rowClick"
      >
<!--        <u-table-column-->
<!--          prop="customizeId"-->
<!--          :label="getLabel('customizeId')"-->
<!--          align="center"-->
<!--          width="60"-->
<!--          show-overflow-tooltip-->
<!--          :resizable="false"-->
<!--        />-->
        <u-table-column
          type="index"
          :resizable="false"
          width="60"/>
        <u-table-column
          prop="deviceType"
          :label="getLabel('deviceType')"
          align="center"
          width="120"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            <span>{{ EnumerationTypeHandling('bdmDeviceType',scope.row.deviceType) }}</span>
          </template>
        </u-table-column>
        <u-table-column
          prop="deviceCategory"
          :label="getLabel('deviceCategory')"
          align="center"
          width="210"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            <span>{{ EnumerationTypeHandling('bdmDeviceType',scope.row.deviceCategory) }}</span>
          </template>
        </u-table-column>
        <u-table-column
          prop="deviceUniqueId"
          :label="getLabel('deviceUniqueId')"
          align="center"
          width="160"
          show-overflow-tooltip
          :resizable="false"
        />
        <u-table-column
          prop="accStr"
          :label="'ACC状态'"
          align="center"
          width="80"
          show-overflow-tooltip
          :resizable="false"
        />
        <u-table-column
          prop="targetType"
          :label="getLabel('targetType')"
          align="center"
          width="80"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            <span>{{ EnumerationTypeHandling('targetType',scope.row.targetType) | nullValueStr }}</span>
          </template>
        </u-table-column>
        <u-table-column
          prop="targetName"
          :label="getLabel('targetName')"
          align="center"
          width="160"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            {{ scope.row.targetName || $utils.emptymap.targetName }}
          </template>
        </u-table-column>
        <u-table-column
          prop="wireless"
          :label="getLabel('charge')"
          align="center"
          width="90"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.charge === null ? '-' : `${scope.row.charge}%` }}
          </template>
        </u-table-column>
        <u-table-column
          prop="gnssNum"
          :label="getLabel('gnssNum')"
          align="center"
          width="90"
          show-overflow-tooltip
          :resizable="false"
        />
        <u-table-column
          prop="wireless"
          :label="getLabel('wireless')"
          align="center"
          width="90"
          show-overflow-tooltip
          :resizable="false"
        />
        <u-table-column
          prop="time"
          :label="getLabel('time')"
          align="center"
          width="180"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            <span class="table-date-td">{{ parseTimes(scope.row.time) }}</span>
          </template>
        </u-table-column>
        <u-table-column
          prop="speed"
          :label="getLabel('speed')"
          align="center"
          width="80"
          show-overflow-tooltip
          :resizable="false"
        />
        <u-table-column
          prop="longitudeTable"
          :label="getLabel('longitude')"
          align="center"
          width="120"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            {{ handlePosition(scope.row.longitudeTable) }}
          </template>
        </u-table-column>
        <u-table-column
          prop="latitudeTable"
          :label="getLabel('latitude')"
          align="center"
          width="120"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            {{ handlePosition(scope.row.latitudeTable) }}
          </template>
        </u-table-column>
        <u-table-column
          prop="stopStartTime"
          :label="getLabel('stopStartTime')"
          align="center"
          width="180"
        >
          <template slot-scope="scope">
            {{ scope.row.stopStartTime ? parseTimes(scope.row.stopStartTime) : '-' }}
          </template>
        </u-table-column>

        <u-table-column
          prop="stopEndTime"
          :label="getLabel('stopEndTime')"
          align="center"
          width="180"
        >
          <template slot-scope="scope">
            {{ scope.row.stopEndTime ? parseTimes(scope.row.stopEndTime) : '-' }}
          </template>
        </u-table-column>

        <u-table-column
          prop="stopDuration"
          :label="getLabel('stopDuration')"
          align="center"
          width="180"
        >
          <template slot-scope="scope">
            {{ scope.row.stopDuration ? handlerTime(scope.row.stopDuration) : '-' }}
          </template>
        </u-table-column>
        <u-table-column
          prop="bearing"
          :label="getLabel('bearing')"
          align="center"
          width="80"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            {{ scope.row.bearing | getDirection() }}
          </template>
        </u-table-column>
        <u-table-column
          prop="batch"
          label="上传类型"
          align="center"
          width="90"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            {{ getEnumDictLabel('batch', scope.row.batch) }}
          </template>
        </u-table-column>
        <u-table-column
          prop="valid"
          :label="getLabel('valid')"
          align="center"
          width="90"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            {{ scope.row.valid === 0 ? '否' : scope.row.valid === 1 ? '是' : '-' }}
          </template>
        </u-table-column>
        <u-table-column
          prop="posSys"
          :label="getLabel('posSys')"
          align="center"
          width="90"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            {{ scope.row.posSys === 0 ? '卫星定位' : scope.row.posSys === 2 ? 'LBS定位' : '-' }}
          </template>
        </u-table-column>
        <u-table-column
          prop="mileage"
          :label="getLabel('mileage')"
          align="center"
          width="150"
          show-overflow-tooltip
          :resizable="false"
        />
        <u-table-column
          prop="realSpeed"
          :label="getLabel('realSpeed')"
          align="center"
          width="150"
          show-overflow-tooltip
          :resizable="false"
        />
        <u-table-column
          prop="oilMass"
          :label="getLabel('oilMass')"
          align="center"
          width="130"
          show-overflow-tooltip
          :resizable="false"
        />
        <u-table-column
          prop="ioStatus"
          :label="getLabel('ioStatus')"
          align="center"
          width="150"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            {{ scope.row.ioStatus === 1 ? '深度休眠' : scope.row.ioStatus === 2 ? '休眠' : '无' }}
          </template>
        </u-table-column>
        <u-table-column
          prop="locAddr"
          :label="getLabel('locAddr')"
          align="center"
          width="450"
          show-overflow-tooltip
          :resizable="false"
        />
        <el-empty
          slot="empty"
          :image="require('@/assets/images/nodata.png')"
        />
      </u-table>
    </template>
  </div>
</template>

<script>
import getLabel from '@/utils/getLabel';
import { batchAddr } from '@/api/monitoring/track.js';
import { load } from '@amap/amap-jsapi-loader';
export default {
  name: 'TrackTable',
  components: {
  },
  props: {
    dict: {
      type: Object,
      required: true
    }
  },
  filters: {
    getDirection (bearing) {
      if (bearing === 0) {
        return '正北';
      }
      if (bearing) {
        switch (true) {
          case bearing === 90:
            return '正东';
          case bearing === 180:
            return '正南';
          case bearing === 270:
            return '正西';
          case bearing > 0 && bearing < 90:
            return '东北';
          case bearing > 90 && bearing < 180:
            return '东南';
          case bearing > 180 && bearing < 270:
            return '西南';
          case bearing > 270 && bearing < 360:
            return '西北';
          default:
            return '无';
        }
      }
    }
  },
  data () {
    return {
      headerStyle: {
        'background-color': '#F7F7F7',
        'font-size': '14px'
      },
      // tableHeight: window.outerHeight - 840,
      tableData: [],
      tableHeight: 0,
      tableLoading: false
      // addrObj: {},
    };
  },
  // 枚举类型处理
  computed: {
    EnumerationTypeHandling () {
      return (dictName, val) => {
        if (this.dict.dict[dictName] && this.dict.dict[dictName][val]) {
          return this.dict.dict[dictName][val].label;
        }
      };
    }
  },
  methods: {
    // 处理时间格式化为"x天x小时x分x秒"
    handlerTime(seconds) {
      if (seconds === 0) return "0秒";
      const duration = this.$moment.duration(seconds, 'seconds');
      const days = duration.days();
      const hours = duration.hours();
      const minutes = duration.minutes();
      const secs = duration.seconds();
      let result = '';
      if (days > 0) result += days + '天';
      if (hours > 0) result += hours + '小时';
      if (minutes > 0) result += minutes + '分';
      if (secs > 0) result += secs + '秒';
      return result;
    },
    // 动态改变表格高度
    editTableHeight() {
      this.$nextTick(()=>{
        this.tableHeight = this.$refs['work_eltable'].offsetHeight;
      });
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Track', value);
    },
    async setTableData (data) {
      this.tableData = []
      // if(data.length === 0) return false
      this.tableLoading = true
      console.time('设置数据');
      data.forEach((item, index) => {
        item.customizeId = index + 1;
      });
      this.tableData = Object.freeze(data)
      console.timeEnd('设置数据')
      this.tableLoading = false
      return false
      let i = 1;
      if (this.tableData.length) {
        for (let index = 0; index < this.tableData.length; index++) {
          const element = this.tableData[index];
          element.customizeId = i++;
        }
        let coordinates = data.map(item => {
          return {
            latitude: Number(item.latitudeTable),
            longitude: Number(item.longitudeTable),
            id: item.customizeId
          };
        });
        // 先截取前50个经纬度使地址显示出来
        let hundredList = coordinates.splice(0, 100);
        batchAddr(hundredList).then(res => {
          for (let index = 0; index < res.data.length; index++) {
            const element = res.data[index];
            // this.addrObj[element.id] = {
            //   locAddr: element.locAddr
            // };
            this.tableData[element.id - 1].locAddr = element.locAddr;
          }
        });
        // 循环剩下的经纬度数组
        let trackList = this.chunkArray(coordinates, 1000);
        for (let index = 0; index < trackList.length; index++) {
          const element = trackList[index];
          await batchAddr(element).then(res => {
            for (let index = 0; index < res.data.length; index++) {
              const element = res.data[index];
              // this.addrObj[element.id] = {
              //   locAddr: element.locAddr
              // };
              this.tableData[element.id - 1].locAddr = element.locAddr;
            }
          });
        }
      }
    },
    rowClick (row) {
      this.$emit('goToPoint', row);
    },
    chunkArray(array, chunkSize) {
      let result = [];
      for (let i = 0; i < array.length; i += chunkSize) {
        let chunk = array.slice(i, i + chunkSize);
        result.push(chunk);
      }
      return result;
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    parseTimes (time) {
      return this.$moment(time * 1000).format('YYYY-MM-DD HH:mm:ss');
    }
  }
};
</script>

<style lang="less" scoped>
  .track-table{
    width: 100%;
    height: 100%;
    margin-top: 0px;
    overflow-y: auto;
  }
  ::v-deep.el-table__header-wrapper {
    // height: 30px
  }
  .offline-car-tab{
    font-size: 13px;
    ::v-deep .el-table{
      // height: 100%;
    }
  }
  ::v-deep.current-row{
    color: green;
  }
  ::v-deep.plTableBox .el-table th {
    padding: 5px 0;
  }
  .work_eltable{
    min-height: 170px;
    overflow-y: hidden !important;
  }
</style>
