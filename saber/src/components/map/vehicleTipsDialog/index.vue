<template>
  <div>
    <transition name="el-fade-in-linear">
      <div
        v-show="vehicleTips"
        class="vehicle-dialog"
      >
        <div class="vehicle-table">
          <el-table
            ref="table"
            :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
            :data="staffData"
            :cell-style="{'text-align':'center'}"
          >
            <el-table-column
              type="index"
              width="55"
              label="序号"
            />
            <el-table-column
              prop="image"
              label="设备列表图标"
              width="400"
              :resizable="false"
            >
              <template slot-scope="scope">
                <svg-icon
                  :icon-class="svgStaffType(scope.row.image, 'vehicle')"
                />
                <svg-icon
                  :icon-class="svgStaffType(scope.row.image, 'materials')"
                />
                <svg-icon
                  :icon-class="svgStaffType(scope.row.image, 'personnel')"
                />
                <svg-icon
                  :icon-class="svgStaffType(scope.row.image, 'shortMessage')"
                />
                <svg-icon
                  :icon-class="svgStaffType(scope.row.image, 'timeService')"
                />
                <svg-icon
                  :icon-class="svgStaffType(scope.row.image, 'monitor')"
                />
                <svg-icon
                  :icon-class="svgStaffType(scope.row.image, 'other')"
                />
              </template>
            </el-table-column>
            <el-table-column
              prop="image"
              label="设备地图图标"
              width="400"
              :resizable="false"
            >
              <template slot-scope="scope">
                <div class="map-icon">
                  <div
                    class="map-icon-bg"
                    :style="{ background: `url(${judgeBackgroundIcon(scope.row.image)})`, backgroundSize: '100%' }"
                  >
                    <img :src="judgeTerminalIcon('vehicle')">
                  </div>
                  <div
                    class="map-icon-bg"
                    :style="{ background: `url(${judgeBackgroundIcon(scope.row.image)})`, backgroundSize: '100%' }"
                  >
                    <img :src="judgeTerminalIcon('materials')">
                  </div>
                  <div
                    class="map-icon-bg"
                    :style="{ background: `url(${judgeBackgroundIcon(scope.row.image)})`, backgroundSize: '100%' }"
                  >
                    <img :src="judgeTerminalIcon('personnel')">
                  </div>
                  <div
                    class="map-icon-bg"
                    :style="{ background: `url(${judgeBackgroundIcon(scope.row.image)})`, backgroundSize: '100%' }"
                  >
                    <img :src="judgeTerminalIcon('shortMessage')">
                  </div>
                  <div
                    class="map-icon-bg"
                    :style="{ background: `url(${judgeBackgroundIcon(scope.row.image)})`, backgroundSize: '100%' }"
                  >
                    <img :src="judgeTerminalIcon('timeService')">
                  </div>
                  <div
                    class="map-icon-bg"
                    :style="{ background: `url(${judgeBackgroundIcon(scope.row.image)})`, backgroundSize: '100%' }"
                  >
                    <img :src="judgeTerminalIcon('monitor')">
                  </div>
                  <div
                    class="map-icon-bg"
                    :style="{ background: `url(${judgeBackgroundIcon(scope.row.image)})`, backgroundSize: '100%' }"
                  >
                    <img :src="judgeTerminalIcon('other')">
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="text"
              width="100"
              label="状态"
              :resizable="false"
            />
            <el-table-column
              prop="explain"
              label="说明"
              width="300"
              :resizable="false"
            />
          </el-table>
          <span class="vehicle-tips">tips: 设备状态图标形状依次为车辆、物资、人员、短报文终端、授时终端、监测终端、其他。</span>
          <div
            class="close-btn"
            @click="questionHandle"
          >
            <i class="el-icon-circle-close" />
          </div>
        </div>
      </div>
    </transition>
    <div
      v-show="vehicleTips"
      class="vehicle-shade"
    />
  </div>
</template>
<script>
export default {
  data() {
    return {
      vehicleTips: false,
      terminalData: [
        {image: 0, text: '离线', explain: '离线是终端断开连接，没有上传数据'},
        {image: 1, text: '行驶', explain: 'ACC开（点火），且有正常定位信息'},
        {image: 2, text: '停驶-ACC关', explain: 'ACC关（熄火），但上传数据，或者速度为0'},
        {image: 3, text: '告警', explain: '上传定位数据有告警标志位'},
        {image: 4, text: '未定位', explain: '上传定位数据中为无效定位'},
        {image: 5, text: '停驶-ACC开', explain: 'ACC开（点火），正常上传数据，但速度为0'}
      ],
      staffData: [
        {image: 0, text: '离线', explain: '离线是终端断开连接，没有上传数据'},
        {image: 1, text: '静止', explain: '终端在静止'},
        {image: 2, text: '移动', explain: '终端在移动'}
      ]
    };
  },
  methods: {
    questionHandle () {
      this.vehicleTips = !this.vehicleTips;
    },
    svgStaffType (val, type) {
      // 0-离线 1-静止 2-移动
      // 离线(Offline) 静止(Static) 移动(Move)
      let vehicleIcon = '';
      switch (val) {
      case 0:
        vehicleIcon = `${type}Offline`;
        break;
      case 1:
        vehicleIcon = `${type}Static`;
        break;
      case 2:
        vehicleIcon = `${type}Move`;
        break;
      }
      return vehicleIcon;
    },
    /**
     * 根据终端类型判断图标
     */
    judgeTerminalIcon (type) {
      let vehicleIcon = `/bdsplatform/static/images/pic/${type}.png`;
      return vehicleIcon;
    },
    judgeBackgroundIcon (val) {
      // 0-离线 1-静止 2-移动
      // 离线(Offline) 静止(Static) 移动(Move)
      let vehicleIcon = '';
      switch (val) {
      case 0:
        vehicleIcon = `/bdsplatform/static/images/pic/offline.png`;
        break;
      case 1:
        vehicleIcon = `/bdsplatform/static/images/pic/static.png`;
        break;
      case 2:
        vehicleIcon = `/bdsplatform/static/images/pic/move.png`;
        break;
      }
      return vehicleIcon;
    }
  }
};
</script>

<style lang="less" scoped>
.vehicle-dialog{
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .vehicle-table{
      width: max-content;
      background-color: white;
      padding: 20px;
      position: relative;
      ::v-deep .el-table{
        .svg-icon{
          width: 35px;
          height: 35px;
          margin-right: 5px;
        }
      }
      .close-btn{
        position: absolute;
        top: -45px;
        right: -35px;
        font-size: 35px;
        color: white;
        cursor: pointer;
      }
    }
    .staff-table {
      margin-top: 30px;
    }
  }
  .vehicle-shade{
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.4);
    z-index: 2999;
  }
  .vehicle-tips{
    font-size: 14px;
    color: rgb(153, 153, 153);
    display: inline-block;
    padding-top: 5px;
  }
  .map-icon {
    display: flex;
    justify-content: start;
    .map-icon-bg {
      width: 50px;
      height: 50px;
    }
    img {
      width: 50px;
      height: 50px;
    }
  }
</style>
