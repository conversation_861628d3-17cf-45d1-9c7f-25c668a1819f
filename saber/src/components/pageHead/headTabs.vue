<template>
  <div class="tabs">
    <div class="tabs-box" v-loading="!headLoading">
      <div class="tabs-item">
        <div class="tabs-item-count">
          <div
            class="tabs-item-info"
            :class="[activeIndex === safety.alarmType ? 'active' : '']"
            @click="handleClick(safety.alarmType)"
          >
            <div class="tabs-item-number">
              <span
                class="tabs-size"
                :class="{'tabs-size-color' : safety.numPending}"
              >{{ safety.numPending | nullValueStr }}</span>
              <span> / </span>
              <span class="tabs-size">{{ safety.numAlarm | nullValueStr }}</span>
            </div>
            <div class="tabs-item-info-content">
              <div class="left">
                <div class="tabs-item-icon">
                  <img :src="require('@/assets/images/alarm/icon1.png')" alt="">
                </div>
              </div>
              <div class="right">
                <div class="tabs-item-content base-title">
                  24小时
                </div>
                <div class="tabs-item-content tabs-title">
                  {{ getEnumDictLabel('alarmTypeSpecial', safety.alarmType) | nullValueStr }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="tabs-line"/>
        <div class="tabs-content">
          <div
            v-for="(item, index) in dynamicMonitorGroup"
            :key="index"
            class="tabs-content-item"
            :class="[ activeIndex === item.alarmType ? 'active' : '']"
            @click="handleClick(item.alarmType)"
          >
            <div class="tabs-content-item-container">
              <div class="tabs-content-item-number">
              <span
                class="tabs-size"
                :class="{'tabs-size-color' : item.numPending}"
              >{{ item.numPending | nullValueStr }}</span>
                <span> / </span>
                <span class="active-count">{{ item.numAlarm | nullValueStr }}</span>
              </div>
              <div class="tabs-title">
                <div class="tabs-content-item-icon">
                  <div :style="{'-webkit-mask-image': `url(${require(`@/assets/images/alarm/icon${getIcons(item.alarmType)}.png`)})`}"/>
                </div>
                <span>
                  {{ getEnumDictLabel('alarmTypeSpecial', item.alarmType) | nullValueStr }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="tabs-item">
        <div class="tabs-item-count">
          <div
            class="tabs-item-info"
            :class="[activeIndex === monitoring.alarmType ? 'active' : '']"
            @click="handleClick(monitoring.alarmType)"
          >
            <div class="tabs-item-number">
              <span
                class="tabs-size"
                :class="{'tabs-size-color' : monitoring.numPending}"
              >{{ monitoring.numPending | nullValueStr }}</span>
              <span> / </span>
              <span class="tabs-size">{{ monitoring.numAlarm | nullValueStr }}</span>
            </div>
            <div class="tabs-item-info-content">
              <div class="left">
                <div class="tabs-item-icon">
                  <img :src="require('@/assets/images/alarm/icon1.png')" alt="">
                </div>
              </div>
              <div class="right">
                <div class="tabs-item-content base-title">
                  24小时
                </div>
                <div class="tabs-item-content tabs-title">
                  {{ getEnumDictLabel('alarmTypeSpecial', monitoring.alarmType) | nullValueStr }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="tabs-line"/>
        <div class="tabs-content">
          <div
            v-for="(item, index) in activeSafeGroup"
            :key="index"
            class="tabs-content-item"
            :class="[ activeIndex === item.alarmType ? 'active' : '']"
            @click="handleClick(item.alarmType)"
          >
            <!--            <div class="tabs-content-item-icon">-->
            <!--              <img :src="require(`@/assets/images/alarm/icon${getIcons(item.alarmType)}.png`)" alt="">-->
            <!--            </div>-->
            <div class="tabs-content-item-container">
              <div class="tabs-content-item-number">
              <span
                class="tabs-size"
                :class="{'tabs-size-color' : item.numPending}"
              >{{ item.numPending }}</span>
                <span> / </span>
                <span class="active-count">{{ item.numAlarm }}</span>
              </div>
              <div class="tabs-title">
                <div class="tabs-content-item-icon">
                  <div :style="{'-webkit-mask-image': `url(${require(`@/assets/images/alarm/icon${getIcons(item.alarmType)}.png`)})`}"/>
                </div>
                <span>
                {{ getEnumDictLabel('alarmTypeSpecial', item.alarmType) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    alarmList: {
      type: Array,
      default: () => {
        return []
      }
    },
    pendingList: {
      type: Array,
      default: () => {
        return []
      }
    },
    dict: {
      type: Object,
      default: () => {
        return {}
      }
    },
    headLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeIndex: null,
      monitoring: {
        numPending: 0,
        numAlarm: 0,
        alarmType: "1001"
      },
      safety: {
        numPending: 0,
        numAlarm: 0,
        alarmType: "1002"
      },
      activeSafeGroup: [
        { numPending: 0, numAlarm: 0, alarmType: "0"},
        { numPending: 0, numAlarm: 0, alarmType: "103"},
        { numPending: 0, numAlarm: 0, alarmType: "1003"},
        { numPending: 0, numAlarm: 0, alarmType: "120"},
        { numPending: 0, numAlarm: 0, alarmType: "82"},
        { numPending: 0, numAlarm: 0, alarmType: "83"},
        { numPending: 0, numAlarm: 0, alarmType: "84"},
        { numPending: 0, numAlarm: 0, alarmType: "30"}
      ],
      dynamicMonitorGroup: [
        { numPending: 0, numAlarm: 0, alarmType: "211"},
        { numPending: 0, numAlarm: 0, alarmType: "212"},
        { numPending: 0, numAlarm: 0, alarmType: "213"},
        { numPending: 0, numAlarm: 0, alarmType: "214"},
        { numPending: 0, numAlarm: 0, alarmType: "161"},
        { numPending: 0, numAlarm: 0, alarmType: "162"},
        { numPending: 0, numAlarm: 0, alarmType: "163"},
        { numPending: 0, numAlarm: 0, alarmType: "164"}
      ],
      icons: {
        '211': '6',
        '212': '9',
        '213': '4',
        '214': '3',
        '161': '8',
        '162': '5',
        '163': '7',
        '164': '2',
        '0': '14',
        '103': '15',
        '1003': '11',
        '120': '18',
        '82': '16',
        '83': '12',
        '84': '17',
        '30': '13'
      }
    }
  },
  computed: {
    getIcons() {
      return (alarmType) => {
        if (alarmType) {
          return this.icons[alarmType]
        }
        else {
          return 12
        }
      }
    }
  },
  watch: {
    alarmList: {
      handler(newVal) {
        if (newVal && newVal.length) {
          this.activeSafeGroup = JSON.parse(JSON.stringify(newVal))
          // 1001 动态监控告警
          const index = this.activeSafeGroup.findIndex(item => item.alarmType === '1001')
          this.monitoring = this.activeSafeGroup.splice(index, 1)[0]
        }
      },
      deep: true
    },
    pendingList: {
      handler(newVal) {
        if (newVal && newVal.length) {
          this.dynamicMonitorGroup = JSON.parse(JSON.stringify(newVal))
          // 1002 主动安全告警
          const num = this.dynamicMonitorGroup.findIndex(item => item.alarmType === '1002')
          this.safety = this.dynamicMonitorGroup.splice(num, 1)[0]
        }
      },
      deep: true
    }
  },
  methods: {
    defaultActiveIndex () {
      this.activeIndex = null;
    },
    handleClick(index) {
      if (index === this.activeIndex) {
        this.activeIndex = null;
      } else {
        this.activeIndex = index;
      }
      this.$emit('toQuery', this.activeIndex);
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label
      }
      else {
        return ''
      }
    }
  }
}
</script>
<style lang="less" scoped>
.tabs {
  font-size: 16px;
  color: #3d3c3c;
  font-weight: 500;
  margin-bottom: 4px;

  &-box {
    height: 100px;
    display: flex;
    justify-content: space-between;
  }

  &-item {
    width: calc(50% - 2px);
    background: #ffffff;
    display: flex;
    position: relative;

    &-content {
      color: #3c3c3c;
      font-size: 14px;
      margin-bottom: 3px;
    }

    &-info {
      width: 160px;
      height: 100%;
      line-height: 1.4;
      text-align: center;
      border-right: 1px solid #e1e5e8;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 8px 0 8px 0;
    }

    &-count {
      display: flex;
      align-items: center;
      // padding: 0 12px 0 8px;
      padding-left: 8px;
    }
  }

  &-content {
    width: calc(100% - 140px);
    display: flex;
    flex-wrap: wrap;
    padding: 2px 0;

    &-item {
      width: 25%;
      cursor: pointer;
      display: flex;
      align-items: center;

      &-container {
        display: flex;
        flex-direction: column;
        text-align: center;
        flex: 1;
        padding-right: 10px;
        line-height: 1.1;
      }

      &-number {
        text-align: center;
        padding-left: 10px;
        font-size: 16px;
        //font-weight: 600;
      }
    }
  }

  &-line {
    position: absolute;
    left: 0;
    top: 50%;
    height: 1px;
    width: 100%;
  }

  &-size {

    &-color {
      color: #ee3e3e;
    }
  }

  .tabs-item-number {
    font-size: 18px;
    text-align: center;
    margin-bottom: 12px;
    margin-top: 5px;
    padding-left: 8px;
    //font-weight: 600;
  }

  .tabs-item-info-content {
    display: flex;
    flex: 1;
    justify-content: center;
    .left {
      height: 100%;
      margin-right: 4px;
      display: flex;
      align-items: center;
    }

    .right {
      text-align: left;
    }
  }

  .tabs-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 46px;
    }
  }

  .tabs-content-item-icon {
    width: 32px;
    height: 100%;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 6px;
    padding-left: 8px;

    div {
      width: 22px;
      height: 22px;
      -webkit-mask-size: 100% auto;
      -webkit-mask-position: center;
      -webkit-mask-repeat: no-repeat;
      background-color: #1d6dcf;
    }

    & + span {
      color: #7f7f7f;
    }
  }
}
.tabs-title {
  color: #7f7f7f;
  font-size: 14px;
  display: flex;
  align-items: center;
  margin-bottom: 0;
  justify-content: center;
}

.active-count {
  transition: all .3s;
}

.active {
  .tabs-content-item-number {
    transition: all .3s;
  }

  .tabs-title {
    color: white;
    background: #f3241f;
    padding: 1px 6px;
    font-size: 12px;
    border-radius: 4px;
    transition: all .3s;
    display: flex;
    align-items: center;
    flex-grow: 0;
    .tabs-content-item-icon>div{
      background-color: white;
    }
    span{
      color: white;
    }
  }
}

// 不同分辨率媒体查询样式

@media screen and (max-width: 1500px) {
    // 头部报警区域
    .tabs-item-count {
        padding-left: 0px !important;
        .tabs-item-info {
          width: 145px !important;
        }
    }
    .tabs-content-item-icon {
      padding-left: 0px !important;
      margin-right: 0px !important;
      & + span {
        white-space: nowrap;
      }
    }
}
</style>
