<template>
  <div class="table-title-slot">
    <div class="title-left">
      <div class="title">
        {{ title || '标题' }}
      </div>
      <slot name="center"/>
    </div>
    <div class="title-right">
      <slot name="right"/>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      required: true
    }
  },
  data() {
    return {}
  }
}
</script>

<style lang="less" scoped>
.table-title-slot {
  height: 40px;
  display: flex;
  align-items: center;
  color: #3c3c3c;
  padding: 4px 0 4px 4px;
  flex-shrink: 0;
  justify-content: space-between;
}

.title-left {
  display: flex;
}

.title {
  font-size: 14px;
  cursor: default;
  display: flex;
  align-items: center;
  margin: 0 40px 0 0;
  font-weight: bold;
  font-family: "Microsoft YaHei UI", sans-serif;

  &::before {
    content: "";
    width: 6px;
    height: 16px;
    background-color: var(--gn-color);
    margin-right: 6px;
    border-radius: 6px;
  }
}
</style>
