<template>
  <div id="singleBdMonitor">
    <div id="canvas-frame"/>
  </div>
</template>

<script>
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import IMG_EARTH from '@/assets/images/beidouVerification/earth4.jpg';
import IMG_EARTH_BUMP from '@/assets/images/beidouVerification/earth_bump.jpg';
import IMG_EARTH_SPEC from '@/assets/images/beidouVerification/earth_spec.jpg';
import IMG_EARTH_CLOUD from '@/assets/images/beidouVerification/earth_cloud.png';
import IMG_LOCATION from '@/assets/images/beidouVerification/location.png';
import SAT_INFOS from '@/assets/json/satinfos.json';

export default {
  name: 'SatelliteBgPage',
  data() {
    return {
      /** THREE 数据 */
      width: null,
      height: null,
      satellites: [],
      earthRadius: 100,
      chartBD: null,
      BDSNumber: 32,
      skip: 0,
    };
  },
  beforeDestroy() {
    this.camera = null;
    this.scene = null;
    this.controls = null;
    this.renderer = null;
    this.cloudsMesh = null;
    this.ModelGEO = null;
    this.ModelIGSO = null;
    this.ModelMEO = null;
    this.chartBD = null;
  },
  mounted() {
    // 将threejs相关变量初始化
    this.camera = null;
    this.scene = null;
    this.controls = null;
    this.renderer = null;
    this.cloudsMesh = null;
    this.ModelGEO = null;
    this.ModelIGSO = null;
    this.ModelMEO = null;
    this.chartBD = null;
    this.initThree();
    this.initCamera();
    this.initScene();
    this.initLight();
    this.initEarth();
    // 载入控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.animate();
    // this.addLocalMarker();
    this.loadGltf();
  },
  methods: {
    initThree() {
      this.width = document.getElementById('canvas-frame').clientWidth;
      this.height = document.getElementById('canvas-frame').clientHeight;
      let _renderer;
      // 创建渲染器对象
      _renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true,
        canvas: _renderer
      });
      this.renderer = _renderer;
      this.renderer.setSize(this.width, this.height);
      document.getElementById('canvas-frame').appendChild(this.renderer.domElement);
    },
    initCamera() {
      // 透视相机 视角越大，看到的场景越大，那么中间的物体相对于整个场景来说，就越小了
      this.camera = new THREE.PerspectiveCamera(45, this.width / this.height, 1, 10000);
      // 设置相机位置
      this.camera.position.set(0, 80, 550);
      // 设置相机方向
      this.camera.lookAt(new THREE.Vector3(0, 0, 0));
    },
    initScene() {
      // 创建场景对象scene
      this.scene = new THREE.Scene();
    },
    initLight() {
      // 环境光
      const hemisphereLight = new THREE.HemisphereLight(0xffffff, 0x393939, 1.6);
      hemisphereLight.position.set(-200, 200, -200);
      this.scene.add(hemisphereLight);
    },
    animate() {
      this.renderer.render(this.scene, this.camera);
      requestAnimationFrame(this.animate);
      if (this.skip !== 0) {
        this.skip = ++this.skip % 2;
        return;
      }
      else {
        this.skip = ++this.skip % 2;
      }
      this.cloudsMesh.rotation.y -= 0.0005;
      this.cloudsMesh.rotation.z -= 0.0005;
      this.cloudsMesh.rotation.x += 0.0004;
      for (let i = 0; i < this.satellites.length; i++) {
        if (this.satellites[i].move === 0) {
          this.satellites[i].satellite.rotation.x -= this.satellites[i].speed;
        }
        else if (this.satellites[i].move === 1) {
          this.satellites[i].satellite.rotation.y -= this.satellites[i].speed;
        }
        else if (this.satellites[i].move === 2) {
          this.satellites[i].satellite.rotation.z = this.satellites[i].speed === 0 ? this.satellites[i].satellite.rotation.z : this.satellites[i].satellite.rotation.z - (this.satellites[i].speed - 0.003);
        }
      }
    },
    initEarth() {
      const loader = new THREE.TextureLoader();
      // 创建球体
      let geometry = new THREE.SphereGeometry(this.earthRadius, 128, 128);
      // 添加贴图
      let texture = loader.load(IMG_EARTH);
      let material = new THREE.MeshPhongMaterial();
      material.map = texture;
      material.transparent = true;
      // 添加浮雕凹凸贴图
      material.bumpMap = loader.load(IMG_EARTH_BUMP);
      material.bumpScale = 12;
      // 添加高光贴图
      material.specularMap = loader.load(IMG_EARTH_SPEC);
      material.specular = new THREE.Color('#1a2948');
      material.shininess = 4;
      let mesh = new THREE.Mesh(geometry, material);
      mesh.rotateY(Math.PI * -0.55);
      const group = new THREE.Group();
      group.name = 'earth';
      group.add(mesh);
      // 添加地球到场景
      this.scene.add(group);
      this.scene.rotation.y -= 1.7;
      this.scene.rotation.x += 0.5;
      // 创建云层
      let cloud = new THREE.SphereGeometry(this.earthRadius + 4, 100, 100);
      const earthCloudsMat = new THREE.MeshLambertMaterial({
        color: 0xffffff,
        blending: THREE.NormalBlending,
        transparent: true,
        depthTest: false
      });
      earthCloudsMat.map = loader.load(IMG_EARTH_CLOUD);
      earthCloudsMat.needsUpdate = true;
      const sphereCloudsMesh = new THREE.Mesh(cloud, earthCloudsMat);
      sphereCloudsMesh.name = 'cloud';
      // 添加云层到场景
      group.add(sphereCloudsMesh);
      this.cloudsMesh = sphereCloudsMesh;
    },

    /**
     * 返回一个卫星和轨道的组合体
     * @param satelliteSize 卫星的大小
     * @param satelliteRadius 卫星的旋转半径
     * @param rotation 组合体的x,y,z三个方向的旋转角度
     * @param speed 卫星运动速度
     * @param scene 场景
     * @returns {{satellite: THREE.Mesh, speed: *}} 卫星组合对象;速度
     * e:this.initSatellite(50,230,{ x: -Math.PI * 0.35, y: Math.PI * 0.25, z: 0 },0.023,this.scene)
     */
    initSatellite(satelliteSize, satelliteRadius, rotation, speed, color, scene, move, type) {
      const geometry = new THREE.TorusGeometry(satelliteRadius, 0.4, 16, 100);
      const material = new THREE.MeshBasicMaterial({
        color: color || 0xffffff,
        opacity: 0.5,
        transparent: true
      });
      const torus = new THREE.Mesh(geometry, material);
      const centerMesh = new THREE.Mesh(
        new THREE.SphereGeometry(1, 1, 1),
        new THREE.MeshLambertMaterial()
      ); // 材质设定
      const satellite = new THREE.Sprite(
        new THREE.SpriteMaterial({
          map: new THREE.CanvasTexture(this.generateSprite('196,233,255', color)),
          blending: THREE.AdditiveBlending
        })
      );
      satellite.scale.x = satellite.scale.y = satellite.scale.z = satelliteSize;
      satellite.position.set(satelliteRadius, 0, 0);
      /** 卫星模型加载 */
      let satelliteObj;
      if (type === 'GEO') {
        satelliteObj = this.ModelGEO.clone();
      }
      else if (type === 'IGSO') {
        satelliteObj = this.ModelIGSO.clone();
      }
      else if (type === 'MEO') {
        satelliteObj = this.ModelMEO.clone();
      }
      else {
        console.error('卫星加载错误，未知类型，请检查数据');
      }
      satelliteObj.position.set(satelliteRadius, 0, 0);
      const pivotPoint = new THREE.Object3D();
      pivotPoint.add(torus);
      pivotPoint.add(satellite);
      pivotPoint.add(satelliteObj);
      centerMesh.add(pivotPoint);
      centerMesh.rotation.set(rotation.x, rotation.y, rotation.z, rotation.order);
      scene.add(centerMesh);
      return {
        satellite: centerMesh,
        speed: speed,
        move: move
      };
    },
    /**
     * 实现发光星星
     * @param color 颜色的r,g和b值,比如：“123,123,123”;
     * @param mainColor
     * @returns {Element} 返回canvas对象
     */
    generateSprite(color, mainColor) {
      const canvas = document.createElement('canvas');
      canvas.width = 16;
      canvas.height = 16;
      const context = canvas.getContext('2d');
      const gradient = context.createRadialGradient(
        canvas.width / 2,
        canvas.height / 2,
        0,
        canvas.width / 2,
        canvas.height / 2,
        canvas.width / 2
      );
      gradient.addColorStop(0, mainColor);
      gradient.addColorStop(0.4, mainColor + '60');
      gradient.addColorStop(1, 'rgba(0,0,0,0)');
      context.fillStyle = gradient;
      context.fillRect(0, 0, canvas.width, canvas.height);
      return canvas;
    },
    /**
     * 经度，纬度转换为坐标
     */
    getPosition(longitude, latitude, radius = this.earthRadius) {
      radius += 1;
      let lg = THREE.MathUtils.degToRad(longitude - 9);
      // 获取x，y，z坐标
      let lt = THREE.MathUtils.degToRad(latitude);
      let temp = radius * Math.cos(lt);
      let x = temp * Math.sin(lg);
      let y = radius * Math.sin(lt);
      let z = temp * Math.cos(lg);
      return new THREE.Vector3(x, y, z);
    },
    // getTexture() {
    //   let loader = new THREE.TextureLoader();
    //   loader.crossOrigin = '';
    //   return loader.load(IMG_LOCATION);
    // },
    // addLocalMarker() {
    //   /** 海格位置 */
    //   let position = this.getPosition(113.446413, 23.144595);
    //   let spriteMaterial = new THREE.SpriteMaterial({
    //     map: this.getTexture(),
    //     fog: true
    //   });
    //   let sprite = new THREE.Sprite(spriteMaterial);
    //   sprite.position.set(position.x, position.y + 20, position.z);
    //   sprite.scale.set(8, 8);
    //   sprite.center.set(0.5, 1.76);
    //   this.scene.add(sprite);
    // },
    loadSatellites() {
      console.info('--> SAT_INFOS : 加载卫星 🛰', SAT_INFOS);
      for (let i = 0; i < SAT_INFOS.length; i++) {
        const element = SAT_INFOS[i];
        this.satellites.push(
          this.initSatellite(
            20,
            this.earthRadius + element.dis,
            {
              x: -Math.PI * element.xyz[0],
              y: -Math.PI * element.xyz[1],
              z: -Math.PI * element.xyz[2],
              order: element.order
            },
            element.speed,
            element.color,
            this.scene,
            element.move,
            element.type)
        );
      }
    },
    loadGltf() {
      let loader = new GLTFLoader();
      let remain = 3;
      loader.load('/bdsplatform/static/beidouVerification/bdwx-r.gltf', (gltf) => {
        gltf.scene.scale.set(0.02, 0.02, 0.02);
        this.ModelGEO = gltf.scene;
        remain -= 1;
        if (remain <= 0) {
          this.loadSatellites();
        }
      });
      loader.load('/bdsplatform/static/beidouVerification/bdwx-g.gltf', (gltf) => {
        gltf.scene.scale.set(0.02, 0.02, 0.02);
        this.ModelMEO = gltf.scene;
        remain -= 1;
        if (remain <= 0) {
          this.loadSatellites();
        }
      });
      loader.load('/bdsplatform/static/beidouVerification/bdwx-y.gltf', (gltf) => {
        gltf.scene.scale.set(0.02, 0.02, 0.02);
        this.ModelIGSO = gltf.scene;
        remain -= 1;
        if (remain <= 0) {
          this.loadSatellites();
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
#singleBdMonitor {
  line-height: normal !important;
  background: url(../../assets/images/beidouVerification/bg.jpg) no-repeat;
  background-size: 100% 100%;
  display: flex;
  height: 100%;
  position: relative;
}

#canvas-frame {
  border: none;
  width: 100%;
  height: 100%;
}

</style>
