<template>
  <el-select
    ref="deptIdStrRef"
    v-model="treeSelectText"
    class="el-input-search"
    filterable
    :disabled="disabled"
    style="width:100%"
    :filter-method="filterTree"
    :size="size"
    :clearable="clearable"
    :placeholder="disabled ? '' : placeholder"
    @clear="clear"
  >
    <el-option
      hidden
      :value="treeValue"
      style="height:auto;"
    />
    <vue-easy-tree
      ref="tree"
      :data="deptOptions"
      :props="props"
      node-key="id"
      :filter-node-method="filterNode"
      height="200px"
      itemSize="30"
      @node-click="nodeClick"
    />
  </el-select>
</template>

<script>

import { getDeptPerInit } from '@/api/base/dept';

export default {
  name: 'DeptFormSingleSelect',
  props: {
    value: {
      type: [
        String,
      ],
      required: true
    },
    placeholder: {
      type: String,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    isShow: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'medium'
    },
    clearable: {
      type: Boolean,
      default: false
    },
    detailName: {
      type: String,
      default: ''
    },
    deptList: {
      type: Array,
      default: null
    },
    noReq: {
      type: Boolean,
      default: false
    },
    isRegion: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      deptOptions: [],
      props: {
        label: 'title'
      },
      treeSelectText: '',
      treeValue: {
        id: '',
        label: ''
      }
    };
  },
  watch: {
    treeValue: {
      handler(val) {
        if (val.id) {
          this.$emit('input', val.id);
        } else {
          this.$emit('input', '');
        }
      },
      deep: true
    },
    value: {
      handler(val) {
        if (!val) {
          this.clear();
        }
      },
      immediate: true
    },
    isShow: {
      handler(val) {
        if (val) {
          this.getDept();
          this.value && this.setValue(this.value);
        }
      },
      immediate: true
    },
    deptList() {
      this.deptOptions = this.deptList;
    }
  },
  // mounted(){
  //   this.getDept();
  // },
  methods: {
    getDept() {
      // 非禁用状态下 获取列表 优化性能
      if(!this.disabled && !this.noReq) {
        // 如果从外部组件传递了列表数据 就不从接口获取
        this.loading = true;
        getDeptPerInit().then(res => {
          this.deptOptions = res.data;
          console.log(this.value, 'this.value');

          this.loading = false;
          if (this.isRegion) {
            this.$nextTick(() => { // TODO
              this.value && this.setValue(this.value);
            });
          }
        }).catch(() => {
          this.loading = false;
        });
      }
      // 如果有部门名称 直接设置 快速回显
      if(this.detailName) {
        this.treeSelectText = this.detailName;
      }
    },
    // 有些外层组件需要请求详情接口后才给treeSelectText赋值
    setTreeValue (val) {
      this.treeSelectText = val;
    },
    filterTree(val) {
      this.$refs.tree.filter(val);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.title.indexOf(value) !== -1;
    },
    nodeClick(node) {
      const {
        id,
        title
      } = node;
      this.treeValue.id = id;
      this.treeValue.label = title;
      this.treeSelectText = title;
      this.$refs.deptIdStrRef.blur();
    },
    setValue(val) {
      this.$nextTick(() => {
        if (this.$refs.tree && val) {
          this.$refs.tree.setCurrentKey(val);
          const node = this.$refs.tree.getNode(val);
          if(!node) {
            return false;
          }
          const { data } = node;
          const {
            id,
            title
          } = data;
          this.treeValue.id = id;
          this.treeValue.label = title;
          this.treeSelectText = title;
        }
      });
    },
    clear(){
      this.treeSelectText = '';
      this.treeValue.id = '';
      this.treeValue.label = '';
    }
  }
};
</script>

<style lang="less" scoped>
@import "../../../assets/less/variables.less";

.sidebarDeptFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarDeptFade-enter,
.sidebarDeptFade-leave-to {
  opacity: 0;
}

.sidebar-dept-container {
  position: relative;
  width: 100%;
  height: @logoBarHeight;
  line-height: @logoBarHeight;
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 6px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #ffffff;
      font-weight: 600;
      line-height: 50px;
      font-size: 14px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
