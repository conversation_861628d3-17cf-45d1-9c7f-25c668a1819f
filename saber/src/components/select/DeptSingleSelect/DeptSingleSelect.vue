<template>
  <el-cascader
    v-model="selectedDeptId"
    :props="config"
    :options="deptOptions"
    :show-all-levels="false"
    size="small"
    clearable
    filterable
    :placeholder="placeholder"
  />
</template>

<script>
// import getParamDeptId from '@/utils/helper/getParamDeptId';
export default {
  name: 'DeptSingleSelect',
  props: {
    value: {
      type: [Object, Array, String, Number],
      required: true
    },
    placeholder: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      loading: false,
      selectedDeptId: [],
      deptOptions: [],
      config: {
        value: 'id',
        label: 'title',
        checkStrictly: true,
        multiple: false
      }
    };
  },
  watch: {
    selectedDeptId (array) {
      // console.log(array);
      // this.value = array[array.length - 1];

      this.$emit('input', array[array.length - 1]);
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.getDept();
    });
  },
  methods: {
    getDept () {
      this.loading = true;
      this.$store.dispatch('GetDept').then(res => {
        this.deptOptions = res.data;
        // this.selectedDeptId = [this.deptOptions[0].id];
        // this.selectedDeptId = getParamDeptId() || [this.deptOptions[0].id];
        this.$nextTick(() => {
          this.$emit('init', this.selectedDeptId);
        });
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    }
  }
};
</script>

<style lang="less" scoped>
@import "../../../assets/less/variables.less";
.sidebarDeptFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarDeptFade-enter,
.sidebarDeptFade-leave-to {
  opacity: 0;
}

.sidebar-dept-container {
  position: relative;
  width: 100%;
  height: @logoBarHeight;
  line-height: @logoBarHeight;
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 6px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #fff;
      font-weight: 600;
      line-height: 50px;
      font-size: 14px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
