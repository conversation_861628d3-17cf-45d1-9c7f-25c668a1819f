<template>
  <el-select
    class="cascaderRef"
    v-model="selectedDeptId"
    filterable
    remote
    :clearable="clearable"
    reserve-keyword
    :placeholder="placeholder"
    :remote-method="dealDeptSearch"
    :loading="loading"
    :disabled="disabled"
    @change="change"
    @clear="remoteMethod('')"
  >
    <el-option
      v-for="item in depts"
      :key="item.id"
      :label="item.deptName"
      :value="item.id"
    >
    </el-option>
  </el-select>
</template>

<script>
import { getDeptListByName } from "@/api/base/dept";
import _ from "lodash";
export default {
  name: "DeptSingleSelect",
  props: {
    value: {
      type: [String, Array, Object, Number],
      default: null,
    },
    clearable: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    disabled: {
      type: Boolean,
      default: false
    },
  },
  data() {
    this.dealDeptSearch = _.debounce(this.remoteMethod, 200);
    return {
      loading: false,
      selectedDeptId: "",
      depts: [],
    };
  },
  watch: {
    value (v) {
      if (!v) {
        this.selectedDeptId = "";
      }
    }
  },
  mounted() {
    this.remoteMethod()
  },
  methods: {
    remoteMethod(query) {
      if (this.oldValue!==undefined && this.oldValue == query) {
        return;
      }
      this.oldValue = query || "";
      this.loading = true;
      getDeptListByName(this.oldValue)
        .then((res) => {
          this.depts = res.data || [];
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    /**
     * 监听选中节点
     */
    change(value) {
      this.$emit("input", value);
    },
  },
};
</script>
<style lang="less" scoped>
.el-cascader-panel {
  height: 420px;
}
.cascaderRef {
  height: 30px;
  line-height: 30px;
}
.cascaderRef ::v-deep .el-input__inner {
  //width: 180px;
  height: 32px;
  line-height: 32px;
  box-sizing: border-box;
}
.cascaderRef ::v-deep .el-input--small {
  height: 32px;
  line-height: 32px;
}

.cascaderRef ::v-deep .el-input__suffix {
  height: 32px;
  line-height: 32px;
}
</style>
