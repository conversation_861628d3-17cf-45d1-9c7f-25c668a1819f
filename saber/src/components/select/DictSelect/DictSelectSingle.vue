<template>
  <el-select
    v-model="selectValue"
    v-bind="$attrs"
    v-on="$listeners"
    :multiple="multiple"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    >
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: 'DictSingleSelect',
  components: {},
  props: {
    options: {
      type: Array,
      default: () => []
    },
    value: {
      type: [
        String,
        Number,
        Array
      ],
      required: true
    },
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
    };
  },
  computed: {
    selectValue: {
      get () {
        if(this.multiple) {
          return this.value?.filter( val => !!this.options.find( item => item.value === val)?.label)
        }
        const obj = this.options.find(item => item.value == this.value)
        if (obj && obj.label) {
          return obj.value
        }
        return ''
      },
      set (val) {
        this.$emit('input', val);
      }
    }
  }
}
</script>

<style lang="less" scoped>
</style>
