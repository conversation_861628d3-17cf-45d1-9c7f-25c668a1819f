<template>
  <div>
    <div class="xh-page-searchItem-content">
      <el-input
          v-model="query.licencePlate"
          clearable
          size="mini"
          :placeholder="getPlaceholder('licencePlate')"
          @input="crud.toQuery"
          style="width: 150px"
      />
    </div>
    <rrOperation
      :crud="crud"
    />
  </div>
</template>

<script>
import { header } from '@/components/Crud/crud';
import rrOperation from '@/components/Crud/RR.operation';
import getPlaceholder from '@/utils/getPlaceholder';
export default {
  components: { rrOperation },
  mixins: [header()],
  props: {
    dict: {
      type: Object,
      required: true
    },
    permission: {
      type: Object,
      required: true
    }
  },
  methods: {
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('Vehicle', value);
    }
  }
};
</script>
