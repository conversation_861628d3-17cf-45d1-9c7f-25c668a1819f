<template>
  <div
    v-loading="loading"
    class="vehicle-tree-container"
  >
    <div
      ref="selectComponentContainerDom"
      class="vehicle-tree-content"
    >
      <div
        ref="headerDom"
        class="xh-select-component-header"
      >
        <span class="xh-select-component-header-title">终端列表</span>
        <span
          v-if="pageSrc === 'rm'"
          title="介绍"
          class="xh-select-component-header-refreshBtn"
          @click="$emit('questionHandle')"
        >
          <i class="el-icon-question" />
        </span>
      </div>
      <div
        ref="firstCollapseItemComponent"
        class="header"
      >
        <div class="car-filter-container">
          <i class="el-icon-search" />
          <el-input
            v-model="filterText"
            clearable
            size="small"
            placeholder="搜索终端名称/终端类型/机构"
            class="car-filter"
          />
          <i
            class="el-icon-refresh"
            @click="handleRefresh"
          />
          <el-button
            size="small"
            class="search-button"
            @click="searchFilterText(filterText)"
          >
            查询
          </el-button>
        </div>
      </div>
      <el-collapse
        v-model="activeNames"
        class="vehicle-tree-content-collapse"
        @change="handleCollapseChange"
      >
        <el-collapse-item
          name="secondItem"
          class="xh-select-component-collapse-item"
        >
          <!--车辆标题插槽-->
          <template slot="title">
            <div class="collapse-item-title-slot">
              终端目录
            </div>
          </template>
          <!--树渲染-->
          <vue-easy-tree
            ref="tree"
            :key="treeKey"
            :data="data"
            node-key="id"
            class="easy-tree"
            show-checkbox
            :lazy="lazy"
            :props="defaultProps"
            :load="loadTreeNode"
            :style="tableStyle"
            :height="treeMaxHeight"
            :default-checked-keys="defaultCheckedKeys"
            :default-expanded-keys="defaultExpandedKeys"
            :indent="10"
            :default-expand-all="defaultExpand"
            @check="handleCheck"
            @node-click="handleNodeClick"
            @node-expand="handleNodeExpand"
            @node-collapse="handleNodeCollapse"
          >
            <span
              slot-scope="{ node, data }"
              class="custom-tree-node"
            >
              <span v-if="data.type === 0">
                <svg-icon
                  :icon-class="getTreeIconClass(data)"
                />
                <span>
                  {{ data.name }}({{ data.onlineNum }}/{{ data.total }})
                </span>
              </span>
              <span v-if="data.type === 1">
                {{ data.name }}({{ data.onlineNum }}/{{ data.total }})
              </span>
              <span v-if="data.type === undefined">
                <svg-icon
                  :icon-class="getStaffIconClass(data)"
                  class="svg-icon-vehicle"
                />
                <span>{{ data.name }}</span>
                <svg-icon :icon-class="getIconByterminal(data)" />
                <span
                  title="历史轨迹查询"
                  class="tree-icon"
                  @click="toHistoryTrack(node, data)"
                >
                  <svg-icon icon-class="trajectory" />
                </span>
              </span>
            </span>
          </vue-easy-tree>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script>
import crudVehicle from '@/api/base/vehicle';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

export default {
  name: 'VehicleMultiSelect',
  components: {},
  directives: {
    drag (el, bindings, vnode) { // 横向拖拽tree
      el.onmousedown = bindings.value ? function (e) {
        let w = el.clientWidth || 230;
        let x = e.clientX;
        document.onmousemove = function (e) {
          let k = w + e.clientX - x;
          if (k >= 230 && k <= 660) {
            el.style.width = k + 'px';
            vnode.context.$emit('setTableWidth', k);
          }
        };
        document.onmouseup = function () {
          document.onmousemove = document.onmouseup = null;
        };
      } : null;
    }
  },
  props: {
    pageSrc: {
      type: String,
      default: ''
    },
    customTreeApi: {
      type: Function,
      default: null
    },
    isShow: {
      type: Boolean,
      default: false
    },
    monitorCarObj: {
      type: Object,
      default: () => {
        return {};
      }
    },
    fromCe: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      activeNames: ['secondItem'],
      tableStyle: '',
      data: [],
      loading: false,
      defaultCheckedKeys: [],
      defaultExpandedKeys: [0],
      treeMaxHeight: '0px',
      filterText: '',
      searchText: '', // 用来手动清除filterText值但未点击查询按钮的情况下搜索整个树节点使用
      treeKey: 0, // 车辆树的key(用来更新树)
      defaultProps: {
        isLeaf: 'leaf'
      },
      terminalTreeData: [],
      lazy: true,
      defaultExpand: false,
      treeCheckedKeys: [],
      filterCheckedKeys: []
    };
  },
  watch: {
    isShow:{
      handler(val) {
        if (val) {
          this.$nextTick(()=> {
            if (this.$store.state.vehicle.tree?.length) {
              this.terminalTreeData = Object.freeze(this.$store.state.vehicle.tree);
              this.data = (this.$store.state.vehicle.tree || []).map(item => ({...item, children: []}));
            } else {
              this.onRefresh('store');
            }
          });
        }
      },
      immediate: true
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.updateStyle();
      this.$nextTick(() => {
        if (this.$route.query.deviceId) {
          this.setSelectedVehicle(this.$route.query);
        }
      });
    });
  },
  activated () {
    this.$nextTick(() => {
      if (this.$route.query.deviceId) {
        this.setSelectedVehicle(this.$route.query);
      }
    });
  },
  methods: {
    // 点击行
    handleNodeClick(data, node) {
      this.$emit('treeNodeClick', {
        ...data,
        deviceType: data.type === undefined ? Number(node.parent.data.id.slice(-1)) : undefined
      });
    },
    // 修改终端树节点状态
    updateStateTerminal (data) {
      // 实测修改2000个终端状态, 执行需要两秒多, 期间会导致页面阻塞, 因此加个限制<500
      if (data && data.length && data.length < 500) {
        console.time();
        data.forEach(element => {
          this.updateNodeById(this.terminalTreeData, element.id, (node) => {
            node.fusionState = element.fusionState;
          });
        });
        const defaultCheckedKeys = this.$refs.tree.getCheckedKeys();
        if (this.lazy) {
          this.data = (this.terminalTreeData || []).map(item => ({...item, children: []}));
        } else {
          const list = this.filterNestedArray(this.terminalTreeData, this.searchText);
          this.data = list;
        }
        this.$nextTick(() => {
          this.$refs.tree.setCheckedKeys(defaultCheckedKeys);
        });
        console.timeEnd();
      }
    },
    // 查找对应的对象修改其内容
    updateNodeById (tree, id, updateFn) {
      for (let node of tree) {
        if (node.id === id) {
          updateFn(node);
          return true;
        }
        if (node.children && node.children.length > 0) {
          const found = this.updateNodeById(node.children, id, updateFn);
          if (found) {
            return true;
          }
        }
        if (node.tags && node.tags.length > 0) {
          const found = this.updateNodeById(node.tags, id, updateFn);
          if (found) {
            return true;
          }
        }
      }
      return false;
    },
    toHistoryTrack (node, data) {
      const trackInfo = JSON.stringify({
        deviceId: data.id,
        deviceType: Number(node.parent.data.id.at(-1)),
        targetName: data.name,
        id: data.id
      });
      localStorage.setItem('TRACK_INFO', trackInfo);
      this.$router.push({
        path: `/monitoring/trackInfo/${this.fromCe ? 'indexCE': 'index'}`,
        query: {
          isRouter: this.$route.fullPath
        }
      });
    },
    /**
     * 点击刷新按钮
     */
    handleRefresh() {
      // 先清除地图上的车辆图标
      this.$emit('clearAll');
      this.onRefresh();
    },
    // 加载子节点
    loadTreeNode (node, resolve) {
      if (node.level > 0) {
        const result = this.findInNestedArray(this.terminalTreeData, item => item.id === node.data.id);
        if (result) {
          if (node.data.type === 1) {
            const list = result.tags.map(item => ({
              ...item,
              leaf: true
            }));
            resolve(list);
          } else {
            const list = result.children.map(item => ({
              ...item,
              children: [],
              tags: []
            }));
            resolve(list);
          }
        } else {
          resolve([]);
        }
      } else {
        resolve(this.terminalTreeData.map(item => ({...item, children: []})));
      }
    },
    findInNestedArray(array, condition) {
      for (let item of array) {
        if (condition(item)) {
          return item;
        }
        if (item.children && item.children.length > 0) {
          const found = this.findInNestedArray(item.children, condition);
          if (found) {
            return found;
          }
        }
      }
      return null;
    },
    /**
     * 刷新
     */
    onRefresh (type) {
      this.loading = true;
      this.onClear();
      if (!this.customTreeApi) {
        // 使用新的动态树接口，传递当前展开的节点ID
        crudVehicle.dynamicTree({ expandedNodeIds: this.defaultExpandedKeys }).then(treeData => {
          this.terminalTreeData = Object.freeze(treeData);
          this.data = (treeData || []).map(item => ({...item, children: []}));
          this.loading = false;
          if (type === 'store') {
            this.$store.commit('SET_TREE', treeData || []);
          }
        }).catch(msg => {
          this.$notify({
            title: msg,
            type: 'warning'
          });
          this.loading = false;
        });
      } else {
        this.customTreeApi().then(treeData => {
          this.terminalTreeData = Object.freeze(treeData);
          this.data = (treeData || []).map(item => ({...item, children: []}));
          this.loading = false;
          if (type === 'store') {
            this.$store.commit('SET_TREE', treeData || []);
          }
        }).catch(msg => {
          this.$notify({
            title: msg,
            type: 'warning'
          });
          this.loading = false;
        });
      }
      // 滚动后刷新接口无法恢复到初始状态, 因此手动将scrollTop设为0
      this.$nextTick(() => {
        if (this.$refs?.tree.$children[0]?.$el) {
          this.$refs.tree.$children[0].$el.scrollTop = 0;
        }
      });
    },
    /**
     * 节点过滤(替换新的查询搜索)
     */
    searchFilterText (val) {
      if (val) {
        this.searchText = val;
        if (this.lazy) { // lazy为true时, 代表此前是没有搜索过数据的
          const defaultCheckedKeys = this.$refs.tree.getCheckedKeys();
          this.treeCheckedKeys = defaultCheckedKeys;
        } else { // lazy为false时, 代表此前是搜索过数据的, 搜索过数据就直接拿filterCheckedKeys设置选中
          this.treeCheckedKeys = [...new Set([...this.treeCheckedKeys, ...this.filterCheckedKeys])];
        }
        const list = this.filterNestedArray(this.terminalTreeData, val);
        this.data = list;
        this.lazy = false;
        this.defaultExpand = true;
        this.treeKey += 1;
      } else {
        this.searchText = '';
        this.data = this.terminalTreeData.map(item => ({...item, children: []}));
        this.lazy = true;
        this.defaultExpand = false;
        this.treeKey += 1;
        for (let index = 0; index < this.filterCheckedKeys.length; index++) {
          const element = this.filterCheckedKeys[index];
          this.defaultExpandedKeys = [...new Set([...this.defaultExpandedKeys, ...this.findAncestorsById(this.terminalTreeData, element)])];
        }
        this.treeCheckedKeys = [...new Set([...this.treeCheckedKeys, ...this.filterCheckedKeys])];
        this.filterCheckedKeys = [];
      }
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys(this.treeCheckedKeys);
      });
    },
    // 查找指定id的节点及其所有父级和祖宗级的id
    findAncestorsById(data, targetId) {
      const ancestors = [];
      return this.findRecursive(data, targetId, ancestors);
    },
    findRecursive(items, targetId, path = []) {
      for (let item of items) {
        if (item.id === targetId) {
          return [...path];
        }
        if (item.children && item.children.length > 0) {
          const result = this.findRecursive(item.children, targetId, [...path, item.id]);
          if (result) {
            return result;
          }
        }
        if (item.tags && item.tags.length > 0) {
          const result = this.findRecursive(item.tags, targetId, [...path, item.id]);
          if (result) {
            return result;
          }
        }
      }
      return null;
    },
    filterNestedArray(array, filterValue) {
      const result = [];
      result.push(...this.recursiveFilter(array, filterValue));
      return result;
    },
    recursiveFilter(items, filterValue) {
      return items.reduce((acc, item) => {
        if (item.name.includes(filterValue)) {
          acc.push(this.renameTagsToChildren({ ...item }));
        } else if (item.children && item.children.length > 0) {
          const filteredChildren = this.recursiveFilter(item.children, filterValue);
          if (filteredChildren.length > 0) {
            acc.push({ ...item, children: filteredChildren });
          }
        } else if (item.tags && item.tags.length > 0) {
          const filteredChildren = this.recursiveFilter(item.tags, filterValue);
          if (filteredChildren.length > 0) {
            acc.push({ ...item, children: filteredChildren });
          }
        }
        return acc;
      }, []);
    },
    // 将所有的tags属性值替换为children, 保持统一便组件使用
    renameTagsToChildren(item) {
      if (item.tags) {
        item.children = item.tags;
      }
      if (item.children) {
        item.children = item.children.map(child => this.renameTagsToChildren(child));
      }
      return item;
    },
    /**
     * 勾选-处理多个
     * @param node
     * @param options
     * @param options.checkedNodes 勾选的节点
     * @param options.checkedKeys 勾选节点的key
     * @param options.halfCheckedNodes
     * @param options.halfCheckedKeys
     */
    handleCheck(node, options) {
      console.log('node', node);
      console.log('options', options);
      let checkedKeys = options.checkedKeys;
      let checkedNode = node;
      let treeData = !this.lazy ? this.data : this.terminalTreeData;
      checkedNode.checked = checkedKeys.includes(node.id);
      if (!this.lazy && checkedNode.checked) {
        this.filterCheckedKeys.push(node.id);
      } else if (!this.lazy && !checkedNode.checked) {
        this.filterCheckedKeys = this.filterCheckedKeys.filter(item => item !== node.id);
        this.treeCheckedKeys = this.treeCheckedKeys.filter(item => item !== node.id);
      }
      let checkedList = [];
      // 勾选
      if (checkedNode.checked) {
        // 勾选机构或分组时
        if (checkedNode.type !== undefined) {
          const result = this.findInNestedArray(treeData, item => item.id === checkedNode.id);
          if (result) {
            checkedList = this.getAllTerminal([result]);
          }
        } else {
          const nodeData = this.$refs.tree.getNode(checkedNode.id);
          // 勾选终端时
          checkedList = [{
            ...checkedNode,
            deviceType: Number(nodeData.parent.data.id.slice(-1))
          }];
        }
      } else { // 取消勾选
        // 取消勾选机构或分组时
        if (checkedNode.type !== undefined) {
          const result = this.findInNestedArray(treeData, item => item.id === checkedNode.id);
          if (result) {
            checkedList = this.getDelTerminal([result]);
          }
        } else {
          const nodeData = this.$refs.tree.getNode(checkedNode.id);
          // 取消勾选终端时
          checkedList = [{
            ...checkedNode,
            deviceType: Number(nodeData.parent.data.id.slice(-1))
          }];
        }
      }
      this.$emit('checkedVehiclesChange', checkedNode, checkedList);
    },
    // 获取勾选节点下的所有终端(已显示的不在获取), 返回一个终端数组
    getAllTerminal(data) {
      let arr = [];
      for (let index = 0; index < data.length; index++) {
        const element = data[index];
        if (element.tags && element.tags.length) {
          for (let k = 0; k < element.tags.length; k++) {
            const item = element.tags[k];
            if (!this.monitorCarObj[item.id]) {
              arr.push({
                ...item,
                deviceType: Number(element.id.slice(-1))
              });
            }
          }
          // arr = arr.concat(element.tags.filter(v => !this.monitorCarObj[v.id]));
        }
        if (element.children && element.children.length) {
          const result = this.getAllTerminal(element.children);
          arr = arr.concat(result);
        }
      }
      return arr;
    },
    // 获取勾选节点下的所有终端(已删除的不在获取), 返回一个终端数组
    getDelTerminal(data) {
      let arr = [];
      for (let index = 0; index < data.length; index++) {
        const element = data[index];
        if (element.tags && element.tags.length) {
          for (let k = 0; k < element.tags.length; k++) {
            const item = element.tags[k];
            if (this.monitorCarObj[item.id]) {
              arr.push({
                ...item,
                deviceType: Number(element.id.slice(-1))
              });
            }
          }
          // arr = arr.concat(element.tags.filter(v => this.monitorCarObj[v.id]));
        }
        if (element.children && element.children.length) {
          const result = this.getDelTerminal(element.children);
          arr = arr.concat(result);
        }
      }
      return arr;
    },
    /**
     * 获取其他类型的图标名
     * @param {Object} data
     * @param {Object} data.originData
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getStaffIconClass (data) {
      const vehicleModel = ['101', '102', '103', '104', '105'];
      const materialsModel = ['106', '108', '109', '111', '112', '114'];
      const personnelModel = ['201', '202', '301', '107', '110', '113'];
      const shortMessageModel = ['302', '303'];
      const timeServiceModel = ['501', '502', '503'];
      const monitorModel = ['401', '402'];
      let vehicleIcon = '';
      if (vehicleModel.includes(data.treeCategory)) {
        vehicleIcon = this.colorStaffType(data, 'vehicle'); // 车辆
      } else if (materialsModel.includes(data.treeCategory)) {
        vehicleIcon = this.colorStaffType(data, 'materials'); // 物资
      } else if (personnelModel.includes(data.treeCategory)) {
        vehicleIcon = this.colorStaffType(data, 'personnel'); // 人员
      } else if (shortMessageModel.includes(data.treeCategory)) {
        vehicleIcon = this.colorStaffType(data, 'shortMessage'); // 短报文终端
      } else if (timeServiceModel.includes(data.treeCategory)) {
        vehicleIcon = this.colorStaffType(data, 'timeService'); // 授时终端
      } else if (monitorModel.includes(data.treeCategory)) {
        vehicleIcon = this.colorStaffType(data, 'monitor'); // 监测终端
      } else if (data.treeCategory === '0') {
        vehicleIcon = this.colorStaffType(data, 'other'); // 其他
      }
      return vehicleIcon;
    },
    colorStaffType (val, type) {
      // 0-离线 1-静止 2-移动
      // 离线(Offline) 静止(Static) 移动(Move)
      let vehicleIcon = '';
      switch (val.fusionState) {
      case 0:
        vehicleIcon = `${type}Offline`;
        break;
      case 1:
        vehicleIcon = `${type}Static`;
        break;
      case 2:
        vehicleIcon = `${type}Move`;
        break;
      }
      return vehicleIcon;
    },
    /**
     * 获取车组的图标名称
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getTreeIconClass (data) {
      let treeIcon = '';
      if (data.onlineNum > 0) {
        treeIcon = 'treeOnline';
      } else {
        treeIcon = 'tree';
      }
      return treeIcon;
    },
    /**
     * 获取定位终端/视频终端
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getIconByterminal (data) {
      if (data.isVideo === 0) {
        return 'positioningTerminal';
      } else if (data.isVideo === 1) {
        return 'videoTerminal';
      }
    },
    /**
     * 设置keys
     * @param keys
     */
    setCheckedKeys (keys) {
      this.$refs.tree.setCheckedKeys(keys);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Vehicle', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('Vehicle', value);
    },
    /**
     * 收缩状态改变
     * @param val
     */
    handleCollapseChange (val) {
      this.$nextTick(() => {
        this.updateStyle();
      });
    },
    /**
     * 更新样式
     */
    updateStyle () {
      let selectComponentDomHeight = this.$refs['selectComponentContainerDom'].clientHeight;
      let firstCollapseItemDomHeight = this.$refs['firstCollapseItemComponent'].clientHeight; // 搜索条件高度
      let headerDomHeight = this.$refs['headerDom'].clientHeight; // 车辆列表标题高度/车辆目录标题高度(两个高度一致)
      let maxHeight = selectComponentDomHeight - firstCollapseItemDomHeight - 2 * headerDomHeight - 8;
      if (this._lastTableHeight !== maxHeight) {
        this.tableStyle = `height: ${maxHeight + 'px'}; overflow-y: auto`;
        this._lastTableHeight = maxHeight;
        this.treeMaxHeight = maxHeight + 'px';
        requestAnimationFrame(this.updateStyle);
      }
    },
    /**
     * 清除搜索条件
     */
    onClear () {
      this.filterText = '';
      this.searchText = '';
      // 清除勾选和展开节点的状态
      this.defaultCheckedKeys = [];
      this.defaultExpandedKeys = [];
      this.treeCheckedKeys = [];
      this.filterCheckedKeys = [];
      this.lazy = true;
      this.defaultExpand = false;
      // 修改lazy后需要手动更新tree
      this.treeKey += 1;
    },
    /**
     * 清空用户选择
     * @public
     */
    clear () {
      this.$refs.tree.setCheckedAll(false);
    },
    /**
     * 设置选中的车辆并搜索
     * @param {String} val 车牌号
     */
    setSelectedVehicle (val) {
      if (this.loading) {
        setTimeout(() => {
          this.setSelectedVehicle(val);
        }, 1000);
      } else {
        // 替换新的查询搜索
        this.filterText = val.targetName;
        this.searchFilterText(this.filterText);
        this.$nextTick(() => {
          // 搜索后勾选
          this.$refs.tree.setCheckedKeys([val.deviceId], false);
          const { data: node, parent } = this.$refs.tree.getNode(val.deviceId);
          node.checked = true;
          this.filterCheckedKeys.push(node.id);
          let checkedList = [];
          // 勾选机构或分组时
          if (node.type !== undefined) {
            const result = this.findInNestedArray(this.data, item => item.id === node.id);
            if (result) {
              checkedList = this.getAllTerminal([result]);
            }
          } else {
            // 勾选终端时
            checkedList = [{
              ...node,
              deviceType: Number(parent.data.id.slice(-1))
            }];
          }
          // 勾选后下发事件
          this.$emit('checkedVehiclesChange', node, checkedList);
        });
      }
    },
    /**
     * 展开节点触发的事件
     * @description 改变defaultExpandedKeys用于保留状态
     * @see onUpdate
     */
    handleNodeExpand (data, node) {
      this.defaultExpandedKeys.push(data.id);
    },
    /**
     * 收起节点触发的事件
     * @description 改变defaultExpandedKeys用于保留状态
     * @see onUpdate
     */
    handleNodeCollapse (data, node) {
      let val = data.id;
      let index = -1;
      for (let i = 0; i < this.defaultExpandedKeys.length; i++) {
        if (this.defaultExpandedKeys[i] === val) {
          index = i;
          break;
        }
      }
      if (index > -1) {
        this.defaultExpandedKeys.splice(index, 1);
      }
      // 收起父节点时同时收起子节点
      if (node.childNodes.length > 0) {
        let list = this.flattenKeyList(node.childNodes);
        for (let i = 0; i < list.length; i++) {
          const element = list[i];
          const key = this.defaultExpandedKeys.findIndex(item => item === element);
          if (key !== -1) {
            this.defaultExpandedKeys.splice(key, 1);
          }
        }
      }
    },
    flattenKeyList(data) {
      let result = [];
      data.forEach((item)=> {
        if (item.data.type === 0 || item.data.type === 1) {
          result.push(item.data.id);
          if (item.childNodes && item.childNodes.length > 0 && item.data.type === 0) {
            let childId = this.flattenKeyList(item.childNodes);
            result = result.concat(childId);
          }
        }
      });
      return result;
    },
    /**
     * 更新数据，保留之前的状态
     * @see defaultCheckedKeys
     * @see defaultExpandedKeys
     */
    onUpdate () {
      // this.loading = true;
      if (!this.customTreeApi) {
        // 使用新的动态树接口，传递当前展开的节点ID
        crudVehicle.dynamicTree({ expandedNodeIds: this.defaultExpandedKeys }).then(treeData => {
          if (JSON.stringify(treeData) !== JSON.stringify(this.terminalTreeData)) {
            // console.log('crudVehicle.tree-->', treeData);
            // 直接使用default-checked-keys传入keys可能会出现不可预估的错误, 因此使用setCheckedKeys设置keys
            const defaultCheckedKeys = this.$refs.tree.getCheckedKeys();
            this.terminalTreeData = Object.freeze(treeData);
            if (this.lazy) {
              this.data = (treeData || []).map(item => ({...item, children: []}));
            } else {
              const list = this.filterNestedArray(this.terminalTreeData, this.searchText);
              this.data = list;
            }
            this.$nextTick(() => {
              this.$refs.tree.setCheckedKeys(defaultCheckedKeys);
            });
          }
          // this.loading = false;
          this.$emit('CheckedNodesUpdate');
        }).catch(msg => {
          // this.loading = false;
        });
      } else {
        this.customTreeApi().then(treeData => {
          if (JSON.stringify(treeData) !== JSON.stringify(this.terminalTreeData)) {
            // 直接使用default-checked-keys传入keys可能会出现不可预估的错误, 因此使用setCheckedKeys设置keys
            const defaultCheckedKeys = this.$refs.tree.getCheckedKeys();
            this.terminalTreeData = Object.freeze(treeData);
            if (this.lazy) {
              this.data = (treeData || []).map(item => ({...item, children: []}));
            } else {
              const list = this.filterNestedArray(this.terminalTreeData, this.searchText);
              this.data = list;
            }
            this.$nextTick(() => {
              this.$refs.tree.setCheckedKeys(defaultCheckedKeys);
            });
          }
          // this.loading = false;
          this.$emit('CheckedNodesUpdate');
        }).catch(msg => {
          // this.loading = false;
        });
      }
      // 滚动后刷新接口无法恢复到初始状态, 因此手动将scrollTop设为0
      // this.$nextTick(() => {
      //   if (this.$refs?.tree.$children[0]?.$el) {
      //     this.$refs.tree.$children[0].$el.scrollTop = 0;
      //   }
      // });
    }
  }

};

</script>

<style lang="less" scoped>
  @import "../../../assets/less/variables.less";
  .xh-select-component-collapse-item /deep/.el-collapse-item__content{
    padding-bottom: 0;
    padding-top: 0;
  }
  .custom-tree-node{
    width: 100%;
  }
  .custom-tree-node:hover{
    .tree-icon{
      display: inline-block;
    }
  }
  .tree-icon{
    padding-left: 5px;
    display: none;
  }
  .vehicle-tree-container, .vehicle-tree-content, .xh-select-component-collapse-item, .xh-select-component-collapse-item /deep/ .el-collapse-item__content, .easy-tree, .easy-tree /deep/ .vue-recycle-scroller{
    height: 100% !important;
  }
  .vehicle-tree-content, .xh-select-component-collapse-item {
    display: flex;
    flex-direction: column;
  }
  .vehicle-tree-content-collapse, .xh-select-component-collapse-item /deep/ .el-collapse-item__wrap{
    flex: 1;
  }
  .vehicle-tree-content-collapse{
    overflow: auto;
  }
  .easy-tree /deep/ .vue-recycle-scroller__item-wrapper{
    overflow: visible;
  }
  .header {
    background-color: #f3f6f8;
    height: 54px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    .car-filter-container {
      display: flex;
      align-items: center;
      border: 1px solid #bfbfbf;
      background-color: #ffffff;
      padding-left: 4px;
      flex: 1;
      box-sizing: border-box;
      overflow: hidden;

      ::v-deep .el-input__inner {
        border: none !important;
        box-shadow: none !important;
      }

      .el-icon-search, .el-icon-refresh {
        font-size: 18px;
        color: #c0c0c0;
      }

      .el-icon-refresh {
        padding-right: 4px;
        cursor: pointer;
      }
      .search-button {
        border: none;
        border-left: 1px solid #bfbfbf !important;
        border-radius: 0 !important;
      }
    }
  }
</style>
