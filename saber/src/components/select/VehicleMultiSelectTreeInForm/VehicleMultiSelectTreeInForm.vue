<template>
  <div
    :class="{'xh-dropDown-invisible': !isDropDown}"
    class="vehicle-tree-container"
  >
    <div
      ref="selectComponentContainerDom"
      v-drag="pageSrc==='lm'"
      :class="selectComponentLayoutDom"
      v-loading="loading"
    >
      <div
        ref="headerDom"
        class="xh-select-component-header"
      >
        <span class="xh-select-component-header-title">终端列表</span>
        <!-- <span
          title="刷新"
          class="xh-select-component-header-refreshBtn"
          @click="onRefresh"
        >
          <i class="el-icon-refresh" />
        </span> -->
        <span
          v-show="nextButtonVisible"
          title="展开下一个搜索结果"
          class="xh-select-component-header-refreshBtn"
          @click="onNext"
        >
          <i class="el-icon-bottom-right" />
        </span>
        <div
          v-if="!hiddenToggleButton"
          v-show="formVideoPage && !fromSettings"
          class="xh-select-component-toggle-video-button"
          @click="changeCarList"
        />
      </div>
      <div
        ref="firstCollapseItemComponent"
        class="header"
      >
        <div
          class="car-filter-container"
        >
          <i class="el-icon-search" />
          <el-input
            v-model="filterText"
            clearable
            size="mini"
            placeholder="搜索终端名称/终端类型/机构"
            class="car-filter"
            @input="searchFilterText"
          />
          <i
            class="el-icon-refresh"
            @click="onRefresh"
          />
        </div>
      </div>
      <el-collapse
        v-show="isDropDown"
        v-model="activeNames"
        class="collapse"
        @change="handleCollapseChange"
      >
        <!-- <el-collapse-item
          ref="firstCollapseItemComponent"
          name="firstItem"
          class="xh-select-component-collapse-item"
        >
          <template slot="title">
            <span
              class="xh-select-component-filter-item-key space-set"
            >{{ getLabel('licencePlate') }}</span>
            <el-input
              v-model="query.licencePlate"
              clearable
              size="small"
              :placeholder="getPlaceholder('licencePlate')"
              class="xh-select-component-filter-item-value"
              @input="searchFilter"
            />
          </template>
          <div class="over-hidden">
            <span class="xh-select-component-filter-item-key">{{ getLabel('vehicleState') }}</span>
            <xh-select
              v-model="query.terminalState"
              clearable
              size="small"
              :placeholder="getPlaceholder('vehicleState')"
              class="xh-select-component-filter-item-value"
              @change="searchFilter"
            >
              <el-option
                v-for="item in dict.dict.terminalState"
                :key="item.value"
                :label="item.label"
                :value="Number(item.value)"
              />
            </xh-select>
            <span
              class="xh-select-component-filter-item-key width-set"
            >{{ getLabel('simId') }}</span>
            <el-input
              v-model="query.simId"
              clearable
              size="small"
              :placeholder="getPlaceholder('simId')"
              class="xh-select-component-filter-item-value"
              @input="searchFilter"
            />
            <span
              class="xh-select-component-filter-item-key width-set"
            >{{ getLabel('terminalModel') }}</span>
            <el-input
              v-model="query.terminalModel"
              clearable
              size="small"
              :placeholder="getPlaceholder('terminalModel')"
              class="xh-select-component-filter-item-value"
              @input="searchFilter"
            />
            <div>
              <span class="xh-select-component-filter-item-key">{{ getLabel('deptName4') }}</span>
              <el-input
                v-model="query.deptName"
                clearable
                size="small"
                :placeholder="getPlaceholder('deptName4')"
                class="xh-select-component-filter-item-value"
                @input="searchFilter"
              />
            </div>
          </div>
        </el-collapse-item> -->
        <el-collapse-item
          name="secondItem"
          class="xh-select-component-collapse-item"
        >
          <!--车辆标题插槽-->
          <template slot="title">
            <div
              class="collapse-item-title-slot"
            >
              终端目录
            </div>
          </template>
          <!--树渲染-->
          <vue-easy-tree
            ref="tree"
            :data="data"
            node-key="id"
            draggable
            class="easy-tree"
            :allow-drop="allowDrop"
            :allow-drag="allowDrag"
            show-checkbox
            :filter-node-method="filterHandle"
            :style="tableStyle"
            :height="treeMaxHeight"
            :default-checked-keys="defaultCheckedKeys"
            :default-expanded-keys="defaultExpandedKeys"
            :class="xhTreeLayout"
            :indent="10"
            @node-drag-start="handleDragStart"
            @node-drag-enter="handleDragEnter"
            @node-drag-leave="handleDragLeave"
            @node-drag-over="handleDragOver"
            @node-drag-end="handleDragEnd"
            @node-drop="handleDrop"
            @check-change="handleCheckChange"
            @check="handleCheck"
            @node-click="handleNodeClick"
            @node-expand="handleNodeExpand"
            @node-collapse="handleNodeCollapse"
          >
            <span
              slot-scope="{ node, data }"
              class="custom-tree-node"
              @mouseover="mouseOverNode(node)"
            >
              <!-- <svg-icon
                v-if="data.type === 'dept'"
                icon-class="tree"
              /> -->
              <svg-icon
                v-if="data.type === 'dept'"
                :icon-class="getTreeIconClass(data)"
              />
              <span v-if="data.type === 'dept'">
                {{ node.label }}({{ data.onlineVehicleCount }}/{{ data.vehicleCount }})
              </span>
              <span v-if="data.type === 'dept_son'">
                {{ node.label }}({{ data.onlineVehicleCount }}/{{ data.vehicleCount }})
              </span>
              <span v-if="data.type !== 'dept' && data.type !== 'dept_son'">
                <svg-icon
                  :icon-class="getStaffIconClass(data)"
                  class="svg-icon-vehicle"
                />
                {{ node.label }}
                <!-- <span>{{ getAccState(data) }}</span> -->
              </span>
              <svg-icon
                :icon-class="getIconByterminal(data)"
              />
            </span>
          </vue-easy-tree>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script>
import crudVehicle from '@/api/base/vehicle';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import variables from '@/assets/less/variables.js';
// import terminalStateEnum from '@/enumerate/vehicle/terminalState';

export default {
  name: 'VehicleMultiSelect',
  components: { },
  directives: {
    drag (el, bindings, vnode) { // 横向拖拽tree
      el.onmousedown = bindings.value ? function (e) {
        let w = el.clientWidth || 300;
        let x = e.clientX;
        document.onmousemove = function (e) {
          let k = w + e.clientX - x;
          if (k >= 300 && k <= 440) {
            el.style.width = k + 'px';
            vnode.context.$emit('setTableWidth', k - 300);
          }
        };
        document.onmouseup = function () {
          document.onmousemove = document.onmouseup = null;
        };
      } : null;
    }
  },
  mixins: [],
  dicts: ['terminalState', 'vehicleColor'],
  props: {
    formVideoPage: {
      type: Boolean,
      default: false
    },
    detailButtonVisible: {
      type: Boolean,
      default: false
    },
    fromSettings: {
      type: Boolean,
      default: false
    },
    hiddenToggleButton: {
      type: Boolean,
      default: false
    },
    widthAble: {
      type: Boolean,
      default: false
    },
    pageSrc: {
      type: String,
      default: ''
    },
    customTreeApi: {
      type: Function,
      default: null
    }
  },
  data () {
    return {
      name: '',
      permission: {
        add: ['admin', 'vehicle:add'],
        edit: ['admin', 'vehicle:edit'],
        del: ['admin', 'vehicle:del']
      },
      activeNames: ['secondItem'],
      tableStyle: '',
      query: {
        licencePlate: '',
        terminalState: '',
        deptName: '',
        simId: '',
        terminalModel: ''
      },
      nextButtonVisible: false,
      data: [],
      bakData: [], // 记录数据
      lastCheckedChannels: [],
      lastCheckedKeys: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      variables: {...variables},
      isDropDown: true,
      loading: false,
      currentVehicle: [],
      defaultCheckedKeys: [],
      defaultExpandedKeys: [],
      treeMaxHeight: '0px',
      filterText: '',
    };
  },
  computed: {
    selectComponentLayoutDom: function () {
      if (this.pageSrc === 'lm') { // live-map
        return 'xh-select-component-container-full-fill';
      } else if (this.pageSrc === 'st') {
        return 'xh-select-component-video-container-system';
      } else if (this.pageSrc === 'rm') { // region-manage
        return 'vehicle-tree-content';
      } else {
        return 'xh-select-component-container';
      }
    },
    xhTreeLayout: function () {
      if (this.pageSrc === 'lm') { // live-map
        return 'xh-tree-live-map';
      } else {
        return false;
      }
    }
  },
  watch: {
  },
  mounted () {
    if (sessionStorage.getItem('storedVehicleTreeData')) {
      this.data = JSON.parse(sessionStorage.getItem('storedVehicleTreeData'));
    }
    this.$nextTick(() => {
      this.updateStyle();
      this.onRefresh();

      this.$nextTick(() => {
        if (this.$route.query.licencePlate) {
          this.setSelectedVehicle(this.$route.query.licencePlate, this.$route.query.id);
        }
      });
    });
  },
  activated () {
    this.$nextTick(() => {
    });
  },
  methods: {
    /**
     * 刷新
     */
    onRefresh () {
      this.loading = true;
      this.onClear();
      if (!this.customTreeApi) {
        crudVehicle.tree().then(treeData => {
          this.data = treeData;
          let vehicleCount = 0;
          for (let i = 0; i < treeData.length; i++) {
            vehicleCount += treeData[i].vehicleCount;
          }
          this.vehicleCount = vehicleCount;
          this.nextButtonVisible = false;
          this.loading = false;
        }).catch(msg => {
          this.$notify({
            title: msg,
            type: 'warning'
          });
          this.loading = false;
        });
      } else {
        this.customTreeApi().then(treeData => {
          this.data = treeData;
          let vehicleCount = 0;
          for (let i = 0; i < treeData.length; i++) {
            vehicleCount += treeData[i].vehicleCount;
          }
          this.vehicleCount = vehicleCount;
          this.nextButtonVisible = false;
          this.loading = false;
        }).catch(msg => {
          this.$notify({
            title: msg,
            type: 'warning'
          });
          this.loading = false;
        });
      }
    },
    /**
     * 下一个
     */
    onNext () {
      this.$refs.tree.filter(this.query, {
        ignoreLeafType: 'dept',
        expandNew: true
      });
      this.nextButtonVisible = true;
    },
    /**
     * 节点过滤
     */
    searchFilter () {
      if (this.query.licencePlate === '' &&
        this.query.terminalState === '' &&
        this.query.deptName === '' &&
        this.query.simId === '' &&
        this.query.terminalModel === '') {
        this.$refs.tree.filter(this.query);
        this.nextButtonVisible = false;
      } else {
        this.$refs.tree.filter(this.query, {
          ignoreLeafType: 'dept',
          expandNew: false
        });
        this.nextButtonVisible = true;
      }
    },
    /**
     * 节点过滤(替换新的查询搜索)
     */
    searchFilterText (val) {
      this.$refs.tree.filter(val);
    },
    /**
     * 节点搜索过滤函数
     * @param value
     * @param {String} value.licencePlate
     * @param {Int} value.terminalState
     * @param {String} value.deptName
     * @param {String} value.simId
     * @param {Object} data
     * @param {Object} data.originData
     * @param {String} data.originData.licencePlate
     * @param {Number} data.originData.terminalState
     * @param {String} data.originData.deptName
     * @param {Object} node
     * @param data
     */
    filterNode (value, data, node) {
      if (data.type === 'dept' && data.originData.name.indexOf(value.deptName) === -1) {
        return false;
      }
      if (data.type === 'vehicle') {
        let vehicleNode = node.parent;
        // 判断如果子节点的单位和目标单位不一致，则不显示
        if (value.deptName !== '' && vehicleNode.data.label.indexOf(value.deptName) === -1) {
          return false;
        }
        if (value.licencePlate !== '' && data.originData.licencePlate.indexOf(value.licencePlate) === -1) {
          return false;
        }
        if (value.simId !== '' && data.originData.simId.indexOf(value.simId) === -1) {
          return false;
        }
        if (value.terminalModel !== '' && data.originData.terminalModel.indexOf(value.terminalModel) === -1) {
          return false;
        }
        if ((value.terminalState === 0 || value.terminalState === 1) && data.originData.terminalState !== value.terminalState) {
          return false;
        }
      }
      return true;
    },
    /**
     * 节点搜索过滤函数(替换新的查询搜索)
     */
    filterHandle (value, data, node) {
      let parentNode = node.parent; // 父级node
      let labels = [node.label]; // 当前node的名字
      let level = 1; // 层级
      while (level < node.level) {
        labels = [...labels, parentNode.label]; // 当前node名字，父级node的名字
        parentNode = parentNode.parent;
        level++;
      }
      return labels.some(d => d.indexOf(value) !== -1);
    },
    /**
     * 节点开始拖拽时触发的事件
     */
    handleDragStart (node, ev) {
      // console.log('drag start', node);
      // ev.preventDefault();
      // let data = node.data;
      if (node.data.type === 'channel') {
        let transferData = {
          origin: 'VEHICLETREE',
          channelID: node.parent.data.originData.licencePlate + '_' + node.data.originData.channel,
          customData: {
            treeNodeKey: node.data.id
          }
        };
        ev.dataTransfer.setData('Text', JSON.stringify(transferData));
      } else {
        // ev.dataTransfer.setData('Text', node.data.originData.licencePlate);
      }
    },
    /**
     * 拖拽进入其他节点时触发的事件
     */
    handleDragEnter (draggingNode, dropNode, ev) {
      // console.log('tree drag enter: ', dropNode.label);
      ev.preventDefault();
    },
    /**
     * 拖拽离开某个节点时触发的事件
     */
    handleDragLeave (draggingNode, dropNode, ev) {
      // console.log('tree drag leave: ', dropNode.label);
      ev.preventDefault();
    },
    /**
     * 在拖拽节点时触发的事件（类似浏览器的 mouseover 事件）
     */
    handleDragOver (draggingNode, dropNode, ev) {
      // console.log('tree drag over: ', dropNode.label);
      ev.preventDefault();
    },
    /**
     * 拖拽结束时（可能未成功）触发的事件
     */
    handleDragEnd (draggingNode, dropNode, dropType, ev) {
      // console.log('tree drag end: ', dropNode && dropNode.label, dropType);
      ev.preventDefault();
    },
    /**
     * 拖拽成功完成时触发的事件
     * @param {Object} draggingNode 被拖拽节点对应的Node
     * @param {Object} dropNode 结束拖拽时最后进入的节点
     * @param {Object} dropType 被拖拽节点的放置位置
     * @param {Event} ev
     */
    handleDrop (draggingNode, dropNode, dropType, ev) {
      console.log('tree drop: ', dropNode.label, dropType);
    },
    /**
     * 拖拽时判定目标节点能否被放置。
     * @param {Object} draggingNode
     * @param {Object} dropNode
     * @param {Object} type 参数有三种情况：'prev'、'inner' 和 'next'，分别表示放置在目标节点前、插入至目标节点和放置在目标节点后
     */
    allowDrop (draggingNode, dropNode, type) {
      // if (dropNode.data.label === '二级 3-1') {
      //   return type !== 'inner';
      // } else {
      //   return true;
      // }
      return false;
    },
    /**
     * 判断某个节点是否允许拖拽
     * @param {Object} draggingNode
     */
    allowDrag (draggingNode) {
      // console.log(draggingNode);
      if (draggingNode.data.type === 'channel') {
        return true;
      }
      return false;
    },
    /**
     * 节点被点击
     * @param {Object} data 节点对象
     */
    handleNodeClick(data) {
      if (data.type !== 'dept' && data.type !== 'dept_son') {
        this.$emit('selectedRowSingle', data.originData);
      }
    },
    /**
     * 勾选-处理单个
     */
    handleCheckChange (data, checked, childrenNode) {
      // 以前处理this.currentVehicle，现在不处理
    },
    /**
     * 勾选-处理多个
     * @param node
     * @param options
     * @param options.checkedNodes 勾选的节点
     * @param options.checkedKeys 勾选节点的key
     * @param options.halfCheckedNodes
     * @param options.halfCheckedKeys
     */
    handleCheck (node, options) {
      let checkedNodes = options.checkedNodes;
      let checkedVehicles = [];
      checkedNodes.forEach(item => {
        if (item.type !== 'dept' && item.type !== 'dept_son') {
          checkedVehicles.push({
            deviceType: item.originData.deviceType,
            deviceId: item.originData.deviceId,
            id: item.id
          });
        }
      });
      this.$emit('checkedVehiclesChange', {
        checkedVehicles: checkedVehicles
      });
    },
    /**
     * 设置地图中心点
     * @param {Object} data
     */
    setLocation (data) {
      console.log('serLocation-->', data);
    },
    /**
     * 获取其他类型的图标名
     * @param {Object} data
     * @param {Object} data.originData
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getStaffIconClass (data) {
      const vehicleModel = ['101', '102', '103', '104', '105'];
      const materialsModel = ['106', '108', '109', '111', '112', '114'];
      const personnelModel = ['201', '202', '301', '107', '110', '113'];
      const shortMessageModel = ['302', '303'];
      const timeServiceModel = ['501', '502', '503'];
      const monitorModel = ['401', '402'];
      let vehicleIcon = '';
      if (vehicleModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'vehicle'); // 车辆
      } else if (materialsModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'materials'); // 物资
      } else if (personnelModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'personnel'); // 人员
      } else if (shortMessageModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'shortMessage'); // 短报文终端
      } else if (timeServiceModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'timeService'); // 授时终端
      } else if (monitorModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'monitor'); // 监测终端
      } else if (data.originData.treeCategory === '0') {
        vehicleIcon = this.colorStaffType(data.originData, 'other'); // 其他
      }
      return vehicleIcon;
    },
    /**
     * 获取车辆类型的图标名
     * @param {Object} data
     * @param {Object} data.originData
     * @param {Number} data.originData.terminalState
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getVehicleIconClass (data) {
      /* switch (data.originData.terminalState) {
        case terminalStateEnum.ONLINE: {
          return 'truckOnline';
        }
        case terminalStateEnum.OFFLINE: {
          return 'truckOffline';
        }
      }
      return ''; */
      let passengerModel = ['10', '11', '12', '13', '14', '15', '16'];
      let truckModel = ['20', '21', '22', '23'];
      let vehicleIcon = '';
      if (passengerModel.includes(data.originData.vehicleModel)) {
        // vehicleIcon = data.originData.terminalState ? 'busOnline' : 'busOffline';
        vehicleIcon = this.colorType(data.originData, 'passenger');
      } else if (truckModel.includes(data.originData.vehicleModel)) {
        vehicleIcon = this.colorType(data.originData, 'vehicle');
        // vehicleIcon = data.originData.terminalState ? 'truckOnline' : 'truckOffline';
      } else if (data.originData.vehicleModel === '66') {
        vehicleIcon = this.colorType(data.originData, 'bus'); // 公交车
      } else if (data.originData.vehicleModel === '73' || data.originData.vehicleModel === '33') {
        vehicleIcon = this.colorType(data.originData, 'coldChain'); // 冷链车
      } else if (data.originData.vehicleModel === '35' || data.originData.vehicleModel === '40') {
        vehicleIcon = this.colorType(data.originData, 'oilTank'); // 油罐车
      } else if (data.originData.vehicleModel === '70' || data.originData.vehicleModel === '71') {
        vehicleIcon = this.colorType(data.originData, 'rentOut'); // 出租车
      } else {
        vehicleIcon = this.colorType(data.originData, 'otherOffline');
        // vehicleIcon = data.originData.terminalState ? 'otherCarOnline' : 'otherCarOffline';
      }
      return vehicleIcon;
    },
    colorStaffType (val, type) {
      // 0-离线 1-静止 2-移动
      // 离线(Offline) 静止(Static) 移动(Move)
      let vehicleIcon = '';
      switch (val.fusionState) {
      case 0:
        vehicleIcon = `${type}Offline`;
        break;
      case 1:
        vehicleIcon = `${type}Static`;
        break;
      case 2:
        vehicleIcon = `${type}Move`;
        break;
      }
      return vehicleIcon;
    },
    colorType (val, type) {
      // 0-离线 1-行驶 2-停驶-ACC关 3-告警 4-未定位 5-停驶-ACC开
      // 离线(灰色) 行驶(蓝色) 停驶-ACC关(紫色) 告警(红色) 未定位(黄色) 停驶-ACC开(绿色)
      let vehicleIcon = '';
      switch (val.fusionState) {
        case 0:
          vehicleIcon = `${type}`;
          break;
        case 1:
          vehicleIcon = `${type}Blue`;
          break;
        case 2:
          vehicleIcon = `${type}Purple`;
          break;
        case 3:
          vehicleIcon = `${type}Red`;
          break;
        case 4:
          vehicleIcon = `${type}Yellow`;
          break;
        case 5:
          vehicleIcon = `${type}Green`;
          break;
      }
      return vehicleIcon;
    },
    /**
     * 获取车组的图标名称
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getTreeIconClass (data) {
      let treeIcon = '';
      if (data.onlineVehicleCount > 0) {
        treeIcon = 'treeOnline';
      } else {
        treeIcon = 'tree';
      }
      return treeIcon;
    },
    /**
     * 获取车辆视频通道列表的图标名
     * @param {Object} data
     * @param {Object} data.status
     */
    getChannelIconClass (data) {
      switch (data.status) {
        case 'online': {
          return 'videoOnline';
        }
        case 'offline': {
          return 'videoOffline';
        }
        case 'playing': {
          return 'videoPlaying';
        }
      }
      return '';
    },
    /**
     * 获取定位终端/视频终端
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getIconByterminal (data) {
      if (data.type === 'vehicle' && data.video === 0) {
        return 'positioningTerminal';
      } else if (data.type === 'vehicle' && data.video === 1) {
        return 'videoTerminal';
      }
    },
    /**
     * 鼠标滑过某个节点
     * @param node
     */
    mouseOverNode (node) {
      if (node.data.type === 'channel') {
        let data = {
          licencePlate: node.parent.data.originData.licencePlate,
          channel: node.data.originData.channel
        };
        this.$emit('mouseOverNode', data);
      } else if (node.data.type === 'vehicle') {
        let data = {
          licencePlate: node.data.originData.licencePlate
        };
        this.$emit('mouseOverNode', data);
      } else {
        this.$emit('mouseOverNode', {});
      }
    },
    /**
     * 设置keys
     * @param keys
     */
    setCheckedKeys (keys) {
      this.$refs.tree.setCheckedKeys(keys);
    },
    setDefaultCheckedKeys(keys) {
      this.defaultCheckedKeys = keys;
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Vehicle', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('Vehicle', value);
    },
    /**
     * 切换状态
     */
    changeCarList () {
      this.$emit('changeCarList');
    },
    /**
     * 收缩状态改变
     * @param val
     */
    handleCollapseChange (val) {
      this.$nextTick(() => {
        this.updateStyle();
      });
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    /**
     * 更新样式
     */
    updateStyle () {
      let selectComponentDomHeight = this.$refs['selectComponentContainerDom'].clientHeight;
      // let firstCollapseItemDomHeight = this.$refs['firstCollapseItemComponent'].$el.clientHeight;
      let firstCollapseItemDomHeight = this.$refs['firstCollapseItemComponent'].clientHeight; // 搜索条件高度
      let headerDomHeight = this.$refs['headerDom'].clientHeight;
      // const xhSpacingBase = variables.xhSpacingBase;// @xhSpacingBase
      // const collapseItemTitleHeight = variables.tagsViewHeight; // @tagsViewHeight
      // let maxHeight = selectComponentDomHeight - firstCollapseItemDomHeight - headerDomHeight - collapseItemTitleHeight - 4 * xhSpacingBase;
      let maxHeight = selectComponentDomHeight - firstCollapseItemDomHeight - 2 * headerDomHeight - 8;
      if (this._lastTableHeight !== maxHeight) {
        this.tableStyle = `height: ${maxHeight + 'px'}; overflow-y: auto`;
        this._lastTableHeight = maxHeight;
        this.treeMaxHeight = maxHeight + 'px';
        requestAnimationFrame(this.updateStyle);
      }
    },
    /**
     * 清除搜索条件
     */
    onClear () {
      // this.query = {
      //   licencePlate: '',
      //   terminalState: '',
      //   simId: '',
      //   deptName: '',
      //   terminalModel: ''
      // };
      this.filterText = '';
      // 清除勾选和展开节点的状态
      this.defaultCheckedKeys = [];
      this.defaultExpandedKeys = [];
      this.searchFilterText(this.filterText);
    },
    /**
     * 点击详情按钮
     * @param val
     */
    toDetail (val) {
      this.$emit('toDetail', val);
    },
    /**
     * 点击多选框
     * @param selection
     * @param row
     */
    clickCheckbox (selection, row) {
      this.$emit('toDetail', row);
    },
    /**
     * 清空用户选择
     * @public
     */
    clear () {
      this.$refs.tree.setCheckedAll(false);
    },
    /**
     * 设置选中的车辆并搜索
     * @param {String} val 车牌号
     */
    setSelectedVehicle (val, id) {
      if (this.loading) {
        setTimeout(() => {
          this.setSelectedVehicle(val, id);
        }, 1000);
      } else {
        // this.query.licencePlate = val;
        // this.searchFilter();
        // 替换新的查询搜索
        this.filterText = val;
        this.searchFilterText(this.filterText);
        // 搜索后勾选
        this.$refs.tree.setCheckedKeys(['v-' + id], false);
        // 勾选后下发事件
        this.$emit('checkedVehiclesChange', {
          checkedVehicles: [{
            licencePlate: val,
            id: id
          }],
          currentVehicle: [{
            licencePlate: val,
            checked: true,
            id: id
          }],
          // 该参数用于marker刷新
          setFitView: true
        });
      }
    },
    /**
     * 展开节点触发的事件
     * @description 改变defaultExpandedKeys用于保留状态
     * @see onUpdate
     */
    handleNodeExpand (data, node) {
      this.defaultExpandedKeys.push(data.id);
    },
    /**
     * 收起节点触发的事件
     * @description 改变defaultExpandedKeys用于保留状态
     * @see onUpdate
     */
    handleNodeCollapse (data, node) {
      let val = data.id;
      let index = -1;
      for (let i = 0; i < this.defaultExpandedKeys.length; i++) {
        if (this.defaultExpandedKeys[i] === val) {
          index = i;
          break;
        }
      }
      if (index > -1) {
        this.defaultExpandedKeys.splice(index, 1);
      }
    },
    /**
     * 更新数据，保留之前的状态
     * @see defaultCheckedKeys
     * @see defaultExpandedKeys
     */
    onUpdate () {
      this.loading = true;
      crudVehicle.tree().then(treeData => {
        if (JSON.stringify(treeData) !== JSON.stringify(this.data)) {
          // console.log('crudVehicle.tree-->', treeData);
          this.defaultCheckedKeys = this.$refs.tree.getCheckedKeys(true);
          this.data = treeData;
          let vehicleCount = 0;
          for (let i = 0; i < treeData.length; i++) {
            vehicleCount += treeData[i].vehicleCount;
          }
          this.vehicleCount = vehicleCount;
          this.loading = false;
          // 处理已经勾选的列表数据

          /* setTimeout(() => {
          if (this.query.licencePlate || this.query.terminalState || this.query.simId || this.query.deptName) {
            this.searchFilter();
          } else {
            this.onRefresh();
          }
        }, 200); */
          // setTimeout(() => {
          //   if (this.query.licencePlate || this.query.terminalState || this.query.simId || this.query.deptName || this.query.terminalModel) {
          //     this.searchFilter();
          //   }
          // }, 200);
          // 替换新的查询搜索
          setTimeout(() => {
            if (this.filterText) {
              this.searchFilterText(this.filterText);
            }
          }, 200);
        }
        this.$emit('CheckedNodesUpdate', this.$refs.tree.getCheckedNodes());
      }).catch(msg => {
        this.loading = false;
      });
    },
    getAccState (data) {
      // acc_state ACC状态 0-关，1-开
      return data.originData.accState === 1 ? '[ACC开]' : '[ACC关]';
    }
  }

};

</script>

<style lang="less" scoped>
  @import "../../../assets/less/variables.less";
  .space-set{
    white-space: nowrap
  }
  .width-set{
    min-width:64px
  }
  .over-hidden{
    width: 450px;
    overflow: hidden;
  }
  .xh-select-component-collapse-item ::v-deep.el-collapse-item__content{
    padding-bottom: 0;
  }
  .xh-select-component-filter-item-value{
    max-width: 150px;
  }
  .xh-select-component-container-full-fill{
    width: @selectComponentContainerWidth;
    overflow-y: hidden;
    overflow-x: hidden;
    border-radius: @xhBorderRadiusBase;
    transition: @xhTransitionFast;
    padding-right: 5px;
    background-color: #fff;
    cursor: e-resize
  }
  .xh-dropDown-invisible .xh-select-component-container-full-fill{
    width: 240px;
  }
  .xh-tree-live-map{
    // height: 500px !important;
    // 车辆列表标题高度/车辆目录标题高度(36px) // 搜索条件高度(40px) // 绝对定位上下偏移(8px)
    // height: calc(100% - 2 * 36px -  40px - 8px) !important;
  }
  // 视频页需要一个头部高度@videoSelectHeight  plus
  .xh-select-component-video-container-system{
    width: @selectComponentContainerWidthPlus;
    height: calc(100vh - 2 * @logoBarHeight - 2 * @xhSpacingBase - @videoSelectHeight);
    overflow-y: hidden;
    overflow-x: hidden;
    border-radius: @xhBorderRadiusBase;
    transition: @xhTransitionFast;
  }
  .vehicle-tree-container, .vehicle-tree-content, .xh-select-component-collapse-item, .xh-select-component-collapse-item ::v-deep .el-collapse-item__content, .easy-tree, .easy-tree ::v-deep .vue-recycle-scroller, .xh-select-component-container-full-fill{
    height: 100% !important;
  }
  .vehicle-tree-content, .xh-select-component-collapse-item, .xh-select-component-container-full-fill {
    display: flex;
    flex-direction: column;
  }
  .vehicle-tree-content-collapse, .xh-select-component-collapse-item ::v-deep .el-collapse-item__wrap, .collapse{
    flex: 1;
  }
  .vehicle-tree-content-collapse, .collapse{
    overflow: auto;
  }
  .easy-tree ::v-deep .vue-recycle-scroller__item-wrapper{
    overflow: visible;
  }
  .xh-select-component-collapse-item ::v-deep.el-collapse-item__content{
    padding-bottom: 0;
    padding-top: 0;
  }
  .header {
    background-color: #f3f6f8;
    height: 46px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px 0 14px;
    .car-filter-container {
      display: flex;
      align-items: center;
      border: 1px solid #bfbfbf;
      background-color: #ffffff;
      padding: 0 4px;
      flex: 1;
      height: 28px;
      box-sizing: border-box;
      overflow: hidden;

      ::v-deep .el-input__inner {
        border: none !important;
        box-shadow: none !important;
      }

      .el-icon-search {
        font-size: 18px;
        color: #176ac0;
      }

      .el-icon-refresh {
        font-size: 18px;
        color: #c0c0c0;
        cursor: pointer;
      }
    }
  }
</style>
