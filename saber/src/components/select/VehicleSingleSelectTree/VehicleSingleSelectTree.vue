<template>
  <div
    class="video-single-select-tree"
  >
    <div
      ref="selectComponentContainerDom"
      v-loading="loading"
      v-drag="isDrag"
      class="video-select-component-container"
    >
      <div
        ref="headerDom"
        class="header"
      >
        <div class="car-filter-container">
          <i class="el-icon-search"/>
          <el-input
            v-model="filterText"
            clearable
            size="small"
            placeholder="搜索终端名称/终端类型/机构"
            class="car-filter"
          />
          <i
            class="el-icon-refresh"
            @click="onRefresh"
          />
          <el-button
            size="small"
            class="search-button"
            @click="searchFilterText(filterText)"
          >
            查询
          </el-button>
        </div>
      </div>
      <vue-easy-tree
        ref="tree"
        :key="treeKey"
        :data="data"
        class="xh-tree"
        node-key="id"
        :indent="10"
        :lazy="lazy"
        :props="defaultProps"
        :load="loadTreeNode"
        :default-expand-all="defaultExpand"
        :default-expanded-keys="defaultExpandedKeys"
        :height="treeMaxHeight"
        :style="tableStyle"
        @node-click="nodeClick"
        @node-expand="handleNodeExpand"
        @node-collapse="handleNodeCollapse"
      >
        <span
          slot-scope="{ node, data }"
          class="custom-tree-node"
        >

          <span v-if="data.type === 0">
            <svg-icon
              :icon-class="getTreeIconClass(data)"
            />
            {{ data.name }}{{ getCount(data) }}
          </span>
          <span v-if="data.type === 1">
            {{ data.name }}{{ getCount(data) }}
          </span>
          <span v-if="data.type === undefined">
            <svg-icon
              :icon-class="getStaffIconClass(data)"
              class="svg-icon-vehicle"
            />
            {{ data.name }}
          </span>
        </span>
      </vue-easy-tree>
    </div>
  </div>
</template>

<script>
import crudVehicle from '@/api/base/vehicle';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

export default {
  name: 'VehicleSingleSelectTree',
  components: {},
  directives: {
    drag(el, bindings) { // 横向拖拽tree
      el.onmousedown = bindings.value ? function (e) {
        let w = el.clientWidth || 300;
        let x = e.clientX;
        document.onmousemove = function (e) {
          let k = w + e.clientX - x;
          if (k >= 300 && k <= 440) {
            el.style.width = k + 'px';
          }
        };
        document.onmouseup = function () {
          document.onmousemove = document.onmouseup = null;
        };
      } : null;
    }
  },
  props: {
    // 是否可拖动
    isDrag: {
      type: Boolean,
      default: false
    },
    customTreeApi: {
      type: Function,
      default: null
    },
    isShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableStyle: '',
      filterText: '',
      data: [],
      loading: false,
      treeMaxHeight: '0px', // TODO
      terminalTreeData: [],
      lazy: true,
      defaultProps: {
        isLeaf: 'leaf'
      },
      defaultExpand: false,
      treeKey: 0, // 车辆树的key(用来更新树)
      defaultExpandedKeys: [],
      filterCurrentKey: null
    };
  },
  watch: {
    isShow: {
      handler(val) {
        if (val) {
          this.$nextTick(()=> {
            if (this.$store.state.vehicle.tree?.length) {
              this.terminalTreeData = Object.freeze(this.$store.state.vehicle.tree);
              this.data = (this.$store.state.vehicle.tree || []).map(item => ({...item, children: []}));
            } else {
              this.onRefresh('store');
            }
          });
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.updateStyle(); // TODO
    });
  },
  methods: {
    // 修改终端树节点状态
    updateStateTerminal (data) {
      // 实测修改2000个终端状态, 执行需要两秒多, 期间会导致页面阻塞, 因此加个限制<500
      if (data && data.length && data.length < 500) {
        console.time();
        data.forEach(element => {
          this.updateNodeById(this.terminalTreeData, element.id, (node) => {
            node.fusionState = element.fusionState;
          });
        });
        const defaultCheckedKeys = this.$refs.tree.getCheckedKeys();
        if (this.lazy) {
          this.data = (this.terminalTreeData || []).map(item => ({...item, children: []}));
        } else {
          const list = this.filterNestedArray(this.terminalTreeData, this.filterText);
          this.data = list;
        }
        this.$nextTick(() => {
          this.$refs.tree.setCheckedKeys(defaultCheckedKeys);
        });
        console.timeEnd();
      }
    },
    // 查找对应的对象修改其内容
    updateNodeById (tree, id, updateFn) {
      for (let node of tree) {
        if (node.id === id) {
          updateFn(node);
          return true;
        }
        if (node.children && node.children.length > 0) {
          const found = this.updateNodeById(node.children, id, updateFn);
          if (found) {
            return true;
          }
        }
        if (node.tags && node.tags.length > 0) {
          const found = this.updateNodeById(node.tags, id, updateFn);
          if (found) {
            return true;
          }
        }
      }
      return false;
    },
    // 在线/总数车辆
    getCount(data) {
      return `(${data.onlineNum}/${data.total})`;
    },
    // 加载子节点
    loadTreeNode (node, resolve) {
      if (node.level > 0) {
        const result = this.findInNestedArray(this.terminalTreeData, item => item.id === node.data.id);
        if (result) {
          if (node.data.type === 1) {
            const list = result.tags.map(item => ({
              ...item,
              leaf: true
            }));
            resolve(list);
          } else {
            const list = result.children.map(item => ({
              ...item,
              children: [],
              tags: []
            }));
            resolve(list);
          }
        } else {
          resolve([]);
        }
      } else {
        resolve(this.terminalTreeData.map(item => ({...item, children: []})));
      }
    },
    findInNestedArray(array, condition) {
      for (let item of array) {
        if (condition(item)) {
          return item;
        }
        if (item.children && item.children.length > 0) {
          const found = this.findInNestedArray(item.children, condition);
          if (found) {
            return found;
          }
        }
      }
      return null;
    },
    /**
     * 节点被点击
     */
    nodeClick (data, node) {
      if (data.type === undefined) {
        this.filterCurrentKey = data.id;
        this.$emit('selectedRowSingle', {
          deviceId: data.id,
          targetName: data.name,
          deviceType: Number(node.parent.data.id.slice(-1)),
          treeCategory: data.treeCategory,
          fusionState: data.fusionState
        });
      }
    },
    /**
     * 展开节点触发的事件
     * @description 改变defaultExpandedKeys用于保留状态
     * @see onUpdate
     */
    handleNodeExpand (data, node) {
      this.defaultExpandedKeys.push(data.id);
    },
    /**
     * 收起节点触发的事件
     * @description 改变defaultExpandedKeys用于保留状态
     * @see onUpdate
     */
    handleNodeCollapse (data, node) {
      let val = data.id;
      let index = -1;
      for (let i = 0; i < this.defaultExpandedKeys.length; i++) {
        if (this.defaultExpandedKeys[i] === val) {
          index = i;
          break;
        }
      }
      if (index > -1) {
        this.defaultExpandedKeys.splice(index, 1);
      }
      // 收起父节点时同时收起子节点
      if (node.childNodes.length > 0) {
        let list = this.flattenKeyList(node.childNodes);
        for (let i = 0; i < list.length; i++) {
          const element = list[i];
          const key = this.defaultExpandedKeys.findIndex(item => item === element);
          if (key !== -1) {
            this.defaultExpandedKeys.splice(key, 1);
          }
        }
      }
    },
    flattenKeyList(data) {
      let result = [];
      data.forEach((item)=> {
        if (item.data.type === 0 || item.data.type === 1) {
          result.push(item.data.id);
          if (item.childNodes && item.childNodes.length > 0 && item.data.type === 0) {
            let childId = this.flattenKeyList(item.childNodes);
            result = result.concat(childId);
          }
        }
      });
      return result;
    },
    /**
     * 刷新
     */
    onRefresh(type) {
      this.loading = true;
      this.onClear();
      if (!this.customTreeApi) {
        crudVehicle.deptTagTree().then(treeData => {
          console.log('crudVehicle.tree-->', treeData);
          this.terminalTreeData = Object.freeze(treeData);
          this.data = (treeData || []).map(item => ({...item, children: []}));
          this.loading = false;
          if (type === 'store') {
            this.$store.commit('SET_TREE', treeData || []);
          }
        }).catch(msg => {
          this.$notify({
            title: msg,
            type: 'warning'
          });
          this.loading = false;
        });
      } else {
        this.customTreeApi().then(treeData => {
          this.terminalTreeData = Object.freeze(treeData);
          this.data = (treeData || []).map(item => ({...item, children: []}));
          this.loading = false;
          if (type === 'store') {
            this.$store.commit('SET_TREE', treeData || []);
          }
        }).catch(msg => {
          this.$notify({
            title: msg,
            type: 'warning'
          });
          this.loading = false;
        });
      }
    },
    /**
     * 获取其他类型的图标名
     * @param {Object} data
     * @param {Object} data.originData
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getStaffIconClass (data) {
      const vehicleModel = ['101', '102', '103', '104', '105'];
      const materialsModel = ['106', '108', '109', '111', '112', '114'];
      const personnelModel = ['201', '202', '301', '107', '110', '113'];
      const shortMessageModel = ['302', '303'];
      const timeServiceModel = ['501', '502', '503'];
      const monitorModel = ['401', '402'];
      let vehicleIcon = '';
      if (vehicleModel.includes(data.treeCategory)) {
        vehicleIcon = this.colorStaffType(data, 'vehicle'); // 车辆
      } else if (materialsModel.includes(data.treeCategory)) {
        vehicleIcon = this.colorStaffType(data, 'materials'); // 物资
      } else if (personnelModel.includes(data.treeCategory)) {
        vehicleIcon = this.colorStaffType(data, 'personnel'); // 人员
      } else if (shortMessageModel.includes(data.treeCategory)) {
        vehicleIcon = this.colorStaffType(data, 'shortMessage'); // 短报文终端
      } else if (timeServiceModel.includes(data.treeCategory)) {
        vehicleIcon = this.colorStaffType(data, 'timeService'); // 授时终端
      } else if (monitorModel.includes(data.treeCategory)) {
        vehicleIcon = this.colorStaffType(data, 'monitor'); // 监测终端
      } else if (data.treeCategory === '0') {
        vehicleIcon = this.colorStaffType(data, 'other'); // 其他
      }
      return vehicleIcon;
    },
    colorStaffType (val, type) {
      // 0-离线 1-静止 2-移动
      // 离线(Offline) 静止(Static) 移动(Move)
      let vehicleIcon = '';
      switch (val.fusionState) {
      case 0:
        vehicleIcon = `${type}Offline`;
        break;
      case 1:
        vehicleIcon = `${type}Static`;
        break;
      case 2:
        vehicleIcon = `${type}Move`;
        break;
      }
      return vehicleIcon;
    },
    /**
     * 获取车组的图标名称
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getTreeIconClass(data) {
      let treeIcon = '';
      if (data.onlineNum > 0) {
        treeIcon = 'treeOnline';
      }
      else {
        treeIcon = 'tree';
      }
      return treeIcon;
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('Vehicle', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder(value) {
      return getPlaceholder('Vehicle', value);
    },
    /**
     * 更新样式
     */
    updateStyle() {
      let selectComponentDomHeight = this.$refs['selectComponentContainerDom'].clientHeight;
      let headerDomHeight = this.$refs['headerDom'].clientHeight;
      let maxHeight = selectComponentDomHeight - headerDomHeight;
      if (this._lastTableHeight !== maxHeight) {
        this.tableStyle = `height: ${maxHeight + 'px'}; overflow-y: auto`;
        this._lastTableHeight = maxHeight;
        this.treeMaxHeight = maxHeight + 'px';
        requestAnimationFrame(this.updateStyle);
      }
    },
    /**
     * 清除搜索条件
     */
    onClear() {
      this.filterText = '';
      this.defaultExpandedKeys = [];
      this.filterCurrentKey = null;
      this.lazy = true;
      this.defaultExpand = false;
      // 修改lazy后需要手动更新tree
      this.treeKey += 1;
    },

    /**
     * 清空选择
     */
    clear() {
      this.updateStyle();
    },
    /**
     * 节点过滤(替换新的查询搜索)
     */
    searchFilterText (val) {
      const defaultCurrentKeys = this.$refs.tree.getCurrentKey();
      if (val) {
        const list = this.filterNestedArray(this.terminalTreeData, val);
        this.data = list;
        this.lazy = false;
        this.defaultExpand = true;
        this.treeKey += 1;
      } else {
        this.data = this.terminalTreeData.map(item => ({...item, children: []}));
        this.lazy = true;
        this.defaultExpand = false;
        this.treeKey += 1;
        if (this.filterCurrentKey) {
          this.defaultExpandedKeys = [...new Set([...this.defaultExpandedKeys, ...this.findAncestorsById(this.terminalTreeData, this.filterCurrentKey)])];
        }
      }
      this.$nextTick(() => {
        this.filterCurrentKey = null;
        if (defaultCurrentKeys) {
          this.$refs.tree.setCurrentKey(defaultCurrentKeys);
        }
      });
    },
    // 查找指定id的节点及其所有父级和祖宗级的id
    findAncestorsById(data, targetId) {
      const ancestors = [];
      return this.findRecursive(data, targetId, ancestors);
    },
    findRecursive(items, targetId, path = []) {
      for (let item of items) {
        if (item.id === targetId) {
          return [...path];
        }
        if (item.children && item.children.length > 0) {
          const result = this.findRecursive(item.children, targetId, [...path, item.id]);
          if (result) {
            return result;
          }
        }
        if (item.tags && item.tags.length > 0) {
          const result = this.findRecursive(item.tags, targetId, [...path, item.id]);
          if (result) {
            return result;
          }
        }
      }
      return null;
    },
    filterNestedArray(array, filterValue) {
      const result = [];
      result.push(...this.recursiveFilter(array, filterValue));
      return result;
    },
    recursiveFilter(items, filterValue) {
      return items.reduce((acc, item) => {
        if (item.name.includes(filterValue)) {
          acc.push(this.renameTagsToChildren({ ...item }));
        } else if (item.children && item.children.length > 0) {
          const filteredChildren = this.recursiveFilter(item.children, filterValue);
          if (filteredChildren.length > 0) {
            acc.push({ ...item, children: filteredChildren });
          }
        } else if (item.tags && item.tags.length > 0) {
          const filteredChildren = this.recursiveFilter(item.tags, filterValue);
          if (filteredChildren.length > 0) {
            acc.push({ ...item, children: filteredChildren });
          }
        }
        return acc;
      }, []);
    },
    // 将所有的tags属性值替换为children, 保持统一便组件使用
    renameTagsToChildren(item) {
      if (item.tags) {
        item.children = item.tags;
      }
      if (item.children) {
        item.children = item.children.map(child => this.renameTagsToChildren(child));
      }
      return item;
    },
    /**
     * 设置选中的终端并搜索
     * @param {Object} data
     * @param {Number} queryParams.id 树id
     * @param {String} queryParams.targetName 终端名称
     * @param {String} queryParams.deviceType 终端类型
     * @param {String} queryParams.deviceId 终端ID
     */
    setSelectedVehicle(val) {
      if (this.loading) {
        setTimeout(() => {
          this.setSelectedVehicle(val);
        }, 1000);
      }
      else {
        this.filterText = val.targetName;
        this.searchFilterText(this.filterText);
        // 模拟点击节点
        this.$nextTick(()=>{
          if (this.$refs.tree) {
            this.$refs.tree.setCurrentKey(val.id);
            const node = this.$refs.tree.getNode(val.id);
            if (node) {
              this.nodeClick(node.data, node);
            }
          }
        });
      }
    },
  }
};
</script>

<style lang="less" scoped>
@import "../../../assets/less/variables.less";

.custom-tree-node {
  .el-radio {
    margin-right: 4px;
  }
}

.space-set {
  white-space: nowrap
}

.video-select-component-container {
  height: 100%;
  overflow-y: hidden;
  overflow-x: hidden;
  border-radius: @xhBorderRadiusBase;
  transition: @xhTransitionFast;
  display: flex;
  flex-direction: column;
  .xh-tree {
    flex: 1;
    ::v-deep .vue-recycle-scroller {
      height: 100% !important;
    }
  }
}

.video-single-select-tree {
  height: 100%;
  .header {
    background-color: #f3f6f8;
    height: 54px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
  }

  .car-filter-container {
    display: flex;
    align-items: center;
    border: 1px solid #bfbfbf;
    background-color: #ffffff;
    padding-left: 4px;
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;

    ::v-deep .el-input__inner {
      border: none !important;
      box-shadow: none !important;
    }

    .el-icon-search, .el-icon-refresh {
      font-size: 18px;
      color: #c0c0c0;
    }

    .el-icon-refresh {
      padding-right: 4px;
      cursor: pointer;
    }
    .search-button {
        border: none;
        border-left: 1px solid #bfbfbf !important;
        border-radius: 0 !important;
      }
  }

  ::v-deep .el-tree-node__content {
    .el-checkbox {
      margin-right: 4px !important;
    }
  }
}
</style>
