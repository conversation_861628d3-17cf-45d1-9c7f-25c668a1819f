<template>
  <div
    :class="{'xh-dropDown-invisible': !isDropDown}"
    class="video-multi-select-tree"
  >
    <div
      ref="selectComponentContainerDom"
      v-loading="loading"
      class="video-select-component-container"
    >
      <div
        ref="headerDom"
        class="header"
      >
        <div class="car-filter-container">
          <i class="el-icon-search"/>
          <el-input
            v-model="query.filterText"
            clearable
            size="small"
            placeholder="搜索终端"
            class="car-filter"
            @input="searchFilter"
          />
          <i
            class="el-icon-refresh"
            @click="onRefresh"
          />
        </div>
      </div>
      <vue-easy-tree
        ref="tree"
        class="xh-tree"
        :data="data"
        node-key="id"
        draggable
        :height="treeMaxHeight"
        :allow-drop="allowDrop"
        :allow-drag="allowDrag"
        :show-checkbox="!isVoice"
        :filter-node-method="filterHandle"
        :style="tableStyle"
        :default-checked-keys="defaultCheckedKeys"
        :default-expanded-keys="defaultExpandedKeys"
        :indent="10"
        @node-drag-start="handleDragStart"
        @node-drag-enter="handleDragEnter"
        @node-drag-leave="handleDragLeave"
        @node-drag-over="handleDragOver"
        @node-drag-end="handleDragEnd"
        @node-drop="handleDrop"
        @check-change="handleCheckChange"
        @check="handleCheck"
        @node-contextmenu="rightClick"
        @node-click="leftClick"
        @node-expand="handleNodeExpand"
        @node-collapse="handleNodeCollapse"
      >
        <span
          slot-scope="{ node, data }"
          class="custom-tree-node"
          @mouseover="mouseOverNode(node)"
        >
          <template v-if="!isVoice">
            <span v-if="data.type === 'dept'">
              <svg-icon
                :icon-class="getTreeIconClass(data)"
              />
              {{ node.label }}{{ getCount(data) }}
            </span>
            <span v-if="data.type === 'dept_son'">
              {{ node.label }}{{ getCount(data) }}
            </span>
            <span v-if="data.type !== 'dept' && data.type !== 'dept_son' && data.type !== 'channel'">
              <svg-icon
                :icon-class="getStaffIconClass(data)"
                class="svg-icon-vehicle"
              />
              <span>
                {{ data.originData.targetName }}
                <span>{{ getAccState(data) }}</span>
              </span>
            </span>
            <span v-if="data.type === 'channel'">
              <img
                class="tree-node-icon-other"
                :src="getOtherIcon(node)"
                alt=""
              >
              <span>{{ node.label }}</span>
            </span>
          </template>
          <temlpate v-else>
            <span v-if="data.type === 'dept'">
              <svg-icon
                :icon-class="getTreeIconClass(data)"
              />
              {{ node.label }}{{ getCount(data) }}
            </span>
            <span v-if="data.type === 'dept_son'">
              {{ node.label }}{{ getCount(data) }}
            </span>
            <span v-if="data.type !== 'dept' && data.type !== 'dept_son' && data.type !== 'channel'">
              <svg-icon
                :icon-class="getStaffIconClass(data)"
                class="svg-icon-vehicle"
              />
              <span>
                {{ data.originData.targetName }}
                <span>{{ getAccState(data) }}</span>
              </span>
            </span>
          </temlpate>
        </span>
      </vue-easy-tree>
    </div>
    <div
      v-if="pageSrc === 'vl'"
      class="mod-intercom"
    >
      <el-tabs
        v-model="activeName"
        :stretch="true"
        style="height: 100%"
      >
        <el-tab-pane
          label="对讲"
          name="first"
        >
          <div class="intercom-head">
            <div>
              <span>对讲</span>
              <span>
                [{{ intercomOriginData.targetName || '请点击终端' }}]
              </span>
            </div>
          </div>
          <div class="intercom-mid">
            <i class="el-icon-microphone"/>
          </div>
          <div class="intercom-bottom">
            <div
              class="intercom-bottom-btn common-default-btn"
              @click="intercomOpen"
            >
              打开
            </div>
            <div
              class="intercom-bottom-btn common-default-btn"
              @click="intercomClose"
            >
              停止
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane
          v-if="!isVoice"
          label="监听"
          name="second"
        >
          <div class="intercom-head">
            <div>
              <span>监听</span>
              <span>
                [{{ intercomOriginData.targetName || '请点击终端' }}]
              </span>
            </div>
          </div>
          <div class="intercom-mid">
            <i class="el-icon-service"/>
          </div>
          <div class="intercom-bottom">
            <div
              class="intercom-bottom-btn common-default-btn"
              @click="monitorOpen"
            >
              打开
            </div>
            <div
              class="intercom-bottom-btn common-default-btn"
              @click="monitorClose"
            >
              停止
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { rnssChannelTree } from '@/api/base/vehicle';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import variables from '@/assets/less/variables.js';
import folderOpen from '@/assets/images/tree/folder-open.png';
import folder from '@/assets/images/tree/folder.png';
import onlineVehicle from '@/assets/images/tree/icon01.png';
import offlineVehicle from '@/assets/images/tree/icon02.png';
import onVideo from '@/assets/images/tree/icon03.png';
import offVideo from '@/assets/images/tree/icon04.png';
import accIcon from '@/assets/images/tree/icon05.png';

export default {
  name: 'VehicleMultiSelectTree',
  components: {},
  dicts: ['terminalState'],
  props: {
    // 标识来源：vl-videolive；
    pageSrc: {
      type: String,
      default: ''
    },
    hiddenToggleButton: {
      type: Boolean,
      default: false
    },
    isLimitCheckedCount: {
      type: Boolean,
      default: true
    },
    isVoice: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      name: '',
      permission: {
        add: [
          'admin',
          'vehicle:add'
        ],
        edit: [
          'admin',
          'vehicle:edit'
        ],
        del: []
      },
      activeNames: ['secondItem'],
      tableStyle: '',
      query: {
        deviceId: '',
        terminalState: '',
        deptName: '',
        filterText: ''
      },
      nextButtonVisible: false,
      data: [],
      bakData: [], // 记录数据
      lastCheckedChannels: [],
      lastCheckedKeys: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      variables: { ...variables },
      isDropDown: true,
      selectedList: [],
      fromStr: '',
      vehicleCount: null,
      loading: false,
      rightMenu: false,
      defaultCheckedKeys: [],
      defaultExpandedKeys: [],
      intercomOriginData: {},
      activeName: 'first',
      treeMaxHeight: '0px',
      accIcon: accIcon
    };
  },
  mounted() {
    if (sessionStorage.getItem('storedVehicleTreeData')) {
      this.dataSource(JSON.parse(sessionStorage.getItem('storedVehicleTreeData')));
    }
    else {
      this.loading = true;
    }
    this.$nextTick(() => {
      this.updateStyle();
      this.onRefresh(() => {
        if (this.$route.query.deviceId) {
          this.setSelectedVehicle(this.$route.query);
        }
      });
    });
  },
  activated() {
    this.$nextTick(() => {
      if (this.$route.query.deviceId) {
        this.setSelectedVehicle(this.$route.query);
      }
    });
  },
  methods: {
    // 监听开启
    monitorOpen() {
      if (!this.intercomOriginData?.deviceId) {
        this.$message.warning('请点击终端');
        return;
      }
      this.$parent.handleMonitorSelect(this.intercomOriginData);
      this.$nextTick(() => {
        this.intercomStyle();
      });
    },
    // 监听关闭
    monitorClose() {
      if (!this.intercomOriginData?.deviceId) {
        return;
      }
      this.$parent.handleMonitorStop(this.intercomOriginData);
      this.$nextTick(() => {
        this.updateStyle();
      });
    },
    // 对讲开启
    intercomOpen() {
      console.log('-> this.intercomOriginData', this.intercomOriginData);
      if (!this.intercomOriginData?.deviceId) {
        this.$message.warning('请点击终端');
        return;
      }
      this.$parent.handleSelect(this.intercomOriginData);
      this.$nextTick(() => {
        this.intercomStyle();
      });
    },
    // 对讲关闭
    intercomClose() {
      if (!this.intercomOriginData?.deviceId) {
        return;
      }
      this.$parent.handleStop(this.intercomOriginData);
      this.$nextTick(() => {
        this.updateStyle();
      });
    },
    /**
     * 刷新
     */
    onRefresh(callback) {
      // this.loading = true;
      this.onClear();
      rnssChannelTree().then(treeData => {
        console.log('crudVehicle.rnssChannelTree-->', treeData);
        // localStorage.setItem('storedVehicleTreeData', JSON.stringify(treeData));
        this.dataSource(treeData);
        let vehicleCount = 0;
        for (let i = 0; i < treeData.length; i++) {
          vehicleCount += treeData[i].vehicleCount;
        }
        this.vehicleCount = vehicleCount;
        this.nextButtonVisible = false;
        this.loading = false;
      }).catch(msg => {
        this.$notify({
          title: msg,
          type: 'warning'
        });
        this.loading = false;
      }).then(() => {
        typeof callback === 'function' && callback();
      });
    },
    // 在线/总数终端
    getCount(data) {
      if (this.pageSrc === 'vl') {
        return `(${data.onlineVehicleCount}/${data.vehicleCount})`;
      }
      else {
        return `(${data.onlineVehicleCount}/${data.vehicleCount})`;
      }
    },
    // data根据用途筛选
    dataSource(data) {
      if (this.pageSrc === 'vl') {
        this.data = this.videoFilter(data).data;
      }
      else {
        this.data = data;
      }
    },

    // 视频页筛选视频终端终端
    videoFilter(data) {
      let arr = [];
      let flag = false;
      let frontCountTotal = 0;
      let frontCountOnline = 0;
      data.forEach((item) => {
        if (item.type === 'dept' || item.type === 'dept_son') {
          if (item.children) {
            let obj = this.videoFilter(item.children);
            if (obj.flag) {
              flag = true;
              item.children = obj.data;
              item.frontCountTotal = obj.frontCountTotal;
              item.frontCountOnline = obj.frontCountOnline;
              frontCountTotal += obj.frontCountTotal;
              frontCountOnline += obj.frontCountOnline;
              arr.push(item);
            }
          }
        }
        else if (item.type === 'channel') {
          flag = true;
          arr.push(item);
        }
        // 车载终端或头盔
        else {
          if (item.video === 1) {
            frontCountTotal++;
            if (item.originData.terminalState === 1) {
              frontCountOnline++;
            }
            flag = true;
            if (this.isVoice) {
              item.children = undefined;
            }
            arr.push(item);
          }
        }
      });
      return {
        flag: flag,
        data: arr,
        frontCountOnline: frontCountOnline,
        frontCountTotal: frontCountTotal
      };
    },

    /**
     * 下一个
     */
    onNext() {
      this.$refs.tree.filter(this.query, {
        ignoreLeafType: 'dept',
        expandNew: true
      });
      this.nextButtonVisible = true;
    },
    /**
     * 节点过滤
     */
    searchFilter() {
      if (this.query.deviceId === '' &&
        this.query.terminalState === '' &&
        this.query.deptName === '' &&
        this.query.filterText === '') {
        this.$refs.tree.filter(this.query);
        this.nextButtonVisible = false;
      }
      else {
        this.$refs.tree.filter(this.query, {
          ignoreLeafType: 'dept'
        });
        this.nextButtonVisible = true;
      }
    },
    /**
     * 节点搜索过滤函数
     * @param value
     * @param {String} value.deviceId
     * @param {Int} value.terminalState
     * @param {String} value.deptName
     * @param {Object} data
     * @param {Object} data.originData
     * @param {String} data.originData.deviceId
     * @param {Number} data.originData.terminalState
     * @param {String} data.originData.deptName
     * @param {Object} node
     * @param data
     */
    filterNode(value, data, node) {
      // console.log('filterNode-->', data.type, value, data.originData.name);
      if (data.type === 'dept' && data.originData.name.indexOf(value.deptName) === -1) {
        return false;
      }
      if (data.type === 'vehicle') {
        let parentNode = node.parent;
        // 判断如果子节点的单位和目标单位不一致，则不显示
        if (value.deptName !== '' && parentNode.data.label.indexOf(value.deptName) === -1) {
          return false;
        }
        if (value.deviceId !== '' && data.originData.deviceId.indexOf(value.deviceId) === -1) {
          return false;
        }
        if ((value.terminalState === 0 || value.terminalState === 1) && data.originData.terminalState !== value.terminalState) {
          return false;
        }
        // 添加颜色判断, 通过《实时监控地图》页面进入《实时视频》页面的需要判断颜色
        if (value.licenceColor && data.originData.licenceColor !== value.licenceColor) {
          return false;
        }
      }
      if (data.type === 'channel') {
        let channelParentNode = node.parent;
        if (value.deptName !== '' && channelParentNode.parent.data.label.indexOf(value.deptName) === -1) {
          return false;
        }
        if (value.deviceId !== '' && channelParentNode.data.label.indexOf(value.deviceId) === -1) {
          return false;
        }
        if ((value.terminalState === 0 || value.terminalState === 1) && channelParentNode.data.originData.terminalState !== value.terminalState) {
          return false;
        }
        // 添加颜色判断, 通过《实时监控地图》页面进入《实时视频》页面的需要判断颜色
        if (value.licenceColor && channelParentNode.data.originData.licenceColor !== value.licenceColor) {
          return false;
        }
      }
      return true;
    },
    /**
     * 节点搜索过滤函数(替换新的查询搜索)
     */
    filterHandle(value, data, node) {
      let parentNode = node.parent; // 父级node
      let labels = [node.label]; // 当前node的名字
      let level = 1; // 层级
      while (level < node.level) {
        labels = [
          ...labels,
          parentNode.label
        ]; // 当前node名字，父级node的名字
        parentNode = parentNode.parent;
        level++;
      }
      return labels.some(d => d.indexOf(value.filterText) !== -1);
      // TODO 在线状态现在不可用
      // console.log(value.terminalState, 55);
      // if (data.type === 'vehicle') {
      //   if ((value.terminalState === 0 || value.terminalState === 1) && data.originData.terminalState !== value.terminalState) {
      //     return false;
      //   }
      // }
    },
    /**
     * 节点开始拖拽时触发的事件
     */
    handleDragStart(node, ev) {
      // console.log('drag start', node);
      // ev.preventDefault();
      if (node.data.type === 'channel') {
        let transferData = {
          origin: 'VEHICLETREE',
          channelID: node.parent.data.originData.deviceId + '_' + node.data.originData.channel,
          treeNodeKey: node.data.id,
          vehicleId: node.parent.data.originData.id
        };
        ev.dataTransfer.setData('Text', JSON.stringify(transferData));
      }
    },
    /**
     * 拖拽进入其他节点时触发的事件
     */
    handleDragEnter(draggingNode, dropNode, ev) {
      // console.log('tree drag enter: ', dropNode.label);
      ev.preventDefault();
    },
    /**
     * 拖拽离开某个节点时触发的事件
     */
    handleDragLeave(draggingNode, dropNode, ev) {
      // console.log('tree drag leave: ', dropNode.label);
      ev.preventDefault();
    },
    /**
     * 在拖拽节点时触发的事件（类似浏览器的 mouseover 事件）
     */
    handleDragOver(draggingNode, dropNode, ev) {
      // console.log('tree drag over: ', dropNode.label);
      ev.preventDefault();
    },
    /**
     * 拖拽结束时（可能未成功）触发的事件
     */
    handleDragEnd(draggingNode, dropNode, dropType, ev) {
      // console.log('tree drag end: ', dropNode && dropNode.label, dropType);
      ev.preventDefault();
    },
    /**
     * 拖拽成功完成时触发的事件
     * @param {Object} draggingNode 被拖拽节点对应的Node
     * @param {Object} dropNode 结束拖拽时最后进入的节点
     * @param {Object} dropType 被拖拽节点的放置位置
     * @param {Event} ev
     */
    handleDrop(draggingNode, dropNode, dropType, ev) {
      // console.log('tree drop: ', dropNode.label, dropType);
    },
    /**
     * 拖拽时判定目标节点能否被放置。
     * @param {Object} draggingNode
     * @param {Object} dropNode
     * @param {Object} type 参数有三种情况：'prev'、'inner' 和 'next'，分别表示放置在目标节点前、插入至目标节点和放置在目标节点后
     */
    allowDrop(draggingNode, dropNode, type) {
      // if (dropNode.data.label === '二级 3-1') {
      //   return type !== 'inner';
      // } else {
      //   return true;
      // }
      return false;
    },
    /**
     * 判断某个节点是否允许拖拽
     * @param {Object} draggingNode
     */
    allowDrag(draggingNode) {
      // console.log(draggingNode);
      if (draggingNode.data.type === 'channel') {
        return true;
      }
      return false;
    },
    /**
     * 勾选-处理单个
     */
    handleCheckChange(data, isChecked) {

    },
    /**
     * 勾选-处理多个
     * @param node
     * @param options
     * @param options.checkedNodes 勾选的节点
     * @param options.checkedKeys 勾选节点的key
     * @param options.halfCheckedNodes
     * @param options.halfCheckedKeys
     * @typedef {Array.<{deviceId: String, channel: Number, treeNodeKey: String}>} ResultArray
     * @description emit{ResultArray}
     */
    handleCheck(node, options) {
      let checkedNodes = options.checkedNodes;
      let checkedKeys = options.checkedKeys;
      // console.log(checkedNodes, checkedKeys);
      // console.log(node, options);
      let checkedChannels = [];
      let videoMapCar = [];
      let cancelChannels = [];
      let addChannels = [];
      checkedNodes.forEach(item => {
        if (item.type === 'channel') {
          const parentData = this.$refs.tree.getNode(item).parent.data;
          console.log('-> parentData.originData', parentData.originData);
          this.$emit('selectedVehicle',  parentData.originData);
          checkedChannels.push({
            channel: item.originData.channel,
            treeNodeKeys: item.id,
            deviceId: parentData.originData.deviceId,
            deviceType: parentData.originData.deviceType,
            targetName: parentData.originData.targetName,
            simId: parentData.originData.phone
          });
        }
        else if (item.type !== 'dept' && item.type !== 'dept_son' && item.type !== 'channel') {
          videoMapCar.push({
            deviceType: item.originData.deviceType,
            deviceId: item.originData.deviceId
          });
        }
      });
      if (this.isLimitCheckedCount && checkedChannels.length > 16) {
        this.$notify({
          title: `无法播放超过16路视频（已播放${this.lastCheckedChannels.length}路，
          请求再播放${checkedChannels.length - this.lastCheckedChannels.length}），
          请先关闭部分摄像头！`,
          type: 'warning'
        });
        // console.log(JSON.stringify(this.lastCheckedKeys));
        this.$refs.tree.setCheckedKeys(this.lastCheckedKeys);
      }
      else {
        // 取消勾选的视频通道
        cancelChannels = this.arrayDiff(this.lastCheckedChannels, checkedChannels);
        // 新勾选的视频通道
        addChannels = this.arrayDiff(checkedChannels, this.lastCheckedChannels);
        this.lastCheckedChannels = checkedChannels;
        this.lastCheckedKeys = checkedKeys;
        this.checkedChannels = checkedChannels;
        this.$emit('checkedChannelsChange', {
          checkedChannels: checkedChannels,
          videoMapCar: videoMapCar,
          cancelChannels: cancelChannels,
          addChannels: addChannels
        });
      }
    },
    // 数组差集
    arrayDiff(oldArray, newArray) {
      return oldArray.filter(obj1 => {
        return !newArray.find(obj2 => obj1.treeNodeKeys === obj2.treeNodeKeys);
      });
    },
    /**
     * 设置地图中心点
     * @param {Object} data
     */
    setLocation(data) {
      console.log('setLocation-->', data);
    },
    getFolderIcon(node) {
      const { expanded } = node;
      if (expanded) {
        return folderOpen;
      }
      else {
        return folder;
      }
    },
    getVehicleIcon(data) {
      const { fusionState } = data.originData;
      if (fusionState === 0) {
        return offlineVehicle;
      }
      else {
        return onlineVehicle;
      }
    },
    getOtherIcon(node) {
      const { onlineVehicleCount } = node.parent.data;
      if (onlineVehicleCount === 0) {
        return offVideo;
      }
      else {
        return onVideo;
      }
    },
    /**
     * 获取其他类型的图标名
     * @param {Object} data
     * @param {Object} data.originData
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getStaffIconClass(data) {
      const vehicleModel = ['101', '102', '103', '104', '105'];
      const materialsModel = ['106', '108', '109', '111', '112', '114'];
      const personnelModel = ['201', '202', '301', '107', '110', '113'];
      const shortMessageModel = ['302', '303'];
      const timeServiceModel = ['501', '502', '503'];
      const monitorModel = ['401', '402'];
      let vehicleIcon = '';
      if (vehicleModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'vehicle'); // 终端
      }
      else if (materialsModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'materials'); // 物资
      }
      else if (personnelModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'personnel'); // 人员
      }
      else if (shortMessageModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'shortMessage'); // 短报文终端
      }
      else if (timeServiceModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'timeService'); // 授时终端
      }
      else if (monitorModel.includes(data.originData.treeCategory)) {
        vehicleIcon = this.colorStaffType(data.originData, 'monitor'); // 监测终端
      }
      else if (data.originData.treeCategory === '0') {
        vehicleIcon = this.colorStaffType(data.originData, 'other'); // 其他
      }
      return vehicleIcon;
    },
    /**
     * 获取终端类型的图标名
     * @param {Object} data
     * @param {Object} data.originData
     * @param {Number} data.originData.terminalState
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getVehicleIconClass(data) {
      let passengerModel = [
        '10',
        '11',
        '12',
        '13',
        '14',
        '15',
        '16'
      ];
      let truckModel = [
        '20',
        '21',
        '22',
        '23'
      ];
      let vehicleIcon = '';
      if (passengerModel.includes(data.originData.vehicleModel)) {
        // vehicleIcon = data.originData.terminalState ? 'busOnline' : 'busOffline';
        vehicleIcon = this.colorType(data.originData, 'passenger');
      }
      else if (truckModel.includes(data.originData.vehicleModel)) {
        vehicleIcon = this.colorType(data.originData, 'vehicle');
        // vehicleIcon = data.originData.terminalState ? 'truckOnline' : 'truckOffline';
      }
      else if (data.originData.vehicleModel === '66') {
        vehicleIcon = this.colorType(data.originData, 'bus'); // 公交车
      }
      else if (data.originData.vehicleModel === '73' || data.originData.vehicleModel === '33') {
        vehicleIcon = this.colorType(data.originData, 'coldChain'); // 冷链车
      }
      else if (data.originData.vehicleModel === '35' || data.originData.vehicleModel === '40') {
        vehicleIcon = this.colorType(data.originData, 'oilTank'); // 油罐车
      }
      else if (data.originData.vehicleModel === '70' || data.originData.vehicleModel === '71') {
        vehicleIcon = this.colorType(data.originData, 'rentOut'); // 出租车
      }
      else {
        vehicleIcon = this.colorType(data.originData, 'otherOffline');
        // vehicleIcon = data.originData.terminalState ? 'otherCarOnline' : 'otherCarOffline';
      }
      return vehicleIcon;
    },
    colorStaffType(val, type) {
      // 0-离线 1-静止 2-移动
      // 离线(Offline) 静止(Static) 移动(Move)
      let vehicleIcon = '';
      switch (val.fusionState) {
      case 0:
        vehicleIcon = `${type}Offline`;
        break;
      case 1:
        vehicleIcon = `${type}Static`;
        break;
      case 2:
        vehicleIcon = `${type}Move`;
        break;
      }
      return vehicleIcon;
    },
    colorType(val, type) {
      // 0-离线 1-行驶 2-停驶-ACC关 3-告警 4-未定位 5-停驶-ACC开
      // 离线(灰色) 行驶(蓝色) 停驶-ACC关(紫色) 告警(红色) 未定位(黄色) 停驶-ACC开(绿色)
      let vehicleIcon = '';
      switch (val.fusionState) {
      case 0:
        vehicleIcon = `${type}`;
        break;
      case 1:
        vehicleIcon = `${type}Blue`;
        break;
      case 2:
        vehicleIcon = `${type}Purple`;
        break;
      case 3:
        vehicleIcon = `${type}Red`;
        break;
      case 4:
        vehicleIcon = `${type}Yellow`;
        break;
      case 5:
        vehicleIcon = `${type}Green`;
        break;
      }
      return vehicleIcon;
    },
    /**
     * 获取车组的图标名称
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getTreeIconClass(data) {
      let treeIcon = '';
      if (data.onlineVehicleCount > 0) {
        treeIcon = 'treeOnline';
      }
      else {
        treeIcon = 'tree';
      }
      return treeIcon;
    },
    /**
     * 获取定位终端/视频终端
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getIconByterminal(data) {
      if (data.type === 'vehicle' && data.video === 0) {
        return 'positioningTerminal';
      }
      else if (data.type === 'vehicle' && data.video === 1) {
        return 'videoTerminal';
      }
    },
    /**
     * 获取终端视频通道列表的图标名
     * @param {Object} data
     * @param {Object} data.status
     */
    getChannelIconClass(data) {
      switch (data.status) {
      case 'online': {
        return 'videoOnline';
      }
      case 'offline': {
        return 'videoOffline';
      }
      case 'playing': {
        return 'videoPlaying';
      }
      }
      return '';
    },
    /**
     * 鼠标滑过某个节点
     * @param node
     */
    mouseOverNode(node) {
      if (node.data.type === 'channel') {
        let data = {
          deviceId: node.parent.data.originData.deviceId,
          channel: node.data.originData.channel
        };
        this.$emit('mouseOverNode', data);
      }
      else if (node.data.type === 'vehicle') {
        let data = {
          deviceId: node.data.originData.deviceId
        };
        this.$emit('mouseOverNode', data);
      }
      else {
        this.$emit('mouseOverNode', {});
      }
    },
    /**
     * 设置keys
     * @param keys
     */
    setCheckedKeys(keys) {
      this.$nextTick(() => {
        let checkedKeys = this.$refs.tree.getCheckedKeys(true); // (leafOnly) 接收一个 boolean 类型的参数，若为 true 则仅返回被选中的叶子节点的 keys，默认值为 false
        // console.log('old', checkedKeys);
        // console.log('new', keys);
        if (!(checkedKeys.sort().toString() === keys.sort().toString())) {
          // console.log('不是完全相等')
          this.$refs.tree.setCheckedKeys(keys, true);
        }
        // this.$refs.tree.setCheckedKeys(keys, true);
      });
    },
    /**
     * 获取选中的节点
     * @param {Function} callback 回调函数
     * @param {String} [options]
     * @param {String} [options.type] 如果此项不为空，则过滤掉相关的属性
     */
    getCheckedNodes(callback, options) {
      this.$nextTick(() => {
        let checkedNodes = this.$refs.tree.getCheckedNodes(true); // (leafOnly) 接收一个 boolean 类型的参数，若为 true 则仅返回被选中的叶子节点的 keys，默认值为 false
        if (options && options.type) {
          checkedNodes = checkedNodes.filter((currentValue, index) => {
            return currentValue.type === options.type;
          });
        }
        callback(checkedNodes);
      });
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('Vehicle', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder(value) {
      return getPlaceholder('Vehicle', value);
    },
    /**
     * 切换状态
     */
    toggleDropDown() {
      this.isDropDown = !this.isDropDown;
    },
    /**
     * 切换状态
     */
    changeCarList() {
      this.$emit('changeCarList');
    },
    /**
     * 收缩状态改变
     * @param val
     */
    handleCollapseChange(val) {
      this.$nextTick(() => {
        this.updateStyle();
      });
    },
    /**
     * 更新样式
     */
    updateStyle() {
      let selectComponentDomHeight = this.$refs['selectComponentContainerDom'].clientHeight;
      // let firstCollapseItemDomHeight = this.$refs?.['firstCollapseItemComponent']?.$el?.clientHeight || 0
      let headerDomHeight = this.$refs['headerDom'].clientHeight;
      const xhSpacingBase = variables.xhSpacingBase;// @xhSpacingBase
      const collapseItemTitleHeight = variables.tagsViewHeight; // @tagsViewHeight
      let vlStyle = this.pageSrc === 'vl' ? 28 : 0;
      let maxHeight = selectComponentDomHeight - headerDomHeight - collapseItemTitleHeight - 4 * xhSpacingBase + vlStyle;
      if (this._lastTableHeight !== maxHeight) {
        this.tableStyle = `height: ${maxHeight + 'px'}; overflow-y: auto`;
        this._lastTableHeight = maxHeight;
        this.treeMaxHeight = maxHeight + 'px';
        // requestAnimationFrame(this.updateStyle)
      }
    },
    /**
     * 更新样式
     */
    intercomStyle() {
      let selectComponentDomHeight = this.$refs['selectComponentContainerDom'].clientHeight;
      // let firstCollapseItemDomHeight = this.$refs['firstCollapseItemComponent'].$el.clientHeight
      let headerDomHeight = this.$refs['headerDom'].clientHeight;
      const xhSpacingBase = variables.xhSpacingBase;// @xhSpacingBase
      const collapseItemTitleHeight = variables.tagsViewHeight; // @tagsViewHeight
      let maxHeight = selectComponentDomHeight - headerDomHeight - collapseItemTitleHeight - 4 * xhSpacingBase;
      if (this._lastTableHeight !== maxHeight) {
        this.tableStyle = `height: 250px; overflow-y: auto`;
        this._lastTableHeight = maxHeight;
        requestAnimationFrame(this.intercomStyle);
      }
    },
    /**
     * 清除搜索条件
     */
    onClear() {
      // 清除搜索条件
      this.query = {
        deviceId: '',
        terminalState: '',
        deptName: '',
        filterText: ''
      };
      // 清除勾选和展开节点的状态
      this.defaultCheckedKeys = [];
      this.defaultExpandedKeys = [];
    },
    /**
     * 下拉
     * @param {Boolean} val
     */
    setDropDown(val) {
      this.isDropDown = val;
    },
    /**
     * 获取终端的数量
     * @return {Number}
     */
    getCarNumber() {
      return this.vehicleCount;
    },
    /**
     * 设置选中的终端并搜索
     * @param {String} val 车牌号
     */
    setSelectedVehicle(val) {
      this.query.deviceId = val.deviceId;
      this.query.filterText = val.targetName;
      this.$nextTick(() => {
        // 单独处理搜索, 不使用公共searchFilter方法
        this.$refs.tree.filter(this.query, {
          ignoreLeafType: 'dept'
        });
        this.nextButtonVisible = true;
      });
      // if (this.loading) {
      //   setTimeout(() => {
      //     this.setSelectedVehicle(val);
      //   }, 1000);
      // } else {
      //   this.query.deviceId = val;
      //   this.searchFilter();
      //   // 搜索后勾选
      //   // 处理用户要求跳转后默认只勾选通道一的情况
      //   let checkedChannels = [];
      //   this.data.forEach(item => {
      //     console.log('iii', item);

      //     if (item.children) {
      //       for (let i = 0; i < item.children.length; i++) {
      //         if (item.children[i].type === 'dept' && item.children[i].children) {
      //           item.children[i].children.forEach(car => {
      //             if (car.type === 'vehicle' && car.label === val) {
      //               if (car.children && car.children[0].type === 'channel') {
      //                 checkedChannels.push({
      //                   deviceId: car.children[0].originData.deviceId,
      //                   channel: car.children[0].originData.channel,
      //                   treeNodeKey: car.children[0].id
      //                 });
      //               }
      //             }
      //           });
      //         }
      //         if (item.children[i].type === 'vehicle' && item.children[i].label === val) {
      //           if (item.children[i].children && item.children[i].children[0].type === 'channel') {
      //             checkedChannels.push({
      //               deviceId: item.children[i].children[0].originData.deviceId,
      //               channel: item.children[i].children[0].originData.channel,
      //               treeNodeKey: item.children[i].children[0].id
      //             });
      //           }
      //         }
      //       }
      //     }
      //   });
      //   this.$refs.tree.setCheckedKeys([checkedChannels[0].treeNodeKey], false);
      //   this.$emit('checkedChannelsChange', {
      //     checkedChannels: checkedChannels
      //   });
      // }
    },
    /**
     * 右键
     * @param event
     * @param object
     * @param node
     */
    rightClick(event, object, node) {
      // console.log(event, object, node);
      this.intercomOriginData = null;
      if (object.type !== 'vehicle') return;
      this.rightMenu = true;
      var right = document.getElementById('rightMenu');
      right.style.position = 'absolute';
      right.style.left = event.clientX > 200 ? event.clientX - 175 + 'px' : event.clientX + 'px';
      right.style.top = event.clientY - 100 + 'px';
      this.intercomOriginData = object.originData;
      window.addEventListener('click', this.handleListener);
    },
    /**
     * 开始对讲
     */
    handleSelect() {
      this.$parent.handleSelect(this.intercomOriginData);
      this.$nextTick(() => {
        this.intercomStyle();
      });
    },
    /**
     * 停止对讲
     */
    handleStop() {
      this.$parent.handleStop(this.intercomOriginData);
      this.$nextTick(() => {
        this.updateStyle();
      });
    },
    /**
     * listener事件
     */
    handleListener() {
      const e = window.event;
      console.log(e);
      this.rightMenu = false;
      window.removeEventListener('click', () => {
      });
    },
    /**
     * 展开节点触发的事件
     * @description 改变defaultExpandedKeys用于保留状态
     * @see onUpdate
     */
    handleNodeExpand(data, node) {
      this.defaultExpandedKeys.push(data.id);
    },
    /**
     * 收起节点触发的事件
     * @description 改变defaultExpandedKeys用于保留状态
     * @see onUpdate
     */
    handleNodeCollapse(data, node) {
      let val = data.id;
      let index = -1;
      for (let i = 0; i < this.defaultExpandedKeys.length; i++) {
        if (this.defaultExpandedKeys[i] === val) {
          index = i;
          break;
        }
      }
      if (index > -1) {
        this.defaultExpandedKeys.splice(index, 1);
      }
    },
    /**
     * 更新数据，保留之前的状态
     * @see defaultCheckedKeys
     * @see defaultExpandedKeys
     */
    onUpdate() {
      this.loading = true;
      rnssChannelTree().then(treeData => {
        console.log('crudVehicle.rnssChannelTree-->', treeData);
        this.defaultCheckedKeys = this.$refs.tree.getCheckedKeys(true);
        this.dataSource(treeData);
        let vehicleCount = 0;
        for (let i = 0; i < treeData.length; i++) {
          vehicleCount += treeData[i].vehicleCount;
        }
        this.vehicleCount = vehicleCount;
        this.$nextTick(() => {
          this.searchFilter();
        });
        this.loading = false;
      }).catch(msg => {
        this.loading = false;
      });
    },
    // 左击事件
    leftClick(event, object, node) {
      this.intercomOriginData = event.originData.deviceId ? event.originData : object.parent.data.originData;
      this.$parent?.drawVideoMapMarker?.(event.originData);
      if (this.isVoice && event.type !== 'dept' && event.type !== 'dept_son') {
        this.$emit('selectedVehicle',  event.originData);
      }
    },
    // 已选择=>全部关闭
    clearAll() {
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys([]);
        this.handleCheck(null, {
          checkedKeys: [],
          checkedNodes: [],
          halfCheckedKeys: [],
          halfCheckedNodes: []
        });
      });
    },
    getAccState(data) {
      // acc_state ACC状态 0-关，1-开
      console.log('-> data.originData.accState', data.originData.accState);
      return data.originData.accState === 1 ? '[ACC开]' : '[ACC关]';
    }
  }

};

</script>

<style lang="less" scoped>
@import "../../../assets/less/variables.less";
/*.custom-tree-node {*/
/*flex: 1;*/
/*display: flex;*/
/*align-items: center;*/
/*justify-content: space-between;*/
/*font-size: 14px;*/
/*padding-right: 8px;*/
/*}*/
.space-set {
  white-space: nowrap
}

/* ::v-deep .el-button--mini {
  padding: 2px 8px;
} */
.contextmenu__item {
  display: block;
  line-height: 34px;
  text-align: center;
}

.contextmenu__item:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.menu {
  position: absolute;
  background-color: #ffffff;
  width: 100px;
  /*height: 106px;*/
  font-size: 12px;
  color: #444040;
  border-radius: 4px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 3px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  white-space: nowrap;
  z-index: 1000;
}

.contextmenu__item:hover {
  cursor: pointer;
  background: #66b1ff;
  border-color: #66b1ff;
  color: #ffffff;
}

// 对讲模块
.mod-intercom {
  height: 200px;
  text-align: center;
}

::v-deep .el-tabs__nav {
  width: 100%;
  text-align: left;
  background-color: #f3f6f8;
}

::v-deep .el-tabs__item {
  width: 50%;
  text-align: center;
  padding: 0;
  height: 34px;
  line-height: 32px;
}

.intercom-head {
  height: 28px;
  line-height: 28px;
  background: #c1c9da;
}

.intercom-mid {
  height: 80px;
  font-size: 40px;
  line-height: 80px;
  color: #757575;
}

.intercom-bottom {
  height: 36px;
  line-height: 36px;
  //background: #e8eaed;
  padding: 0 0 6px 0;
  display: flex;
}

.intercom-bottom-btn {
  flex: 1;
  border: 1px solid #aebac5;
  cursor: pointer;
  margin: 0 6px;
  line-height: 28px;
}

.intercom-bottom-btn:hover {
  color: #409eff;
}

::v-deep .el-tree-node.is-current > .el-tree-node__content {
  color: #606266 !important;
}

.custom-tree-node {
  width: 100%;
}

.custom-tree-node:hover {
  .history-video {
    display: inline-block;
  }
}

.history-video {
  padding-left: 5px;
  display: none;
}


.video-multi-select-tree {
  .header {
    background-color: #f3f6f8;
    height: 54px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
  }

  .car-filter-container {
    display: flex;
    align-items: center;
    border: 1px solid #bfbfbf;
    background-color: #ffffff;
    padding: 0 4px;
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;

    ::v-deep .el-input__inner {
      border: none !important;
      box-shadow: none !important;
    }

    .el-icon-search, .el-icon-refresh {
      font-size: 18px;
      color: #c0c0c0;
    }

    .el-icon-refresh {
      cursor: pointer;
    }
  }

  .online-select {
    width: 70px;
    margin-left: 4px;

    ::v-deep .el-input__inner {
      border-radius: 0;
      border-color: #bfbfbf;
      color: #4d83c9;
    }
  }

  .tree-node-icon {
    width: 18px;
    position: relative;
    top: 3px;
    margin-right: 4px;
  }

  .tree-node-icon-acc {
    margin-right: 4px;
    width: 26px;
    position: relative;
    top: 3px;
    margin-left: 4px;
  }

  .tree-node-icon-other {
    margin-right: 4px;
    width: 18px;
    position: relative;
    top: 3px;
    margin-left: 4px;
  }

  ::v-deep .el-tree-node__content {
    .el-checkbox {
      margin-right: 4px !important;
    }
  }
}

.video-select-component-container {
  height: calc(100% - 200px);
  overflow-y: hidden;
  overflow-x: hidden;
  border-radius: @xhBorderRadiusBase;
  transition: @xhTransitionFast;
  display: flex;
  flex-direction: column;

  .xh-tree {
    flex: 1;

    ::v-deep .vue-recycle-scroller {
      height: 100% !important;
    }

    ::v-deep .el-checkbox .el-checkbox__inner {
      display: inline-block !important;
    }

    ::v-deep .el-checkbox .is-checked, ::v-deep .el-checkbox .is-indeterminate {
      .el-checkbox__inner {
        background-color: #409eff !important;
        border-color: #409eff !important;
      }
    }

    ::v-deep .el-checkbox__inner {
      border: 1px solid #176ac0;
      width: 17px;
      height: 17px;
      border-radius: 0;
      background-color: #d4e8ff;

      &::after {
        border: 2px solid #ffffff;
        border-left: 0;
        border-top: 0;
        height: 8px;
        left: 5px;
        top: 2px;
      }
    }
  }
}
</style>
