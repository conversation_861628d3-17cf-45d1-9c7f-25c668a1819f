<template>
  <div
    v-loading="loading"
    class="video-multi-select-tree"
  >
    <div
      ref="selectComponentContainerDom"
      class="video-select-component-container"
      :class="{'instruction-select-component-container': !isIntercom}"
    >
      <div
        ref="headerDom"
        class="header"
      >
        <div class="car-filter-container">
          <i class="el-icon-search"/>
          <el-input
            v-model="filterText"
            clearable
            size="small"
            placeholder="搜索终端名称"
            class="car-filter"
          />
          <i
            class="el-icon-refresh"
            @click="onRefresh"
          />
          <el-button
            size="small"
            class="search-button"
            @click="searchFilterText(filterText)"
          >
            查询
          </el-button>
        </div>
      </div>
      <vue-easy-tree
        ref="tree"
        :key="treeKey"
        :data="data"
        node-key="id"
        class="xh-tree"
        :show-checkbox="!isVoice"
        :lazy="lazy"
        :props="defaultProps"
        :load="loadTreeNode"
        :style="tableStyle"
        :height="treeMaxHeight"
        :default-checked-keys="defaultCheckedKeys"
        :default-expanded-keys="defaultExpandedKeys"
        :indent="10"
        :default-expand-all="defaultExpand"
        @check="handleCheck"
        @node-click="handleNodeClick"
        @node-expand="handleNodeExpand"
        @node-collapse="handleNodeCollapse"
      >
        <span
          slot-scope="{ node, data }"
          class="custom-tree-node"
          @mouseover="mouseOverNode(node)"
        >
          <template>
            <span v-if="data.type === 0">
              <svg-icon
                :icon-class="getTreeIconClass(data)"
              />
              {{ data.name }}{{ getCount(data) }}
            </span>
            <span v-if="data.type === 1">
              {{ data.name }}{{ getCount(data) }}
            </span>
            <span v-if="data.type === undefined">
              <svg-icon
                :icon-class="getStaffIconClass(data)"
                class="svg-icon-vehicle"
              />
              <span>
                {{ data.name }}
              </span>
            </span>
            <span v-if="data.type === 3 && !isVoice">
              <img
                class="tree-node-icon-other"
                :src="getOtherIcon(node)"
              >
              <span>{{ data.name }}</span>
            </span>
          </template>
        </span>
      </vue-easy-tree>
    </div>
    <div
      v-if="isIntercom"
      class="mod-intercom"
    >
      <el-tabs
        v-model="activeName"
        :stretch="true"
        style="height: 100%"
      >
        <el-tab-pane
          label="对讲"
          name="first"
        >
          <div class="intercom-head">
            <div>
              <span>对讲</span>
              <span>
                [{{ intercomOriginData.targetName || '请点击终端' }}]
              </span>
            </div>
          </div>
          <div class="intercom-mid">
            <i class="el-icon-microphone"/>
          </div>
          <div class="intercom-bottom">
            <div
              class="intercom-bottom-btn common-default-btn"
              @click="intercomOpen"
            >
              打开
            </div>
            <div
              class="intercom-bottom-btn common-default-btn"
              @click="intercomClose"
            >
              停止
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane
          v-if="!isVoice"
          label="监听"
          name="second"
        >
          <div class="intercom-head">
            <div>
              <span>监听</span>
              <span>
                [{{ intercomOriginData.targetName || '请点击终端' }}]
              </span>
            </div>
          </div>
          <div class="intercom-mid">
            <i class="el-icon-service"/>
          </div>
          <div class="intercom-bottom">
            <div
              class="intercom-bottom-btn common-default-btn"
              @click="monitorOpen"
            >
              打开
            </div>
            <div
              class="intercom-bottom-btn common-default-btn"
              @click="monitorClose"
            >
              停止
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { deptTagTreeChannel } from '@/api/base/vehicle';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import variables from '@/assets/less/variables.js';
import onVideo from '@/assets/images/tree/icon03.png';
import offVideo from '@/assets/images/tree/icon04.png';

export default {
  name: 'VideoMultiSelectTree',
  components: {},
  props: {
    isLimitCheckedCount: {
      type: Boolean,
      default: true
    },
    isVoice: {
      type: Boolean,
      default: false
    },
    isIntercom: {
      type: Boolean,
      default: true
    },
    isShow: {
      type: Boolean,
      default: false
    },
    monitorCarObj: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      lastCheckedKeys: [],
      variables: { ...variables },
      intercomOriginData: {},
      activeName: 'first',
      tableStyle: '',
      data: [],
      loading: false,
      defaultCheckedKeys: [],
      defaultExpandedKeys: [],
      treeMaxHeight: '0px',
      filterText: '',
      treeKey: 0, // 车辆树的key(用来更新树)
      defaultProps: {
        isLeaf: 'leaf'
      },
      terminalTreeData: [],
      lazy: true,
      defaultExpand: false,
      treeCheckedKeys: [],
      filterCheckedKeys: [],
      filterCurrentKey: null
    };
  },
  watch: {
    isShow: {
      handler(val) {
        if (val) {
          this.onRefresh();
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.updateStyle();
      if (this.isIntercom) {
        this.onRefresh(() => {
          if (this.$route.query.deviceId) {
            this.setSelectedVehicle(this.$route.query);
          }
        });
      }
    });
  },
  activated() {
    this.$nextTick(() => {
      if (this.$route.query.deviceId) {
        this.setSelectedVehicle(this.$route.query);
      }
    });
  },
  methods: {
    // 点击事件
    handleNodeClick(data, node) {
      if (data.type === undefined) {
        const terminalData = this.findInNestedArray(this.terminalTreeData, item => item.id === data.id);
        this.filterCurrentKey = data.id;
        this.intercomOriginData = {
          deviceId: data.id,
          targetName: data.name,
          deviceType: Number(node.parent.data.id.slice(-1)),
          treeCategory: data.treeCategory,
          children: terminalData.chans || []
        };
        this.$emit('selectedRowSingle', JSON.parse(JSON.stringify(this.intercomOriginData)));
      }
    },
    // 加载子节点
    loadTreeNode (node, resolve) {
      if (node.level > 0) {
        const result = this.findInNestedArray(this.terminalTreeData, item => item.id === node.data.id);
        if (result) {
          if (node.data.type === 1) {
            const list = result.tags.map(item => ({
              ...item,
              leaf: this.isVoice ? true : false,
              chans: []
            }));
            resolve(list);
          } else if (node.data.type === undefined) {
            const list = result.chans.map(item => ({
              ...item,
              leaf: true
            }));
            resolve(list);
          } else {
            const list = result.children.map(item => ({
              ...item,
              children: [],
              tags: []
            }));
            resolve(list);
          }
        } else {
          resolve([]);
        }
      } else {
        resolve(this.terminalTreeData.map(item => ({...item, children: []})));
      }
    },
    findInNestedArray(array, condition) {
      for (let item of array) {
        if (condition(item)) {
          return item;
        }
        if (item.children && item.children.length > 0) {
          const found = this.findInNestedArray(item.children, condition);
          if (found) {
            return found;
          }
        }
        if (item.tags && item.tags.length > 0) {
          const found = this.findInNestedArray(item.tags, condition);
          if (found) {
            return found;
          }
        }
      }
      return null;
    },
    // 监听开启
    monitorOpen() {
      if (!this.intercomOriginData?.deviceId) {
        this.$message.warning('请点击终端');
        return;
      }
      this.$parent.handleMonitorSelect(this.intercomOriginData);
      this.$nextTick(() => {
        this.intercomStyle();
      });
    },
    // 监听关闭
    monitorClose() {
      if (!this.intercomOriginData?.deviceId) {
        return;
      }
      this.$parent.handleMonitorStop(this.intercomOriginData);
      this.$nextTick(() => {
        this.updateStyle();
      });
    },
    // 对讲开启
    intercomOpen() {
      console.log('-> this.intercomOriginData', this.intercomOriginData);
      if (!this.intercomOriginData?.deviceId) {
        this.$message.warning('请点击终端');
        return;
      }
      if (this.intercomOriginData.treeCategory === '202') {
        this.$parent.handleTalkbackSelect(this.intercomOriginData);
      } else {
        this.$parent.handleSelect(this.intercomOriginData);
      }
      this.$nextTick(() => {
        this.intercomStyle();
      });
    },
    // 对讲关闭
    intercomClose() {
      if (!this.intercomOriginData?.deviceId) {
        return;
      }
      this.$parent.handleStop(this.intercomOriginData);
      this.$nextTick(() => {
        this.updateStyle();
      });
    },
    /**
     * 刷新
     */
    onRefresh(callback) {
      this.loading = true;
      this.onClear();
      deptTagTreeChannel().then(treeData => {
        console.log('crudVehicle.deptTagTreeChannel-->', treeData);
        this.terminalTreeData = Object.freeze(treeData);
        this.data = (treeData || []).map(item => ({...item, children: []}));
        this.loading = false;
        this.$nextTick(() => {
          typeof callback === 'function' && callback();
        });
      }).catch(msg => {
        this.$notify({
          title: msg,
          type: 'warning'
        });
        this.loading = false;
      });
    },
    /**
     * 节点过滤(替换新的查询搜索)
     */
    searchFilterText (val) {
      if (this.isVoice) {
        const defaultCurrentKeys = this.$refs.tree.getCurrentKey();
        if (val) {
          const list = this.filterNestedArray(this.terminalTreeData, val);
          this.data = list;
          this.lazy = false;
          this.defaultExpand = true;
          this.treeKey += 1;
        } else {
          this.data = this.terminalTreeData.map(item => ({...item, children: []}));
          this.lazy = true;
          this.defaultExpand = false;
          this.treeKey += 1;
          if (this.filterCurrentKey) {
            this.defaultExpandedKeys = [...new Set([...this.defaultExpandedKeys, ...this.findAncestorsById(this.terminalTreeData, this.filterCurrentKey)])];
          }
        }
        this.$nextTick(() => {
          this.filterCurrentKey = null;
          if (defaultCurrentKeys) {
            this.$refs.tree.setCurrentKey(defaultCurrentKeys);
          }
        });
      } else {
        if (val) {
          const defaultCheckedKeys = this.$refs.tree.getCheckedKeys();
          this.treeCheckedKeys = [...new Set([...this.treeCheckedKeys, ...defaultCheckedKeys])];
          const list = this.filterNestedArray(this.terminalTreeData, val);
          this.data = list;
          this.lazy = false;
          this.defaultExpand = true;
          this.treeKey += 1;
        } else {
          this.data = this.terminalTreeData.map(item => ({...item, children: []}));
          this.lazy = true;
          this.defaultExpand = false;
          this.treeKey += 1;
          for (let index = 0; index < this.filterCheckedKeys.length; index++) {
            const element = this.filterCheckedKeys[index];
            this.defaultExpandedKeys = [...new Set([...this.defaultExpandedKeys, ...this.findAncestorsById(this.terminalTreeData, element)])];
          }
          this.treeCheckedKeys = [...new Set([...this.treeCheckedKeys, ...this.filterCheckedKeys])];
        }
        this.$nextTick(() => {
          this.filterCheckedKeys = [];
          this.$refs.tree.setCheckedKeys(this.treeCheckedKeys);
        });
      }
    },
    // 查找指定id的节点及其所有父级和祖宗级的id
    findAncestorsById(data, targetId) {
      const ancestors = [];
      return this.findRecursive(data, targetId, ancestors);
    },
    findRecursive(items, targetId, path = []) {
      for (let item of items) {
        if (item.id === targetId) {
          return [...path];
        }
        if (item.children && item.children.length > 0) {
          const result = this.findRecursive(item.children, targetId, [...path, item.id]);
          if (result) {
            return result;
          }
        }
        if (item.tags && item.tags.length > 0) {
          const result = this.findRecursive(item.tags, targetId, [...path, item.id]);
          if (result) {
            return result;
          }
        }
        if (item.chans && item.chans.length > 0) {
          const result = this.findRecursive(item.chans, targetId, [...path, item.id]);
          if (result) {
            return result;
          }
        }
      }
      return null;
    },
    filterNestedArray(array, filterValue) {
      const result = [];
      result.push(...this.recursiveFilter(array, filterValue));
      return result;
    },
    recursiveFilter(items, filterValue) {
      return items.reduce((acc, item) => {
        if (item.name.includes(filterValue)) {
          acc.push(this.renameTagsToChildren({ ...item }));
        } else if (item.children && item.children.length > 0) {
          const filteredChildren = this.recursiveFilter(item.children, filterValue);
          if (filteredChildren.length > 0) {
            acc.push({ ...item, children: filteredChildren });
          }
        } else if (item.tags && item.tags.length > 0) {
          const filteredChildren = this.recursiveFilter(item.tags, filterValue);
          if (filteredChildren.length > 0) {
            acc.push({ ...item, children: filteredChildren });
          }
        } else if (item.chans && item.chans.length > 0) {
          const filteredChildren = this.recursiveFilter(item.chans, filterValue);
          if (filteredChildren.length > 0) {
            acc.push({ ...item, children: filteredChildren });
          }
        }
        return acc;
      }, []);
    },
    // 将所有的tags/chans属性值替换为children, 保持统一便组件使用
    renameTagsToChildren(item) {
      if (item.tags) {
        item.children = item.tags;
      }
      if (item.chans) {
        item.children = item.chans;
      }
      if (item.children) {
        item.children = item.children.map(child => this.renameTagsToChildren(child));
      }
      return item;
    },
    // 在线/总数终端
    getCount(data) {
      return `(${data.onlineNum}/${data.total})`;
    },
    /**
     * 勾选-处理多个
     * @param node
     * @param options
     * @param options.checkedNodes 勾选的节点
     * @param options.checkedKeys 勾选节点的key
     * @param options.halfCheckedNodes
     * @param options.halfCheckedKeys
     * @typedef {Array.<{deviceId: String, channel: Number, treeNodeKey: String}>} ResultArray
     * @description emit{ResultArray}
     */
    handleCheck(node, options) {
      console.log('node', node);
      console.log('options', options);
      let checkedKeys = options.checkedKeys;
      let checkedNode = node;
      let treeData = this.filterText ? this.data : this.terminalTreeData;
      checkedNode.checked = checkedKeys.includes(node.id);
      if (this.filterText && checkedNode.checked) {
        this.filterCheckedKeys.push(node.id);
      } else if (this.filterText && !checkedNode.checked) {
        this.filterCheckedKeys = this.filterCheckedKeys.filter(item => item !== node.id);
        this.treeCheckedKeys = this.treeCheckedKeys.filter(item => item !== node.id);
      }
      let checkedList = [];
      // 勾选
      if (checkedNode.checked) {
        // 勾选机构/分组/终端时
        if (checkedNode.type !== 3) {
          const result = this.findInNestedArray(treeData, item => item.id === checkedNode.id);
          if (result && checkedNode.type !== undefined) {
            checkedList = this.getAllTerminal([result]);
          } else if (result){
            const nodeData = this.$refs.tree.getNode(checkedNode.id);
            const type = Number(nodeData.parent.data.id.slice(-1));
            checkedList = this.getAllTerminal([result], type);
          }
        } else {
          const nodeData = this.$refs.tree.getNode(checkedNode.id);
          // 勾选通道时
          checkedList = [{
            ...checkedNode,
            deviceType: Number(nodeData.parent.parent.data.id.slice(-1)),
            deviceId: nodeData.parent.data.id,
            targetName: nodeData.parent.data.name,
            treeNodeKey: checkedNode.id,
            treeCategory: nodeData.parent.data.treeCategory
          }];
        }
        let allCheckedKeys = Object.keys(this.monitorCarObj).concat(checkedList.map(item => item.treeNodeKey));
        if (this.isLimitCheckedCount && allCheckedKeys.length > 16) {
          this.$notify({
            title: `无法播放超过16路视频（已播放${this.lastCheckedKeys.length}路，
            请求再播放${allCheckedKeys.length - this.lastCheckedKeys.length}），
            请先关闭部分摄像头！`,
            type: 'warning'
          });
          this.$refs.tree.setCheckedKeys(this.lastCheckedKeys);
          return;
        }
        this.lastCheckedKeys = allCheckedKeys;
      } else { // 取消勾选
        // 勾选机构/分组/终端时
        if (checkedNode.type !== 3) {
          const result = this.findInNestedArray(treeData, item => item.id === checkedNode.id);
          if (result && checkedNode.type !== undefined) {
            checkedList = this.getDelTerminal([result]);
          } else if (result) {
            const nodeData = this.$refs.tree.getNode(checkedNode.id);
            const type = Number(nodeData.parent.data.id.slice(-1));
            checkedList = this.getDelTerminal([result], type);
          }
        } else {
          const nodeData = this.$refs.tree.getNode(checkedNode.id);
          // 取消勾选通道时
          checkedList = [{
            ...checkedNode,
            deviceType: Number(nodeData.parent.parent.data.id.slice(-1)),
            deviceId: nodeData.parent.data.id,
            targetName: nodeData.parent.data.name,
            treeNodeKey: checkedNode.id,
            treeCategory: nodeData.parent.data.treeCategory
          }];
        }
        let delCheckedKeys = checkedList.map(item => item.treeNodeKey);
        let allCheckedKeys = Object.keys(this.monitorCarObj).filter(item => !delCheckedKeys.includes(item));
        this.lastCheckedKeys = allCheckedKeys;
      }
      this.$emit('checkedChannelsChange', checkedNode, checkedList);
    },
    // 获取勾选节点下的所有终端(已显示的不在获取), 返回一个终端数组
    getAllTerminal(data, type) {
      let arr = [];
      for (let index = 0; index < data.length; index++) {
        const element = data[index];
        if (element.chans && element.chans.length) {
          for (let k = 0; k < element.chans.length; k++) {
            const item = element.chans[k];
            if (!this.monitorCarObj[item.id]) {
              arr.push({
                ...item,
                deviceType: type,
                deviceId: element.id,
                targetName: element.name,
                treeNodeKey: item.id,
                treeCategory: element.treeCategory
              });
            }
          }
          // arr = arr.concat(element.tags.filter(v => !this.monitorCarObj[v.id]));
        }
        if (element.children && element.children.length) {
          const result = this.getAllTerminal(element.children);
          arr = arr.concat(result);
        }
        if (element.tags && element.tags.length) {
          const result = this.getAllTerminal(element.tags, Number(element.id.slice(-1)));
          arr = arr.concat(result);
        }
      }
      return arr;
    },
    // 获取勾选节点下的所有终端(已删除的不在获取), 返回一个终端数组
    getDelTerminal(data, type) {
      let arr = [];
      for (let index = 0; index < data.length; index++) {
        const element = data[index];
        if (element.chans && element.chans.length) {
          for (let k = 0; k < element.chans.length; k++) {
            const item = element.chans[k];
            if (this.monitorCarObj[item.id]) {
              arr.push({
                ...item,
                deviceType: type,
                deviceId: element.id,
                targetName: element.name,
                treeNodeKey: item.id,
                treeCategory: element.treeCategory
              });
            }
          }
          // arr = arr.concat(element.tags.filter(v => this.monitorCarObj[v.id]));
        }
        if (element.children && element.children.length) {
          const result = this.getDelTerminal(element.children);
          arr = arr.concat(result);
        }
        if (element.tags && element.tags.length) {
          const result = this.getDelTerminal(element.tags, Number(element.id.slice(-1)));
          arr = arr.concat(result);
        }
      }
      return arr;
    },
    getOtherIcon(node) {
      const { fusionState } = node.parent.data;
      if (fusionState === 0) {
        return offVideo;
      } else {
        return onVideo;
      }
    },
    /**
     * 获取其他类型的图标名
     * @param {Object} data
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getStaffIconClass(data) {
      const vehicleModel = ['101', '102', '103', '104', '105'];
      const materialsModel = ['106', '108', '109', '111', '112', '114'];
      const personnelModel = ['201', '202', '301', '107', '110', '113'];
      const shortMessageModel = ['302', '303'];
      const timeServiceModel = ['501', '502', '503'];
      const monitorModel = ['401', '402'];
      let vehicleIcon = '';
      if (vehicleModel.includes(data.treeCategory)) {
        vehicleIcon = this.colorStaffType(data, 'vehicle'); // 终端
      }
      else if (materialsModel.includes(data.treeCategory)) {
        vehicleIcon = this.colorStaffType(data, 'materials'); // 物资
      }
      else if (personnelModel.includes(data.treeCategory)) {
        vehicleIcon = this.colorStaffType(data, 'personnel'); // 人员
      }
      else if (shortMessageModel.includes(data.treeCategory)) {
        vehicleIcon = this.colorStaffType(data, 'shortMessage'); // 短报文终端
      }
      else if (timeServiceModel.includes(data.treeCategory)) {
        vehicleIcon = this.colorStaffType(data, 'timeService'); // 授时终端
      }
      else if (monitorModel.includes(data.treeCategory)) {
        vehicleIcon = this.colorStaffType(data, 'monitor'); // 监测终端
      }
      else if (data.treeCategory === '0') {
        vehicleIcon = this.colorStaffType(data, 'other'); // 其他
      }
      return vehicleIcon;
    },
    colorStaffType(val, type) {
      // 0-离线 1-静止 2-移动
      // 离线(Offline) 静止(Static) 移动(Move)
      let vehicleIcon = '';
      switch (val.fusionState) {
      case 0:
        vehicleIcon = `${type}Offline`;
        break;
      case 1:
        vehicleIcon = `${type}Static`;
        break;
      case 2:
        vehicleIcon = `${type}Move`;
        break;
      }
      return vehicleIcon;
    },
    /**
     * 获取车组的图标名称
     * @return {String} src/assets/icons/svg文件夹中的图片名
     */
    getTreeIconClass(data) {
      let treeIcon = '';
      if (data.onlineNum > 0) {
        treeIcon = 'treeOnline';
      }
      else {
        treeIcon = 'tree';
      }
      return treeIcon;
    },
    /**
     * 鼠标滑过某个节点
     * @param node
     */
    mouseOverNode(node) {
      if (node.data.type === 3) {
        let data = {
          deviceId: node.parent.data.id,
          channel: node.data.channel
        };
        this.$emit('mouseOverNode', data);
      }
      else if (node.data.type === undefined) {
        let data = {
          deviceId: node.data.id
        };
        this.$emit('mouseOverNode', data);
      }
      else {
        this.$emit('mouseOverNode', {});
      }
    },
    /**
     * 设置keys
     * @param keys
     */
    setCheckedKeys(keys) {
      this.$nextTick(() => {
        let checkedKeys = this.$refs.tree.getCheckedKeys(true); // (leafOnly) 接收一个 boolean 类型的参数，若为 true 则仅返回被选中的叶子节点的 keys，默认值为 false
        // console.log('old', checkedKeys);
        // console.log('new', keys);
        if (!(checkedKeys.sort().toString() === keys.sort().toString())) {
          // console.log('不是完全相等')
          this.$refs.tree.setCheckedKeys(keys, true);
        }
        // this.$refs.tree.setCheckedKeys(keys, true);
      });
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('Vehicle', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder(value) {
      return getPlaceholder('Vehicle', value);
    },
    /**
     * 更新样式
     */
    updateStyle() {
      let selectComponentDomHeight = this.$refs['selectComponentContainerDom'].clientHeight;
      let headerDomHeight = this.$refs['headerDom'].clientHeight;
      const xhSpacingBase = variables.xhSpacingBase;// @xhSpacingBase
      const collapseItemTitleHeight = variables.tagsViewHeight; // @tagsViewHeight
      let vlStyle = 28;
      let maxHeight = selectComponentDomHeight - headerDomHeight - collapseItemTitleHeight - 4 * xhSpacingBase + vlStyle;
      if (this._lastTableHeight !== maxHeight) {
        this.tableStyle = `height: ${maxHeight + 'px'}; overflow-y: auto`;
        this._lastTableHeight = maxHeight;
        this.treeMaxHeight = maxHeight + 'px';
        // requestAnimationFrame(this.updateStyle)
      }
    },
    /**
     * 更新样式
     */
    intercomStyle() {
      let selectComponentDomHeight = this.$refs['selectComponentContainerDom'].clientHeight;
      let headerDomHeight = this.$refs['headerDom'].clientHeight;
      const xhSpacingBase = variables.xhSpacingBase;// @xhSpacingBase
      const collapseItemTitleHeight = variables.tagsViewHeight; // @tagsViewHeight
      let maxHeight = selectComponentDomHeight - headerDomHeight - collapseItemTitleHeight - 4 * xhSpacingBase;
      if (this._lastTableHeight !== maxHeight) {
        this.tableStyle = `height: 250px; overflow-y: auto`;
        this._lastTableHeight = maxHeight;
        requestAnimationFrame(this.intercomStyle);
      }
    },
    /**
     * 清除搜索条件
     */
    onClear() {
      this.filterText = '';
      // 清除勾选和展开节点的状态
      this.defaultCheckedKeys = [];
      this.defaultExpandedKeys = [];
      this.treeCheckedKeys = [];
      this.filterCheckedKeys = [];
      this.filterCurrentKey = null;
      this.lazy = true;
      this.defaultExpand = false;
      // 修改lazy后需要手动更新tree
      this.treeKey += 1;
    },
    /**
     * 设置选中的终端并搜索
     * @param {String} val 车牌号
     */
    setSelectedVehicle(val) {
      this.filterText = val.targetName;
      this.$nextTick(() => {
        this.searchFilterText(this.filterText);
      });
    },
    /**
     * 开始对讲
     */
    handleSelect() {
      this.$parent.handleSelect(this.intercomOriginData);
      this.$nextTick(() => {
        this.intercomStyle();
      });
    },
    /**
     * 停止对讲
     */
    handleStop() {
      this.$parent.handleStop(this.intercomOriginData);
      this.$nextTick(() => {
        this.updateStyle();
      });
    },
    /**
     * 展开节点触发的事件
     * @description 改变defaultExpandedKeys用于保留状态
     * @see onUpdate
     */
    handleNodeExpand(data, node) {
      this.defaultExpandedKeys.push(data.id);
    },
    /**
     * 收起节点触发的事件
     * @description 改变defaultExpandedKeys用于保留状态
     * @see onUpdate
     */
    handleNodeCollapse(data, node) {
      let val = data.id;
      let index = -1;
      for (let i = 0; i < this.defaultExpandedKeys.length; i++) {
        if (this.defaultExpandedKeys[i] === val) {
          index = i;
          break;
        }
      }
      if (index > -1) {
        this.defaultExpandedKeys.splice(index, 1);
      }
      // 收起父节点时同时收起子节点
      if (node.childNodes.length > 0) {
        let list = this.flattenKeyList(node.childNodes);
        for (let i = 0; i < list.length; i++) {
          const element = list[i];
          const key = this.defaultExpandedKeys.findIndex(item => item === element);
          if (key !== -1) {
            this.defaultExpandedKeys.splice(key, 1);
          }
        }
      }
    },
    flattenKeyList(data) {
      let result = [];
      data.forEach((item)=> {
        if (item.data.type === 0 || item.data.type === 1) {
          result.push(item.data.id);
          if (item.childNodes && item.childNodes.length > 0 && item.data.type === 0) {
            let childId = this.flattenKeyList(item.childNodes);
            result = result.concat(childId);
          }
        }
      });
      return result;
    },
    // 已选择=>全部关闭
    clearAll() {
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys([]);
      });
    }
  }

};

</script>

<style lang="less" scoped>
@import "../../../assets/less/variables.less";
.space-set {
  white-space: nowrap
}

/* ::v-deep .el-button--mini {
  padding: 2px 8px;
} */
.contextmenu__item {
  display: block;
  line-height: 34px;
  text-align: center;
}

.contextmenu__item:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.menu {
  position: absolute;
  background-color: #ffffff;
  width: 100px;
  /*height: 106px;*/
  font-size: 12px;
  color: #444040;
  border-radius: 4px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 3px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  white-space: nowrap;
  z-index: 1000;
}

.contextmenu__item:hover {
  cursor: pointer;
  background: #66b1ff;
  border-color: #66b1ff;
  color: #ffffff;
}

// 对讲模块
.mod-intercom {
  height: 200px;
  text-align: center;
}

::v-deep .el-tabs__nav {
  width: 100%;
  text-align: left;
  background-color: #f3f6f8;
}

::v-deep .el-tabs__item {
  width: 50%;
  text-align: center;
  padding: 0;
  height: 34px;
  line-height: 32px;
}

.intercom-head {
  height: 28px;
  line-height: 28px;
  background: #c1c9da;
}

.intercom-mid {
  height: 80px;
  font-size: 40px;
  line-height: 80px;
  color: #757575;
}

.intercom-bottom {
  height: 36px;
  line-height: 36px;
  //background: #e8eaed;
  padding: 0 0 6px 0;
  display: flex;
}

.intercom-bottom-btn {
  flex: 1;
  border: 1px solid #aebac5;
  cursor: pointer;
  margin: 0 6px;
  line-height: 28px;
}

.intercom-bottom-btn:hover {
  color: #409eff;
}

::v-deep .el-tree-node.is-current > .el-tree-node__content {
  color: #606266 !important;
}

.custom-tree-node {
  width: 100%;
}

.custom-tree-node:hover {
  .history-video {
    display: inline-block;
  }
}

.history-video {
  padding-left: 5px;
  display: none;
}


.video-multi-select-tree {
  .header {
    background-color: #f3f6f8;
    height: 54px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
  }

  .car-filter-container {
    display: flex;
    align-items: center;
    border: 1px solid #bfbfbf;
    background-color: #ffffff;
    padding-left: 4px;
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;

    ::v-deep .el-input__inner {
      border: none !important;
      box-shadow: none !important;
    }

    .el-icon-search, .el-icon-refresh {
      font-size: 18px;
      color: #c0c0c0;
    }

    .el-icon-refresh {
      padding-right: 4px;
      cursor: pointer;
    }
    .search-button {
        border: none;
        border-left: 1px solid #bfbfbf !important;
        border-radius: 0 !important;
      }
  }

  .online-select {
    width: 70px;
    margin-left: 4px;

    ::v-deep .el-input__inner {
      border-radius: 0;
      border-color: #bfbfbf;
      color: #4d83c9;
    }
  }

  .tree-node-icon {
    width: 18px;
    position: relative;
    top: 3px;
    margin-right: 4px;
  }

  .tree-node-icon-acc {
    margin-right: 4px;
    width: 26px;
    position: relative;
    top: 3px;
    margin-left: 4px;
  }

  .tree-node-icon-other {
    margin-right: 4px;
    width: 18px;
    position: relative;
    top: 3px;
    margin-left: 4px;
  }

  ::v-deep .el-tree-node__content {
    .el-checkbox {
      margin-right: 4px !important;
    }
  }
}

.video-select-component-container {
  height: calc(100% - 200px);
  overflow-y: hidden;
  overflow-x: hidden;
  border-radius: @xhBorderRadiusBase;
  transition: @xhTransitionFast;
  display: flex;
  flex-direction: column;

  .xh-tree {
    flex: 1;

    ::v-deep .vue-recycle-scroller {
      height: 100% !important;
    }

    ::v-deep .el-checkbox .el-checkbox__inner {
      display: inline-block !important;
    }

    ::v-deep .el-checkbox .is-checked, ::v-deep .el-checkbox .is-indeterminate {
      .el-checkbox__inner {
        background-color: #409eff !important;
        border-color: #409eff !important;
      }
    }

    ::v-deep .el-checkbox__inner {
      border: 1px solid #176ac0;
      width: 17px;
      height: 17px;
      border-radius: 0;
      background-color: #d4e8ff;

      &::after {
        border: 2px solid #ffffff;
        border-left: 0;
        border-top: 0;
        height: 8px;
        left: 5px;
        top: 2px;
      }
    }
  }
}
.instruction-select-component-container {
  height: 100%;
}
</style>
