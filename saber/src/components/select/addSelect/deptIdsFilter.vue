<template>
  <div class="layout">
    <div class="input-decorate el-input__inner" @click="treeOpen">
      <div class="input-decorate-content">
        {{ valueDisplay }}
        <span v-show="!valueDisplay" class="input-decorate-hold el-input__clear">请选择单位</span>
      </div>
      <i v-show="valueDisplay" class="el-icon-circle-close input-close-icon" @click="iconClear" />
    </div>
    <transition name="el-zoom-in-top">
      <div v-show="treeShow" class="layout-content" @click="(e)=>{e.stopPropagation();}">
        <el-input ref="filterText" v-model="filterText" class="input-search" placeholder="搜索单位" size="small"
          clearable />
        <!-- 全选暂时去除 -->
        <!-- <div class="tree-check">
          <el-checkbox
            v-model="checked"
            @change="handleCheckAllChange"
          >
            全选
          </el-checkbox>
        </div> -->
        <vue-easy-tree
          ref="tree"
          class="layout-tree"
          :data="deptOptions"
          show-checkbox
          node-key="id"
          height="500px"
          :props="defaultProps"
          :filter-node-method="filterNode"
          @check="handleCheckChange"
          @node-click="handleNodeClick"
        />
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  data () {
    return {
      defaultProps: {
        children: 'children',
        label: 'title'
      },
      deptOptions: [],
      valueDisplay: null,
      treeShow: false,
      filterText: '',
      checked: false,
      timer: null // 定时器
    };
  },
  watch: {
    filterText: {
      handler (newVal) {
        this.debounce(newVal, 300);
      }
    }
  },
  created () {
    this.getDept();
  },
  methods: {
    debounce (newVal, delay) {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        this.$refs.tree.filter(newVal);
      }, delay);
    },
    // 获取单位树
    getDept () {
      this.$store.dispatch('GetDept').then(res => {
        console.log('-> res.data', res.data)
        this.deptOptions = res.data;
      }).catch(() => {
        this.deptOptions = [];
      });
    },
    // 清空按钮
    iconClear (e) {
      if (e) {
        e.stopPropagation();
      }
      this.valueDisplay = null;
      this.$emit('input', null);
      this.$refs.tree.setCheckedKeys([]);
      this.filterText = '';
    },
    // 选择后触发
    handleCheckChange (data, checked, indeterminate) {
      this.sendNodeNow();
    },
    filterNode (value, data) {
      if (!value) return true;
      const label = data.label || data.title
      return label.indexOf(value) !== -1;
    },
    // 传值
    sendNodeNow () {
      let arr = this.$refs.tree.getCheckedNodes();
      let str = '';
      let ids = '';
      arr.forEach(item => {
        str = str ? str + '；' + item.title : item.title;
        ids = ids ? ids + ',' + item.id : item.id + ''; // TODO 可能影响性能
      });
      this.valueDisplay = str;
      this.$emit('input', ids);
    },
    // 关闭监听
    treeClose () {
      if (this.treeShow) this.treeShow = false;
      document.removeEventListener('click', this.treeClose);
    },
    // 显示单位树
    treeOpen (e) {
      e.stopPropagation();
      this.treeShow = !this.treeShow;
      if (this.treeShow) {
        document.addEventListener('click', this.treeClose);
      }
      this.$nextTick(() => {
        this.$refs.filterText.focus();
      });
    },
    // 全选
    handleCheckAllChange (val) {
      if (val) this.$refs.tree.setCheckedNodes(this.deptOptions);
      else this.$refs.tree.setCheckedNodes([]);
    },
    handleNodeClick(node, treeNode) {
      // 取消选中
      this.$refs.tree.setCurrentKey(null)
      // console.log(data);
    },
  }
};
</script>

<style lang="less" scoped>
.layout{
  position: relative;
  width: calc(100% - 80px);
}
.input-decorate{
    //width: 156px;
    height: 32px;
    line-height: 32px;
    cursor: pointer;
    position: relative;
}
.input-decorate-content{
    //width: 110px;
    overflow-x: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: flex; // 字体出现偏移, 设置flex
}
.input-decorate-hold{
    font-size: 13px;
    color: #C0C4CC;
}
.input-close-icon{
    position: absolute;
    right: 10px;
    top: 0;
    line-height: 32px;
    color: #C0C4CC;
}
.layout-content{
    position: absolute;
    top: 32px;
    left: 0;
    z-index: 2001;
    box-shadow: 0 0 8px 0 rgb(0 0 0 / 20%);
    // width: 400px;
    background-color: white;
}
.input-search{
  width: 100%;
}
.input-search ::v-deep .el-input__inner {
  border-radius: 0;
}
.tree-check{
    padding-left: 6px;
}
.layout-tree{
    min-width: 400px;
    height: 500px;
    // overflow-y: scroll;
}
</style>
