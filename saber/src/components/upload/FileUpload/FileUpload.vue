<template>
  <div class="FileUpload">
    <el-upload
      ref="FileUpload"
      :disabled="canuse"
      :action="Urlupload"
      :on-exceed="handleExceed"
      :on-change="handleChange"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      :before-upload="beforeUpload"
      :headers="getHeader"
      :file-list="fileList"
      multiple
      with-credentials
      :limit="((drivingFilesLength === 1) ? 1 : 3) - (currentHistoryFilesLength ? currentHistoryFilesLength : 0)"
    >
      <el-button
        v-show="uploadButtonVisible"
        size="small"
        type="primary"
        title="点击上传附件"
      >
        点击上传
      </el-button>
      <div
        v-show="uploadButtonVisible"
        slot="tip"
        class="el-upload__tip"
        style="display: inline-block"
      >
        上传文件不超过25M
      </div>
    </el-upload>
  </div>
</template>

<script>
// import {getToken} from '@/api/utils/auth';
export default {
  name: 'FileUpload',
  components: {
  },
  props: {
    // 行驶证只能上传一张图片
    drivingFilesLength: {
      type: Number,
      default: 0
    },
    currentHistoryFilesLength: {
      type: Number,
      default: 0
    },
    isableclick: Boolean
  },
  data () {
    let self = this;
    console.log(self.$serverURL);
    return {
      Urlupload: self.$serverURL + '/fs/group1/upload', // FIXME
      // Urlupload: 'http://**************:20143/group1/upload',
      fileList: []
    };
  },
  computed: {
    getHeader () {
      return {
        'Authorization': 'Bearer ' + 'fastdfs_authorization' // FIXME 文件上传下载带的token都要用固定的“fastdfs_authorization
      };
    },
    canuse () {
      return this.isableclick;
    },
    uploadButtonVisible () {
      if (this.currentHistoryFilesLength === undefined) {
        if (this.isableclick === false) {
          return true;
        } else {
          return false;
        }
      } else {
        let valid = 3 - this.currentHistoryFilesLength;
        if (valid - this.fileList.length > 0) {
          return true;
        } else {
          return false;
        }
      }
    },
    showButtonForCost () {
      if (this.isableclick === false) {
        return true;
      } else {
        return false;
      }
    }
  },
  mounted () {
    console.log(process.env);
  },
  methods: {
    getFiles () {
      return this.fileList;
    },
    getFilesString () {
      let files = this.fileList || [];
      let filesString = [];
      for (let i = 0; i < files.length; i++) {
        const element = files[i];
        filesString.push(element.response.data);
      }
      return filesString.toString();
    },
    handleExceed (files, fileList) {
      if (this.drivingFilesLength === 1) {
        this.$msgbox.alert('当前只允许上传1个文件', '上传限制', {
          confirmButtonText: '确定'
        });
      } else {
        console.info('---> handleExceed:', `当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
        this.$msgbox.alert('最多只能上传3个文件', '上传限制', {
          confirmButtonText: '确定'
        });
      }
    },
    handleRemove (file, fileList) {
      this.fileList = fileList;
    },
    handleChange (file, fileList) {
      this.fileList = fileList;
    },
    handleSuccess (response, file, fileList) {
      console.log('1------>', response.data);
    },
    clearFiles () {
      this.$refs.FileUpload.clearFiles();
      this.fileList = [];
    },
    getFilesNameString () {
      let fileList = this.fileList;
      let fileNameString = '';
      for (let i = 0; i < fileList.length; i++) {
        if (i > 0) {
          fileNameString += ',';
        }
        fileNameString += fileList[i].name;
      }
      return fileNameString;
    },
    getFilesArray () {
      let fileList = this.fileList;
      let filesArray = [];
      for (let i = 0; i < fileList.length; i++) {
        filesArray.push({
          fileUrl: fileList[i].response.data,
          fileName: fileList[i].name
        });
      }
      return filesArray;
    },
    beforeUpload (file) {
      const fileSize = file.size > 0;
      if (!fileSize) {
        this.$message.error('上传的附件必须大于0B!');
      }
      return fileSize;
    }
  }
};
</script>

<style lang="less" scoped>
.FileUpload{
  // display:flex;
}
</style>
