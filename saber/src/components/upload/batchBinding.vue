<template>
  <el-dialog
    :visible.sync="batchBindingVisible"
    :before-close="handleClose"
    :modal-append-to-body="false"
    title="批量绑定"
    width="400px"
  >
    <el-upload
      ref="batchBinding"
      :auto-upload="false"
      :on-change="handleChange"
      :on-exceed="handleLimit"
      :limit="1"
      class="uploadBatch"
      drag
      action="#"
      accept=".xlsx,.xls"
    >
      <i class="el-icon-upload" />
      <div class="el-upload__text">
        将Excel拖到此处，或<em class="upload-text">点击上传</em>
      </div>
      <div
        slot="tip"
        class="el-upload__tip"
      >
        <a
          :href="`/bdsplatform/static/excelTemplate/${src[mod]}批量绑定模板.xlsx`"
          class="downloadFile"
        >下载模板</a>
      </div>
      <div v-if="tips" slot="tip">
        {{ tips }}
      </div>
    </el-upload>
    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="handleClose"
      >
        取消
      </el-button>
      <el-button
        type="primary"
        size="small"
        @click="handleconfirm"
      >
        确认
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    batchBindingVisible: {
      type: Boolean,
      default: false
    },
    mod: {
      type: String,
      default: ''
    },
    tips: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      src: {
        rdssDevice: '短报文终端管理',
        wear: '穿戴式终端管理',
        detection: '监测终端管理',
        time: '授时终端管理',
        terminal: '定位终端管理',
        driver: '人员管理',
        vehicleBind: '车辆',
        facility: '基础设施管理',
        simCardManager: '物联网卡管理',
        equipmentLedger: '终端设备入库查询',
        expatriate: '外派管理',
        visitor: '访客管理',
        container: '集装箱管理',
        carriage: '铁路货车车厢管理',
        ship: '货船管理',
        precision: '精密装备管理',
        user: '用户管理',
        vehicleTruck: '矿用卡车管理'
      },
      fileList: [],
      file: '',
      upData: undefined,
    };
  },
  watch: {
    batchBindingVisible() {
      if(this.batchBindingVisible) {
        this.$EventBus.$on('clearFileErr', () => {
          this.upData = undefined;
          this.$refs.batchBinding.clearFiles()
        })
      } else {
        this.$EventBus.$off('clearFileErr');
      }
    }
  },
  methods: {
    handleconfirm () {
      if (!this.file) {
        this.$message.warning('请选择文件');
        return false;
      }
      if (this.file) {
        this.$emit('setBatchBindingData', {
          file: this.file,
          close: this.handleClose
        });
      }
    },
    handleClose () {
      this.upData = undefined;
      this.file = undefined;
      this.$refs.batchBinding.clearFiles();
      this.$emit('close');
    },
    handleLimit() {
      this.$message.warning('只能上传1个文件')
    },
     handleChange(file, fileList) {
      if (!this.beforeUp(file)) {
        this.$refs.batchBinding.clearFiles();
        return;
      }
      this.fileList = [fileList[fileList.length - 1]]; // 只能上传一个Excel，重复上传会覆盖之前的
      this.file = file;
    },
    // 上传格式做限制
    beforeUp (file) {
      let testmsg = file.name.substring(file.name.lastIndexOf('.') + 1);
      if (testmsg !== 'xls' && testmsg !== 'xlsx') {
        this.$message({
          message: '上传文件格式错误!',
          type: 'warning'
        });
        return false;
      } else {
        return true;
      }
    }
  }
};
</script>

<style scoped>
.downloadFile{
  color: var(--gn-color);
}
.upload-text {
  color: var(--gn-color) !important;
}

</style>
