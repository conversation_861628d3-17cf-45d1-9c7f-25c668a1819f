<!-- 带有单位下拉框的文件上传组件：前端解析excel -->

<template>
  <el-dialog
    :visible.sync="visible"
    :before-close="handleClose"
    :modal-append-to-body="false"
    title="批量导入"
    width="400px"
  >
    <div class="dept-item">
      <span style="margin-right: 8px;">
        归属单位
      </span>
      <DeptFormSingleSelect
        v-if="isShowDept"
        ref="dept"
        v-model="deptId"
        :is-show="true"
        placeholder="请选择归属单位"
        size="small"
        style="width: calc(100% - 68px);"
      />
    </div>
    <el-upload
      ref="uploadBatch"
      :auto-upload="false"
      :on-change="handleChange"
      :on-exceed="handleLimit"
      :limit="1"
      class="uploadBatch"
      style="margin-top:10px;"
      drag
      action="#"
      accept=".xlsx,.xls"
    >
      <i class="el-icon-upload" />
      <div class="el-upload__text">
        将Excel拖到此处，或<em class="upload-text">点击上传</em>
      </div>
      <div
        v-if="isTemplate"
        slot="tip"
        class="el-upload__tip"
      >
        <a
          :href="`/bdsplatform/static/excelTemplate/${src[mod]}批量导入模板.xlsx`"
          class="downloadFile"
          download
        >下载模板</a>
      </div>
      <div
        v-if="tips"
        slot="tip"
      >
        {{ tips }}
      </div>
    </el-upload>
    <!-- 只有新增车辆导入时需要显示此字段   -->
    <div
      v-if="rulebindrealtime.length > 0"
      class="rule-config"
    >
      <div class="rule-label">告警规则配置: </div>
      <xh-select
        v-model="ruleIds"
        style="width: calc(100% - 120px);"
        placeholder="请选择告警规则"
        clearable
        multiple
      >
        <el-option
          v-for="item in rulebindrealtime"
          :key="item.rule_type_id_str"
          :label="item.rule_name"
          :value="item.rule_type_id_str"
        />
      </xh-select>
    </div>
    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="handleClose"
      >
        取消
      </el-button>
      <el-button
        type="primary"
        size="small"
        @click="handleconfirm"
      >
        确认
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import DeptFormSingleSelect from "@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue";
export default {
  components: {DeptFormSingleSelect},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mod: {
      type: String,
      default: ''
    },
    rulebindrealtime: {
      type: Array,
      default: () => []
    },
    // 解析excel跳过的行
    range: {
      type: Number,
      default: 0
    },
    isTemplate: {
      type: Boolean,
      default: true
    },
    tips: {
      type: String,
      default: ''
    },
    isShowDept: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      src: {
        rdssDevice: '短报文终端管理',
        wear: '穿戴式终端管理',
        detection: '监测终端管理',
        time: '授时终端管理',
        terminal: '定位终端管理',
        driver: '人员管理',
        vehicle: '车辆信息管理',
        facility: '基础设施管理',
        simCardManager: '物联网卡管理',
        equipmentLedger: '终端设备入库查询',
        expatriate: '外派管理',
        visitor: '访客管理',
        container: '集装箱管理',
        carriage: '铁路货车车厢管理',
        ship: '货船管理',
        precision: '精密装备管理',
        user: '用户管理',
        vehicleTruck: '矿用卡车管理'
      },
      fileList: [],
      file: '',
      dialogVisible: true,
      upData: undefined,
      deptId: '',
      ruleIds: []
    };
  },
  watch: {
    visible() {
      if(this.visible) {
        this.$EventBus.$on('clearFileErr', () => {
          this.upData = undefined;
          this.$refs.uploadBatch.clearFiles();
        });
      } else {
        this.$EventBus.$off('clearFileErr');
      }
    }
  },
  methods: {
    handleconfirm () {

      if (this.isShowDept && !this.deptId) {
        this.$message.warning('请选择归属单位');
        return false;
      }
      if (!this.file) {
        this.$message.warning('请选择文件');
        return false;
      }

      if (this.upData) {
        this.$emit('getBatchData', {
          data: this.upData,
          deptId: this.deptId,
          close: this.handleClose,
          ruleIds: this.ruleIds
        });
      }
    },
    handleClose () {
      this.upData = undefined;
      this.$refs.uploadBatch.clearFiles();
      this.deptId = '';
      this.$refs.dept.clear();
      this.ruleIds = [];
      this.$emit('close');
    },
    handleLimit() {
      this.$message.warning('只能上传1个文件');
    },
    handleChange (file, fileList) {
      if (!this.beforeUp(file)) {
        this.$refs.uploadBatch.clearFiles();
        return;
      }
      this.fileList = [fileList[fileList.length - 1]]; // 只能上传一个Excel，重复上传会覆盖之前的
      this.file = file.raw;
      let reader = new FileReader();
      let _this = this;
      reader.readAsArrayBuffer(this.file);
      reader.onload = function () {
        let buffer = reader.result;
        let bytes = new Uint8Array(buffer);
        let length = bytes.byteLength;
        let binary = '';
        for (let i = 0; i < length; i++) {
          binary += String.fromCharCode(bytes[i]);
        }
        let XLSX = require('xlsx');
        // wb 是核心
        let wb = XLSX.read(binary, {
          type: 'binary'
        });
        // 拿到了xlsx中的第一个sheet 如果还有其他的sheet 还可以继续拿到其他sheet
        let outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]], { range: _this.range });
        // 拿到xlsx中的第2个sheet ,同理以此类推
        // let outdata1 = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[1]]);
        console.log('outdata', outdata);

        _this.upData = outdata;
      };
    },
    // 上传格式做限制
    beforeUp (file) {
      let testmsg = file.name.substring(file.name.lastIndexOf('.') + 1);
      if (testmsg !== 'xls' && testmsg !== 'xlsx') {
        this.$message({
          message: '上传文件格式错误!',
          type: 'warning'
        });
        return false;
      } else {
        return true;
      }
    }
  }
};
</script>

<style scoped>
.downloadFile{
  color: var(--gn-color);
}
.upload-text {
  color: var(--gn-color) !important;
}
.rule-config {
  margin-top: 12px;
  display: flex;

  .rule-label {
    height: 40px;
    display: flex;
    align-items: center;
    margin-right: 6px;
    color: #3c3c3c;
  }
}

.dept-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
