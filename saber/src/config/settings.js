import configProjectVersion from './configProjectVersion';
import {localMemory} from '@/utils/utils';

export default {
  /**
   * @description 网站标题
   */
  title: configProjectVersion.nameWithVersion,
  /**
   * @description 是否显示 tagsView
   */
  tagsView: true,
  /**
   * @description 记住密码状态下的token在Cookie中存储的天数，默认1天
   */
  tokenCookieExpires: 1,
  /**
   * @description 记住密码状态下的密码在Cookie中存储的天数，默认1天
   */
  passCookieExpires: 1,
  /**
   * @description 是否只保持一个子菜单的展开，即手风琴模式
   */
  uniqueOpened: true,
  /**
   * @description token key
   */
  TokenKey: `XH-${localMemory}-TOEKN`,
  /**
   * @description 请求超时时间，毫秒（默认600秒）
   */
  timeout: 600000,
  /**
   * @description 是否显示logo
   */
  sidebarDept: false,
  /**
   * 是否显示设置的底部信息
   */
  showFooter: true,
  /**
   * 底部文字，支持html语法
   */
  footerTxt: '广州海格星航信息科技有限公司技术支持',
  /**
   * 备案号
   */
  caseNumber: ''
};
