const options = [
    {
      value: 0,
      label: '成功' // 后台自定
    },
    {
      value: 100,
      label: '鉴权失败' // 后台自定
    },
    {
      value: 101,
      label: '接口/菜单/模块无权限' // 后台自定
    }
  ];
  
  /**
   * ApiErrorCode
   * @description 后台接口返回值中的错误码
   * @alias Enumerate_ApiErrorCode
   */
  export default {
    SUCCESS: 200,
    AUTHORIZATION_FAIL: 100,
    MODULE_NO_PERMISSION: 101,
  
    /**
     * 获取下拉框的选项
     * @return {Array.<{value: Number, label: String}>}
     */
    getOptions: function () {
      return options;
    },
    /**
     * 根据键值获取字符串
     * @param {String} value 枚举值
     * @return {String}
     */
    getLabel: function (value) {
      for (let i = 0; i < options.length; i++) {
        if (value === options[i].value) {
          return options[i].label;
        }
      }
      return '错误值';
    }
  };
  