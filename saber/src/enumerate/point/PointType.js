const options = [
  {
    value: 1,
    label: '预设点'
  }
];

/**
 * PointType
 * @alias Enumerate_PointType
 */
export default {
  BASE: 1,

  /**
   * 获取下拉框的选项
   * @return {Array.<{value: Number, label: String}>}
   */
  getOptions: function () {
    return options;
  },
  /**
   * 根据键值获取字符串
   * @param {Number} value 枚举值
   * @return {String}
   */
  getLabel: function (value) {
    for(let i = 0; i < options.length; i++){
      if(options[i].value === value){
        return options[i].label;
      }
    }
    return '错误值';
  }
};
