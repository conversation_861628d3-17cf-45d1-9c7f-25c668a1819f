const options = [
  {
    value: 0,
    label: '离线'
  },
  {
    value: 1,
    label: '在线'
  }
];

/**
 * terminalState
 * @alias Enumerate_terminalState
 * @description 需要时刻与后台保持一致，放在这里是为了某些特殊处理
 */
export default {
  OFFLINE: 0,
  ONLINE: 1,

  /**
   * 获取下拉框的选项
   * @return {Array.<{value: Number, label: String}>}
   */
  getOptions: function () {
    return options;
  },
  /**
   * 根据键值获取字符串
   * @param {String} value 枚举值
   * @return {String}
   */
  getLabel: function (value) {
    for (let i = 0; i < options.length; i++) {
      if (value === options[i].value) {
        return options[i].label;
      }
    }
    return '错误值';
  }
};
