const options = [
  {
    value: 0,
    label: '音视频'
  },
  {
    value: 1,
    label: '视频'
  },
  {
    value: 2,
    label: '双向对讲'
  },
  {
    value: 3,
    label: '监听'
  },
  {
    value: 4,
    label: '中心广播'
  },
  {
    value: 5,
    label: '透传'
  }
];

/**
 * DATA_TYPE
 * @alias Enumerate_DATA_TYPE
 */
export default {
  VIDEO_AUDIO: 0,
  VIDEO: 1,
  TALKBACK: 2,
  MONITOR: 3,
  BROADCAST: 4,
  TRANSMISSION: 5,

  /**
   * 获取下拉框的选项
   * @return {Array.<{value: Number, label: String}>}
   */
  getOptions: function () {
    return options;
  },
  /**
   * 根据键值获取字符串
   * @param {String} value 枚举值
   * @return {String}
   */
  getLabel: function (value) {
    for (let i = 0; i < options.length; i++) {
      if (value === options[i].value) {
        return options[i].label;
      }
    }
    return '错误值';
  }
};
