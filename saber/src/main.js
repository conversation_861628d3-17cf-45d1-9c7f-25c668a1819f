import Vue from 'vue';
import axios from './router/axios';
import VueAxios from 'vue-axios';
import App from './App';
import router from './router/router';
import './permission'; // 权限
import './error'; // 日志
import './cache';//页面缓存
import store from './store';
import { loadStyle } from './util/util';
import * as urls from '@/config/env';
import Element from 'element-ui';
import {
  iconfontUrl,
  iconfontVersion
} from '@/config/env';
// 初始化样式
import '@/assets/less/base.less';
// global css
import './assets/less/index.less';
import './assets/icons'; // icon
import * as Utils from '@/utils/utils.js';
// 日期处理类库
import moment from 'moment';
import i18n from './lang'; // Internationalization
import './styles/common.scss';
import basicContainer from './components/basic-container/main';
import avueUeditor from 'avue-plugin-ueditor';
import website from '@/config/website';
import crudCommon from '@/mixins/crud';
// 数据字典
import dict from './components/Dict';

import XhPlugins from './components/xh-plugins.js';

// 权限指令
import permission from './components/Permission';
/**
 *  ECharts  相  关
 */
import echarts from './plugins/echarts';
import dayjs from 'dayjs';

Vue.prototype.$dayjs = dayjs;

import download from './utils/download/downloadChinese.js';

Vue.prototype.$download = download;
import 'umy-ui/lib/theme-chalk/index.css';
// import '../public/cdn/element-ui/2.15.6/theme-chalk/index.css';
// import 'element-ui/lib/theme-chalk/index.css';
import '@/assets/element-theme/element-variables.scss';
import {
  UTableColumn,
  UTable
} from 'umy-ui';
import { nullValueStr } from '@/filters/filter';

Vue.use(UTableColumn);
Vue.use(UTable);

// import { Tree } from '@femessage/element-ui'; // TODO 该虚拟加载会导致搜索出现问题
// Vue.component('v-tree', Tree);

import VueEasyTree from '@wchbrad/vue-easy-tree';

Vue.component('vue-easy-tree', VueEasyTree);

import VueDOMPurifyHTML from 'vue-dompurify-html';

Vue.use(VueDOMPurifyHTML);
import 'github-markdown-css/github-markdown-light.css';

// 注册全局crud驱动
window.$crudCommon = crudCommon;
// 加载Vue拓展
Vue.use(router);
Vue.use(VueAxios, axios);
Vue.use(Element, {
  i18n: (key, value) => i18n.t(key, value)
});
Vue.use(window.AVUE, {
  size: 'small',
  tableSize: 'small',
  calcHeight: 65,
  i18n: (key, value) => i18n.t(key, value)
});
Vue.use(dict);
Vue.use(XhPlugins);
Vue.use(permission);

// 注册全局容器
Vue.component('basicContainer', basicContainer);
Vue.component('avueUeditor', avueUeditor);
// 加载相关url地址
Object.keys(urls).forEach(key => {
  Vue.prototype[key] = urls[key];
});
// 加载NutFlow
// Vue.use(window.WfDesignBase);
// 注册filter
Vue.filter('nullValueStr', nullValueStr);
// 加载website
Vue.prototype.website = website;
// 动态加载阿里云字体库
iconfontVersion.forEach(ele => {
  loadStyle(iconfontUrl.replace('$key', ele));
});
Vue.prototype.$moment = moment;
Vue.prototype.$utils = Utils;
Vue.prototype.$serverURL = '';
// eventbus 事件总线
Vue.prototype.$EventBus = new Vue();
// 注册图表
Vue.prototype.$echarts = echarts;
Vue.config.productionTip = false;
// eslint-disable-next-line no-undef
// let player
// NodePlayer.load(()=>{
//   player = new NodePlayer();
//   player.useWorker()
  new Vue({
    router,
    store,
    i18n,
    render: h => h(App)
  }).$mount('#app');
// });


Vue.prototype.validInput = function (value) {
  value = value.replace(/[`~!@#$%^&*()_\-+=<>?:"{}|,./;'\\[\]~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘’，。、]/g, '').replace(/\s/g, '');
  return value;
};


Vue.prototype.handlePosition = (str, num = 5) => {
  // 经纬度无需前端处理
  if (!str) return str;
  // return `${str}`.replace(/(\d+)\.(\d+)/, (_, $1, $2) => `${$1}.${$2.substr(0,num)}`)
  if (typeof +str === 'number') {
    return Number(str).toFixed(8);
  }
  return str;
};
