<template>
  <div class="alarm-container">
    <div class="alarm-head">
      <!-- <div>
        <i class="el-icon-jingqing" />
      </div> -->
      <span>告警提示</span>
      <div
        class="head-close"
        @click="$emit('closeDialog')"
      >
        <i class="el-icon-close" />
      </div>
    </div>
    <div
      v-for="(item, index) in alarmList"
      :key="index"
    >
      <div
        v-if="index === activeIndex"
        class="alarm-content"
      >
        <p><span>告警等级：</span><span class="label-red">{{ getEnumDictLabel('alarmLevel', item.alarmLevel) }}</span></p>
        <p>
          <span>车牌号码：</span><span>{{ item.licencePlate }}</span>
          <span style="padding-left: 20px;">车牌颜色：</span><span>{{ getEnumDictLabel('licenceColor', item.licenceColor) }}</span>
        </p>
        <p><span>告警类型：</span><span class="label-red">{{ getEnumDictLabel('alarmType', item.alarmType) }}</span></p>
        <p><span>告警时间：</span><span>{{ item.alarmTime }}</span></p>
        <!-- <p><span>告警地址：</span><span>{{ item.address }}</span></p> -->
      </div>
    </div>
    <div class="btn-container">
      <button
        class="deal-btn"
        @click="toDeal"
      >
        处理
      </button>
      <!-- <button
        class="deal-btn"
      >
        快捷处理
      </button> -->
    </div>
    <div class="pagination-container">
      <el-pagination
        layout="total, prev, pager, next"
        :total="alarmList.length"
        :pager-count="3"
        :page-size="1"
        :current-page.sync="currentPage"
        @current-change="currentChange"
      />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    alarmList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    dict: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return{
      activeIndex: 0,
      currentPage: 1
    };
  },
  watch: {
    alarmList: {
      handler() {
        this.currentPage = 1;
        this.activeIndex = 0;
      },
      deep: true
    }
  },
  methods: {
    // 处理
    toDeal() {
      const data = JSON.parse(JSON.stringify(this.alarmList[this.activeIndex]));
      data.id = data.alarmId;
      data.alarmTime = this.$moment(data.alarmTime).unix();
      data.alarmEndTime = this.$moment(data.alarmEndTime).unix();
      const routerQuery = JSON.stringify(data);
      if (this.$route.path === '/monitoring/realTimeMonitoring/index') {
        this.$EventBus.$emit('alarmDealDialog', routerQuery);
      } else {
        localStorage.setItem('ROUTER_QUERY', routerQuery);
        this.$router.push({ path: '/monitoring/realTimeMonitoring/index', query: { isRouter: this.$route.fullPath }});
      }
    },
    // 切换页码
    currentChange(val) {
      this.activeIndex = val - 1;
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
  }
};
</script>

<style lang="less" scoped>
.alarm-container{
  width: 360px;
  position: fixed;
  right: 16px;
  bottom: 16px;
  z-index: 1200;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.3);
}
.alarm-head{
  background-color: rgb(64, 150, 209);
  height: 50px;
  padding-left: 20px;
  color: #fff;
  font-size: 20px;
  display: flex;
  align-items: center;
  position: relative;
}
.head-close{
  position: absolute;
  right: 12px;
  color: #fff;
  font-size: 18px;
  background: transparent;
  border: none;
  outline: none;
  cursor: pointer;
  padding: 0;
}
.alarm-content{
  width: 100%;
  padding: 8px 20px;
  font-size: 14px;
  color: #333;
}
.alarm-content p{
  margin: 7px 0;
  line-height: 22px;
  width: 100%;
  white-space: pre-wrap;
  word-wrap: break-word;
  text-indent: -5em;
  padding-left: 5em;
  position: relative;
}
.btn-container{
  display: flex;
  justify-content: center;
}
.pagination-container{
  height: 30px;
  display: flex;
  justify-content: center;
  margin: 10px 0;
}
.label-red{
  color: red;
}
.deal-btn{
  height: 32px;
  border-width: 0px;
  background-color: rgb(64, 150, 209);
  padding: 0px 10px;
  width: 96px;
  text-align: center;
  color: #fff;
  font-size: 14px;
  letter-spacing: 2px;
  text-indent: 1px;
  cursor: pointer;
  margin: 0 5px;
}
</style>
