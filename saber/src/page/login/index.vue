<template>
  <div ref="login" class="login-container" style="background-image: url('/bdsplatform/img/bg/bg1.jpg')" @keyup.enter.native="handleLogin">
<!--    <div class="login-bg" style="background-image: url('/bdsplatform/img/bg/login-bg.jpg')">-->
<!--      <div class="login-notice-container">-->
<!--        <h3 class="notice-title">-最新消息通知-</h3>-->
<!--        <div class="notice-list">-->
<!--          <div class="notice-item" v-for="item in noticeList">-->
<!--            <span class="notice-date">{{ item.create_time }}</span>-->
<!--            <el-tooltip effect="light" :content="item.content" placement="top-start">-->
<!--              <span class="notice-content">{{ item.content }}-->

<!--              </span>-->
<!--            </el-tooltip>-->


<!--          </div>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->
    <div class="login-weaper animated bounceInDown">
      <div class="login-logo"></div>
      <p class="title">-{{ $t('login.info') }}-</p>
      <p class="title">{{ version }}</p>
      <div class="login-main">
        <userLogin v-if="activeName === 'user'" @editSystemVersion="editSystemVersion" />
      </div>
      <!-- <div style="font-size: 15px">
        <span>----------------------------------------------</span>
        <br>
        <span>管理租户编号：000000</span>
        <br>
        <span>超级管理员账号: admin / admin</span>
        <br>
        <span>人事账号: hr / hr</span>
        <br>
        <span>经理账号: manager / manager</span>
        <br>
        <span>老板账号: boss / boss</span>
        <br>
        <span>----------------------------------------------</span>
        <br>
        <span>普通租户编号：详见租户管理模块</span>
        <br>
        <span>租户管理员账号: admin / admin</span>
        <br>
        <span>----------------------------------------------</span>
      </div> -->
    </div>
  </div>
</template>
<script>
import userLogin from './userlogin'
import { mapGetters } from 'vuex'
import { validatenull } from '@/util/validate'
import { getQueryString, getTopUrl } from '@/util/util'
import configProjectVersion from '@/config/configProjectVersion';

export default {
  name: 'login',
  components: {
    userLogin
  },
  props: [],
  data() {
    return {
      time: '',
      activeName: 'user',
      socialForm: {
        tenantId: '000000',
        source: '',
        code: '',
        state: ''
      },
      noticeList: [
        {
          'create_time': '2024/06/29',
          'content': `北斗位置服务平台${configProjectVersion.version}版本发布!`,
        }
      ],
      version: configProjectVersion.version
    }
  },
  computed: {
    ...mapGetters([
      'website',
      'tagWel'
    ])
  },
  watch: {
    $route() {
      this.handleLogin()
    }
  },
  created() {
    this.handleLogin()
  },
  mounted() {
  },
  methods: {
    // 修改系统版本号
    editSystemVersion(val) {
      this.version = val;
      this.noticeList[this.noticeList.length - 1].content = `北斗位置服务平台${val}版本发布!`;
    },
    handleLogin() {
      const topUrl = getTopUrl()
      const redirectUrl = '/oauth/redirect/'
      const ssoCode = '?code='
      this.socialForm.source = getQueryString('source')
      this.socialForm.code = getQueryString('code')
      this.socialForm.state = getQueryString('state')
      if (validatenull(this.socialForm.source) && topUrl.includes(redirectUrl)) {
        let source = topUrl.split('?')[0]
        source = source.split(redirectUrl)[1]
        this.socialForm.source = source
      }
      if (topUrl.includes(redirectUrl) && !validatenull(this.socialForm.source) && !validatenull(this.socialForm.code) && !validatenull(this.socialForm.state)) {
        const loading = this.$loading({
          lock: true,
          text: '第三方系统登录中,请稍后。。。',
          spinner: 'el-icon-loading'
        })
        this.$store.dispatch('LoginBySocial', this.socialForm).then(() => {
          window.location.href = topUrl.split(redirectUrl)[0]
          this.$router.push({ path: this.tagWel.value, query: { isRouter: this.$route.fullPath }})
          loading.close()
        }).catch(() => {
          loading.close()
        })
      }
      else if (!topUrl.includes(redirectUrl) && !validatenull(this.socialForm.code) && !validatenull(this.socialForm.state)) {
        const loading = this.$loading({
          lock: true,
          text: '单点系统登录中,请稍后。。。',
          spinner: 'el-icon-loading'
        })
        this.$store.dispatch('LoginBySso', this.socialForm).then(() => {
          window.location.href = topUrl.split(ssoCode)[0]
          this.$router.push({ path: this.tagWel.value, query: { isRouter: this.$route.fullPath }})
          loading.close()
        }).catch(() => {
          loading.close()
        })
      }
    }
  }
}
</script>

<style lang="scss">
@import "@/styles/login.scss";
</style>
