/**
 * 全站http配置
 *
 * axios参数说明
 * isSerialize是否开启form表单提交
 * isToken是否需要token
 */
import axios from 'axios';
import store from '@/store/';
import router from '@/router/router';
import {serialize} from '@/util/util';
import {getToken} from '@/util/auth';
import {Message} from 'element-ui';
import website from '@/config/website';
import {Base64} from 'js-base64';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import Config from '@/config/settings';

//默认超时时间
axios.defaults.timeout = Config.timeout;
//返回其他状态码
axios.defaults.validateStatus = function (status) {
  return status >= 200 && status <= 500;
};
//跨域请求，允许保存cookie
axios.defaults.withCredentials = true;
// NProgress 配置
NProgress.configure({
  showSpinner: false
});
//http request拦截
axios.interceptors.request.use(config => {
  // console.log("this is axios request interceptor");
  //开启 progress bar
  NProgress.start();
  //headers判断是否需要
  const authorization = config.authorization === false;
  //指定请求前缀
  config.baseURL = website.apiPrefix;
  if (!authorization) {
    config.headers['Authorization'] = `Basic ${Base64.encode(`${website.clientId}:${website.clientSecret}`)}`;
  }
  /**
   * 逻辑修改：适配 国能平台
   * 国能项目中，需要将位置平台集成到国能平台中。在国能平台中，会通过iframe的形式将位置平台的页面嵌入进去，解决跨域取token的问题。
   * 为了方便使用，位置平台也要保留自己的登录验证方式，所以这里需要做兼容处理。
   * 如果localStorage中包含国能平台的token，则表示当前是国能平台在访问页面，则添加国能平台的token配置；否则就是自己的位置平台，就添加blade的token设置
   */
  //让每个请求携带token
  const meta = (config.meta || {});
  const isToken = meta.isToken === false;
  //是否是国能平台
  let isCE = window.localStorage.hasOwnProperty("cedi__Access-Token");
  // console.log("isCE is "+isCE);
  // 如果是集成页面
  if (sessionStorage.getItem('saber-integration')) {
    const { content } = JSON.parse(sessionStorage.getItem('saber-token'));
    config.headers[website.tokenHeader] = 'bearer ' + content;
  } else if (getToken() && !isToken && !isCE) {
    //如果是位置平台
    config.headers[website.tokenHeader] = 'bearer ' + getToken()
  } else if(isCE){
    //如果是国能平台
    let currentToken =window.localStorage.getItem("cedi__Access-Token")
    let currentTokenValue= JSON.parse(currentToken).value;
    config.headers[website.ceTokenHeader] = 'bearer ' + currentTokenValue;
  }

  //headers中配置text请求
  if (config.text === true) {
    config.headers["Content-Type"] = "text/plain";
  }
  //headers中配置serialize为true开启序列化
  if (config.method === 'post' && meta.isSerialize === true) {
    config.data = serialize(config.data);
  }
  return config
}, error => {
  return Promise.reject(error)
});
//http response 拦截
axios.interceptors.response.use(res => {
  //关闭 progress bar
  NProgress.done();
  //获取状态码
  const status = res.data.code || res.status;
  const statusWhiteList = website.statusWhiteList || [];
  const message = res.data.msg || res.data.error_description || '未知错误';
  const errorType = res.data.error || '';
  //如果在白名单里则自行catch逻辑处理
  if (statusWhiteList.includes(status)) return Promise.reject(res);
  /**
   * 情况说明：
   * 国能项目中，需要将位置平台集成到国能平台中。在国能平台中，会通过iframe的形式将位置平台的页面嵌入进去，解决跨域取token的问题。
   * 为了方便使用，位置平台也要保留自己的登录验证方式，所以这里需要做兼容处理。
   */
  //如果是401则跳转到登录页面
  if (status === 401){
    console.log("here is logcheck 2");
    let crrentToken = '';
    let crrentTokenValue = '';
    if (window.localStorage.hasOwnProperty("cedi__Access-Token") || window.localStorage.hasOwnProperty("cedi__Login_Userinfo")) {
      console.log("here is logcheck 3 ");
      //如果是底座系统,token失效后，刷新页面即可
      parent.location.reload();
    }else{
      console.log("here is logcheck 4");
      //如果是位置平台，则跳转到自己的登录页面
      store.dispatch('FedLogOut').then(() => router.push({path: '/login'}));
    }
  }
  // A登录了admin账号, B也要登录admin账号, 但B输错了5次密码导致账号被禁用, A这边刷新token失败退回登录页
  if (status === 400 && errorType === 'access_denied') {
    console.log("here is logcheck 5");
    store.dispatch('FedLogOut').then(() => router.push({path: '/login'}));
  }
  // 如果请求为非200否者默认统一处理
  if (status !== 200 && status !== 401) {
    Message({
      message: message,
      type: 'error',
      dangerouslyUseHTMLString: true
    });
    return Promise.reject(new Error(message))
  }
  return res;
}, error => {
  NProgress.done();
  return Promise.reject(new Error(error));
});

export default axios;
