import request from '@/router/axios'

const vehicle = {
  state: {
    tree: []
  },
  actions: {
    GetVehicleTree({ commit }) {
      return new Promise((resolve, reject) => {
        request.get('/baseinfo-wrapper/baseinfo/target/depttagtree').then(res => {
          const { code, data } = res.data;
          if (code === 200) {
            commit('SET_TREE', data || [])
            // resolve(data || [])
          }
        }).catch((error) => {
          commit('SET_TREE', [])
          reject(error)
        })
      })
    }
  },
  mutations: {
    SET_TREE(state, data) {
      state.tree = Object.freeze(data)
    }
  }
}

export default vehicle
