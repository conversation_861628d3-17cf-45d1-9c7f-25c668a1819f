.login-container {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-position: center;
}
.login-bg {
  height: 100%;
  flex: 1;
  background-color: #000;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  .login-notice-container {
    position: absolute;
    top: 220px;
    left: 40px;
    background-color: rgba(0,0,0,0.6);
    width: 540px;
    min-height: 130px;
    max-height: 470px;
    color: #fff;
    display: flex;
    flex-direction: column;
    .notice-title {
      letter-spacing:4px;
      font-size: 20px;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 70px;
    }
    .notice-list {
      flex: 1;
    }
    .notice-item {
      height: 34px;
      display: flex;
      font-size: 16px;
      box-sizing: border-box;
      padding: 0 30px;
    }
    .notice-date {
      margin-right: 16px;
    }
    .notice-content {
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
.login-weaper {
  width: 640px;
  height: 600px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  padding: 0 100px 40px;
  min-height: 600px;
  overflow: auto;
  border-radius: 8px;
  //.el-input-group__append {
  //  border: none;
  //}
}

.login-main {
  width: 100%;
  box-sizing: border-box;
}

.login-main > h4 {
  margin-bottom: 20px;
}
.login-main > p {
  color: #76838f;
}
.title {
  font-weight: 500;
  font-size: 26px;
  text-align: center;
  margin-bottom: 20px;
  color: #494A49;
  letter-spacing: 1px;
  white-space: nowrap;
}

.login-menu {
  margin-top: 40px;
  width: 100%;
  text-align: center;

  a {
    color: #999;
    font-size: 12px;
    margin: 0 8px;
  }
}

.login-submit {
  width: 100%;
  height: 54px;
  border: 1px solid #4479FE;
  border-radius: 6px;
  background: none;
  font-size: 18px;
  letter-spacing: 2px;
  font-weight: 400;
  color: #4479FE;
  cursor: pointer;
  //margin-top: 30px;
  font-family: "neo";
  transition: 0.25s;
}

.login-form {
  margin: 10px 0;

  i {
    color: #333;
  }

  .el-form-item__content {
    width: 100%;
  }

  .el-form-item {
    margin-bottom: 28px;
  }

  .el-input {
    height:54px;
    line-height: 54px;

    input {
      height: 100%;
      text-indent: 5px;
      background: #F6F7FB;
      border: none;
      border-radius: 6px;
      color: #333;
      padding-right: 20px;
      //border-bottom: 1px solid rgb(235, 237, 242);
    }

    .el-input__prefix {
      i {
        font-size: 18px !important;
      }
    }
  }
}

.login-code {
  height: 54px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
  border: 1px solid #E7E7E7;
  border-radius: 6px;
  box-sizing: border-box;
  overflow: hidden;

  .login-code-img {
    cursor:pointer!important;
    object-fit: fill;
  }
}
.password-icon {
  width: 22px;
  height: 22px;
  position: relative;
  right: 12px;
  top: 6px;
  cursor: pointer;
}
