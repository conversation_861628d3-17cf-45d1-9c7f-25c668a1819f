/**
 * 获取标签
 * @param {String} className 数据的类型名如“Vehicle”
 * @param {String} value 字段名称
 * @return {String}
 */
function getLabel(className, value) {
  let test = {
    'User': {
      'uniName': '用户', // 本类型名称
      'indexStart': '序号',
      'id': '用户ID',
      'userName': '用户名',
      'email': '邮箱地址',
      'avatar': '头像地址',
      'phone': '手机号',
      'nickName': '昵称',
      'sex': '性别',
      'roleIds': '角色ID数组',
      'password': '密码密文',
      'oldPass': '旧密码',
      'newPass': '新密码',
      'enabled': '账户生效',
      'updateTime': '更新时间',
      'createTime': '生成时间',
      'roles': '角色',
      'dept': '车组',
      'job': '岗位'
    },
    'Role': {
      'uniName': '角色', // 本类型名称
      'roleName': '角色名称',
      'roleAlias': '角色别名',
      'parentId': '上级角色',
      'sort': '角色排序'
    },
    'Dept': {
      'uniName': '车组', // 本类型名称
      'id': 'ID',
      'indexStart': '序号',
      'name': '车组名称',
      'type': '车组类型',
      'region': '所属地区',
      'pid': '上级车组ID',
      'pname': '上级车组ID',
      'adminName': '管理员名称',
      'adminPhone': '管理员电话',
      'enabled': '状态',
      'label': '标签',
      'updateTime': '更新时间',
      'createTime': '创建时间'
    },
    'Job': {
      'uniName': '岗位', // 本类型名称
      'id': '岗位id',
      'name': '岗位名称',
      'enabled': '状态',
      'sort': '排序',
      'dept': '部门',
      'updateTime': '更新时间',
      'createTime': '创建时间'
    },
    'Dict': {
      'uniName': '系统字典', // 本类型名称
      'code': '字典编号',
      'dictValue': '字典名称',
      'sort': '字典排序',
      'isSealed': '封存',
      'remark': '字典备注',
      'parentId': '上级字典',
      'parentName': '上级字典',
      'dictKey': '字典键值'
    },
    'Dictbiz': {
      'uniName': '业务字典', // 本类型名称
      'code': '字典编号',
      'dictValue': '字典名称',
      'sort': '字典排序',
      'isSealed': '封存',
      'remark': '字典备注',
      'parentId': '上级字典',
      'parentName': '上级字典',
      'dictKey': '字典键值'
    },
    'DictDetail': {
      'uniName': '字典取值', // 本类型名称
      'id': '字典ID',
      'name': '字典名称',
      'remark': '字典描述',
      'updateTime': '更新时间',
      'createTime': '创建时间'
    },
    'Vehicle': {
      'number': '车辆编号',
      'category': '车辆类型',
      'targetType': '监控对象',
      'deptId': '所属机构',
      'deptName': '所属机构',
      'createTime': '创建时间',
      'licenceColor': '车牌颜色',
      'licencePlate': '车牌号码',
      'terminalCategories': '绑定终端类型',
      'uniqueId': '绑定序列号',
      'vin': '车架号',
      'maxPower': '最大马力',
      'manufacturer': '制造商',
      'ratedLoad': '额定载重',
      'model': '车辆型号',
      'deviceNum': '绑定赋码编号'
    },
    'Carriage': {
      'number': '车厢编号',
      'deptId': '所属机构',
      'deptName': '所属机构',
      'createTime': '创建时间',
      'licenceColor': '车牌颜色',
      'terminalCategories': '绑定终端类型',
      'uniqueId': '绑定序列号',
      'model': '车辆型号',
      'modelName': '车辆型号',
      'size': "尺寸",
      'maxGross': '总重',
      'tare': '自重/皮重',
      'net': '载重/净重',
      'cuCap': '最大装货容积',
      'length': '长度',
      'height': '高度',
      'deviceNum': '绑定赋码编号'
    },
    'PrecisionEquipment': {
      'number': '装备编号',
      'deptId': '所属机构',
      'deptName': '所属机构',
      'createTime': '创建时间',
      'name': '装备名称',
      'model': '装备型号',
      'manufacturer': '制造商',
      'terminalCategories': '绑定终端类型',
      'uniqueId': '绑定序列号',
      'deviceNum': '绑定赋码编号'
    },
    'ShipManagement': {
      'number': '货船编号',
      'deptId': '所属机构',
      'deptName': '所属机构',
      'category': '货船类型',
      'name': '中文船名',
      'nameEn': '英文船名',
      'mmsi': '海上移动通信业务标识MMSI',
      'imoNumber': '国际海事组织编号IMO',
      'callSign': '呼号',
      'maxGross': '总重',
      'net': '载重/净重',
      'displcement': '排水量',
      'length': '船长',
      'breadth': '船宽',
      'depth': '船深',
      'draught': '吃水',
      'cruiseSpeed': '航速',
      'createTime': '创建时间',
      'terminalCategories': '绑定终端类型',
      'uniqueId': '绑定序列号',
      'deviceNum': '绑定赋码编号'
    },
    'Terminal': {
      'uniName': '终端',
      'terminalId': '序列号',
      'phone': '终端手机号',
      'updateTime': '通信时间',
      'createTime': '创建时间'
    },
    'VehicleModel': {
      'uniName': '车辆类型', // 本类型名称
      'id': '车辆类型ID',
      'typeValue': '类型名称',
      'typeCode': '类型编码',
      'pid': '父类型',
      'pName': '父类型',
      'vehicleCount': '车辆数量'
    },
    'VehicleUseType': {
      'uniName': '行业类型', // 本类型名称
      'id': '行业类型ID',
      'typeValue': '类型名称',
      'typeCode': '类型编码',
      'pid': '父类型',
      'pName': '父类型',
      'vehicleCount': '车辆数量'
    },
    'RepairRecord': {
      'uniName': '车辆报停报修', // 本类型名称
      'repairRecordId': '报修ID',
      'id': '报修ID',
      'vehicleId': '车辆',
      'licencePlate': '车辆号码',
      'vehicleModel': '车辆类型',
      'deptId': '车组',
      'deptName': '车组名称',
      'repairRecordType': '类别',
      'repairRecordState': '状态',
      'maintainerId': '维护人员ID',
      'maintainerName': '维护人员姓名',
      'maintainerPhone': '维护人员电话',
      'startTime': '开始时间',
      'endTime': '终止时间',
      'authorizer': '核准人',
      'updateTime': '更新时间',
      'reason': '原因'
    },
    'Driver': {
      'uniName': '人员信息', // 本类型名称
      'id': '编号',
      'personId': '人员编号',
      'name': '人员姓名',
      'wkno': '工号',
      'post': '岗位类型',
      'deptId': '所属机构',
      'deptName': '所属机构',
      'phone': '手机号码',
      'busyArea': '业务领域',
      'birthdate': '生日',
      'age': '年龄',
      'sex': '性别',
      'education': '学历',
      'nation': '民族',
      'region': '籍贯',
      'address': '地址',
      'driveLicense': '驾驶证号',
      // 'licenceOrg': '发证机构',
      'driveArchive': '档案编号',
      'vehicleType': '准驾车型',
      'yearVerifyDate': '年审日期',
      'licenceGetDate': '初次领证日期',
      'industry': '从业类型',
      'licencePubDate': '发证日期',
      'licenceStartDate': '驾驶证有效期开始日期',
      'licenceExpire': '有效期限',
      'certificate': '资格证号',
      'certificateType': '类别',
      'certificateAuthority': '发证机构',
      'certificateGetDate': '从业资格证初次领证日期',
      'certificateStartDate': '从业资格证有效起始时间',
      'certificateExpire': '有效期限',
      'superviseOrg': '监督机构',
      'supervisePhone': '监督电话',
      'serviceStar': '服务星级',
      'taxiCompany': '出租公司',
      'uniqueLabel': '防伪密标',
      'bgTitle': '背景标题',
      'avatarUrl': '个人照片',
      'licencePhoto': '驾驶证',
      'certificateImg': '从业资格证',
      'bindTerminal': '绑定终端',
      'terminalType': '终端类型',
      'terminalId': '序列号',
      'master': '是否为主设备',
      'model': '终端型号',
      'uniqueId': '序列号',
      'terminalCategories': '绑定终端类型',
      'deviceType': '终端类别',
      'category': '终端类型',
      'deviceNum': '赋码编号'
    },
    'Speeding': {
      'uniName': '超速', // 本类型名称
      'id': '规则ID', // PK
      'isOpen': '是否开启',
      'ruleName': '规则名称',
      'deptId': '车组ID',
      'speedType': '限速类型',
      'highSpeed': '限高速',
      'lowSpeed': '限低速',
      'alarmTime': '告警持续时长',
      'delayReport': '超速延迟上报',
      'alarmLevel': '告警等级',
      'matchDeptId': '匹配的车组id',
      'isPhotograph': '是否拍照',
      'isVideo': '是否拍照',
      'isRecording': '是否录音'
    },
    'SegmentedSpeedLimit': {
      'uniName': '分段限速', // 本类型名称
      'key': '规则ID', // PK
      'isOpen': '是否开启',
      'ruleName': '规则名称',
      'deptNames': '车组名称',
      'licencePlates': '车辆名称',
      'bindDeptIds': '绑定车组',
      'vehicleIds': '绑定车辆',
      'creatorId': '创建账号ID',
      'deptId': '创建账号车组ID',
      'effectiveStartTime': '开始日期',
      'effectiveEndTime': '结束日期',
      'dayStartTime': '开始时间',
      'dayEndTime': '结束时间',
      'limitRate': '限速比例'
    },
    'FatigueDriving': {
      'uniName': '疲劳驾驶', // 本类型名称
      'id': '规则ID', // PK
      'isOpen': '是否开启',
      'driveTime': '驾驶时间',
      'restTime': '休息时间',
      'isSevere': '是否严重疲劳',
      'ruleName': '规则名称',
      'bindDeptIds': '绑定车组',
      'vehicleIds': '绑定车辆',
      'deptNames': '车组名称',
      'licencePlates': '车辆名称',
      'creatorId': '创建账号ID',
      'deptId': '创建账号车组ID'
    },
    'DrivingNight': {
      'uniName': '夜间行驶', // 本类型名称
      'id': '规则ID', // PK
      'isOpen': '是否开启',
      'ruleName': '规则名称',
      'startTime': '开始时间',
      'endTime': '结束时间',
      'startDate': '开始日期',
      'endDate': '结束日期',
      'startDateString': '开始日期',
      'endDateString': '结束日期',
      'deptNames': '车组名称',
      'licencePlates': '车牌号码',
      'bindDeptIds': '绑定车组',
      'vehicleIds': '绑定车辆',
      'creatorId': '创建账号ID',
      'deptId': '创建账号车组ID'
    },
    'RouteAlarm': {
      'uniName': '线路告警', // 本类型名称
      'id': '规则ID', // PK
      'isOpen': '是否开启',
      'ruleName': '规则名称',
      'deptNames': '车组名称',
      'licencePlates': '车牌名称',
      'routeId': '线路',
      'routeName': '线路名称',
      'route': '线路',
      'alias': '别名',
      'centerSetting': '上报中心设置',
      'inRoute': '进线路设置',
      'outRoute': '出线路设置',
      'effectiveStartTime': '开始时间',
      'effectiveEndTime': '结束时间',
      'effectiveStartTimeString': '开始时间',
      'effectiveEndTimeString': '结束时间',
      'limitSpeed': '限速',
      'delayReport': '延迟上报(秒)',
      'bindDeptIds': '绑定车组',
      'vehicleIds': '绑定车辆',
      'creatorId': '创建账号ID',
      'deptId': '创建账号车组ID'
    },
    'RegionAlarm': {
      'uniName': '区域告警', // 本类型名称
      'id': '规则ID', // PK
      'isOpen': '是否开启',
      'ruleName': '规则名称',
      'regionId': '区域名称',
      'alarmType': '告警类型',
      'aliasName': '别名',
      'limitSpeed': '限速(km/h)',
      'fenceType': '围栏类型',
      'reminderInterval': '提醒间隔(秒)',
      'reminderDriver': '提醒司机',
      'reminderContent': '提醒内容',
      'reminderWay': '提醒方式',
      'effectiveStartTime': '开始时间',
      'effectiveEndTime': '结束时间',
      'effectiveStartTimeString': '开始时间',
      'effectiveEndTimeString': '结束时间',
      'remark': '备注',
      'deptNames': '车组名称',
      'licencePlates': '车牌名称',
      'bindDeptIds': '绑定车组',
      'vehicleIds': '绑定车辆',
      'creatorId': '创建账号ID',
      'deptId': '创建账号车组ID',
      'regionName': '区域名称',
      'regionType': '区域类型',
      'stayTime': '停留时间(分钟)'
    },
    'AlarmSetting': {
      'uniName': '告警设置', // 本类型名称
      'key': '告警参数设置key', // PK
      'isOpen': '是否开启',
      'bindUser': '绑定用户',
      'alarmType': '告警类型',
      'regionName': '区域名称',
      'reminderWay': '提醒方式',
      'alarmContent': '告警播放内容',
      'alarmLevel': '风险等级',
      'autoDealEnable': '自动处理',
      'alarmSoundEnable': '警报声音'
    },
    'NormalAlarmSetting': {
      'uniName': '通用告警设置', // 本类型名称
      'key': '告警参数设置key', // PK
      'isOpen': '是否开启',
      'alarmType': '告警类型',
      'reminderWay': '提醒方式',
      'alarmContent': '告警播放内容',
      'alarmLevel': '告警等级',
      'autoDeal': '自动处理',
      'alarmSound': '警报声音',
      'userId': '用户ID',
      'userName': '用户名',
      'deptName': '所属机构'
    },
    'AutoAlarmSetting': {
      'uniName': '自动告警设置', // 本类型名称
      'key': '告警参数设置key', // PK
      'isOpen': '是否开启',
      'alarmType': '告警类型',
      'reminderWay': '提醒方式',
      'alarmContent': '告警播放内容',
      'alarmLevel': '风险等级',
      'autoDeal': '自动处理',
      'alarmSound': '警报声音',
      'userId': '用户ID'
    },
    'Region': {
      'uniName': '区域', // 本类型名称
      'id': '区域ID', // PK
      'regionName': '区域名称',
      'regionAlias': '区域别名',
      'regionType': '区域类型',
      'attribute': '区域属性',
      'remark': '备注',
      'polygonPoints': '区域点',
      'circular': '区域点',
      'cityId': '行政区域'
    },
    'Route': {
      'uniName': '线路', // 本类型名称
      'id': '线路ID', // PK
      'routeName': '线路名称',
      'routeAlias': '线路别名',
      'routeType': '线路类型',
      'routeWidth': '线路宽度',
      'remark': '备注',
      'points': '线路点',
      'drawType': '绘制方式'
    },
    'Point': {
      'uniName': '预设点', // 本类型名称
      'id': '预设点ID', // PK
      'pointName': '预设点名称',
      'pointType': '预设点类型',
      'mapLevel': '地图等级',
      'remark': '备注'
    },
    'BasicEquipments': {
      'uniName': '基本配置', // 本类型名称
      'key': '告警参数设置key', // PK
      'isOpen': '是否开启',
      'alarmType': '告警类型',
      'regionName': '区域名称',
      'reminderWay': '提醒方式',
      'alarmContent': '告警播放内容',
      'alarmLevel': '风险等级',
      'autoDealEnable': '自动处理',
      'alarmSoundEnable': '警报声音'
    },
    'ParameterConfiguration': {
      'uniName': '参数配置', // 本类型名称
      'key': '告警参数设置key', // PK
      'isOpen': '是否开启',
      'alarmType': '告警类型',
      'regionName': '区域名称',
      'reminderWay': '提醒方式',
      'alarmContent': '告警播放内容',
      'alarmLevel': '风险等级',
      'autoDealEnable': '自动处理',
      'alarmSoundEnable': '警报声音'
    },
    'MediaEvent': {
      'uniName': '多媒体事件', // 本类型名称
      'key': '多媒体事件设置key', // PK
      'licencePlate': '车牌号码',
      'cmdType': '事件类型',
      'driverName': '驾驶员姓名',
      'mediaType': '文件格式',
      'msgType': '信息类型',
      'createTime': '媒体时间',
      'channel': '通道号',
      'reportTime': '上传时间',
      'size': '资源大小',
      'fileUrl': '视频/图片',
      'vehicleModel': '车辆类型',
      'licenceColor': '车牌颜色',
      'eventType': '事件类型'
    },
    'Command': {
      'uniName': '命令列表', // 本类型名称
      'key': '命令列表设置key', // PK
      'licencePlate': '车牌号码',
      'cmd': '命令',
      'driverName': '驾驶员姓名',
      'reportTime': '上传时间',
      'result': '执行结果',
      'vehicleModel': '车辆类型'
    },
    'Info': {
      'uniName': '监控信息', // 本类型名称
      'key': '监控信息设置key', // PK
      'licencePlate': '车牌号码',
      'cmd': '命令',
      'installTime': '定位时间',
      'state': '终端状态',
      'speed': '速度(km/h)',
      'position': '地址',
      'department': '所属车组',
      'simId': 'SIM卡号',
      'longitude': '经度',
      'latitude': '纬度',
      'address': '地址',
      'mileage': '里程km',
      'driverName': '驾驶员姓名',
      'parkingTime': '停车时长',
      'load': '载重',
      'tonnage': '吨位',
      'depart': '出发地',
      'destination': '目的地',
      'goodsInfo1': '前一载货信息',
      'goodsInfo2': '前二载货信息',
      'goodsInfo3': '前三载货信息',
      'phone': '联系电话',
      'vehicleModel': '车辆类型',
      'plateColor': '车牌颜色',
      'licenceColor': '车牌颜色',
      'stateAcc': '终端状态',
      'terminalModel': '终端型号',
      'parkingStr': '行驶状态',
      'terminalId': '序列号',
      'vehicleUseType': '行业类型',
      'time': '定位时间',
      'deptName': '使用单位',
      'targetName': '监控对象',
      'targetType': '目标类型',
      'targetId': '目标ID',
      'deviceType': '终端类别',
      'treeCategory': '终端类型',
      'uniqueId': '序列号',
      'status': '终端状态',
      'locTime': '定位时间',
      'deviceCategory': '终端类型',
      'charge': '电量',
      'gnssNum': '定位卫星',
      'wireless': '通信信号',
      'realSpeed': '行驶速度',
      'oilMass': '油量',
      'ioStatus': '休眠状态'
    },
    'AlarmLive': {
      'uniName': '实时告警分析', // 本类型名称
      'key': '实时告警分析设置key', // PK
      'id': '告警id',
      'licencePlate': '车牌号码',
      'vehicleModel': '车辆类型',
      'alarmType': '告警类型',
      'alarmTime': '告警时间',
      'vehicleState': '车辆状态',
      'deptName': '车组',
      'driverName': '驾驶员',
      'dealState': '处理状态',
      'dealDescribe': '处理描述',
      'speed': '实时速度(km/h)',
      'alarmOrigin': '告警来源',
      'alarmLevel': '告警等级',
      'alarmState': '告警状态',
      'terminalStatus': '终端状态',
      'regionRule': '平台规则',
      'licenceColor': '车牌颜色',
      'alarmName': '告警类型'
    },
    'AlarmHistory': {
      'uniName': '历史告警统计', // 本类型名称
      'key': '历史告警统计设置key', // PK
      'id': '告警id',
      'licencePlate': '车牌号码',
      'vehicleModel': '车辆类型',
      'alarmType': '告警类型',
      'alarmStartTime': '告警开始时间',
      'alarmEndTime': '告警结束时间',
      'enterprise': '企业名称',
      'deptName': '车队名称',
      'driverName': '驾驶员',
      'dealState': '处理状态',
      'dealDescribe': '处理描述',
      'speed': '车速(km/h)',
      'alarmOrigin': '告警来源',
      'alarmStartAddress': '告警开始地址',
      'alarmEndAddress': '告警结束位置',
      'vehicleStatus': '车辆状态',
      'simId': 'SIM卡号',
      'latitude': '纬度',
      'longitude': '经度',
      'mileage': '告警里程',
      'oilvolume': '油量',
      'alarmLevel': '告警等级',
      'alarmState': '告警状态',
      'regionRule': '平台规则',
      'terminalStatus': '终端状态',
      'alarmDmsLevel': '告警级别',
      'alarmDmsAppendix': '附件数量',
      'dealAlarmTime': '处理时间',
      'licenceColor': '车牌颜色'
    },
    'AlarmArea': {
      'uniName': '区域告警报表', // 本类型名称
      'key': '历史告警统计设置key', // PK
      'id': '告警id',
      'licencePlate': '车牌号码',
      'vehicleModel': '车辆类型',
      'alarmType': '区域告警类型',
      'alarmStartTime': '区域告警开始时间',
      'alarmEndTime': '区域告警结束时间',
      'enterprise': '企业名称',
      'deptName': '车队名称',
      'driverName': '驾驶员',
      'dealState': '处理状态',
      'dealDescribe': '处理描述',
      'speed': '车速(km/h)',
      'alarmOrigin': '区域告警来源',
      'alarmStartAddress': '区域告警开始地址',
      'alarmEndAddress': '区域告警结束位置',
      'vehicleStatus': '车辆状态',
      'simId': 'SIM卡号',
      'latitude': '纬度',
      'longitude': '经度',
      'mileage': '告警里程',
      'oilvolume': '油量',
      'alarmLevel': '告警等级',
      'alarmState': '告警状态',
      'regionRule': '平台规则',
      'terminalStatus': '终端状态',
      'alarmDmsLevel': '告警级别',
      'alarmDmsAppendix': '附件数量',
      'dealAlarmTime': '处理时间',
      'regionName': '区域名称',
      'licenceColor': '车牌颜色'
    },
    'LimitspeedMap': {
      'uniName': '车辆地图超速', // 本类型名称
      'key': '车辆地图超速设置key', // PK
      'enterprise': '企业名称',
      'deptName': '车队名称',
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色',
      'startAlarmTime': '地图限速告警时间',
      'durationTime': '持续时间(秒)',
      'maxSpeed': '最大速度(km/h)',
      'limitSpeed': '地图限速(km/h)',
      'longitude': '经度',
      'latitude': '纬度',
      'startAlarmAddress': '告警位置',
      'vehicleModel': '行业类型',
      'vehicleOwner': '车辆归属'
    },
    'LimitspeedTerminal': {
      'uniName': '终端限速', // 本类型名称
      'key': '终端限速设置key', // PK
      'enterprise': '企业名称',
      'deptName': '车队名称',
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色',
      'vehicleModel': '行业类型',
      'vehicleOwner': '车辆归属',
      'startTime': '开始时间',
      'endTime': '结束时间',
      'durationTime': '持续时间(秒)',
      'overSpeedCount': '超速次数',
      'minSpeed': '最小速度(km/h)',
      'maxSpeed': '最大速度(km/h)',
      'averageSpeed': '平均速度(km/h)',
      'overSpeedMileage': '超速里程(千米)',
      'limitSpeed': '限速阈值(km/h)',
      'startLongitude': '起点经度',
      'startLatitude': '起点纬度',
      'startAddress': '超速起点',
      'endLongitude': '终点经度',
      'endLatitude': '终点纬度',
      'endAddress': '超速终点',
      'driverInfo': '驾驶员信息'
    },
    'FatigueDrivingCount': {
      'uniName': '疲劳驾驶统计', // 本类型名称
      'key': '疲劳驾驶统计设置key', // PK
      'enterprise': '企业名称',
      'deptName': '车队名称',
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色',
      'vehicleModel': '行业类型',
      'vehicleOwner': '车辆归属',
      'alarmType': '告警类型',
      'startTime': '疲劳驾驶开始时间 ',
      'endTime': '疲劳驾驶结束时间',
      'durationTime': '持续时间',
      'speed': '速度(km/h)',
      'startAddress': '开始位置',
      'endAddress': '结束位置'
    },
    'VehicleFlowCount': {
      'uniName': '车辆每日流量', // 本类型名称
      'key': '车辆每日流量设置key', // PK
      'megaByte': '数据大小（MB）',
      'ymd': '日期',
      'enterprise': '企业名称',
      'deptName': '车队名称',
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色',
      'vehicleModel': '行业类型',
      'vehicleOwner': '车辆归属',
      'accessMode': '接入方式'
    },
    'Anomalous': {
      'uniName': '报停异动统计', // 本类型名称
      'key': '报停异动统计设置key', // PK
      'alarmType': '告警类型',
      'startTime': '开始时间 ',
      'endTime': '结束时间',
      'startTimeAnomalous': '报停开始时间 ',
      'endTimeAnomalous': '报停结束时间',
      'durationTime': '持续时间',
      'speed': '速度(km/h)',
      'startAddress': '开始位置',
      'endAddress': '结束位置',
      'accessMode': '车辆接入方式',
      'alarmTime': '告警时间',
      'enterprise': '企业名称',
      'deptName': '车队名称',
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色',
      'vehicleModel': '行业类型',
      'vehicleOwner': '车辆归属',
      'alarmAddress': '告警地点'
    },
    'ReportformOnline': {
      'uniName': '车辆上线情况抽查', // 本类型名称
      'key': '车辆上线情况抽查设置key', // PK
      'enterprise': '企业名称',
      'deptName': '车队名称',
      'vehicleTotal': '车辆总数',
      'vehicleOnline': '在线车辆数',
      'vehicleOffline': '离线车辆数',
      'onlineRate': '上线率',
      'monitorPerson': '监控人员',
      'licencePlate': '车牌号码',
      'checkTime': '抽查时间',
      'licenceColor': '车牌颜色'
    },
    'VehicleOperation': {
      'uniName': '查询车辆运行情况巡检', // 本类型名称
      'key': '查询车辆运行情况巡检设置key', // PK
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色',
      'vehicleModel': '行业类型',
      'vehicleOwner': '车辆归属',
      'enterprise': '企业名称',
      'deptName': '车队名称',
      'sampleTime': '抽查时间',
      'onlineState': '车辆点名',
      'drivingTrack': '行驶轨迹',
      'latestOnlineTime': '最后上线时间',
      'speed': '车速(km/h)',
      'longitude': '经度',
      'latitude': '纬度',
      'address': '地理位置',
      'monitorPersion': '监控人员'
    },
    'DayMileage': {
      'uniName': '查询日里程统计', // 本类型名称
      'key': '查询日里程统计设置key', // PK
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色',
      'vehicleModel': '行业类型',
      'vehicleOwner': '车辆归属',
      'enterprise': '企业名称',
      'deptName': '车队名称',
      'totalMileage': '总计里程（千米）',
      'dayMileage': '日里程'
    },
    'OnlineRate': {
      'uniName': '车辆在线率统计', // 本类型名称
      'key': '车辆在线率统计设置key', // PK
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色',
      'vehicleModel': '行业类型',
      'vehicleOwner': '车辆归属',
      'enterprise': '企业名称',
      'deptName': '车队名称',
      'currenDays': '在线天数',
      'dayOnlinerate': '平均在线率'
    },
    'OfflineRate': {
      'uniName': '车辆离线统计', // 本类型名称
      'key': '车辆离线统计设置key', // PK
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色',
      'vehicleModel': '行业类型',
      'vehicleOwner': '车辆归属',
      'enterprise': '企业名称',
      'deptName': '车队名称',
      'offlineTime': '离线时间',
      'address': '位置'
    },
    'NightDriving': {
      'uniName': '车辆夜间违规行车', // 本类型名称
      'key': '车辆夜间违规行车设置key', // PK
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色',
      'vehicleModel': '行业类型',
      'vehicleOwner': '车辆归属',
      'enterprise': '企业名称',
      'deptName': '车队名称',
      'startAlarmTime': '夜间违规行车开始时间',
      'endAlarmTime': '夜间违规行车结束时间',
      'durationTime': '持续时间',
      'address': '位置'
    },
    'OnOffline': {
      'uniName': '查询上下线统计', // 本类型名称
      'key': '查询上下线统计设置key', // PK
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色',
      'vehicleModel': '行业类型',
      'vehicleOwner': '车辆归属',
      'enterprise': '企业名称',
      'deptName': '车队名称',
      'onlineState': '在线状态 ',
      'onlineTime': '上线时间',
      'offlineTime': '离线时间',
      'positionCount': '定位条数',
      'queryTime': '查询时间',
      'driverInfo': '驾驶员信息'
    },
    'SecurityInfo': {
      'uniName': ' 查询车辆安全信息记录', // 本类型名称
      'key': ' 查询车辆安全信息记录设置key', // PK
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色',
      'vehicleModel': '行业类型',
      'vehicleOwner': '车辆归属',
      'deptName': '车队名称',
      'enterprise': '企业名称',
      'sendTime': '发送时间 ',
      'onlineTime': '上线时间',
      'sendMessage': '信息内容',
      'sendState': '发送状态',
      'monitorPerson': '监控人员'
    },
    'VideoHistory': {
      'uniName': '历史视频',
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色',
      'channelId': '通道号',
      'startTime': '开始时间',
      'endTime': '结束时间',
      'alarm': '告警标志',
      'mediaType': '音视频类型',
      'streamType': '码流类型',
      'storageType': '存储器类型',
      'fileSize': '文件大小',
      'uploadPercentage': '传输进度估算'
    },
    'QueryRegion': {
      'uniName': '多区域查车',
      'deptName': '车组名称',
      'startTime': '开始时间',
      'endTime': '结束时间',
      'regionName': '绘制区域',
      'licencePlate': '车牌号码',
      'carStatus': '状态',
      'licenceColor': '车牌颜色',
      'type': '查询类型',
      'status': '车辆状态',
      'cover': '绘制图形',
      'locTime': '定位时间'
    },
    'Announcement': {
      'uniName': '公告',
      'timeFormat': '上传时间',
      'createTime': '上传时间',
      'noticeType': '公告对象',
      'title': '公告标题',
      'content': '公告内容',
      'state': '状态',
      'fileUrl': '公告附件',
      'deptList': '所属车组',
      'receive': '公告状态',
      'attachmentImg': '公告附件',
      'deptId': '所属车组',
      'licenceColor': '车牌颜色'
    },
    'receiveAnnouncement': {
      'uniName': '接收平台公告',
      'timeFormat': '上传时间',
      'createTime': '上传时间',
      'title': '公告标题',
      'content': '公告内容',
      'state': '状态',
      'fileUrl': '公告附件',
      'deptList': '所属车组',
      'receive': '公告状态',
      'attachmentImg': '公告附件',
      'deptId': '所属车组'
    },
    'InstructionSelect': {
      'licencePlate': '车牌号码',
      'regionName': '区域名称',
      'vehicleUseType': '行业类型',
      'department': '车组名称'
    },
    'TextInstruction': {
      'uniName': '文字指令',
      'infoFlag': '文本标志',
      'infoType': '文本类型',
      'showOn': '终端显示器',
      'broadcastOn': '终端TTS播读',
      'message': '常用信息',
      'text': '指令内容',
      'urgent': '是否紧急',
      'ttsRead': '是否TTS播报',
      'display': '是否终端屏幕展示',
    },
    'PhotoInstruction': {
      'uniName': '拍照指令',
      'channel': '通道',
      'picNum': '图片数量',
      'interval': '拍照间隔',
      'saveFlag': '保存标志',
      'resolution': '分辨率',
      'ratio': '图像质量',
      'light': '亮度',
      'contrast': '对比度',
      'saturation': '饱和度',
      'color': '色度'
    },
    'MonitorInstruction': {
      'uniName': '监听指令',
      'phone': '电话号码',
      'flag': '监听类型'
    },
    'MessageTemplate': {
      'uniName': '预设消息配置', // 本类型名称
      'id': '规则ID', // PK
      'type': '消息类型',
      'content': '消息内容'
    },
    'SegmentLimitConfiguration': {
      'uniName': '分段限速配置', // 本类型名称
      'id': '分段限速配置ID', // PK
      'deptNames': '车组名称',
      'licencePlates': '车牌名称',
      'bindDeptIds': '绑定车组',
      'vehicleIds': '车牌名称',
      'closeType': '是否开启分段限速语音播报',
      'sectionalSpeedLimitThreshold': '分段限速阈值(Km/h)',
      'voiceBroadcastThreshold': '语音播报阈值(Km/h)'
    },
    'PlatformRecord': {
      "deviceCategory": "终端类型",
      "deviceUniqueId": "序列号",
      "deviceNum": "赋码编号",
      "targetName": "监控对象",
      "type": "指令类型",
      "param": "指令内容",
      "updateTime": "发送时间",
      "resParam": "执行结果"
    },
    'centerSheet': {
      'uniName': '平台指令记录',
      'licencePlate': '车牌号码',
      'type': '指令类型',
      'param': '指令内容',
      'updateTime': '发送时间',
      'result': '执行结果',
      'licenceColor': '车牌颜色',
      'createTime': '任务创建时间',
      'sendTime': '任务发送时间',
      'instructionTemplate': '指令模板',
      'content': '指令模板',
      'terminalModel': '终端型号',
      'paramType': '参数类型',
      'paramLicencePlate': '终端车牌',
      'paramlicenceColor': '终端车牌颜色',
      'attrIccid': '终端iccid',
      'attrSevenId': '序列号',
      'queryTime': '终端参数查询时间',
      'queryResult': '终端参数查询结果',
      'attrTime': '终端属性查询时间',
      'attrResult': '终端属性查询结果'
    },
    'TemplateIssue': {
      'uniName': '指令任务',
      'licencePlate': '车牌号码',
      'type': '指令类型',
      'param': '指令内容',
      'updateTime': '发送时间',
      'result': '任务执行结果',
      'taskName': '任务名称',
      'plateColor': '车牌颜色',
      'createTime': '任务创建时间',
      'sendTime': '任务发送时间',
      'instructionTemplate': '指令模板',
      'content': '模板内容',
      'terminalModel': '终端型号',
      'paramType': '参数类型',
      'paramLicencePlate': '终端车牌',
      'paramPlateColor': '终端车牌颜色',
      'attrIccid': '终端iccid',
      'attrSevenId': '序列号',
      'queryTime': '终端参数查询时间',
      'queryResult': '终端参数查询结果',
      'attrTime': '终端属性查询时间',
      'attrResult': '终端属性查询结果',
      'executeMaxCount': '任务最大执行次数',
      'startTime': '开始日期时间',
      'endTime': '结束日期时间',
      'commandList': '指令列表',
      'templateName': '模板名称',
      'commandDescribe': '指令描述',
      'commandType': '指令类型',
      'executeResult': '任务执行结果',
      'deptName': '归属企业',
      'executeTime': '任务执行时间',
      'executeCount': '任务执行次数',
      'executeState': '任务执行状态',
      'expire': '任务过期状态',
      'taskTime': '任务时间',
      'interval': '任务频次',
      'planExecuteTime': '计划执行时间',
      'taskState': '任务状态',
      'vehicleState': '车辆状态',
      'distributeType': '指令类型',
      'paramConfigId': '指令',
      'text': '指令内容'
    },
    'IssueDetails': {
      'uniName': '平台指令记录',
      'licencePlate': '车牌号码',
      'type': '指令类型',
      'param': '模板参数',
      'updateTime': '发送时间',
      'result': '执行结果',
      'taskName': '任务名称',
      'licenceColor': '车牌颜色',
      'createTime': '任务创建时间',
      'sendTime': '任务发送时间',
      'instructionTemplate': '指令模板',
      'content': '模板内容',
      'terminalModel': '终端型号',
      'paramType': '参数类型',
      'paramLicencePlate': '终端车牌',
      'paramlicenceColor': '终端车牌颜色',
      'attrIccid': '终端iccid',
      'attrSevenId': '序列号',
      'queryTime': '终端参数查询时间',
      'queryResult': '终端参数查询结果',
      'attrTime': '终端属性查询时间',
      'attrResult': '终端属性查询结果',
      'executeMaxCount': '任务执行最大次数',
      'startTime': '开始日期时间',
      'endTime': '结束日期时间',
      'commandList': '指令列表',
      'templateName': '模板名称',
      'commandDescribe': '指令描述',
      'commandType': '指令类型',
      'executeResult': '下发结果',
      'deptName': '归属企业',
      'executeTime': '执行时间',
      'executeCount': '执行次数',
      'commandExecuteResult': '执行结果',
      'name': '指令名称',
      'commandExecuteResultDetail': '执行结果明细'
    },
    'GovernmentRecord': {
      'uniName': '政府指令记录',
      'licencePlate': '车牌号码',
      'cmdType': '指令类型',
      'cmdContent': '指令内容',
      'sendTime': '发送时间',
      'cmdResult': '执行结果',
      'cmdName': '指令名字'
    },
    'EnterpriseNotice': {
      'uniName': '政府通知',
      'noticeType': '信息类型',
      'uploadTime': '上传时间',
      'message': '信息内容',
      'state': '状态'
    },
    'EnterpriseInspection': {
      'uniName': '政府查岗',
      'deptResps': '查岗目标',
      'content': '查岗内容',
      'uploadTime': '上传时间',
      'result': '执行结果',
      'formName': '企业查岗/下级查岗',
      'deptIds': '车组列表',
      'formMessage': '常用信息',
      'cmdResult': '执行结果',
      'dataType': '信息类型',
      'respContext': '查岗回复'
    },
    'Track': {
      'uniName': '轨迹回放',
      'id': '编号',
      'customizeId': '编号',
      'locStateStr': '状态',
      'locTime': '定位时间',
      'speed': '定位速度',
      'locAddr': '地址',
      'limitSpeed': '限速(km/h)',
      'alarmType': '告警类型',
      'locAccuracy': '定位精度',
      'mileage': '总里程km',
      'longitude': '经度',
      'latitude': '纬度',
      'stopStartTime': '停靠开始时间',
      'stopEndTime': '停靠结束时间',
      'stopDuration': '停靠时长',
      'altitude': '高程',
      'locAddr': '地址',
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色',
      'phone': '终端手机号',
      'charge': '电量',
      'gnssNum': '定位卫星',
      'wireless': '通信信号',
      'ioState': '休眠状态',
      'batch': '异常类型',
      'targetName': '监控对象',
      'targetType': '目标类型',
      'targetId': '目标ID',
      'deviceType': '终端类别',
      'deviceCategory': '终端类型',
      'deviceUniqueId': '序列号',
      'status': '终端状态',
      'time': '定位时间',
      'statusDsc': '终端状态',
      'bearing': '方向',
      'valid': '有效定位',
      'posSys': '定位类型',
      'realSpeed': '行驶速度',
      'oilMass': '油量',
      'ioStatus': '休眠状态'
    },
    'Transmit': {
      'uniName': '转发配置',
      'name': '平台名称',
      'operatorNo': '运营商编号',
      'ip': '平台IP地址',
      'port': '平台端口号',
      'userName': '用户名',
      'isEncrypt': '是否加密',
      'userId': '车辆关联账号',
      'password': '密码',
      'm1': 'M1',
      'ia1': 'IA1',
      'ic1': 'IC1',
      'accountName': '账号名称',
      'isTop': '是否上级平台',
      'enabled': '是否启用'
    },
    'OutProvince': {
      'uniName': '出省车辆',
      'driverName': '驾驶员',
      'terminalModel': '终端型号',
      'vehicleUse': '使用状态',
      'district': '行政区域',
      'terminalId': '序列号',
      'vehicleModel': '车辆类型',
      'speed': '实时车速',
      'vehicleUseType': '行业类型',
      'time': '定位时间',
      'l&l': '经纬度',
      'deptName': '车组',
      'state': '终端状态',
      'position': '定位地址',
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色'
    },
    'FindSendHistory': {
      'id': '历史记录id',
      'emailType': '邮件类型',
      'emailSendType': '邮件发送类型',
      'username': '用户名',
      'deptName': '企业名称',
      'receiveMailAddress': '用户邮箱',
      'dataDate': '数据统计日期',
      'sendResultDesc': '邮件发送结果',
      'sendTime': '邮件发送日期',
      'note': '发送备注'
    },
    'TemplateManage': {
      'uniName': '指令下发模板',
      'templateName': '模板名称',
      'terminalModel': '终端型号',
      'commandDescribe': '指令描述',
      'content': '模板内容',
      'createTime': '创建时间'
    },
    'RealTimeMonitoring': {
      'uniName': '实时监控',
      'key': '告警参数设置key',
      'id': '告警id',
      'targetType': '监控对象',
      'deviceType': '终端类别',
      'deviceTypeName': '终端类别',
      'deviceCate': '终端类型',
      'deviceCateName': '终端类型',
      'category': '终端类型',
      'deviceNum': '赋码编号',
      'uniqueId': '序列号',
      'alarmType': '告警类型',
      'alarmLevel': '告警等级',
      'alarmSource': '告警来源',
      'startTime': '开始时间',
      'startAddr': '开始地址',
      'endTime': '结束时间',
      'endAddr': '结束地址',
      'numAttach': '实际附件数/预期附件数',
      'ruleName': '告警规则',
      'handleMeasures': '处理措施',
      'handleContent': '处理内容',
      'handleTime': '处理时间',
      'handler': '处理人员',
      'handleState': '处理状态',
      'deptId': "所属机构",
      'deptName': "所属机构",
    },
    'ThirdMonitoring': {
      'uniName': '实时监控',
      'id': '告警id',
      'key': '告警参数设置key',
      'deptId': '企业ID',
      'deptName': '企业名称',
      'licenceColor': '车牌颜色',
      'licencePlate': '车牌号',
      'alarmLevel': '告警等级',
      'alarmType': '告警类型',
      'startTime': '开始时间',
      'startLon': '开始经度',
      'startLat': '开始纬度',
      'startAddress': '开始地址',
      'endTime': '结束时间',
      'endLon': '结束经度',
      'endLat': '结束纬度',
      'endAddress': '结束地址',
      'speed': '速度',
      'limitSpeed': '限速',
      'maxSpeed': '最高速度',
      'thirdState': '处理状态',
      'thirdResult': '第三方处理结果',
      'thirdMeasures': '第三方处理措施',
      'thirdContent': '第三方处理内容',
      'thirdTime': '第三方处理时间',
      'thirdUser': '第三方处理人员',
      'appealState': '申诉状态',
      'appealResult': '申诉结果',
      'appealTime': '申诉时间',
      'appealUser': '申诉人员',
      'auditResult': '审核结果',
      'auditTime': '审核时间',
      'auditUser': '审核人员',
      'appealAttach': '申诉附件',
      'companyAttach': '处理附件',
      'appealReason': '申诉理由',
      'auditContent': '审核内容',
      'speedUnit': '速度(km/h)',
      'maxSpeedUnit': '最高速度(km/h)',
      'limitSpeedUnit': '限速(km/h)',
      'duration': '持续时间',
      'alarmComplete': '持续状态',
      'numAttach': '实际附件数/预期附件数',
      'dealOperateState': '认领状态',
      'alarmOrigin': '告警来源',
      'ruleName': '告警规则'
    },
    'CompanyMonitoring': {
      'uniName': '实时监控',
      'id': '告警id',
      'key': '告警参数设置key',
      'deptId': '企业ID',
      'deptName': '企业名称',
      'licenceColor': '车牌颜色',
      'licencePlate': '车牌号',
      'alarmLevel': '告警等级',
      'alarmType': '告警类型',
      'startTime': '开始时间',
      'startLon': '开始经度',
      'startLat': '开始纬度',
      'startAddress': '开始地址',
      'endTime': '结束时间',
      'endLon': '结束经度',
      'endLat': '结束纬度',
      'endAddress': '结束地址',
      'speed': '速度',
      'limitSpeed': '限速',
      'maxSpeed': '最高速度',
      'serverState': '服务商处理状态',
      'serverResult': '服务商处理结果',
      'serverMeasures': '服务商处理措施',
      'serverContent': '服务商处理内容',
      'serverTime': '服务商处理时间',
      'serverUser': '服务商处理人员',
      'thirdState': '第三方处理状态',
      'thirdResult': '第三方处理结果',
      'thirdMeasures': '第三方处理措施',
      'thirdContent': '第三方处理内容',
      'thirdTime': '第三方处理时间',
      'thirdUser': '第三方处理人员',
      'companyState': '企业处理状态',
      'companyResult': '企业处理结果',
      'companyMeasures': '企业处理措施',
      'companyContent': '企业处理内容',
      'companyTime': '企业处理时间',
      'companyUser': '企业处理人员',
      'appealState': '申诉状态',
      'appealResult': '申诉结果',
      'appealTime': '申诉时间',
      'appealUser': '申诉人员',
      'auditResult': '审核结果',
      'auditTime': '审核时间',
      'auditUser': '审核人员',
      'appealAttach': '申诉附件',
      'companyAttach': '处理附件',
      'appealReason': '申诉理由',
      'auditContent': '审核内容',
      'speedUnit': '速度(km/h)',
      'maxSpeedUnit': '最高速度(km/h)',
      'limitSpeedUnit': '限速(km/h)',
      'duration': '持续时间',
      'alarmComplete': '持续状态',
      'numAttach': '实际附件数/预期附件数',
      'alarmOrigin': '告警来源',
      'ruleName': '告警规则',
      'serviceState': '服务状态'
    },
    'FatigueRule': {
      'uniName': '疲劳规则',
      'vehicleIds': '绑定车辆',
      'ruleTypeId': '告警规则分类id',
      'interRegionId': '干扰电子围栏',
      'ruleName': '规则名称',
      'limitSpeed': '速度阈值',
      'duration': '持续时长',
      'restDuration': '休息时长',
      'isWarn': '是否预警',
      'warnTime': '预警值',
      'bindDriverCard': '绑定驾驶员卡',
      'cumulativeDrivingTime': '累计驾驶时长',
      'isPhoto': '是否拍照',
      'photoNumber': '拍照次数',
      'photoInterval': '拍照间隔',
      'camera': '摄像头',
      'isAlarm': '是否告警',
      'tipsEmergence': '语音提示：紧急',
      'tipsDisplay': '语音提示：显示器显示',
      'tipsTts': '语音提示：TTS播读',
      'tipsScreen': '语音提示：广告屏显示',
      'tipsText': '语音提示内容',
      'tipsTimes': '语音提示总次数',
      'tipsInterval': '语音提示间隔',
      'warnIsPhoto': '预警是否拍照',
      'warnPhotoNumber': '预警拍照次数',
      'warnPhotoInterval': '预警拍照间隔',
      'warnCamera': '预警摄像头',
      'warnIsAlarm': '预警是否告警',
      'warnAlarmIntervalDuration': '预警持续间隔时间',
      'warnTipsEmergence': '预警语音提示：紧急',
      'warnTipsDisplay': '预警语音提示：显示器显示',
      'warnTipsTts': '预警语音提示：TTS播读',
      'warnTipsScreen': '预警语音提示：广告屏显示',
      'warnTipsTimes': '预警语音提示总次数',
      'warnTipsInterval': '预警语音提示间隔',
      'warnTipsText': '预警语音提示内容',
      'remark': '描述',
      'createTime': '创建时间',
      'creatorName': '创建者',
      'intervalDuration': '持续间隔时间',
      'tips': '语音提示',
      'warnTips': '预警语音提示',
      'logs': '操作日志',
      'targetNum': '关联对象数',
      'limitSpeedUnit': '速度阈值(km/h)',
      'durationUnit': '持续时长(分)',
      'restDurationUnit': '休息时长(分)',
      'tipsIntervalUnit': '语音提示间隔(分)',
      'tipsTimesUnit': '语音提示总次数(次)',
      'startTime': '开始时间',
      'endTime': '结束时间',
      'deptIds': '所属机构',
      'deptName': '所属机构'
    },
    'AnomalousRule': {
      'uniName': '报停异动规则',
      'vehicleIds': '绑定车辆',
      'ruleTypeId': '告警规则分类id',
      'interRegionId': '干扰电子围栏',
      'ruleName': '规则名称',
      'displacementDistance': '位移距离',
      'duration': '持续时长',
      'restDuration': '休息时长',
      'isWarn': '是否预警',
      'warnTime': '预警值',
      'bindDriverCard': '绑定驾驶员卡',
      'cumulativeDrivingTime': '累计驾驶时长',
      'isPhoto': '是否拍照',
      'photoNumber': '拍照次数',
      'photoInterval': '拍照间隔',
      'camera': '摄像头',
      'isAlarm': '是否告警',
      'tipsEmergence': '语音提示：紧急',
      'tipsDisplay': '语音提示：显示器显示',
      'tipsTts': '语音提示：TTS播读',
      'tipsScreen': '语音提示：广告屏显示',
      'tipsText': '语音提示内容',
      'tipsTimes': '语音提示总次数',
      'tipsInterval': '语音提示间隔',
      'warnIsPhoto': '预警是否拍照',
      'warnPhotoNumber': '预警拍照次数',
      'warnPhotoInterval': '预警拍照间隔',
      'warnCamera': '预警摄像头',
      'warnIsAlarm': '预警是否告警',
      'warnAlarmIntervalDuration': '预警持续间隔时间',
      'warnTipsEmergence': '预警语音提示：紧急',
      'warnTipsDisplay': '预警语音提示：显示器显示',
      'warnTipsTts': '预警语音提示：TTS播读',
      'warnTipsScreen': '预警语音提示：广告屏显示',
      'warnTipsTimes': '预警语音提示总次数',
      'warnTipsInterval': '预警语音提示间隔',
      'warnTipsText': '预警语音提示内容',
      'remark': '描述',
      'createTime': '创建时间',
      'createUserName': '创建者',
      'intervalDuration': '持续间隔时间',
      'tips': '语音提示',
      'warnTips': '预警语音提示',
      'logs': '操作日志',
      'targetNum': '关联对象数',
      'limitSpeedUnit': '速度阈值(km/h)',
      'durationUnit': '持续时长(分)',
      'restDurationUnit': '休息时长(分)',
      'tipsIntervalUnit': '语音提示间隔(分)',
      'tipsTimesUnit': '语音提示总次数(次)',
      'endDate': '有效结束日期',
      'startDate': '有效开始日期',
      'startTime': '开始时间',
      'endTime': '结束时间',
    },
    'OverspeedRule': {
      'uniName': '超速规则',
      'vehicleIds': '绑定车辆',
      'ruleTypeId': '告警规则分类id',
      'deptId': '租户id',
      'interRegionId': '干扰电子围栏',
      'regionId': '规则围栏',
      'regionName': '规则围栏名称',
      'ruleName': '规则名称',
      'limitingMode': '限制方式',
      'limitSpeed': '速度阈值',
      'duration': '持续时间',
      'warnAlarmDuration': '预警持续时长',
      'isWarn': '是否预警',
      'startTime': '开始时间',
      'endTime': '结束时间',
      'warnSpeed': '预警值',
      'isPhoto': '是否拍照',
      'photoNumber': '拍照次数',
      'photoInterval': '拍照间隔',
      'camera': '摄像头',
      'isAlarm': '是否告警',
      'tipsEmergence': '语音提示：紧急',
      'tipsDisplay': '语音提示：显示器显示',
      'tipsTts': '语音提示：TTS播读',
      'tipsScreen': '语音提示：广告屏显示',
      'tipsText': '语音提示内容',
      'tipsTimes': '语音提示总次数',
      'tipsInterval': '语音提示间隔',
      'warnIsPhoto': '预警是否拍照',
      'warnPhotoNumber': '预警拍照次数',
      'warnPhotoInterval': '预警拍照间隔',
      'warnCamera': '预警摄像头',
      'warnIsAlarm': '预警是否告警',
      'warnAlarmIntervalDuration': '预警持续间隔时间',
      'warnTipsEmergence': '预警语音提示：紧急',
      'warnTipsDisplay': '预警语音提示：显示器显示',
      'warnTipsTts': '预警语音提示：TTS播读',
      'warnTipsScreen': '预警语音提示：广告屏显示',
      'warnTipsTimes': '预警语音提示总次数',
      'warnTipsInterval': '预警语音提示间隔',
      'warnTipsText': '预警语音提示内容',
      'remark': '描述',
      'createTime': '创建时间',
      'createUserName': '创建者',
      'intervalDuration': '持续间隔时间',
      'tips': '语音提示',
      'warnTips': '预警语音提示',
      'logs': '操作日志',
      'targetNum': '关联对象数',
      'limitSpeedUnit': '速度阈值(km/h)',
      'tipsIntervalUnit': '语音提示间隔(分)',
      'tipsTimesUnit': '语音提示总次数(次)'
    },
    'ProhibitRule': {
      'uniName': '禁行规则',
      'vehicleIds': '绑定车辆',
      'ruleTypeId': '告警规则分类id',
      'regionId': '电子围栏',
      'ruleName': '规则名称',
      'startTime': '开始时间',
      'endTime': '结束时间',
      'accCondition': 'ACC',
      'limitMode': '电子围栏范围',
      'limitSpeed': '速度阈值',
      'duration': '持续时长',
      'isPhoto': '是否拍照',
      'photoNumber': '拍照次数',
      'photoInterval': '拍照间隔',
      'camera': '摄像头',
      'isAlarm': '是否告警',
      'tipsEmergence': '语音提示：紧急',
      'tipsDisplay': '语音提示：显示器显示',
      'tipsTts': '语音提示：TTS播读',
      'tipsScreen': '语音提示：广告屏显示',
      'tipsText': '语音提示内容',
      'tipsTimes': '语音提示总次数',
      'tipsInterval': '语音提示间隔',
      'remark': '描述',
      'createTime': '创建时间',
      'creatorName': '创建者',
      'tips': '语音提示',
      'logs': '操作日志',
      'targetNum': '关联对象数',
      'limitSpeedUnit': '速度阈值(km/h)',
      'tipsIntervalUnit': '语音提示间隔(分)',
      'tipsTimesUnit': '语音提示总次数(次)',
      'deptIds': '所属机构',
      'deptName': '所属机构'
    },
    'NightRestrictRule': {
      'uniName': '夜间限速规则',
      'vehicleIds': '绑定车辆',
      'ruleTypeId': '告警规则分类id',
      'ruleName': '规则名称',
      'startTime': '开始时间',
      'endTime': '结束时间',
      'minRoadSpeed': '路网最低速度',
      'minPositionSpeed': '定位最低速度',
      'percentage': '限速百分比',
      'limitSpeed': '限速阈值',
      'isAlarm': '是否告警',
      'duration': '持续时间',
      'targetNum': '关联对象数',
      'tips': '语音提示',
      'tipsEmergence': '语音提示：紧急',
      'tipsDisplay': '语音提示：显示器显示',
      'tipsTts': '语音提示：TTS播读',
      'tipsScreen': '语音提示：广告屏显示',
      'tipsText': '语音提示内容',
      'tipsTimes': '语音提示总次数',
      'tipsInterval': '语音提示间隔',
      'remark': '描述',
      'createTime': '创建时间',
      'creatorName': '创建者',
      'logs': '操作日志',
      'limitSpeedUnit': '速度阈值(km/h)',
      'percentageUnit': '限速百分比(%)',
      'tipsIntervalUnit': '语音提示间隔(分)',
      'tipsTimesUnit': '语音提示总次数(次)',
      'deptIds': '所属机构',
      'deptName': '所属机构'
    },
    'SubsectionRestrictRule': {
      'uniName': '分段限速规则',
      'vehicleIds': '绑定车辆',
      'ruleTypeId': '告警规则分类id',
      'deptId': '租户id',
      'ruleName': '规则名称',
      'isAlarm': '是否告警',
      'minSpeed': '路网最低速度',
      'positionMinSpeed': '定位最低速度',
      'startDate': '有效开始日期',
      'endDate': '有效结束日期',
      'duration': '持续时间',
      'startTime': '开始时间',
      'endTime': '结束时间',
      'limitRate': '限速比例',
      'targetNum': '关联对象数',
      'tips': '语音提示',
      'tipsEmergence': '语音提示：紧急',
      'tipsDisplay': '语音提示：显示器显示',
      'tipsTts': '语音提示：TTS播读',
      'tipsScreen': '语音提示：广告屏显示',
      'tipsText': '语音提示内容',
      'tipsTimes': '语音提示总次数',
      'tipsInterval': '语音提示间隔',
      'remark': '描述',
      'createTime': '创建时间',
      'creatorName': '创建者',
      'logs': '操作日志',
      'minSpeedUnit': '路网最低速度(km/h)',
      'positionMinSpeedUnit': '定位最低速度(km/h)',
      'tipsIntervalUnit': '语音提示间隔(分)',
      'tipsTimesUnit': '语音提示总次数(次)',
      'deptIds': '所属机构',
      'deptName': '所属机构'
    },
    'NightSteerRule': {
      'uniName': '夜间异动规则',
      'vehicleIds': '绑定车辆',
      'ruleTypeId': '告警规则分类id',
      'deptId': '租户id',
      'ruleName': '规则名称',
      'isAlarm': '是否告警',
      'speedLimit': '限速阈值',
      'duration': '持续时间',
      'startTime': '开始时间',
      'endTime': '结束时间',
      'startDate': '开始日期',
      'endDate': '结束日期',
      'targetNum': '关联对象数',
      'remark': '描述',
      'createTime': '创建时间',
      'creatorName': '创建者',
      'logs': '操作日志',
      'threshold': '时长',
      'speedLimitUnit': '限速阈值(km/h)',
      'deptIds': '所属机构',
      'deptName': '所属机构'
    },
    'ElectronicFenceRule': {
      'uniName': '电子围栏规则',
      'vehicleIds': '绑定车辆',
      'ruleTypeId': '告警规则分类id',
      'regionId': '电子围栏',
      'regionName': '电子围栏名称',
      'deptId': '租户id',
      'ruleName': '规则名称',
      'ruleType': '规则类型',
      'durationType': '围栏逗留时长',
      'startTime': '开始时间',
      'endTime': '结束时间',
      'duration': '持续时长',
      'isPhoto': '是否拍照',
      'photoNumber': '拍照次数',
      'photoInterval': '拍照间隔',
      'camera': '摄像头',
      'isAlarm': '是否告警',
      'tipsEmergence': '语音提示：紧急',
      'tipsDisplay': '语音提示：显示器显示',
      'tipsTts': '语音提示：TTS播读',
      'tipsScreen': '语音提示：广告屏显示',
      'tipsText': '语音提示内容',
      'tipsTimes': '语音提示总次数',
      'tipsInterval': '语音提示间隔',
      'remark': '描述',
      'createTime': '创建时间',
      'creatorName': '创建者',
      'tips': '语音提示',
      'logs': '操作日志',
      'targetNum': '关联对象数',
      'tipsIntervalUnit': '语音提示间隔(分)',
      'tipsTimesUnit': '语音提示总次数(次)',
      'deptIds': '所属机构',
      'deptName': '所属机构'
    },
    'PathExcursion': {
      'uniName': '路线偏航规则',
      'vehicleIds': '绑定车辆',
      'ruleTypeId': '告警规则分类id',
      'regionIds': '电子围栏',
      'regionNames': '电子围栏名称',
      'deptId': '租户id',
      'ruleName': '规则名称',
      'ruleType': '规则类型',
      'durationType': '围栏逗留时长',
      'yawThreshold': '偏航阈值(米)',
      'startDate': '开始日期',
      'endDate': '结束日期',
      'startTime': '开始时间',
      'endTime': '结束时间',
      'duration': '持续时长(秒)',
      'isPhoto': '是否拍照',
      'photoNumber': '拍照次数',
      'photoInterval': '拍照间隔',
      'camera': '摄像头',
      'alarmable': '是否告警',
      'tipsEmergence': '语音提示：紧急',
      'tipsDisplay': '语音提示：显示器显示',
      'tipsTts': '语音提示：TTS播读',
      'tipsScreen': '语音提示：广告屏显示',
      'tipsText': '语音提示内容',
      'tipsTimes': '语音提示总次数',
      'tipsInterval': '语音提示间隔',
      'remark': '描述',
      'createTime': '创建时间',
      'creatorName': '创建者',
      'tips': '语音提示',
      'logs': '操作日志',
      'targetNum': '关联对象数',
      'tipsIntervalUnit': '语音提示间隔(分)',
      'tipsTimesUnit': '语音提示总次数(次)',
      'deptIds': '所属机构',
      'deptName': '所属机构'
    },
    'ApproachRegion': {
      'uniName': '接近区域规则',
      'vehicleIds': '绑定车辆',
      'ruleTypeId': '告警规则分类id',
      'regionIds': '电子围栏',
      'regionNames': '电子围栏名称',
      'deptId': '租户id',
      'ruleName': '规则名称',
      'ruleType': '规则类型',
      'durationType': '围栏逗留时长',
      'closeThreshold': '接近阈值(米)',
      'startDate': '开始日期',
      'endDate': '结束日期',
      'startTime': '开始时间',
      'endTime': '结束时间',
      'duration': '持续时长(秒)',
      'isPhoto': '是否拍照',
      'photoNumber': '拍照次数',
      'photoInterval': '拍照间隔',
      'camera': '摄像头',
      'alarmable': '是否告警',
      'tipsEmergence': '语音提示：紧急',
      'tipsDisplay': '语音提示：显示器显示',
      'tipsTts': '语音提示：TTS播读',
      'tipsScreen': '语音提示：广告屏显示',
      'tipsText': '语音提示内容',
      'tipsTimes': '语音提示总次数',
      'tipsInterval': '语音提示间隔',
      'remark': '描述',
      'createTime': '创建时间',
      'creatorName': '创建者',
      'tips': '语音提示',
      'logs': '操作日志',
      'targetNum': '关联对象数',
      'tipsIntervalUnit': '语音提示间隔(分)',
      'tipsTimesUnit': '语音提示总次数(次)',
      'deptIds': '所属机构',
      'deptName': '所属机构'
    },
    'RoadwayRestrict': {
      'uniName': '道路限速规则',
      'vehicleIds': '绑定车辆',
      'ruleTypeId': '告警规则分类id',
      'deptId': '租户id',
      'interRegionId': '干扰电子围栏',
      'regionId': '电子围栏',
      'ruleName': '规则名称',
      'limitMode': '电子围栏范围',
      'highLimitSpeed': '高速路超速',
      'highLimitText': '高速语音提示',
      'highWarnDiffSpeed': '高速路预警速度差值',
      'highWarnText': '高速预警语音提示',
      'countryLimitSpeed': '国道超速',
      'countryLimitText': '国道语音提示',
      'countryWarnDiffSpeed': '国道预警速度差值',
      'countryWarnText': '国道预警语音提示',
      'provinceLimitSpeed': '省道超速',
      'provinceLimitText': '省道语音提示',
      'provinceWarnDiffSpeed': '省道预警速度差值',
      'provinceWarnText': '省道预警语音提示',
      'otherLimitSpeed': '其它道路超速',
      'otherLimitText': '其它道路语音提示',
      'otherWarnDiffSpeed': '其它道路预警速度差值',
      'otherWarnText': '其它道路预警语音提示',
      'duration': '持续时间',
      'isPhoto': '是否拍照',
      'photoNumber': '拍照次数',
      'photoInterval': '拍照间隔',
      'camera': '摄像头',
      'isAlarm': '是否告警',
      'tipsEmergence': '语音提示：紧急',
      'tipsDisplay': '语音提示：显示器显示',
      'tipsTts': '语音提示：TTS播读',
      'tipsScreen': '语音提示：广告屏显示',
      'tipsText': '语音提示内容',
      'tipsTimes': '语音提示总次数',
      'tipsInterval': '语音提示间隔',
      'remark': '描述',
      'createTime': '创建时间',
      'createUserName': '创建者',
      'tips': '语音提示',
      'logs': '操作日志',
      'targetNum': '关联对象数',
      'highLimitSpeedUnit': '高速路超速(km/h)',
      'countryLimitSpeedUnit': '国道超速(km/h)',
      'provinceLimitSpeedUnit': '省道超速(km/h)',
      'otherLimitSpeedUnit': '其它道路超速(km/h)',
      'tipsIntervalUnit': '语音提示间隔(分)',
      'tipsTimesUnit': '语音提示总次数(次)'
    },
    'OfflineDisplacement': {
      'uniName': '离线位移规则',
      'vehicleIds': '绑定车辆',
      'ruleTypeId': '告警规则分类id',
      'deptId': '租户id',
      'ruleName': '规则名称',
      'dayStartTime': '开始时间',
      'dayEndTime': '结束时间',
      'offlineTime': '离线时长',
      'distance': '离线位移',
      'targetNum': '关联对象数',
      'remark': '描述',
      'createTime': '创建时间',
      'creatorName': '创建者',
      'logs': '操作日志',
      'offlineTimeUnit': '离线时长(秒)',
      'distanceUnit': '离线位移(米)',
      'deptIds': '所属机构',
      'deptName': '所属机构'
    },
    'DriverIdentity': {
      'uniName': '驾驶员身份识别',
      'vehicleIds': '绑定车辆',
      'ruleTypeId': '告警规则分类id',
      'deptId': '租户id',
      'ruleName': '规则名称',
      'shootingChannel': '拍摄通道',
      'identifyPassValue': '识别通过值>=',
      'retryNumber': '重试次数',
      'produceType': '产生类型',
      'startTime': '开始时间',
      'endTime': '结束时间',
      'accConditions': 'ACC条件',
      'accTriggerInterval': 'acc开时间触发间隔',
      'speedThreshold': '速度阈值',
      'duration': '持续时间',
      'isPhoto': '是否拍照',
      'photoNumber': '拍照次数',
      'photoInterval': '拍照间隔',
      'camera': '摄像头',
      'isAlarm': '是否告警',
      'tipsEmergence': '语音提示：紧急',
      'tipsDisplay': '语音提示：显示器显示',
      'tipsTts': '语音提示：TTS播读',
      'tipsScreen': '语音提示：广告屏显示',
      'tipsText': '语音提示内容',
      'tipsTimes': '语音提示总次数',
      'tipsInterval': '语音提示间隔',
      'rightTipsEmergence': '正确语音提示：紧急',
      'rightTipsDisplay': '正确语音提示：显示器显示',
      'rightTipsTts': '正确语音提示：TTS播读',
      'rightTipsScreen': '正确语音提示：广告屏显示',
      'rightTipsText': '正确语音提示内容',
      'remark': '描述',
      'createTime': '创建时间',
      'createUserName': '创建者',
      'tips': '告警时语音提示',
      'rightTips': '正确时语音提示',
      'logs': '操作日志',
      'targetNum': '关联对象数',
      'speedThresholdUnit': '速度阈值(km/h)',
      'retryNumberUnit': '重试次数(次)'
    },
    'OfflineNotification': {
      'uniName': '离线通知规则',
      'vehicleIds': '绑定车辆',
      'ruleTypeId': '告警规则分类id',
      'deptId': '租户id',
      'regionId': '电子围栏',
      'limitMode': '电子围栏范围',
      'ruleName': '规则名称',
      'startTime': '开始时间',
      'endTime': '结束时间',
      'acc': 'ACC',
      'isSpeed': '是否判断速度',
      'limitSpeed': '速度阈值',
      'ruleType': '规则类型',
      'alarmLevel': '告警级别',
      'notifyOnce': '是否只通知一次',
      'offlineTime': '离线时长',
      'notifyObj': '通知对象',
      'phone': '电话',
      'email': '邮箱',
      'remark': '描述',
      'createTime': '创建时间',
      'createUserName': '创建者',
      'logs': '操作日志',
      'targetNum': '关联对象数'
    },
    'SimCardManage': {
      'deptName': '所属机构',
      'number': '物联网卡号',
      'cardNumber': '本机号',
      'iccid': 'ICCID',
      'imsi': 'IMSI号',
      'holder': '客户名称',
      'operator': '运营商',
      'status': '卡状态',
      'issueTime': '发卡日期',
      'activationTime': '激活日期',
      'expire': '有效期',
      'dataPlan': '流量套餐',
      'packetSize': '流量套餐总量(MB)',
      'category': '卡类型',
      'deviceId': '设备ID',
      'deviceType': '终端类别'
    },
    'TerminalManage': {
      'uniName': '终端', // 本类型名称
      'phone': '终端手机号',
      'registerTime': '注册时间',
      'vendorId': '厂商编号',
      'terminalModel': '终端型号',
      'terminalType': '终端类型',
      'localStandards': '标准',
      'terminalFunctionType': '功能类型',
      'terminalNo': '序列号',
      'isAi': '主动安全设备',
      'serial': '唯一性编码',
      'terminalId': '序列号',
      'imei': 'IMEI号',
      'simId': 'SIM卡号',
      'iccid': 'ICCID',
      'imsi': 'IMSI号',
      'channelNum': '视频通道数',
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色',
      'fm': '赋码编号',
      'fmyz': '赋码验证',
      'terminalNo': '物联网卡号',
    },
    'PeripheralManage': {
      'uniName': '外设', // 本类型名称
      'deviceId': '通道编号',
      'uniqueId': '外设编号',
      'deviceModel': '设备型号',
      'deviceType': '设备类型',
      'deviceName': '设备名称',
      'deviceSerial': '序列号',
      'locationNo': '安装位置编号',
      'locationName': '位置名称',
      'lowerThreshold': '阈值下限',
      'upperThreshold': '阈值上限',
      'manufacturer': '厂商名称',
      'shotPhoto': '拍照通道标志',
      'buyDate': '采购日期',
      'warrantyPeriod': '质保期',
      'firstInstallDate': '首次安装时间',
      'deviceState': '设备状态',
      'createUserName': '创建人',
      'createTime': '创建时间',
      'updateTime': '更新时间',
      'warrantyPeriodUnit': '质保期(月)',
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色'
    },
    'ManualIntervention': {
      'licencePlate': '车牌号码',
      'alarmType': '告警类型',
      'startTime': '开始时间'
    },
    'OfflineFiling': {
      'uniName': '离线备案车辆', // 本类型名称
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色',
      'startDate': '备案开始日期',
      'endDate': '备案结束日期',
      'state': '备案状态',
      'reason': '备案原因'
    },
    'SecurityEvent': {
      'id': '告警id',
      'key': '告警参数设置key',
      'deptId': '企业ID',
      'deptName': '企业名称',
      'licenceColor': '车牌颜色',
      'licencePlate': '车牌号',
      'alarmLevel': '告警等级',
      'alarmType': '告警类型',
      'startTime': '开始时间',
      'startLon': '开始经度',
      'startLat': '开始纬度',
      'alarmAddress': '开始地址',
      'endTime': '结束时间',
      'endLon': '结束经度',
      'endLat': '结束纬度',
      'alarmEndAddress': '结束地址',
      'speed': '速度',
      'limitSpeed': '速度限制',
      'serverState': '服务商处理状态',
      'serverResult': '服务商处理结果',
      'serverMeasures': '服务商处理措施',
      'serverContent': '服务商处理内容',
      'serverTimeStr': '服务商处理时间',
      'serverUser': '服务商处理人员',
      'thirdState': '第三方处理状态',
      'thirdResult': '第三方处理结果',
      'thirdMeasures': '第三方处理措施',
      'thirdContent': '第三方处理内容',
      'thirdTimeStr': '第三方处理时间',
      'thirdUser': '第三方处理人员',
      'companyState': '企业处理状态',
      'companyResult': '企业处理结果',
      'companyMeasures': '企业处理措施',
      'companyContent': '企业处理内容',
      'companyTimeStr': '企业处理时间',
      'companyUser': '企业处理人员',
      'appealState': '申诉状态',
      'appealResult': '申诉结果',
      'appealTimeStr': '申诉时间',
      'appealUser': '申诉人员',
      'auditResult': '审核结果',
      'auditTimeStr': '审核时间',
      'auditUser': '审核人员',
      'appealAttach': '申诉附件',
      'companyAttach': '处理附件',
      'appealReason': '申诉理由',
      'auditContent': '审核内容',
      'speedUnit': '速度(km/h)',
      'maxSpeedUnit': '最高速度(km/h)',
      'limitSpeedUnit': '速度限制(km/h)',
      'duration': '持续时间',
      'alarmComplete': '持续状态',
      'alarmOrigin': '告警来源',
      'ruleName': '告警规则',
      'serviceState': '服务状态',
      'phone': '告警手机号',
      'vehicleUseType': '行业类型',
      'idCard': '驾驶员身份证号',
      'driverName': '驾驶员姓名',
      'vehicleSpeed': '车辆仪表速度(km/h)',
      'roadName': '道路名称',
      'roadType': '道路类型',
      'direction': '方向',
      'altitude': '高程',
      'mileage': '里程',
      'simId': 'SIM卡号',
      'autoDeal': '是否为自动处理',
      'alarmDealState': '告警处理状态',
      'ruleTypeName': '平台规则名称',
      'ruleId': '平台规则编号',
      'ruleTypeId': '平台规则类型编号',
      'vehicleStates': '车辆状态',
      'terminalStateString': '终端状态',
      'vehicleId': '车辆主键',
      'uniqueId': '告警附件关联',
      'alarmDmsAppendix': '告警附件数量',
      'isAppeal': '是否申诉',
      'isWrong': '是否误报',
      'reportingState': '上报状态',
      'thirdPlatform': '809第三方转发平台名称',
      'removeAlarm': '解除告警状态',
      'punishState': '是否已开罚单',
      'protocolType': '协议类型',
      'drivingTimeStr': '行驶时间',
      'fatigueStartTimeStr': '疲劳开始时间',
      'fatigueEndTimeStr': '疲劳结束时间',
      'regionId': '车辆归属区域',
      'dealOperateState': '告警处理操作状态',
      'createTimeStr': '创建时间',
      'updateTimeStr': '更新时间',
    },
    'TicketRecord': {
      'uniName': '罚单', // 本类型名称
      'title': '罚单标题',
      'statusName': '罚单状态',
      'deptName': '车组',
      'createTime': '创建时间',
      'createUserName': '创建人',
      'confirmAttach': '处罚确认单',
      'docUrl': '处罚通知'
    },
    'IssueTicket': {
      'uniName': '罚单', // 本类型名称
      'deptId': '企业ID',
      'deptName': '企业名称',
      'licenceColor': '车牌颜色',
      'licencePlate': '车牌号',
      'alarmLevel': '告警等级',
      'alarmType': '告警类型',
      'startTime': '开始时间',
      'startLon': '开始经度',
      'startLat': '开始纬度',
      'startAddress': '开始地址',
      'endTime': '结束时间',
      'endLon': '结束经度',
      'endLat': '结束纬度',
      'endAddress': '结束地址',
      'speed': '速度',
      'limitSpeed': '速度限制',
      'companyState': '企业处理状态',
      'companyResult': '企业处理结果',
      'companyMeasures': '企业处理措施',
      'companyContent': '企业处理内容',
      'companyTime': '企业处理时间',
      'companyUser': '企业处理人员',
      'appealState': '申诉状态',
      'appealResult': '申诉结果',
      'appealTime': '申诉时间',
      'appealUser': '申诉人员',
      'auditResult': '审核结果',
      'auditTime': '审核时间',
      'auditUser': '审核人员',
      'appealAttach': '申诉附件',
      'companyAttach': '处理附件',
      'appealReason': '申诉理由',
      'auditContent': '审核内容',
      'speedUnit': '速度(km/h)',
      'maxSpeedUnit': '最高速度(km/h)',
      'limitSpeedUnit': '速度限制(km/h)',
      'duration': '持续时间',
      'id': '告警id',
      'alarmOrigin': '告警来源',
      'ruleName': '告警规则'
    },
    'IssueRecord': {
      'uniName': '今日提醒',
      'licencePlate': '车牌号码',
      'type': '指令类型',
      'param': '指令内容',
      'updateTime': '发送时间',
      'resParam': '执行结果',
      'licenceColor': '车牌颜色'
    },
    'MileageStatistic': {
      'uniName': '里程统计',
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色',
      'vehicleModel': '行业类型',
      'vehicleOwner': '车辆归属',
      'enterprise': '企业名称',
      'deptName': '车队名称',
      'totalMileage': '总计里程（千米）',
      'dayMileage': '日里程',
      'accessMode': '车辆接入方式'
    },
    'InstructTask': {
      'uniName': '指令任务',
      'licencePlate': '车牌号码',
      'type': '指令类型',
      'param': '指令内容',
      'updateTime': '发送时间',
      'result': '任务执行结果',
      'taskName': '任务名称',
      'plateColor': '车牌颜色',
      'createTime': '任务创建时间',
      'sendTime': '任务发送时间',
      'instructionTemplate': '指令模板',
      'content': '模板内容',
      'terminalModel': '终端型号',
      'paramType': '参数类型',
      'paramLicencePlate': '终端车牌',
      'paramPlateColor': '终端车牌颜色',
      'attrIccid': '终端iccid',
      'attrSevenId': '序列号',
      'queryTime': '终端参数查询时间',
      'queryResult': '终端参数查询结果',
      'attrTime': '终端属性查询时间',
      'attrResult': '终端属性查询结果',
      'executeMaxCount': '任务最大执行次数',
      'startTime': '开始日期时间',
      'endTime': '结束日期时间',
      'commandList': '指令列表',
      'templateName': '模板名称',
      'commandDescribe': '指令描述',
      'commandType': '指令类型',
      'executeResult': '任务执行结果',
      'deptName': '归属企业',
      'executeTime': '任务执行时间',
      'executeCount': '任务执行次数',
      'executeState': '任务执行状态',
      'expire': '任务过期状态',
      'taskTime': '任务时间',
      'interval': '任务频次',
      'planExecuteTime': '计划执行时间',
      'taskState': '任务状态',
      'vehicleState': '车辆状态',
      'distributeType': '指令类型',
      'paramConfigId': '指令',
      'text': '指令内容'
    },
    'InstructDetails': {
      'uniName': '平台指令记录',
      'licencePlate': '车牌号码',
      'type': '指令类型',
      'param': '模板参数',
      'updateTime': '发送时间',
      'result': '执行结果',
      'taskName': '任务名称',
      'licenceColor': '车牌颜色',
      'createTime': '任务创建时间',
      'sendTime': '任务发送时间',
      'instructionTemplate': '指令模板',
      'content': '模板内容',
      'terminalModel': '终端型号',
      'paramType': '参数类型',
      'paramLicencePlate': '终端车牌',
      'paramPlateColor': '终端车牌颜色',
      'attrIccid': '终端iccid',
      'attrSevenId': '序列号',
      'queryTime': '终端参数查询时间',
      'queryResult': '终端参数查询结果',
      'attrTime': '终端属性查询时间',
      'attrResult': '终端属性查询结果',
      'executeMaxCount': '任务执行最大次数',
      'startTime': '开始日期时间',
      'endTime': '结束日期时间',
      'commandList': '指令列表',
      'templateName': '模板名称',
      'commandDescribe': '指令描述',
      'commandType': '指令类型',
      'executeResult': '下发结果',
      'deptName': '所属机构',
      'executeTime': '执行时间',
      'executeCount': '执行次数',
      'commandExecuteResult': '执行结果',
      'name': '指令名称',
      'commandExecuteResultDetail': '执行结果明细'
    },
    'SpeedAlarm': {
      'vehicleUseType': '行业类型',
      'limitSpeed1': '最小限速值',
      'limitSpeed2': '最大限速值',
      'percentage': '超速百分比',
      'duration': '持续时间',
      'alarmLevel': '告警等级',
      'durationUnit': '持续时间(分钟)',
      'percentageUnit': '超速百分比(%)',
      'limitSpeed1Unit': '最小限速值(km/h)',
      'limitSpeed2Unit': '最大限速值(km/h)',
    },
    'terminalConfiguration': {
      'speedThreshold': '告警判断速度阈值',
      'tipVolume': '告警提示音量',
      'photoStrategy': '主动拍照策略',
      'photoTimeInterval': '主动定时拍照间隔',
      'photoDistanceInterval': '主动定距拍照间隔',
      'photoNums': '单次主动拍照张数',
      'photoInterval': '单次主动拍照时间间隔',
      'photoResolution': '拍照分辨率',
      'videoResolution': '视频录制分辨率',
      'alarmEnable': '告警使能',
      'eventEnable': '事件使能',
      'obstacleDisThreshold': '障碍物告警距离阈值',
      'obstacleSpeedThreshold': '障碍物告警分级速度阈值',
      'obstacleVideoDuration': '障碍物告警前后视频录制时间',
      'obstaclePhotoNums': '障碍物告警拍照张数',
      'obstaclePhotoInterval': '障碍物告警拍照间隔',
      'oftenWayDuration': '频繁变道告警判断时间段',
      'oftenWayTimes': '频繁变道告警判断次数',
      'oftenWaySpeedThreshold': '频繁变道告警分级速度阈值',
      'oftenWayVideoDuration': '频繁变道告警前后视频录制时间',
      'oftenWayPhotoNums': '频繁变道告警拍照张数',
      'oftenWayPhotoInterval': '频繁变道告警拍照间隔',
      'wayDeviateSpeedThreshold': '车道偏离告警分级速度阈值',
      'wayDeviateVideoDuration': '车道偏离告警前后视频录制时间',
      'wayDeviatePhotoNums': '车道偏离告警拍照张数',
      'wayDeviatePhotoInterval': '车道偏离告警拍照间隔',
      'frontDurationThreshold': '前向碰撞告警时间阈值',
      'frontSpeedThreshold': '前向碰撞告警分级速度阈值',
      'frontVideoDuration': '前向碰撞告警前后视频录制时间',
      'frontPhotoNums': '前向碰撞告警拍照张数',
      'frontPhotoInterval': '前向碰撞告警拍照间隔',
      'personDurationThreshold': '行人碰撞告警时间阈值',
      'personSpeedThreshold': '行人碰撞告警使能速度阈值',
      'personVideoDuration': '行人碰撞告警前后视频录制时间',
      'personPhotoNums': '行人碰撞告警拍照张数',
      'personPhotoInterval': '行人碰撞告警拍照间隔',
      'carDisThreshold': '车距监控告警距离阈值',
      'carSpeedThreshold': '车距监控告警分级速度阈值',
      'carVideoDuration': '车距过近告警前后视频录制时间',
      'carPhotoNums': '车距过近告警拍照张数',
      'carPhotoInterval': '车距过近告警拍照间隔',
      'wayFlagPhotoNums': '道路标志识别拍照张数',
      'wayFlagPhotoInterval': '道路标志识别拍照间隔',
      'obligate': '预留字段',
      'obligateBytes_4': '保留字段',
      // DMS
      'tirePhotoNums': '疲劳驾驶告警拍照张数',
      'tirePhotoInterval': '疲劳驾驶告警拍照间隔时间',
      'tireVideoDuration': '疲劳驾驶告警前后视频录制时间',
      'tireSpeedThreshold': '疲劳驾驶告警分级速度阈值',
      'abnormalPhotoNums': '驾驶行为异常抓拍照片张数',
      'abnormalInterval': '驾驶行为异常拍照间隔',
      'abnormalVideoDuration': '驾驶行为异常视频录制时间',
      'abnormalSpeedThreshold': '驾驶行为异常分级速度阈值',
      'phoneInterval': '接打电话告警判断时间间隔',
      'phoneDriverNums': '接打电话告警拍驾驶员面部特征照片张数',
      'phoneDriverInterval': '接打电话告警拍驾驶员面部特征照片间隔时间',
      'phoneVideoDuration': '接打电话告警前后视频录制时间',
      'phoneSpeedThreshold': '接打电话告警分级速度阈值',
      'attentionPhotoNums': '分神驾驶告警拍照张数',
      'attentionInterval': '分神驾驶告警拍照间隔时间',
      'attentionVideoDuration': '分神驾驶告警前后视频录制时间',
      'attentionSpeedThreshold': '分神驾驶告警分级车速阈值',
      'smokeInterval': '吸烟告警判断时间间隔',
      'smokePhotoNums': '抽烟告警拍驾驶员面部特征照片张数',
      'smokeDriverInterval': '抽烟告警拍驾驶员面部特征照片间隔时间',
      'smokeVideoDuration': '抽烟告警前后视频录制时间',
      'smokeSpeedThreshold': '抽烟告警分级车速阈值',
      'driverReg': '驾驶员身份识别触发',
      'obligateBytes_3': '预留字段',
      'obligateBytes_2': '保留字段',
      // 音视频
      'rtsBitType': '实时流编码模式',
      'rtsResolution': '实时流分辨率',
      'rtsFrame': '实时流关键帧间隔',
      'rtsFps': '实时流目标帧率',
      'rtsBps': '实时流目标码率',
      'msBitType': '存储流编码模式',
      'msResolution': '存储流分辨率',
      'msFrame': '存储流关键帧间隔',
      'msFps': '存储流目标帧率',
      'msBps': '存储流目标码率',
      'osdCoverStr': 'OSD字幕叠加设置',
      'audioOn': '音频输出',
      // 激烈驾驶
      'violentEnable': '激烈驾驶告警使能',
      'acceleratedTime': '急加速告警时间阈值',
      'accelerated': '急加速告警重力加速度阈值',
      'moderateTime': '急减速告警时间阈值',
      'moderate': '急减速告警重力加速度阈值',
      'turnTime': '急转弯告警时间阈值',
      'turn': '急转弯告警重力加速度阈值',
      'idlingTime': '怠速告警时间阈值',
      'idlingSpeed': '怠速告警车速阈值',
      'idlingRotation': '怠速告警发动机转速阈值',
      'misfireTime': '异常熄火告警时间阈值',
      'misfireSpeed': '异常熄火告警车速阈值',
      'misfireRotation': '异常熄火告警发动 机转速阈值',
      'neutralSlide': '空挡滑行告警时间阈值',
      'neutralSpeed': '空挡滑行告警车速阈值',
      'neutralRotation': '空挡滑行告警发送机转速阈值',
      'excessTime': '发动机超转告警时间阈值',
      'excessSpeed': '发送机超转告警车速阈值',
      'excessRotation': '发送机超转告警发动机转速阈值',
      // 路况
      'volume': '告警提示音量',
      'roadPhotoResolution': '拍照分辨率',
      'roadVideoResolution': '视频录制分辨率',
      'checkEnable': '检测使能',
      'overTakeNum': '超车道占用拍照张数',
      'overTakeInterval': '超车道占用拍照间隔',
      'overTakeRecord': '超车道占用前后视频录制时间',
      'roadDamageNum': '道路破损拍照张数',
      'roadDamageInterval': '道路破损拍照间隔',
      'roadDamageRecord': '道路破损前后视频录制时间',
      // new
      'threshHold': '存储阈值',
      'duration': '持续时间',
      'start': '起始时间'
    },
    'RegionManage': {
      'fenceName': '电子围栏名称',
      'fenceId': '电子围栏ID',
      'fenceType': '电子围栏类型',
      'enableTime': '时间限制',
      'startTime': '开始时间',
      'endTime': '结束时间',
      'enableEveryday': '每天',
      'everydayStart': '开始时间',
      'everydayEnd': '结束时间',
      'enableSpeed': '超速限制',
      'highSpeed': '最高速度',
      'nightSpeed': '夜间限速',
      'duration': '持续时长',
      'enterDriver': '进报驾员',
      'outDriver': '出报驾员',
      'enterPlatform': '进报平台',
      'outPlatform': '出报平台',
      'enterCloseTcp': '进关通信',
      'enterGatherGnss': '进采GNNS'
    },
    'DriverPortrait': {
      'idCardOrPhone': '身份证号',
      'driverName': '驾驶员姓名',
      'driverAge': '驾驶员年龄',
      'driverDriveYear': '驾驶员驾龄',
      'certificate': '从业资格证号',
      'certificateExpire': '从业资格证失效日期',
      'driveDuration': '驾驶时长',
      'driveMileage': '驾驶里程',
      'adasAlarmCount': 'ADAS告警数',
      'dsmAlarmCount': 'DSM告警数',
      'fatigueAlarmCount': '疲劳驾驶告警数',
      'overSpeedAlarmCount': '超速驾驶告警数',
      'allDriveDuration': '总驾驶时长',
      'allDriveMileage': '总驾驶里程',
      'driveDaysCount': '驾驶天数',
      'averageDuration': '日平均驾驶时长',
      'averageMileage': '日平均驾驶里程',
      'allAdasAlarmCount': 'ADAS告警数',
      'allDsmAlarmCount': 'DSM告警数',
      'allFatigueAlarmCount': '疲劳驾驶告警数',
      'allOverSpeedAlarmCount': '超速驾驶告警数',
    },
    'IcDriver': {
      'licencePlate': '车辆号码',
      'licenceColor': '车牌颜色',
      'phone': '终端手机号',
      'time': '时间',
      'name': '驾驶员姓名',
      'operation': '操作',
      'result': 'IC卡读取结果',
      'credential': '从业资格证编码',
      'agency': '发证机构',
      'expire': '证件有效期',
      'idCard': '驾驶员身份证号'
    },
    'EntDailyRpt': {
      'deptName': '企业名称',
      'statement': '报表',
      'date': '日期'
    },
    'EntMonthlyRpt': {
      'deptName': '企业名称',
      'statement': '报表',
      'date': '日期'
    },
    'AlarmForward': {
      'uniName': '乌市告警转发',
      'id': '告警id',
      'key': '告警参数设置key',
      'deptId': '企业ID',
      'deptName': '企业名称',
      'licenceColor': '车牌颜色',
      'licencePlate': '车牌号',
      'alarmLevel': '告警等级',
      'alarmType': '告警类型',
      'startTime': '开始时间',
      'startLon': '开始经度',
      'startLat': '开始纬度',
      'startAddress': '开始地址',
      'endTime': '结束时间',
      'endLon': '结束经度',
      'endLat': '结束纬度',
      'endAddress': '结束地址',
      'speed': '速度',
      'limitSpeed': '限速',
      'maxSpeed': '最高速度',
      'serverState': '服务商处理状态',
      'serverResult': '服务商处理结果',
      'serverMeasures': '服务商处理措施',
      'serverContent': '服务商处理内容',
      'serverTime': '服务商处理时间',
      'serverUser': '服务商处理人员',
      'thirdState': '第三方处理状态',
      'thirdResult': '第三方处理结果',
      'thirdMeasures': '第三方处理措施',
      'thirdContent': '第三方处理内容',
      'thirdTime': '第三方处理时间',
      'thirdUser': '第三方处理人员',
      'companyState': '企业处理状态',
      'companyResult': '企业处理结果',
      'companyMeasures': '企业处理措施',
      'companyContent': '企业处理内容',
      'companyTime': '企业处理时间',
      'companyUser': '企业处理人员',
      'appealState': '申诉状态',
      'appealResult': '申诉结果',
      'appealTime': '申诉时间',
      'appealUser': '申诉人员',
      'auditResult': '审核结果',
      'auditTime': '审核时间',
      'auditUser': '审核人员',
      'appealAttach': '申诉附件',
      'companyAttach': '处理附件',
      'appealReason': '申诉理由',
      'auditContent': '审核内容',
      'speedUnit': '速度(km/h)',
      'maxSpeedUnit': '最高速度(km/h)',
      'limitSpeedUnit': '限速(km/h)',
      'duration': '持续时间',
      'alarmComplete': '持续状态',
      'numAttach': '实际附件数/预期附件数',
      'alarmOrigin': '告警来源',
      'ruleName': '告警规则',
      'serviceState': '服务状态'
    },
    'AlarmForwardOld': {
      'licencePlate': '车牌号码',
      'licenceColor': '车牌颜色',
      'infoId': '告警id',
      'infoContent': '告警内容',
      'warnTimeStart': '告警开始时间',
      'warnTimeEnd': '告警结束时间',
      'warnSrc': '告警来源',
      'warnType': '告警类型',
      'vec1': '卫星速度(km/h)',
      'vec2': '行驶速度(km/h)',
      'phone': '终端手机号',
      'status': '告警状态',
      'level': '告警等级',
      'lon': '经度',
      'lat': '纬度',
      'altitude': '高度'
    },
    'bdResourceDevice':{
      'deviceType':'终端类型',
      'category':'终端类型',
      'vendor':'厂商名称',
      'model':'终端型号',
      'deviceName':'终端名称',
      'uniqueId':'序列号',
      'imei':'IMEI',
      'bdChipSn':'北斗芯片序列号',
      'deptId':'所属机构',
      'deptName':'所属机构',
      'address':'设备安装位置',
      'regtime':'注册时间',
      'installdate':'安装日期',
      'refSource':'参考源输入',
      'clockSignal':'时钟输出信号',
      'iotCard':'物联网卡',
      'iotCardIds':'物联网卡',
      'phone':'联系电话',
      'facilityName':'监测区域名称',
      'facilityId':'监测区域名称',
      'monitoringSrc':'监测源名称',
      'longitude':'经度',
      'latitude':'纬度',
      'altitude':'高程',
      'dataPlan':'卡套餐',
      'cardCategory':'卡类型',
      'expire':'到期时间',
      'targetType':'监控对象',
      'targetName':'监控对象',
      'bdCard':'北斗卡号',
      'bdCardLevel':'北斗卡等级',
      'channelNum':'视频通道数',
      'channelIds': '视频设备编号',
      'workerName':'姓名',
      'specificity': '设备',
      'deviceNum': '赋码编号',
      'domain': '应用方向',
      'scenario': '应用场景',
      'iotNumbers': '物联网卡',
      'numbers': '物联网卡',
      'gnssMode': '定位模式',
      'terminalId': '终端编号',
      'assetType': '资产类型',
      'ownDeptType': '归属单位类型',
      'ownDeptName': '归属单位名称'
    },
    'bdCard':{
      'bdCard':'北斗卡号',
      'conpany':'所属机构',
      'status':'状态',
      'endDate':'到期时间',
      'startDate':'发卡日期',
      'messageTerminalId':'短报文序列号',
      'phone':'联系电话',
      'user':'登记用户',
    },
    'Facility': {
      'uniName': '基础设施',
      'category': '设施类型',
      'name': '设施名称',
      'address': '设施地址',
      'terminalType': '绑定终端类型',
      'bindTerminal': '绑定终端',
      'deptId': '所属机构',
      'deptName': '所属机构',
      'uniqueId': '设备ID',
      'model': '终端型号',
      'terminalCategories': '绑定终端类型',
      'longitude': '经度',
      'latitude': '纬度',
      'code': '编号',
      'deviceNum': '赋码编号'
    },
    'TerminalAssignment': {
      'uniName': '设备赋码',
      'deviceNo': '序列号',
      'batchNo': '批次',
      'imei': 'IMEI',
      'manufacturer': '设备厂商',
      'deviceCate': '设备类别',
      'deviceType': '设备类型',
      'deviceModel': '设备型号',
      'protocol': '协议',
      'sendDate': '送检日期',
      'sim': 'SIM卡号',
      'deviceNum': '赋码编号',
      'codeResult': '赋码结果',
      'codeState': '赋码结果',
      'companyName': '送检企业',
      'deviceState': '终端状态',
      'remark': '备注',
      'codeResMessage': '备注',
      'codeTime': '上次赋码成功时间',
      'codeMachine': '赋码机编号',
      'formalStr': '入网状态',
      'createTime': '创建时间',
      'chipSeq': '北斗芯片序列号'
    },
    'EncoderManagement': {
      'code_machine_num': '赋码机编号',
      'password': '密码',
      'manufacturer': '终端生产厂商',
      'create_time': '创建日期',
      'enable': '状态',
    },
    'InterfaceAdmin': {
      'uniName': '平台接口',
      'code': '接口编码',
      'url': '接口url',
      'name': '接口名称',
      'interfaceDesc': '接口功能描述',
      'serviceId': '所属业务服务',
    },
    'BusinessService': {
      'uniName': '业务服务',
      'code': '服务编码',
      'name': '服务名称',
      'serviceDesc': '服务功能描述'
    },
    'InterfaceOther': {
      'uniName': '第三方平台接口',
      'interCode': '接口编码',
      'systemType': '所属平台',
      'interType': '接口类型',
      'functionType': '接口功能',
      'requestType': '请求方式',
      'protocol': '协议',
      'ip': 'ip',
      'port': '端口',
      'url': '请求url',
      'isNeedToken': '是否需要token',
      'tokenInterCode': 'token编码'
    },
    'ParamList': {
      'uniName': '接口参数',
      'id': '参数id',
      'interfaceManageId': '对应接口id',
      'paramType': '参数类型',
      'paramKey': '参数key',
      'paramValue': '参数value',
      'note': '备注',
      'type': '模板类型',
      'hasToken': '是否带有token',
      'tokenStation': 'token位置',
      'tokenKey': 'token key',
      'tokenPrefix': 'token 前缀',
      'hasHeaderParam': 'header中是否带有参数',
      'paramsInHeader': 'header参数信息',
      'headerKey': '参数key',
      'headerValue': '参数值',
      'hasBodyParam': 'body中是否带有参数',
      'paramsInBody': 'body参数信息',
      'bodyKey': '参数key',
      'bodyValue': '参数值',
      'hasUrlParam': 'url中是否带有参数',
      'paramsInUrl': 'url参数信息',
      'urlKey': '参数key',
      'urlValue': '参数值',
      'dataStation': '报文所在位置',
      'dataKey': '报文 key',
      'hasAuth': '是否带有认证',
      'username': 'username',
      'password': 'password',
      'accessTokenKey': '返回token报文中access_token的key',
      'accessTokenDuration': 'token的有效期(单位：秒)',
    },
    'Datascope': {
      'uniName': '数据权限',
      'settingName': '数据权限配置',
      'name': '菜单名称',
      'path': '路由地址',
      'source': '菜单图标',
      'code': '菜单编号',
      'category': '菜单类型',
      'alias': '菜单别名',
      'action': '按钮功能',
      'sort': '菜单排序',
      'isOpen': '新窗口',
      'scopeName': '权限名称',
      'resourceCode': '权限编号',
      'scopeColumn': '权限字段',
      'scopeType': '规则类型',
      'scopeTypeName': '规则类型',
      'scopeField': '可见字段',
      'scopeClass': '权限类名',
      'scopeValue': '规则值',
      'remark': '备注'
    },
    'Apiscope': {
      'uniName': '接口权限',
      'settingName': '接口权限配置',
      'name': '菜单名称',
      'path': '路由地址',
      'source': '菜单图标',
      'code': '菜单编号',
      'category': '菜单类型',
      'alias': '菜单别名',
      'action': '按钮功能',
      'sort': '菜单排序',
      'isOpen': '新窗口',
      'scopeName': '权限名称',
      'resourceCode': '权限编号',
      'scopePath': '权限路径',
      'scopeType': '接口类型',
      'scopeTypeName': '接口类型',
      'remark': '备注'
    },
    'Param': {
      'uniName': '参数',
      'paramName': '参数名称',
      'paramKey': '参数键名',
      'paramValue': '参数键值',
    },
    'Menu': {
      'uniName': '菜单',
      'name': '菜单名称',
      'path': '路由地址',
      'parentId': '上级菜单',
      'source': '菜单图标',
      'code': '菜单编号',
      'category': '菜单类型',
      'alias': '菜单别名',
      'isOpen': '新窗口',
      'sort': '菜单排序',
      'remark': '菜单备注',
    },
    'AwaitAllot': {
      'uniName': '待分配终端',
      'number': '序列号',
      'deptName': '所属机构',
      'categoryName': '终端类型',
      'targetFlag': '监管目标标识',
      'targetModel': '监管目标类型',
      'deviceNum': '绑定赋码编号'
    },
    'UpgradePatch': {
      'uniName': '升级包',
      'md5Hash': 'MD5校验码',
      'name': '文件名',
      'category': '软件类型',
      'version': '版本名称',
      'size': '文件大小',
      'deviceCategory': '终端类型',
      'deviceModel': '终端型号',
      'vendor': '制造商编号',
      'uploader': '上传用户',
      'uploadTime': '上传时间',
      'remark': '备注',
      'fileUrl': '安装包文件'
    },
    'UpgradeTask': {
      'uniName': '升级任务',
      'name': '任务名称',
      'upgradeMode': '升级包类型',
      'otaPackageName': '升级包名称',
      'version': '升级包版本',
      'deviceCategory': '终端类型',
      'deviceModel': '终端型号',
      'createTime': '创建时间',
      'upgradeTotal': '升级成功终端数/总终端数',
      'state': '任务状态',
      'creator': '创建者',
      'remark': '备注'
    },
    'UpgradeLog': {
      'uniName': '升级记录',
      'taskName': '升级任务名称',
      'deviceCategory': '终端类型',
      'deviceUid': '终端序列号',
      'otaPackageCategory': '升级包类型',
      'otaPackageName': '升级包名称',
      'oldVersion': '升级前版本',
      'newVersion': '升级后版本',
      'createTime': '创建时间',
      'upgradeTime': '升级时间',
      'state': '终端升级状态'
    }
  };

  if (test[className]) {
    if (test[className][value]) {
      return test[className][value];
    }
  }
  return '';
}

/**
 * @alias getLabel
 */
export default getLabel;
