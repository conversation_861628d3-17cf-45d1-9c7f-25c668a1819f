'use strict';
import defined from '../core/defined';

/**
 * 字符串处理帮助模块
 * @exports StringUtil
 * @alias StringUtil（字符串处理帮助）
 */
let StringUtil = {};

/**
 * 左边的字符串是否包含右边
 * @param {String|undefined} left
 * @param {String} right
 * @return {boolean}
 */
StringUtil.leftContainRight = function (left, right) {
  if (defined(left) && typeof left === 'string' && typeof right === 'string') {
    return left.indexOf(right) !== -1;
  } else {
    return false;
  }
};

/**
 * 生成一个随机的短id
 * @return {String}
 */
StringUtil.generateShortUid = function () {
  let guid;

  guid = 'xxxxxxxx4xxxyxxx'.replace(/[xy]/g, function (c) {
    let r = Math.random() * 16 | 0; let v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
  return guid;
};

/**
 * 生成一个长id
 * @return {String}
 */
StringUtil.generateLongUid = function () {
  let guid;

  guid = 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    let r = Math.random() * 16 | 0; let v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
  return guid;
};

/**
 * 生成一个guid
 * @return {String}
 */
StringUtil.generateGuid = function () {
  let guid;

  guid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    let r = Math.random() * 16 | 0; let v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
  return guid;
};

/**
 * 左边的字符串是否等于右边
 * @param {String|undefined} left
 * @param {String} right
 * @return {boolean}
 */
StringUtil.leftEqualsRight = function (left, right) {
  return StringUtil.leftContainRight(left, right) && StringUtil.leftContainRight(right, left);
};

/**
 * 获取最后n个截取的字符串
 * @param {String} originString
 * @param {Number} length
 * @return {String}
 */
StringUtil.getLastSliceString = function (originString, length) {
  if (typeof originString === 'string') {
    if (defined(length) && length > 0 && length < originString.length) {
      let originStringLength = originString.length;
      return originString.substring(originStringLength - length, originStringLength);
    } else {
      return originString;
    }
  } else {
    console.warn('StringUtil#sliceLastNString#error-->', originString, '] is not a string');
    return originString;
  }
};

export default StringUtil;
