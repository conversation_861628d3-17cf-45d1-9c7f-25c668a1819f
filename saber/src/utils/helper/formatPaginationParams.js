import defined from '@/utils/core/defined';
/**
 * 格式化请求后台的数据
 * @param {Object} param
 * @param {Number} [param.page] 页码
 * @param {Number} [param.size] 每页的数量
 * @return {Object}
 */
function formatPaginationParam (param) {
  if (defined(param)) {
    if (defined(param.page) && defined(param.size)) {
      param.current = param.page + 1;
      param.page = undefined;
      param.sort = undefined;
    }
  }
  return param;
}

export default formatPaginationParam;
