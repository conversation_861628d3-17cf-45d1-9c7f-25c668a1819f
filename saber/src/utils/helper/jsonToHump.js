import { isObject } from "lodash";
/**
 * 字符串的下划线格式转驼峰格式，eg：hello_world => helloWorld
 * @param {String} s 下划线命名的key值
 */
function underline2Hump (s) {
    return s.replace(/_(\w)/g, function (all, letter) {
      return letter.toUpperCase();
    });
  }
  
  /**
   * JSON对象的key值中的下划线转换为驼峰式
   * @description 请求的后台数据返回到前端后都要使用这个函数将go语言的下划线分隔转为驼峰
   * @param {Object} obj 对象，即json.parse的那个值
   * @return {*}
   */
  function jsonToHump (obj) {
    if (obj instanceof Array) {
      obj.forEach(function (v, i) {
        jsonToHump(v);
      });
    } else if (obj instanceof Object || isObject(obj)) { // 适配bigint转换后的对象
      Object.keys(obj).forEach(function (key) {
        let newKey = underline2Hump(key);
        if (newKey !== key) {
          obj[newKey] = obj[key];
          delete obj[key];
        }
        jsonToHump(obj[newKey]);
      });
    }
    return obj;
  }
  
  export default jsonToHump;
  