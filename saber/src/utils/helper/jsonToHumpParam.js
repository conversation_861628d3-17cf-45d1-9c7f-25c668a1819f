/**
 * 字符串的下划线格式转驼峰格式，eg：hello_world => helloWorld
 * @param {String} s 下划线命名的key值
 */
function underline2Hump (s) {
  return s.replace(/_(\w)/g, function (all, letter) {
    return letter.toUpperCase();
  });
}

/**
   * JSON对象的key值中的下划线转换为驼峰式
   * @description 请求的后台数据返回到前端后都要使用这个函数将go语言的下划线分隔转为驼峰
   * @param {Object} obj 对象，即json.parse的那个值
   * @param {string} 不需要转换的参数
   * @return {*}
   */
function jsonToHump (obj, param = null) {
  if (obj instanceof Array) {
    obj.forEach(function (v, i) {
      jsonToHump(v, param);
    });
  } else if (obj instanceof Object) {
    Object.keys(obj).forEach(function (key) {
      let newKey = underline2Hump(key);
      if (newKey !== key) {
        obj[newKey] = obj[key];
        delete obj[key];
      }
      if (newKey !== param) {
        jsonToHump(obj[newKey], param);
      }
    });
  }
  return obj;
}

export default jsonToHump;
