import axios from 'axios';

/**
 * 打开添加表单时删除原有tab
 * @param tabName tab名
 */
export function openAddForm (_vue, tabName) {
  console.log('tabName', tabName);
  let options = _vue.$store.state.options;

  options.forEach((item, index, array) => {
    if (item.route === tabName) {
      console.log('清除的tab页', tabName);
      _vue.$store.commit('delete_tabs', tabName);
    }
  });
  setTimeout(function () {
    _vue.$router.push(tabName);
  }, 0);
}

/**
 * 重复打开一个页面时删除原有tab并打开一个新的
 * @param _obj tab参数
 */
export function updateTab (_vue, _obj) {
  console.log('_obj', _obj);
  let options = _vue.$store.state.options; let obj = _obj || null;
  if (obj.name !== undefined) {
    options.forEach((item, index, array) => {
      if (item.name === obj.name) {
        console.log('清除的tab页', item.route);
        _vue.$store.commit('delete_tabs', item.route);
      }
    });
    setTimeout(function () {
      _vue.$router.push(obj);
    }, 0);
  } else {
    console.log('携带的参数有误');
  }
}

/**
 * 复制对象
 * @param obj 被复制的对象
 */
export function cloneObjectFn (obj) { // 对象复制
  return JSON.parse(JSON.stringify(obj));
}

/**
 * 账号名模糊检索
 * @param {String} _val  keyword
 */
export async function getAccountOptions (_val) {
  let optionsReturn = [];
  if (_val !== '') {
    let parme = {
      account: _val + ''
    };
    await axios
      .post('', parme)
      .then((res) => {
        if (res.data.code === 0) {
          let dataArray = res.data.data;
          if (dataArray) {
            for (let i = 0; i < dataArray.length; i++) {
              optionsReturn.push({
                value: dataArray[i].account,
                label: dataArray[i].account
              });
            }
          }
        }
      });
  } else {
    optionsReturn = [];
  }
  return optionsReturn;
}

/**
 * 时间转换函数
 * @param {时间}} date
 * @param {格式} fmt
 */
export function formatDate (date, fmt) {
  var o = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds() // 毫秒
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (date.getFullYear() + '').substr(4 - RegExp.$1.length)
    );
  }
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      );
    }
  }
  return fmt;
}
/**
 *时间转化为天 时 分 秒
 * @param {对象}传入秒数
 * @param {返回信息} _message
 */
export function timeDifference (timeDate) {
  let time = Math.abs(timeDate);
  var day = parseInt(time / 60 / 60 / 24);
  var h = time - day * 24 * 60 * 60;
  var hours = parseInt(h / 60 / 60);
  var mins = h - hours * 60 * 60;
  var m = parseInt(mins / 60);
  var secs = mins - m * 60;
  var s = Math.round(secs);
  return `${day > 0 ? (day + '天') : ''}${hours > 0 ? (hours + '小时') : ''}${m > 0 ? (m + '分钟') : ''}${s}秒`;
}

export const localMemory = `BT@${location.port}`;


export const emptymap = {
  targetName: '--',
  altitude: (val) => {
    if(val < 0) {
      return '--'
    } else {
      return val
    }
  }
}

// 定义一些常量
/* eslint-disable */
var PI = 3.1415926535897932384626
var a = 6378245.0
var ee = 0.00669342162296594323
/**
 * WGS84转GCj02
 * @param lng
 * @param lat
 * @returns {*[]}
 */
// 批量转换
export function wgs84togcj02Batch(data, longitude = 'longitude', latitude = 'latitude') {
  let list = JSON.parse(JSON.stringify(data));
  for (let index = 0; index < list.length; index++) {
    const element = list[index];
    var lat = +element[latitude];
    var lng = +element[longitude];
    if (out_of_china(lng, lat)) {

    } else {
      var dlat = transformlat(lng - 105.0, lat - 35.0)
      var dlng = transformlng(lng - 105.0, lat - 35.0)
      var radlat = lat / 180.0 * PI
      var magic = Math.sin(radlat)
      magic = 1 - ee * magic * magic
      var sqrtmagic = Math.sqrt(magic)
      dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI)
      dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI)
      var mglat = lat + dlat
      var mglng = lng + dlng
      element[latitude] = mglat;
      element[longitude] = mglng;
    }
  }
  return list;
}
// 单个转换
export function wgs84togcj02(lng, lat) {
  var lat = +lat
  var lng = +lng
  if (out_of_china(lng, lat)) {
    return [lng, lat]
  } else {
    var dlat = transformlat(lng - 105.0, lat - 35.0)
    var dlng = transformlng(lng - 105.0, lat - 35.0)
    var radlat = lat / 180.0 * PI
    var magic = Math.sin(radlat)
    magic = 1 - ee * magic * magic
    var sqrtmagic = Math.sqrt(magic)
    dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI)
    dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI)
    var mglat = lat + dlat
    var mglng = lng + dlng
    return [mglng, mglat]
  }
}

var transformlat = function transformlat(lng, lat) {
  var lat = +lat
  var lng = +lng
  var ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng))
  ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0
  ret += (20.0 * Math.sin(lat * PI) + 40.0 * Math.sin(lat / 3.0 * PI)) * 2.0 / 3.0
  ret += (160.0 * Math.sin(lat / 12.0 * PI) + 320 * Math.sin(lat * PI / 30.0)) * 2.0 / 3.0
  return ret
}
var transformlng = function transformlng(lng, lat) {
  var lat = +lat
  var lng = +lng
  let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng))
  ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0
  ret += (20.0 * Math.sin(lng * PI) + 40.0 * Math.sin(lng / 3.0 * PI)) * 2.0 / 3.0
  ret += (150.0 * Math.sin(lng / 12.0 * PI) + 300.0 * Math.sin(lng / 30.0 * PI)) * 2.0 / 3.0
  return ret
}
/**
 * 判断是否在国内，不在国内则不做偏移
 * @param lng
 * @param lat
 * @returns {boolean}
 */
var out_of_china = function out_of_china(lng, lat) {
  lat = +lat
  lng = +lng
  // 纬度3.86~53.55,经度73.66~135.05
  return !(lng > 73.66 && lng < 135.05 && lat > 3.86 && lat < 53.55)
}

/**
 * GCj02转WGS84
 * @param lng
 * @param lat
 * @returns {*[]}
 */
export function gcj02towgs84(lng, lat) {
  var lat = +lat
  var lng = +lng
  if (out_of_china(lng, lat)) {
    return [lng, lat]
  } else {
    var dlat = transformlat(lng - 105.0, lat - 35.0)
    var dlng = transformlng(lng - 105.0, lat - 35.0)
    var radlat = lat / 180.0 * PI
    var magic = Math.sin(radlat)
    magic = 1 - ee * magic * magic
    var sqrtmagic = Math.sqrt(magic)
    dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI)
    dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI)
    var mglat = lat + dlat
    var mglng = lng + dlng
    return [lng * 2 - mglng, lat * 2 - mglat]
  }
}
