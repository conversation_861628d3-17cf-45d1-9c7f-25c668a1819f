/**
 * Created by PanJiaChen on 16/11/18.
 */

/**
 * @param {string} path
 * @returns {Boolean}
 */
import getLabel from '@/utils/getLabel';

export function isExternal (path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername (str) {
  const validMap = ['admin', 'editor'];
  return validMap.indexOf(str.trim()) >= 0;
}

/**
 * @param {string} url
 * @returns {Boolean}
 */
export function validURL (url) {
  const reg = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/;
  return reg.test(url);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validLowerCase (str) {
  const reg = /^[a-z]+$/;
  return reg.test(str);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUpperCase (str) {
  const reg = /^[A-Z]+$/;
  return reg.test(str);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validAlphabets (str) {
  const reg = /^[A-Za-z]+$/;
  return reg.test(str);
}

/**
 * @param {string} email
 * @returns {Boolean}
 */
/* eslint-disable */
export function validEmail (email) {
  const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return reg.test(email);
}
/* eslint-enable */

export function isvalidPhone (phone) {
  const reg = /^1[0-9]\d{9}$/;
  return reg.test(phone);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function isString (str) {
  if (typeof str === 'string' || str instanceof String) {
    return true;
  }
  return false;
}

/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export function isArray (arg) {
  if (typeof Array.isArray === 'undefined') {
    return Object.prototype.toString.call(arg) === '[object Array]';
  }
  return Array.isArray(arg);
}

/**
 * 是否合法IP地址
 * @param rule
 * @param value
 * @param callback
 */
export function validateIP (rule, value, callback) {
  if (value === '' || value === undefined || value == null) {
    callback();
  } else {
    const reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
    if ((!reg.test(value)) && value !== '') {
      callback(new Error('请输入正确的IP地址'));
    } else {
      callback();
    }
  }
}

/* 是否手机号码或者固话 */
export function validatePhoneTwo (rule, value, callback) {
  const reg = /^((0\d{2,3}-\d{7,8})|(1\d{10}))$/;
  if (value === '' || value === undefined || value == null) {
    callback();
  } else {
    if ((!reg.test(value)) && value !== '') {
      callback(new Error('请输入正确的电话号码或者固话号码'));
    } else {
      callback();
    }
  }
}

/* 是否固话 */
export function validateTelephone (rule, value, callback) {
  const reg = /0\d{2}-\d{7,8}/;
  if (value === '' || value === undefined || value == null) {
    callback();
  } else {
    if ((!reg.test(value)) && value !== '') {
      callback(new Error('请输入正确的固话（格式：区号+号码,如010-1234567）'));
    } else {
      callback();
    }
  }
}

/* 是否手机号码 */
export function validatePhone (rule, value, callback) {
  const reg = /^[1][0-9]{10}$/;
  if (value === '' || value === undefined || value == null) {
    callback();
  } else {
    if ((!reg.test(value)) && value !== '') {
      callback(new Error('请输入正确的电话号码'));
    } else {
      callback();
    }
  }
}

/* 是否身份证号码 */
export function validateIdNo (rule, value, callback) {
  const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  if (value === '' || value === undefined || value == null) {
    callback();
  } else {
    if ((!reg.test(value)) && value !== '') {
      callback(new Error('请输入正确的身份证号码'));
    } else {
      callback();
    }
  }
}

/* 是否手机号码或者固话(新) */
export function validatePhoneThree (rule, value, callback) {
  const reg = /^((0\d{2,3}-\d{7,8})|(1\d{10}))$/;
  if (value === '' || value === undefined || value == null) {
    callback(new Error('请输入正确的电话号码或者固话号码'));
  } else {
    if ((!reg.test(value)) && value !== '') {
      callback(new Error('请输入正确的电话号码或者固话号码'));
    } else {
      callback();
    }
  }
}

// 部分是北斗园区 公务车 迁移过来的正则
// 座机(国内)
export const telPhone = /^\d{3}-\d{8}$|^\d{4}-\d{7,8}$/;
// 中文，大写，小写，数字
export const invalidSymbol = /^[\u4E00-\u9FA5A-Za-z0-9]+$/;
// 由数字、26个英文字母或者下划线组成的字符串
export const noChineseSymbol = /^\w+$/;
// 电子邮件
export const email = /(^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$)/;
// 有1~3位小数的正实数
export const float3pot = /^[0-9]+(.[0-9]{1,3})?$/;
// 身份证(1/2代)
export const idCard = /^\d{6}((((((19|20)\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|(((19|20)\d{2})(0[13578]|1[02])31)|((19|20)\d{2})02(0[1-9]|1\d|2[0-8])|((((19|20)([13579][26]|[2468][048]|0[48]))|(2000))0229))\d{3})|((((\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|((\d{2})(0[13578]|1[02])31)|((\d{2})02(0[1-9]|1\d|2[0-8]))|(([13579][26]|[2468][048]|0[048])0229))\d{2}))(\d|X|x)$/;
// 非0的正整数
export const posiIntegers = /^[1-9]\d$/;
// 车牌号(新能源)
export const carLicence = /^([测临京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-HJ-NP-Z](([DF][A-HJ-NP-Z0-9][0-9]{4})|([0-9]{5}[DF]))|[测临京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-HJ-NP-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}([黄蓝黑白绿]?|[黄][绿]|[浓][黄]))$/;
// 手机号码
export const mobile = /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[189]))\d{8}$/;
// 包含特殊字符
export const specialCharacter = new RegExp('[`~!@#$^&*()=|{}\':;\',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“\'。，、？]');
// 包含空格
export const hasBlank = /\s+/;
// 密码校验【等保要求】必须8到20位数字、大小写字母组合
export const passwordRule = /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[a-zA-Z0-9]{8,20}$/;
// 密码校验【等保要求】必须8到20位数字、大小写字母和特殊符号组合
export const passwordRuleUpdate = /^(?=.*[_`%~!@#$^&*()=|{}:;,\[\].<>/?~！@#￥……&*（）——|{}【】‘；：”“\'。，、？])(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[a-zA-Z0-9a-zA-z0-9_`%~!@#$^&*()=|{}:;,\[\].<>/?~！@#￥……&*（）——|{}【】‘；：”“\'。，、？]{8,20}$/;
// 20-200之间的整数
export const fuelNumber = /^2[0-9]$|^[2-9]\d$|^1\d{2}$/;
// 仅包含数字
export const onlyNumber = /^\d{1,}$/;

export const phoneNum =  /^((0\d{2,3}-\d{7,8})|(1\d{10}))$/

export const idNum = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;

/**
 * 验证规则函数
 * @param {String} className  '@/utils/getLabel'数据类型名，如"Driver"
 * @param {String} labelName  '@/utils/getLabel'字段名，如"idcard"
 * @param {String} type  正则表达式类型名，详见上方，如身份证"idCard"，如需增加正则表达式，可自行在上方添加表达式，并在以下函数中给validateType对象添加相应的类型名
 * @param {String} errorInfo  自定义错误提示信息，默认值为label格式错误
 */
export function validateRules (className, labelName, type, errorInfo) {
  const label = getLabel(className, labelName);
  const validateRule = (rule, value, callback) => {
    if (value === null || value === undefined || (typeof value === 'string' && value.trim() === '')) {
      callback(new Error(`${label}不能为空`));
    } else if (type) {
      const validateType = {
        telPhone,
        invalidSymbol,
        noChineseSymbol,
        email,
        float3pot,
        idCard,
        posiIntegers,
        carLicence,
        mobile,
        specialCharacter,
        hasBlank,
        passwordRule,
        fuelNumber,
        onlyNumber
      };
      const tips = errorInfo || `${label}格式错误`;
      const result = validateType[type].test(value);
      result ? callback() : callback(new Error(`${tips}`));
    } else {
      callback();
    }
  };
  return validateRule;
}
