import getLabel from '../../utils/getLabel';
import { Message } from 'element-ui'

// 非空验证
const validateFn = function (module = 'Vehicle', key) {
  return function (rule, value, callback) {
    const baseVal = !!(typeof value !== 'object' && value!==null && value);
    const arrVal = !!(Array.isArray(value) && value.length);
    const objVal = !!(typeof value === 'object' && value!==null && Object.keys(value).length !== 0);
    if (baseVal || arrVal || objVal || value === 0) {
      callback();
    } else {
      callback(new Error(getLabel(module, key) + '不能为空'));
    }
  };
};

// id验证Driver
const validateId = function (rule, value, callback) {
  if (!value && value !== 0) {
    callback(new Error(getLabel('Driver', this) + '不能为空'));
  } else if (!/^[0-9]{1,10}$/.test(value)) {
    callback(new Error('请输入10位以内纯数字'));
  } else {
    callback();
  }
};

// 7位id验证
const validate7Id = function (module = 'Vehicle', key) {
  return function (rule, value, callback) {
    if (!value) {
      callback();
    } else if (value && value.length !== 7) {
      callback(new Error('请输入7位数字字母'));
    } else if (!/^[A-Za-z0-9]+$/.test(value)) {
      callback(new Error('请输入7位数字字母'));
    } else {
      callback();
    }
  };
};

// 密码验证Driver
const validatePwd = function (rule, value, callback) {
  if (!value && value !== 0) {
    callback(new Error(getLabel('Driver', this) + '不能为空'));
  } else if (!/^.{1,8}$/.test(value)) {
    callback(new Error(getLabel('Driver', this) + '最长支持8位'));
  } else {
    callback();
  }
};
// 序列号(terminalId)Driver
const validateTid = function (module = 'Vehicle', key) {
  return function (rule, value, callback) {
    if (!value && value !== 0) {
      callback(new Error(getLabel(module, key) + '不能为空'));
    } else if (!/^[1-9]\d{0,11}$/.test(value)) {
      callback(new Error('请输入12位以内纯数字且首位不能为0'));
      let hasMsg = document.getElementsByClassName('el-message')[0]
      if(!hasMsg) {
        Message.error(`${getLabel(module, key)}请输入12位以内纯数字且首位不能为0`)
      }
    } else {
      callback();
    }
  };
};

export {
  validateFn,
  validateId,
  validateTid,
  validatePwd,
  validate7Id
};
