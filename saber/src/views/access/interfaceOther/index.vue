<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            v-permission="['admin','interfaceOther:edit','interfaceOther:del']"
            width="240"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetail(scope.row)"
                  >
                    详情
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    @click="toEditParams(scope.row)"
                  >
                    编辑接口参数
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <!-- 接口编码 -->
          <el-table-column
            v-if="columns.visible('interCode')"
            :label="getLabel('interCode')"
            prop="interCode"
            min-width="120"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 所属平台 -->
          <el-table-column
            v-if="columns.visible('systemType')"
            :label="getLabel('systemType')"
            prop="systemType"
            min-width="120"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ systemTypeObj[scope.row.systemType] }}
            </template>
          </el-table-column>
          <!-- 接口类型 -->
          <el-table-column
            v-if="columns.visible('interType')"
            :label="getLabel('interType')"
            prop="interType"
            width="130"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ interTypeObj[scope.row.interType] }}
            </template>
          </el-table-column>
          <!-- 接口功能 -->
          <el-table-column
            v-if="columns.visible('functionType')"
            :label="getLabel('functionType')"
            prop="functionType"
            width="120"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ functionTypeObj[scope.row.functionType] }}
            </template>
          </el-table-column>
          <!-- 请求方式 -->
          <el-table-column
            v-if="columns.visible('requestType')"
            :label="getLabel('requestType')"
            prop="requestType"
            width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ requestTypeObj[scope.row.requestType] }}
            </template>
          </el-table-column>
          <!-- 协议 -->
          <el-table-column
            v-if="columns.visible('protocol')"
            :label="getLabel('protocol')"
            prop="protocol"
            width="70"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- ip -->
          <el-table-column
            v-if="columns.visible('ip')"
            :label="getLabel('ip')"
            prop="ip"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 端口 -->
          <el-table-column
            v-if="columns.visible('port')"
            :label="getLabel('port')"
            prop="port"
            width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 请求url -->
          <el-table-column
            v-if="columns.visible('url')"
            :label="getLabel('url')"
            prop="url"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 是否需要token -->
          <el-table-column
            v-if="columns.visible('isNeedToken')"
            :label="getLabel('isNeedToken')"
            prop="isNeedToken"
            width="120"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ whetherTypeObj[scope.row.isNeedToken] }}
            </template>
          </el-table-column>
          <!-- token编码 -->
          <el-table-column
            v-if="columns.visible('tokenInterCode')"
            :label="getLabel('tokenInterCode')"
            prop="tokenInterCode"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
      <!--表单渲染-->
      <eForm
        :dict="dict"
        :is-detail.sync="isDetail"
        :function-type-options="functionTypeOptions"
        :inter-type-options="interTypeOptions"
        :request-type-options="requestTypeOptions"
        :whether-type-options="whetherTypeOptions"
        :system-type-options="systemTypeOptions"
      />
      <ParamList
        ref="paramList"
        :interface-manage-id="interfaceManageId"
        :dialog-visible.sync="dialogParamVisible"
      />
    </div>
  </basic-container>
</template>

<script>
import crudInterfaceOther, { queryInterDict } from '@/api/access/interfaceOther';
import eForm from './module/form';
import ParamList from './module/paramList';
import CRUD, { presenter, header } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import pagination from '@/components/Crud/Pagination';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import HeadCommon from '@/components/formHead/headCommon.vue';
import { listSystem } from "@/api/log/api.js";

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('InterfaceOther', 'uniName'), // 第三方平台接口
  crudMethod: { ...crudInterfaceOther }
});

export default {
  name: 'InterfaceOther',
  components: { eForm, crudOperation, udOperation, pagination, HeadCommon, ParamList },
  mixins: [presenter(crud), header()],
  data () {
    return {
      permission: {
        add: ['admin', 'interfaceOther:add'],
        edit: ['admin', 'interfaceOther:edit'],
        del: ['admin', 'interfaceOther:del'],
        view: ['admin', 'interfaceOther:view']
      },
      headConfig: {
        item: {
          1: {
            name: '接口编码',
            type: 'input',
            value: 'interCode',
          },
          2: {
            name: '接口类型',
            type: 'select',
            value: 'interType',
            options: []
          },
          3: {
            name: '接口功能',
            type: 'select',
            value: 'functionType',
            options: []
          }
        },
        button: {
        }
      },
      isDetail: false,
      functionTypeOptions: [],
      functionTypeObj: {},
      interTypeOptions: [],
      interTypeObj: {},
      requestTypeOptions: [],
      requestTypeObj: {},
      whetherTypeOptions: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' }
      ],
      whetherTypeObj: {
        Y: '是',
        N: '否'
      },
      systemTypeOptions: [],
      systemTypeObj: {},
      dialogParamVisible: false,
      interfaceManageId: null
    };
  },
  created () {
    this.getInterDictList();
  },
  methods: {
    toEditParams (row) {
      this.interfaceManageId = row.id;
      this.dialogParamVisible = true;
    },
    getInterDictList () {
      // 接口功能字典
      queryInterDict('FUNCTION_TYPE').then(res => {
        this.functionTypeOptions = res.data?.map(item => ({
          label: item.dictValue,
          value: item.dictKey
        }));
        this.functionTypeObj = this.functionTypeOptions.reduce((acc, curr) => {
          acc[curr.value] = curr.label;
          return acc;
        }, {});
        this.headConfig.item['3'].options = this.functionTypeOptions;
      });
      // 接口类型字典
      queryInterDict('INTER_TYPE').then(res => {
        this.interTypeOptions = res.data?.map(item => ({
          label: item.dictValue,
          value: item.dictKey
        }));
        this.interTypeObj = this.interTypeOptions.reduce((acc, curr) => {
          acc[curr.value] = curr.label;
          return acc;
        }, {});
        this.headConfig.item['2'].options = this.interTypeOptions;
      });
      // 请求方式字典
      queryInterDict('REQUEST_TYPE').then(res => {
        this.requestTypeOptions = res.data?.map(item => ({
          label: item.dictValue,
          value: item.dictKey
        }));
        this.requestTypeObj = this.requestTypeOptions.reduce((acc, curr) => {
          acc[curr.value] = curr.label;
          return acc;
        }, {});
      });
      // 所属平台
      listSystem().then((res) => {
        this.systemTypeOptions = res.data.data?.map(item => ({
          label: item.systemName,
          value: item.id
        }));
        this.systemTypeObj = this.systemTypeOptions.reduce((acc, curr) => {
          acc[curr.value] = curr.label;
          return acc;
        }, {});
      });
    },
    // 详情
    toDetail (data) {
      this.isDetail = true;
      this.crud.toEdit(data);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('InterfaceOther', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('InterfaceOther', value);
    }
  }
};
</script>

<style lang="less" scoped>

</style>
