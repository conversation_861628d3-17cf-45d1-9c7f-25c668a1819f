<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="btnShow ? crud.status.title : '查看'"
    :validate-on-rule-change="false"
    append-to-body
    width="65%"
  >
    <el-form
      ref="form"
      :model="form"
      size="small"
      class="rewriting-form-disable"
      :hide-required-asterisk="!btnShow"
      label-width="195px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <el-form-item
            label="是否调用该平台接口"
            prop="isCallInterface"
          >
            <el-radio-group
              v-model="form.isCallInterface"
              :disabled="!crud.status.title.includes('新增')"
            >
              <el-radio :label="0">不调用</el-radio>
              <el-radio :label="1">调用</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="平台名称"
            prop="systemName"
            :rules="[{ required: true, trigger: 'blur' }]"
          >
            <el-input
              v-model.trim="form.systemName"
              placeholder="请输入平台名称"
              maxlength="32"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="部门"
            prop="deptId"
            :rules="[{ required: true, trigger: 'blur' }]"
          >
            <DeptFormMultiSelect
              ref="deptIdsRef"
              v-model="deptIds"
              :disabled="!btnShow"
              :is-show="crud.status.cu > 0"
              placeholder="请选择部门"
              size="small"
              check-strictly
              @input="validateTreeSelect('deptIds')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="平台说明"
            prop="note"
          >
            <el-input
              v-model.trim="form.note"
              placeholder="请输入平台说明"
              maxlength="256"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="平台联系人"
            prop="contactName"
          >
            <el-input
              v-model.trim="form.contactName"
              placeholder="请输入平台联系人"
              maxlength="32"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="联系方式"
            prop="contactPhone"
          >
            <el-input
              v-model.trim="form.contactPhone"
              placeholder="请输入联系方式"
              maxlength="32"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="返回的data域key"
            prop="resDataKey"
            :rules="[{ required: form.isCallInterface == 1, trigger: 'blur' }]"
          >
            <el-input
              v-model.trim="form.resDataKey"
              placeholder="请输入接口返回报文中的 data域 key"
              maxlength="32"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="返回的code域key"
            prop="resCodeKey"
            :rules="[{ required: form.isCallInterface == 1, trigger: 'blur' }]"
          >
            <el-input
              v-model.trim="form.resCodeKey"
              placeholder="请输入接口返回报文中的 code域 key"
              maxlength="32"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="返回的成功码"
            prop="successCodeValue"
            :rules="[{ required: form.isCallInterface == 1, trigger: 'blur' }]"
          >
            <el-input
              v-model.trim="form.successCodeValue"
              placeholder="请输入接口请求成功时返回的code值(多个时，中间使用逗号分隔)"
              maxlength="256"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="返回的message域key"
            prop="resMessageKey"
            :rules="[{ required: form.isCallInterface == 1, trigger: 'blur' }]"
          >
            <el-input
              v-model.trim="form.resMessageKey"
              placeholder="请输入接口返回报文中的 message域 key"
              maxlength="32"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="平台协议http推送接口code"
            prop="platHttpSendCode"
            :rules="[{ required: form.isCallInterface == 1, trigger: 'blur' }]"
          >
            <el-input
              v-model.trim="form.platHttpSendCode"
              placeholder="请输入平台协议http推送接口code"
              maxlength="32"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        v-if="btnShow"
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="btnShow"
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import { phoneNum } from '@/utils/validate';
import DeptFormMultiSelect from '@/components/select/DeptFormMultiSelect/DeptFormMultiSelect.vue';

const defaultForm = {
  isCallInterface: 0,
  systemName: '',
  deptId: '',
  note: '',
  contactName: '',
  contactPhone: '',
  resDataKey: '',
  resCodeKey: '',
  successCodeValue: '',
  resMessageKey: '',
  platHttpSendCode: '',
  id: ''
};
export default {
  components: { DeptFormMultiSelect },
  mixins: [form(defaultForm)],
  props: {
    btnShow: {
      type: Boolean,
      default: true
    },
    sysList: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      depts: [],
      loading: false,
      deptIds: []
    };
  },
  watch: {
    'crud.status.cu'(val) {
      if (val > 0) {
        if (this.form.deptId?.length) {
          this.deptIds = this.form.deptId.split(',');
        }
        else {
          this.deptIds = [];
        }
      }
    },
    'form.isCallInterface'() {
      this.$refs.form?.clearValidate();
    },
    deptIds(list) {
      this.form.deptId = list.join(',');
    }
  },
  methods: {
    /** "新建/编辑" 验证 - 之前 */
    [CRUD.HOOK.beforeValidateCU]() {
      this.$refs.form.validate((valid) => {
        if (!valid) {
          // this.validateTreeSelect('deptId');
        }
      });
    },
    /**
     * 提交前的验证
     */
    [CRUD.HOOK.afterValidateCU]() {
      if (this.form.contactPhone && !phoneNum.test(this.form.contactPhone)) {
        this.$message.error('联系电话不正确!');
        return false;
      }
      return true;
    },
    /** 点击取消之前*/
    [CRUD.HOOK.afterEditCancel]() {
      this.$emit('cancelCU');
    },
    // 新增/编辑前
    [CRUD.HOOK.beforeToCU]() {
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
          this.$refs[`deptIdsRef`].$refs[`deptIdsStrRef`].$children[0].$el.style.borderColor = '#BFBFBF';
        }
      });
    },
    /**
     * 验证treeSelect项
     * @param {String} item
     */
    validateTreeSelect(item) {
      this.$refs.form.validateField(item, (valid) => {
        this.$refs[`${item}Ref`].$refs[`${item}StrRef`].$children[0].$el.style.borderColor = valid !== '' ? '#f56c6c' : '#bfbfbf';
      });
    }
  }
};
</script>
<style lang="less" scoped>
/deep/ .el-input__inner {
  text-align: left;
}

/deep/ .el-form-item__label {
  font-weight: 400;
}

/deep/ .el-icon-delete {
  font-size: 30px;
}
</style>
