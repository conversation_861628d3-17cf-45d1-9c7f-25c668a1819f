<template>
  <basic-container>
    <div class="xh-container">
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="80px"
        />
      </div>
      <!--工具栏-->
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="batchPer"
          :download="false"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :data="crud.data"
          :cell-style="{ 'text-align': 'center' }"
          :max-height="tableMaxHeight"
          style="width: 100%; height: calc(100% - 47px)"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            v-permission="[
              'admin',
              'platformAdmin:edit',
              'platformAdmin:del',
            ]"
            width="280"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="{ row }">
              <udOperation
                :data="row"
                :permission="batchPer"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetails(row)"
                  >
                    详情
                  </el-button>
                  <el-button
                    v-permission="batchPer.bindObject"
                    type="text"
                    size="small"
                    @click="handleBind(row)"
                  >
                    绑定业务对象
                  </el-button>
                  <el-button
                    v-permission="batchPer.bindRules"
                    size="mini"
                    type="text"
                    @click="bindRulesHandle(row)"
                  >绑定报警规则
                  </el-button>
                </template>
              </udOperation>

            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('id')"
            label="ID"
            :show-overflow-tooltip="true"
            min-width="120"
            prop="id"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('systemName')"
            label="平台名称"
            :show-overflow-tooltip="true"
            min-width="150"
            prop="systemName"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('deptName')"
            label="部门"
            :show-overflow-tooltip="true"
            prop="deptName"
            min-width="150"
            :resizable="false"
          >
            <template slot-scope="{row}">
              {{ row.deptList?.length ? row.deptList.map(item => item.deptName).join(',') : '' }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('note')"
            label="平台说明"
            prop="note"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('contactName')"
            label="平台联系人"
            :show-overflow-tooltip="true"
            min-width="120"
            prop="contactName"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('contactPhone')"
            label="联系方式"
            :show-overflow-tooltip="true"
            min-width="120"
            prop="contactPhone"
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
    </div>
    <!--表单渲染-->
    <eForm
      :btnShow="btnShow"
      @cancelCU="cancel"
    />
    <bindList
      ref="tabTableList"
      :curInfo="curInfo"
    />
    <BindRules
      :dialog-visible.sync="dialogVisible"
      :current-data="currentData"
      @toQuery="crud.toQuery"
    />
  </basic-container>
</template>

<script>
import crudRealTimeMonitoring from "@/api/access/platformAdmin";
import CRUD, { presenter } from "@/components/Crud/crud";
import crudOperation from "@/components/Crud/CRUD.operation";
import pagination from "@/components/Crud/Pagination";
import HeadCommon from "@/components/formHead/headCommon.vue";
import { phoneNum } from "@/utils/validate";
import udOperation from "@/components/Crud/UD.operation";
import eForm from "./form";
import bindList from "./bindList.vue";
import BindRules from "./module/bindRules.vue";

// crud交由presenter持有
const crud = CRUD({
  title: "", // 车辆
  crudMethod: { ...crudRealTimeMonitoring }
});

export default {
  name: "PlatformAdmin",
  components: {
    crudOperation,
    pagination,
    HeadCommon,
    udOperation,
    eForm,
    bindList,
    BindRules
  },
  mixins: [presenter(crud)],
  // 数据字典
  dicts: [],
  data () {
    return {
      form: {},
      search: {},
      query: {},
      loading: true,
      headConfig: {
        initQuery: false,
        item: {
          1: {
            name: "平台名称",
            type: "input",
            value: "systemName",
          },
        },
        button: {},
      },
      batchPer: {
        add: ["admin", "platformAdmin:add"],
        edit: ["admin", "platformAdmin:edit"],
        del: ["admin", "platformAdmin:del"],
        bindRules: ["admin", "platformAdmin:bindRules"],
        bindObject: ["admin", "platformAdmin:bindObject"]
      },
      btnShow: true, // 显示确认取消按钮
      curInfo: {},
      dialogVisible: false,
      currentData: {}
    };
  },
  computed: {},
  activated () {
    // this.initData();
  },
  methods: {
    bindRulesHandle(row) {
      this.currentData = JSON.parse(JSON.stringify(row));
      this.dialogVisible = true;
    },
    handleBind (curInfo) {
      this.curInfo = curInfo;
      this.$refs.tabTableList.isShowTable = true;
    },
    toDetails (param) {
      crud.toEdit(param);
      this.btnShow = false;
    },
    cancel () {
      this.btnShow = true;
    },
    handlevalidate () {
      if (!phoneNum.test(this.form.contactPhone)) {
        this.$message.error("联系电话不正确!");
        return true;
      }
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-input-number .el-input__inner {
  text-align: left;
}

::v-deep
  .el-table--enable-row-hover
  .el-table__body
  tr:hover
  > td.el-table__cell {
  background-color: #fcf0c1;
}

::v-deep .el-table__row {
  td {
    box-sizing: border-box;
    height: 42px;
    padding: 0;
  }
}

/*滚动条中间滑动部分*/
/deep/ ::-webkit-scrollbar-thumb {
  background-color: rgba(125, 125, 125, 0.5);
}

// 覆盖公共样式, 防止alarmType搜索框被隐藏
/deep/ .el-card {
  overflow: inherit;
}
</style>
