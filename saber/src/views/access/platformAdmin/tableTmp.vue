<template>
  <div>
    <el-table
      ref="multipleTable"
      :data="tableData"
      tooltip-effect="dark"
      :row-key="getKey"
      current-row-key="obj_id"
      style="height: 450px"
      border
      resizable
      v-loading="loading"
      @selection-change="handleSelection"
    >
      <el-table-column
        :resizable="false"
        type="selection"
        reserve-selection
        width="50"
      />
      <el-table-column
        v-for="(item) in heads"
        :prop="item.columnName"
        :label="item.columnTitle"
        :key="item.columnName"
        align="center"
        :width="item.width || undefined"
        :sortable="item.sortable"
        :show-overflow-tooltip="true"
      />
    </el-table>
    <div class="bottom">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-size="query.size"
        :page-sizes="[10, 20, 30, 40, 50, 100]"
        :current-page="query.current"
        :total="totalPage"
        @current-change="pageChange"
        @size-change="sizeChangeHandler"
      />
    </div>
  </div>
</template>
<script>
import { getBindList, handleBind } from "@/api/access/platformAdmin";
export default {
  data () {
    return {
      tabPosition: 'left',
      query: {
        size: 10,
        current: 1,
      },
      loading: false
    };
  },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    heads: {
      type: Array,
      default: () => []
    },
    curInfo: {
      type: Object,
      default: () => ({})
    },
    isShowTable: {
      type: Boolean,
      default: false
    },
    totalPage: {
      type: Number,
      default: 0
    },
    value: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    isShowTable(val) {
      if(!val) {
        this.$refs.multipleTable?.clearSelection();
        this.selectedList = [];
      }
    },
    tableData() {
      if(this.tableData.length) {
        this.initPageData()
      }
    },
    value() {
      if(this.value.length) {
        this.initPageData()
      }
    }
  },
  created() {
    this.selectedList = []
  },
  methods: {
    initPageData() {
      // 页面采用保留勾选，每次碰到没有勾选的就勾选，继续未勾选的仍旧缓存
      if(this.value.length && this.tableData.length) {
        this.othesSelect = []
        this.value.forEach(id => {
          const obj = this.tableData.find( item => item.obj_id === id)
          if(obj) {
            if(!this.selectedList.find( item => item.obj_id === id)) {
              this.$nextTick(() => {
                this.$refs.multipleTable.toggleRowSelection(obj, true);
              })
            }
          }
          if(!this.selectedList.find( item => item.obj_id === id)) {
            this.othesSelect.push(id)
          }
        });
      }
    },
    selectable (row) {
      return row.bind_status == 1
    },
    getKey (row) {
      return row.obj_id
    },
    handleSelection (val) {
      let ids = []
      if(val?.length) {
        ids = val.map( item => item.obj_id)
      }
      this.selectedList = val
      const list = this.othesSelect || []
      // 由于数据可能存在重复，需要去重
      this.$emit('input', Array.from(new Set([...list, ...ids])))
    },
    pageChange (val) {
      this.query.current = val
      this.$emit('getData')
    },
    sizeChangeHandler (val) {
      this.query.size = val
      this.$emit('getData')
    },
  }
};
</script>