<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        />
        <!--表格渲染-->
        <u-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee'}"
          :data="crud.data"
          :treeConfig="{
            children: 'children',
            expandAll: false}"
          use-virtual
          row-id="id"
          :height="tableMaxHeight"
          row-height="54"
          :border="false"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <!--u-table大数据表格 你需要在列上指定某个列显示展开收起 treeNode属性-->
          <u-table-column
            type="selection"
            width="50"
          />
          <u-table-column
            v-permission="['admin','datascope:setting']"
            width="190"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    v-permission="permission.setting"
                    type="text"
                    size="small"
                    @click="handleRoleClick(scope.row)"
                  >
                    权限配置
                  </el-button>
                </template>
              </udOperation>
            </template>
          </u-table-column>
          <!-- 菜单名称 -->
          <u-table-column
            v-if="columns.visible('name')"
            :tree-node="true"
            :label="getLabel('name')"
            prop="name"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 路由地址 -->
          <u-table-column
            v-if="columns.visible('path')"
            :label="getLabel('path')"
            prop="path"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 菜单图标 -->
          <u-table-column
            v-if="columns.visible('source')"
            :label="getLabel('source')"
            prop="source"
            width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <i :class="scope.row.source"/>
            </template>
          </u-table-column>
          <!-- 菜单编号 -->
          <u-table-column
            v-if="columns.visible('code')"
            :label="getLabel('code')"
            prop="code"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 菜单别名 -->
          <u-table-column
            v-if="columns.visible('alias')"
            :label="getLabel('alias')"
            prop="alias"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 菜单排序 -->
          <u-table-column
            v-if="columns.visible('sort')"
            :label="getLabel('sort')"
            prop="sort"
            width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </u-table>
      </div>
      <DatascopeSetting
        ref="datascopeSetting"
        :dialog-title="dialogTitle"
        :row-code="rowCode"
        :dialog-visible.sync="dialogRoleVisible"
        :menu-id="menuId"
      />
    </div>
  </basic-container>
</template>

<script>
import crudDatascope, { getLazyMenuList } from '@/api/system/menuNew';
import CRUD, { presenter, header } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import HeadCommon from '@/components/formHead/headCommon.vue';
import DatascopeSetting from './module/datascopeSetting.vue';

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('Datascope', 'uniName'), // 数据权限
  crudMethod: { ...crudDatascope, pagination: getLazyMenuList }
});

export default {
  name: 'Datascope',
  components: { crudOperation, udOperation, HeadCommon, DatascopeSetting },
  mixins: [presenter(crud), header()],
  data () {
    return {
      permission: {
        add: ['admin', 'datascope:add'],
        edit: ['admin', 'datascope:edit'],
        del: ['admin', 'datascope:del'],
        view: ['admin', 'datascope:view'],
        setting: ['admin', 'datascope:setting'],
      },
      headConfig: {
        item: {
          1: {
            name: '菜单名称',
            type: 'input',
            value: 'name',
          },
          2: {
            name: '菜单编号',
            type: 'input',
            value: 'code',
          }
        },
        button: {
        }
      },
      dialogRoleVisible: false,
      dialogTitle: '',
      menuId: null,
      rowCode: null
    };
  },
  methods: {
    // 权限配置
    handleRoleClick (data) {
      this.dialogTitle = data.name;
      this.menuId = data.id;
      this.rowCode = data.code;
      this.dialogRoleVisible = true;
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Datascope', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('Datascope', value);
    }
  }
};
</script>

<style lang="less" scoped>

</style>
