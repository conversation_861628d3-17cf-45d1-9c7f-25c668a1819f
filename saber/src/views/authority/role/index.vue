<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        >
          <template slot="right">
            <el-button
              v-permission="permission.setting"
              class="filter-item"
              icon="el-icon-setting"
              size="small"
              @click="handleRoleClick"
            >
              权限设置
            </el-button>
          </template>
        </crudOperation>
        <!--表格渲染-->
        <u-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee'}"
          :data="crud.data"
          :treeConfig="{
            children: 'children',
            expandAll: false}"
          use-virtual
          row-id="id"
          :height="tableMaxHeight"
          row-height="54"
          :border="false"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <!--u-table大数据表格 你需要在列上指定某个列显示展开收起 treeNode属性-->
          <u-table-column
            type="selection"
            width="50"
          />
          <u-table-column
            v-permission="['admin','role:edit','role:del']"
            width="190"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetail(scope.row)"
                  >
                    详情
                  </el-button>
                </template>
              </udOperation>
            </template>
          </u-table-column>
          <!-- 角色名称 -->
          <u-table-column
            v-if="columns.visible('roleName')"
            :tree-node="true"
            :label="getLabel('roleName')"
            prop="roleName"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 角色别名 -->
          <u-table-column
            v-if="columns.visible('roleAlias')"
            :label="getLabel('roleAlias')"
            prop="roleAlias"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 角色排序 -->
          <u-table-column
            v-if="columns.visible('sort')"
            :label="getLabel('sort')"
            prop="sort"
            min-width="50"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </u-table>
      </div>
      <!--表单渲染-->
      <eForm
        :dict="dict"
        :is-detail.sync="isDetail"
      />
      <el-dialog
        v-dialog-drag
        title="角色权限配置"
        append-to-body
        :visible.sync="dialogVisible"
        width="345px"
      >
        <el-tabs type="border-card">
          <el-tab-pane label="菜单权限">
            <el-tree
              ref="treeMenu"
              :data="menuGrantList"
              show-checkbox
              node-key="id"
              :default-checked-keys="menuTreeObj"
              :props="props"
            />
          </el-tab-pane>
          <el-tab-pane label="数据权限">
            <el-tree
              ref="treeDataScope"
              :data="dataScopeGrantList"
              show-checkbox
              node-key="id"
              :default-checked-keys="dataScopeTreeObj"
              :props="props"
            />
          </el-tab-pane>
          <el-tab-pane label="接口权限">
            <el-tree
              ref="treeApiScope"
              :data="apiScopeGrantList"
              show-checkbox
              node-key="id"
              :default-checked-keys="apiScopeTreeObj"
              :props="props"
            />
          </el-tab-pane>
        </el-tabs>
        <span
          slot="footer"
          class="dialog-footer"
        >
          <el-button
            size="small"
            @click="dialogVisible = false"
          >取 消</el-button>
          <el-button
            type="primary"
            size="small"
            @click="handleSubmit"
          >确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </basic-container>
</template>

<script>
import crudRole from '@/api/system/role';
import eForm from './module/form';
import CRUD, { presenter, header } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import HeadCommon from '@/components/formHead/headCommon.vue';

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('Role', 'uniName'), // 角色
  crudMethod: { ...crudRole }
});

export default {
  name: 'Role',
  components: { eForm, crudOperation, udOperation, HeadCommon },
  mixins: [presenter(crud), header()],
  data () {
    return {
      permission: {
        add: ['admin', 'role:add'],
        edit: ['admin', 'role:edit'],
        del: ['admin', 'role:del'],
        view: ['admin', 'role:view'],
        setting: ['admin', 'role:setting']
      },
      headConfig: {
        item: {
          1: {
            name: '角色名称',
            type: 'input',
            value: 'roleName',
          },
          2: {
            name: '角色别名',
            type: 'input',
            value: 'roleAlias',
          }
        },
        button: {
        }
      },
      isDetail: false,
      dialogVisible: false,
      menuGrantList: [],
      dataScopeGrantList: [],
      apiScopeGrantList: [],
      menuTreeObj: [],
      dataScopeTreeObj: [],
      apiScopeTreeObj: [],
      props: {
        label: "title",
        value: "key"
      }
    };
  },
  methods: {
    handleSubmit() {
      const menuList = this.$refs.treeMenu.getCheckedKeys();
      const dataScopeList = this.$refs.treeDataScope.getCheckedKeys();
      const apiScopeList = this.$refs.treeApiScope.getCheckedKeys();
      const params = {
        roleIds: [this.crud.selections[0].id],
        menuIds: menuList,
        dataScopeIds: dataScopeList,
        apiScopeIds: apiScopeList
      };
      crudRole.grant(params).then(() => {
        this.dialogVisible = false;
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        this.crud.toQuery();
      });
    },
    handleRoleClick () {
      if (this.crud.selections.length !== 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      this.menuTreeObj = [];
      this.dataScopeTreeObj = [];
      this.apiScopeTreeObj = [];
      crudRole.grantTree().then(res => {
        this.menuGrantList = res.data?.menu;
        this.dataScopeGrantList = res.data?.dataScope;
        this.apiScopeGrantList = res.data?.apiScope;
        crudRole.getRole(this.crud.selections[0].id).then(res => {
          this.menuTreeObj = res.data?.menu;
          this.dataScopeTreeObj = res.data?.dataScope;
          this.apiScopeTreeObj = res.data?.apiScope;
          this.dialogVisible = true;
        });
      });
    },
    // 详情
    toDetail (data) {
      this.isDetail = true;
      this.crud.toEdit(data);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Role', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('Role', value);
    }
  }
};
</script>

<style lang="less" scoped>

</style>
