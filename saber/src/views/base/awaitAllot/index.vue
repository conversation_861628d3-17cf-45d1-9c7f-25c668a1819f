<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="110px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            v-permission="['admin','awaitAllot:change']"
            width="190"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetail(scope.row)"
                  >
                    详情
                  </el-button>
                  <el-button
                    v-permission="permission.change"
                    type="text"
                    size="small"
                    @click="toChange(scope.row)"
                  >
                    变更
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <!-- 序列号 -->
          <el-table-column
            v-if="columns.visible('number')"
            :label="getLabel('number')"
            prop="number"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 所属机构 -->
          <el-table-column
            v-if="columns.visible('deptName')"
            :label="getLabel('deptName')"
            prop="deptName"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 终端类型 -->
          <el-table-column
            v-if="columns.visible('categoryName')"
            :label="getLabel('categoryName')"
            prop="categoryName"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 绑定赋码编号 -->
          <el-table-column
            v-if="columns.visible('deviceNum')"
            :label="getLabel('deviceNum')"
            prop="deviceNum"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
      <!--表单渲染-->
      <eForm
        :dict="dict"
        :is-detail.sync="isDetail"
      />
      <AlterationForm
        ref="alterationForm"
        :dict="dict"
        :dialog-visible.sync="dialogAlterationVisible"
        :alteration-data="alterationData"
        @refresh="crud.refresh"
      />
    </div>
  </basic-container>
</template>

<script>
import crudAwaitAllot from '@/api/base/awaitAllot';
import eForm from './module/form';
import CRUD, { presenter, header } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import pagination from '@/components/Crud/Pagination';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import HeadCommon from '@/components/formHead/headCommon.vue';
import AlterationForm from '@/components/alterationForm/index.vue';

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('AwaitAllot', 'uniName'), // 待分配终端
  crudMethod: { ...crudAwaitAllot }
});

export default {
  name: 'AwaitAllot',
  components: { eForm, crudOperation, udOperation, pagination, HeadCommon, AlterationForm },
  mixins: [presenter(crud), header()],
  // 数据字典
  dicts: [
    'targetModel'
  ],
  data () {
    return {
      permission: {
        add: ['admin', 'awaitAllot:add'],
        edit: ['admin', 'awaitAllot:edit'],
        del: ['admin', 'awaitAllot:del'],
        view: ['admin', 'awaitAllot:view'],
        change: ['admin', 'awaitAllot:change']
      },
      headConfig: {
        item: {
          1: {
            name: '序列号',
            type: 'input',
            value: 'number',
          },
          2: {
            name: '绑定赋码编号',
            type: 'input',
            value: 'deviceNum',
          }
        },
        button: {
        }
      },
      dialogAlterationVisible: false,
      alterationData: {},
      isDetail: false
    };
  },
  methods: {
    // 变更
    toChange (row) {
      this.alterationData = {
        deviceType: row.deviceType,
        deviceId: row.deviceId
      };
      this.dialogAlterationVisible = true;
    },
    // 详情
    toDetail (data) {
      this.isDetail = true;
      this.crud.toEdit(data);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('AwaitAllot', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('AwaitAllot', value);
    }
  }
};
</script>

<style lang="less" scoped>

</style>
