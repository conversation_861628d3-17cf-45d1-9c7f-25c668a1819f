<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="isDetail ? '查看' : crud.status.title"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      :hide-required-asterisk="isDetail"
      label-width="120px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 序列号 -->
          <el-form-item
            :label="getLabel('number')"
            prop="number"
          >
            <el-input
              v-model.trim="form.number"
              :disabled="isDetail"
              :placeholder="getPlaceholder('number')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 所属机构 -->
          <el-form-item
            :label="getLabel('deptName')"
            prop="deptName"
          >
            <el-input
              v-model.trim="form.deptName"
              :disabled="isDetail"
              :placeholder="getPlaceholder('deptName')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 终端类型 -->
          <el-form-item
            :label="getLabel('categoryName')"
            prop="categoryName"
          >
            <el-input
              v-model.trim="form.categoryName"
              :disabled="isDetail"
              :placeholder="getPlaceholder('categoryName')"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        v-if="!isDetail"
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="!isDetail"
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

const defaultForm = {
  number: null,
  deptName: null,
  categoryName: null,
};
export default {
  components: { },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      rules: {

      }
    };
  },
  methods: {
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.$refs?.form?.clearValidate();
    },
    // 监听关闭事件
    closed () {
      this.$emit('update:isDetail', false);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('AwaitAllot', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('AwaitAllot', value);
    }
  }
};
</script>

<style lang="less" scoped>

</style>
