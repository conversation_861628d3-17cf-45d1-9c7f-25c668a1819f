<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="btnShow ? crud.status.title : '查看'"
    append-to-body
    width="60%"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      class="rewriting-form-disable"
      :hide-required-asterisk="!btnShow"
      label-width="150px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('number')"
            prop="number"
          >
            <el-input
              v-model.trim="form.number"
              placeholder="只可输入字母或数字"
              :disabled="!btnShow || crud.status.edit"
              maxlength="11"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('deptId')"
            prop="deptId"
          >
            <DeptFormSingleSelect
              ref="deptIdRef"
              v-model="form.deptId"
              :detail-name="form.deptName"
              :disabled="!btnShow"
              :is-show="crud.status.cu > 0"
              placeholder="请选择所属机构"
              size="small"
              @input="validateTreeSelect('deptId')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('model')"
            prop="model"
          >
            <single-select
              :options="dict.carriageModel"
              v-model="form.model"
              :placeholder="getPlaceholder('model', 'select')"
              clearable
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('size')+'(英尺)'"
            prop="size"
          >
            <el-input-number
              v-model="form.size"
              style="width: 100%"
              :precision="0"
              controls-position="right"
              :disabled="!btnShow"
              :min="0"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('maxGross')+'(kg)'"
            prop="maxGross"
          >
            <el-input-number
              v-model="form.maxGross"
              style="width: 100%"
              :precision="3"
              controls-position="right"
              :disabled="!btnShow"
              :min="0"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('tare')+'(kg)'"
            prop="tare"
          >
            <el-input-number
              v-model="form.tare"
              style="width: 100%"
              :precision="3"
              controls-position="right"
              :disabled="!btnShow"
              :min="0"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('net')+'(kg)'"
            prop="net"
          >
            <el-input-number
              v-model="form.net"
              style="width: 100%"
              :precision="3"
              controls-position="right"
              :disabled="!btnShow"
              :min="0"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('cuCap')+'(m^3)'"
            prop="cuCap"
          >
            <el-input-number
              v-model="form.cuCap"
              style="width: 100%"
              :precision="3"
              controls-position="right"
              :disabled="!btnShow"
              :min="0"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('length') + '(mm)'"
            prop="length"
          >
            <el-input-number
              v-model="form.length"
              style="width: 100%"
              :precision="0"
              controls-position="right"
              :disabled="!btnShow"
              :min="0"
            />
          </el-form-item>
        </div>

        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('height') + '(mm)'"
            prop="height"
          >
            <el-input-number
              v-model="form.height"
              style="width: 100%"
              :precision="0"
              controls-position="right"
              :disabled="!btnShow"
              :min="0"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        v-if="btnShow"
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="btnShow"
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from "@/components/Crud/crud";
import getLabel from "@/utils/getLabel";
import getPlaceholder from "@/utils/getPlaceholder";
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';
import SingleSelect from '@/components/select/DictSelect/DictSelectSingle';
const defaultForm = {
  number: null,
  deptId: null,
  deptName: null,
  model: null,
  size: null,
  maxGross: null,
  tare: null,
  net: null,
  cuCap: null,
  length: null,
  height: null,
  id: null,
};
export default {
  components: { DeptFormSingleSelect, SingleSelect },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      default: () => {
        return {};
      },
    },
    btnShow: {
      type: Boolean,
      default: true,
    },
    regTurn: {
      type: Object,
      default: () => {
        return {};
      },
    },
    regTurnM: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data () {
    return {
      rules: {
        number: {
          required: true,
          trigger: "input",
        }, // 车辆编号
        deptId: {
          required: true,
          trigger: "change",
        }, // 所属机构
        model: {
          required: true,
          trigger: "input",
        }
      },
      oldForm: {},
      depts: [],
    };
  },
  methods: {
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel("Carriage", value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value, type) {
      return getPlaceholder("Carriage", value, type);
    },
    /** 新建 - 之前 */
    [CRUD.HOOK.beforeToAdd] () {
      this.$nextTick(() => {
      });
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
          this.$refs[`deptIdsRef`].$refs[`deptIdStrRef`].$children[0].$el.style.borderColor = '#BFBFBF';
        }
      });
    },
    /** 点击取消之前*/
    [CRUD.HOOK.afterEditCancel] () {
      this.$emit("cancelCU");
    },
    /** "新建/编辑" 验证 - 之前 */
    [CRUD.HOOK.beforeValidateCU] () {
      this.$refs.form.validate((valid) => {
        if (!valid) {
        }
      });
    },
    /** 编辑 - 之后 */
    [CRUD.HOOK.afterToEdit] () {

    },
    /** 新建/编辑" 验证 - 之后 */
    [CRUD.HOOK.afterValidateCU] () {
      const list = Object.keys(this.regTurn);
      for (let i = 0; i < list.length; i++) {
        const key = list[i];
        if (this.form[key] && !this.regTurn[key].test(this.form[key])) {
          this.$message.error(this.regTurnM[key]);
          return false;
        }
      }
      return true;
    },
    /**
     * 验证treeSelect项
     * @param {String} item
     */
    validateTreeSelect (item) {
      this.$refs.form.validateField(item, (valid) => {
        this.$refs[`${item}Ref`].$refs[`${item}StrRef`].$children[0].$el.style.borderColor = valid !== '' ? '#F56C6C' : '#BFBFBF';
      });
    }
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-input__inner {
  text-align: left;
}
</style>
