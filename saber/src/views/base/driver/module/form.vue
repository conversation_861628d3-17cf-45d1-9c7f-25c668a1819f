<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="btnShow?crud.status.title:'查看'"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      class="rewriting-form-disable"
      :hide-required-asterisk="!btnShow"
      label-width="100px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('name')"
            prop="name"
          >
            <el-input
              v-model.trim="form.name"
              :placeholder="getPlaceholder('name')"
              maxlength="20"
              style="width: 100%;"
              :disabled="!btnShow"
              @input="e => form.name = validInput(e)"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('industry')"
            prop="industry"
          >
            <single-select
              :options="dict.bdmWokerPost"
              v-model="form.industry"
              :placeholder="getPlaceholder('industry', 'select')"
              clearable
              :disabled="!btnShow"
              @change="form.post=''"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.industry"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('post')"
            prop="post"
          >
            <single-select
              :options="wokerPost"
              v-model="form.post"
              :placeholder="getPlaceholder('post', 'select')"
              clearable
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('wkno')"
            prop="wkno"
          >
            <el-input
              v-model.trim="form.wkno"
              maxlength="18"
              show-word-limit
              :placeholder="getPlaceholder('wkno')"
              :disabled="!btnShow || crud.status.edit"
              @input="e => form.wkno = validInput(e)"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('phone')"
            prop="phone"
          >
            <el-input
              v-model.trim="form.phone"
              :placeholder="getPlaceholder('phone')"
              maxlength="20"
              style="width: 100%;"
              :disabled="!btnShow"
              @input="e => form.phone = validInput(e)"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('deptId')"
            prop="deptId"
          >
            <DeptFormSingleSelect
              ref="deptIdRef"
              v-model="form.deptId"
              :detail-name="form.deptName"
              :disabled="!btnShow"
              :is-show="crud.status.cu > 0"
              placeholder="请选择所属机构"
              size="small"
              @input="validateTreeSelect('deptId')"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        v-if="btnShow"
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="btnShow"
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import { validateRules, validatePhoneTwo } from '@/utils/validate';
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';
import SingleSelect from '@/components/select/DictSelect/DictSelectSingle';
const defaultForm = {
  name: '',
  wkno: null,
  post: null,
  industry: null,
  deptName: null,
  phone: null,
  deptId:  null,
  select:0
};
export default {
  components: {
    DeptFormSingleSelect,
    SingleSelect
  },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    btnShow: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      rules: {
        deptId: { required: true, trigger: 'change' }, // 车组
        wkno: {
          required: true,
          validator: validateRules('Driver', 'wkno'),
          trigger: 'blur'
        },
        name: {
          required: true,
          validator: validateRules('Driver', 'name'),
          trigger: 'blur'
        },
        industry: {
          required: true,
          validator: validateRules('Driver', 'industry'),
          trigger: 'change'
        },
        post: {
          required: true,
          validator: validateRules('Driver', 'post'),
          trigger: 'blur'
        },
        phone: [
          {
            required: true,
            trigger: 'blur'
          },
          {
            validator: validatePhoneTwo,
            trigger: 'blur'
          },
        ]// 终端电话
      },
      wokerPost: [],
      depts: []
    };
  },
  watch: {
    'form.industry': {
      handler(newVal) {
        const list = this.dict['bdmWokerPost'];
        this.wokerPost = list.find( item => item.value === newVal)?.children || [];
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    /** "新建/编辑" 验证 - 之前 */
    [CRUD.HOOK.beforeValidateCU] () {
      this.$nextTick(()=>{
        if (this.$refs.form) {
          if (this.form.phone && !/^((0\d{2,3}-\d{7,8})|(1\d{10}))$/.test(this.form.phone)) {
            this.$message.error('请输入正确的电话号码或者固话号码');
          }
        }
      });
    },
    [CRUD.HOOK.afterValidateCU] (){

    },
    /** 开始 "新建/编辑" - 之后 */
    [CRUD.HOOK.afterToCU] () {
      if (this.form.industry != null) this.form.industry = this.form.industry.toString();
      if (this.form.post != null) this.form.post = this.form.post.toString();
    },
    /** 点击取消之前*/
    [CRUD.HOOK.afterEditCancel] () {
      this.$emit('cancelCU');
    },
    // 新增/编辑前
    [CRUD.HOOK.beforeToCU]() {
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
          this.$refs[`deptIdRef`].$refs[`deptIdStrRef`].$children[0].$el.style.borderColor = '#BFBFBF';
        }
      });
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('Driver', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder(value, pre) {
      return getPlaceholder('Driver', value, pre);
    },
    // 监听关闭事件
    closed () {
      this.$emit('update:isDetail', false);
    },
    /**
     * 验证treeSelect项
     * @param {String} item
     */
    validateTreeSelect (item) {
      this.$refs.form.validateField(item, (valid) => {
        this.$refs[`${item}Ref`].$refs[`${item}StrRef`].$children[0].$el.style.borderColor = valid !== '' ? '#F56C6C' : '#BFBFBF';
      });
    },
  }
};
</script>
<style lang="less" scoped>
::v-deep .el-input__inner {
  text-align: left;
}

.subTitle ::v-deep .el-divider__text {
  font-size: 14px;
  font-weight: 700;
  color: #606266;
}

::v-deep .el-form-item__label {
  font-weight: 400;
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

::v-deep .avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

::v-deep .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 128px;
  height: 128px;
  line-height: 128px;
  text-align: center;
}

::v-deep .avatar {
  width: 128px;
  height: 128px;
  display: block;
}

::v-deep .el-upload-list__item-actions:hover {
  opacity: 1;
}

::v-deep .el-upload-list__item-actions {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  cursor: default;
  text-align: center;
  color: #ffffff;
  opacity: 0;
  font-size: 20px;
  background-color: rgba(255, 255, 255, 0.5);
  transition: opacity .3s;
}

::v-deep .el-upload-list__item-actions:hover span {
  display: inline-block;
}

::v-deep .upload-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -5px;
  margin-top: -15px;
  width: 30px;
  height: 30px;
}

::v-deep .el-icon-delete {
  font-size: 30px;
}

</style>
