<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="btnShow ? crud.status.title : '查看'"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      class="rewriting-form-disable"
      :hide-required-asterisk="!btnShow"
      label-width="140px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="人员姓名"
            prop="name"
          >
            <el-input
              v-model.trim="form.name"
              placeholder="请输入人员姓名"
              maxlength="20"
              style="width: 100%"
              :disabled="!btnShow"
              @input="(e) => (form.name = validInput(e))"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="所属机构"
            prop="deptId"
          >
            <DeptFormSingleSelect
              ref="deptIdRef"
              v-model="form.deptId"
              :detail-name="form.deptName"
              :disabled="!btnShow"
              :is-show="crud.status.cu > 0"
              placeholder="请选择所属机构"
              size="small"
              @input="validateTreeSelect('deptId')"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="从业类型"
            prop="industry"
          >
            <single-select
              :options="dict.bdmWokerPost"
              v-model="form.industry"
              placeholder="请选择从业类型"
              clearable
              :disabled="!btnShow"
              @change="form.post=''"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.industry"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="岗位类型"
            prop="post"
          >
            <single-select
              :options="wokerPost"
              v-model="form.post"
              placeholder="请选择岗位类型"
              clearable
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="联系电话"
            prop="phone"
          >
            <el-input
              v-model.trim="form.phone"
              placeholder="请输入联系电话"
              maxlength="20"
              style="width: 100%"
              :disabled="!btnShow"
              @input="(e) => (form.phone = validInput(e))"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="外派机构/公司"
            prop="companyId"
          >
            <el-select
                v-model="form.companyId"
                filterable
                remote
                reserve-keyword
                placeholder="请输入外派机构/公司"
                :remote-method="dealCompanySearch"
                allow-create
                :loading="loadingC"
                :disabled="!btnShow"
              >
                <el-option
                  v-for="item in companyList"
                  :key="item.id"
                  :label="item.deptName"
                  :value="item.id"
                >
                </el-option>
              </el-select>
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="工号/身份证号"
            prop="wkno"
          >
            <el-input
              v-model.trim="form.wkno"
              :placeholder="`请输入工号/身份证号`"
              maxlength="50"
              style="width: 100%"
              :disabled="!crud.status.title.includes('新增')"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <!-- 安装日期 -->
          <el-form-item
            label="生效日期"
            prop="validFrom"
          >
            <el-date-picker
              v-model="form.validFrom"
              type="date"
              placeholder="请选择生效日期"
              value-format="yyyy-MM-dd"
              :disabled="!btnShow"
              @blur="judge"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <!-- 安装日期 -->
          <el-form-item
            label="终止日期"
            prop="validTo"
          >
            <el-date-picker
              v-model="form.validTo"
              type="date"
              placeholder="请选择终止日期"
              value-format="yyyy-MM-dd"
              :disabled="!btnShow"
              @blur="judge"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        v-if="btnShow"
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="btnShow"
        :loading="btnLoading"
        type="primary"
        size="small"
        @click="handleSubmit"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from "@/components/Crud/crud";
import {
  idNum,
  phoneNum,
} from "@/utils/validate";
import { add, edit } from "@/api/base/expatriate.js";
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';
import { getDeptListByName } from "@/api/base/dept";
import SingleSelect from '@/components/select/DictSelect/DictSelectSingle';
import _ from "lodash";
const defaultForm = {
  wkno: "",
  company: null,
  validFrom: "",
  validTo: "",
  name: "",
  post: null,
  industry: null,
  phone: null,
  deptId: null,
  deptName: null,
  companyId: null,
  id: null,
};
export default {
  components: {
    DeptFormSingleSelect,
    SingleSelect
  },
  mixins: [form(defaultForm)],
  props: {
    jobType: {
      type: Array,
      required: true,
    },
    districtTreeData: {
      type: Array,
      required: true,
    },
    certificateType: {
      type: Array,
      required: true,
    },
    dict: {
      type: Object,
      required: true,
    },
    btnShow: {
      type: Boolean,
      default: true,
    }
  },
  data() {
    this.dealCompanySearch = _.debounce(this.remoteCompany, 200);
    return {
      rules: {
        name: {
          required: true,
          trigger: "blur",
        },
        wkno: [
          { required: true, trigger: "blur" },
        ],
        deptId: { required: true, trigger: "change" }, // 车组
        industry: {
          required: true,
          trigger: "change",
        },
        post: {
          required: true,
          trigger: "blur",
        },
        phone: [
          {
            required: true,
            trigger: "blur",
          },
        ], // 终端电话
        companyId: { required: true, trigger: "blur" }, // 车组
        validFrom: { required: true, trigger: "change" }, // 车组
        validTo: { required: true, trigger: "change" }, // 车组
      },
      wokerPost: [],
      depts: [],
      companyList: [],
      loading: false,
      loadingC: false,
      btnLoading: false
    };
  },
  watch: {
    "form.industry": {
      handler(newVal) {
        const list = this.dict['bdmWokerPost'];
        this.wokerPost = list.find( item => item.value === newVal)?.children || [];
      },
      deep: true,
      immediate: true,
    },
    'crud.status.cu'(val) {
      if(val > 0) {
        if(this.form.companyId || this.form.companyId === 0 ) {
          this.oldCompany = this.form.company
          this.companyList = [{
            deptName: this.form.company,
            id: this.form.companyId
          }]
        } else {
          this.companyList = []
          this.oldCompany = ""
          this.remoteCompany("")
        }
      }
    },
  },
  methods: {
    remoteCompany(query) {
      if (this.oldCompany && this.oldCompany == query) {
        return;
      }
      this.oldCompany = query || ''
      this.loadingC = true
      getDeptListByName(this.oldCompany).then(res => {
        this.companyList = res.data || []
        this.loadingC = false
      }).catch(() => {
        this.loadingC = false
      })
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const { validFrom, validTo, companyId, ...others } = this.form;
          if (!phoneNum.test(this.form.phone)) {
            this.$message.error("请输入正确的电话号码或者固话号码");
            return;
          }
          if ( +new Date(this.form.validFrom) > +new Date(this.form.validTo) ) {
            this.$message.error("生效日期不能大于终止日期");
            return;
          }
          const result = this.companyList.find( item => item.id === companyId);
          if(!result) {
            others.company = companyId;
            others.companyId = 0;
          } else {
            others.company = result?.deptName;
            others.companyId = companyId;
          }
          if(others.companyId == 0 && !idNum.test(this.form.wkno)) {
            this.$message.error("请输入正确的身份证号码");
            return;
          }
          this.btnLoading = true; // TODO 后续建议统一为CRUD
          const fun = this.form.id ? edit : add;
          fun({
            validFrom: this.$moment(validFrom).format("YYYY-MM-DD HH:mm:ss"),
            validTo: this.$moment(validTo).format("YYYY-MM-DD HH:mm:ss"),
            ...others,
          }).then((res) => {
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            this.form.companyId = null;
            this.crud.cancelCU();
            this.$emit('refresh');
          }).finally(() => {
            this.btnLoading = false;
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    [CRUD.HOOK.afterValidateCU]() {},
    /** 开始 "新建/编辑" - 之后 */
    [CRUD.HOOK.afterToCU]() {
      if (this.form.industry != null)
        this.form.industry = this.form.industry.toString();
      if (this.form.post != null) this.form.post = this.form.post.toString();
    },
    /** 点击取消之前*/
    [CRUD.HOOK.afterEditCancel]() {
      this.$emit("cancelCU");
    },
    // 新增/编辑前
    [CRUD.HOOK.beforeToCU]() {
      this.btnLoading = false;
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
          this.$refs[`deptIdRef`].$refs[`deptIdStrRef`].$children[0].$el.style.borderColor = '#BFBFBF';
        }
      });
    },
    // 监听关闭事件
    closed() {
      this.$emit("update:isDetail", false);
    },
    /**
     * 验证treeSelect项
     * @param {String} item
     */
    validateTreeSelect (item) {
      this.$refs.form.validateField(item, (valid) => {
        this.$refs[`${item}Ref`].$refs[`deptIdStrRef`].$children[0].$el.style.borderColor = valid !== '' ? '#F56C6C' : '#BFBFBF';
      });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-input__inner {
  text-align: left;
}

.subTitle /deep/ .el-divider__text {
  font-size: 14px;
  font-weight: 700;
  color: #606266;
}

/deep/ .el-form-item__label {
  font-weight: 400;
}

/deep/ .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

/deep/ .avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

/deep/ .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 128px;
  height: 128px;
  line-height: 128px;
  text-align: center;
}

/deep/ .avatar {
  width: 128px;
  height: 128px;
  display: block;
}

/deep/ .el-upload-list__item-actions:hover {
  opacity: 1;
}

/deep/ .el-upload-list__item-actions {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  cursor: default;
  text-align: center;
  color: #ffffff;
  opacity: 0;
  font-size: 20px;
  background-color: rgba(255, 255, 255, 0.5);
  transition: opacity 0.3s;
}

/deep/ .el-upload-list__item-actions:hover span {
  display: inline-block;
}

/deep/ .upload-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -5px;
  margin-top: -15px;
  width: 30px;
  height: 30px;
}

/deep/ .el-icon-delete {
  font-size: 30px;
}
</style>
