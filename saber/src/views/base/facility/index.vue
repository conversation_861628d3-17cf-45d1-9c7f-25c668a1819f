<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="110px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="batchPer"
          :download="false"
        >
          <template slot="right">
            <el-button
              v-permission="batchPer.imp"
              class="filter-item"
              icon="el-icon-upload2"
              size="small"
              @click="batchvisible = true"
            >
              导入
            </el-button>
            <el-button
              v-permission="batchPer.exp"
              :loading="crud.downloadLoading"
              class="filter-item"
              size="small"
              icon="el-icon-download"
              @click="handleExport"
            >
              导 出
            </el-button>
            <!-- 批量修改组织 -->
            <el-button
              v-permission="permission.updateDept"
              class="filter-item"
              size="small"
              icon="el-icon-download"
              @click="toUpdateDept"
            >
              修改组织
            </el-button>
          </template>
        </crudOperation>
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            v-if="columns.visible('code')"
            :label="getLabel('code')"
            prop="code"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            v-permission="['admin','facility:edit','facility:del']"
            width="190"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    v-permission="permission.bind"
                    type="text"
                    size="small"
                    class="table-button-add"
                    @click="onBindTerminal(scope.row)"
                  >
                    绑定终端
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetail(scope.row)"
                  >
                    详情
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <!-- 设施类型 -->
          <el-table-column
            v-if="columns.visible('categoryName')"
            :label="getLabel('category')"
            prop="categoryName"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 设施名称 -->
          <el-table-column
            v-if="columns.visible('name')"
            :label="getLabel('name')"
            prop="name"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 绑定终端类型 -->
          <el-table-column
            v-if="columns.visible('terminalCategories')"
            :label="getLabel('terminalCategories')"
            prop="terminalCategories"
            min-width="200"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('address')"
            :label="getLabel('address')"
            prop="address"
            min-width="200"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 所属机构 -->
          <el-table-column
            v-if="columns.visible('deptName')"
            :label="getLabel('deptName')"
            prop="deptName"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 绑定终端 -->
          <el-table-column
            v-if="columns.visible('bindTerminal')"
            :label="getLabel('bindTerminal')"
            prop="bindTerminal"
            min-width="90"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span
                class="active-label"
                @click="toTerminal(scope.row)"
              >
                查看终端
              </span>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
      <!--表单渲染-->
      <eForm
        :dict="dict"
        :is-detail.sync="isDetail"
      />
      <BatchImportWithDept
        :visible="batchvisible"
        mod="facility"
        @close="batchvisible = false"
        @getBatchData="getBatchData"
      />
      <!-- 批量修改组织 -->
      <DeptDialog
        :visible="updateDeptVisible"
        mod="driver"
        @close="updateDeptVisible = false"
        @getBatchData="getChoosedDept"
      />
      <msgDialog
        ref="msgDialog"
        :msg-data="msgData"
      />
      <TerminalDialog
        :dialog-visible.sync="dialogVisible"
        :facility-id="facilityId"
        :dict="dict"
      />
      <BindTerminal
        :bind-dialog-visible.sync="bindDialogVisible"
        :bind-facility="bindFacility"
        :dict="dict"
        @refresh="refresh"
      />
    </div>
  </basic-container>
</template>

<script>
import crudFacility from '@/api/base/facility';
import eForm from './module/form';
import CRUD, { presenter, header } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import pagination from '@/components/Crud/Pagination';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import HeadCommon from '@/components/formHead/headCommon.vue';
import BatchImport from '@/components/upload/batchImport.vue';
import msgDialog from '@/components/importErr';
import TerminalDialog from './module/terminalDialog.vue';
import BindTerminal from './module/bindTerminal.vue';
import { getDeptPerInit } from '@/api/base/dept';
import BatchImportWithDept from "@/components/upload/batchImportWithDept.vue";
import DeptDialog from "@/components/deptDialog/deptDialog.vue";

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('Facility', 'uniName'), // 基础设施
  crudMethod: { ...crudFacility }
});

export default {
  name: 'Facility',
  components: {
    DeptDialog,
    BatchImportWithDept, eForm, crudOperation, udOperation, pagination, HeadCommon, BatchImport, msgDialog, TerminalDialog, BindTerminal },
  mixins: [presenter(crud), header()],
  // 数据字典
  dicts: [
    'facilityType',
    'bdmDeviceType'
  ],
  data () {
    this.maxLen = {
      name: 50,
      code: 20
    };
    return {
      batchPer: {
        add: ['admin', 'facility:add'],
        imp: ['admin', 'facility:imp'],
        exp: ['admin', 'facility:exp'],
        del: ['admin', 'facility:del']
      },
      permission: {
        add: ['admin', 'facility:add'],
        edit: ['admin', 'facility:edit'],
        del: ['admin', 'facility:del'],
        bind: ['admin', 'facility:bind'],
        updateDept: ['admin', 'facility:updateDept']
      },
      headConfig: {
        item: {
          1: {
            name: '设施类型',
            type: 'select',
            value: 'category',
            dictOptions: 'facilityType'
          },
          2: {
            name: '设施名称',
            type: 'input',
            value: 'name',
          },
          3: {
            name: '绑定赋码编号',
            type: 'input',
            value: 'deviceNum'
          }
        },
        button: {
        }
      },
      //批量导入时选择的部门id
      importDeptId: 0,
      // 批量引入相关
      batchvisible: false,
      //批量修改组织
      updateDeptVisible: false,
      msgData: [], // 批量导入提示消息
      numKey: undefined, // 递归中继值
      dataType: { // excel里文字对应的key
        '设施类型': 'category',
        '设施名称': 'name',
        '所属机构': 'deptId',
        '编号': 'code',
        '多个点坐标，描述基础设施范围': 'geometry',
        '设施地址': 'address'
      },
      // 必填项
      typeRequired: ['category', 'name', 'geometry', 'code'],
      // 表单名称对应字典(表单名称与字典名称不一致时)
      typeDictName: {
        category:'facilityType'
      },
      tipsKey: [], // 提示点集合
      isDetail: false,
      dialogVisible: false,
      facilityId: null,
      bindDialogVisible: false, // 绑定终端弹窗
      bindFacility: {} // 当前绑定的设施
    };
  },
  computed: {
    getTerminalType () {
      return (dictName, val) => {
        if (this.dict.dict[dictName] && val) {
          let str = '';
          let list = [...new Set(val.split(','))];
          for (let index = 0; index < list.length; index++) {
            const element = list[index];
            let name = this.dict.dict[dictName][element]?.label;
            if (name) {
              str = str + name + ',';
            }
          }
          return str.slice(0, -1);
        }
      };
    }
  },
  watch: {
    batchvisible(val) {
      if (val) {
        getDeptPerInit().then(({ data }) => {
          this.dict['deptId'] = data;
        });
      }
    }
  },
  methods: {
    [CRUD.HOOK.beforeExport]() {
      // 获取当前选中的列
      const columnList = Object.keys(this.crud.props.tableColumns);
      let list = [];
      // 获取当前选中的字段名
      this.crud.query.columnNameList = columnList.filter((key) => this.crud.props.tableColumns[key].visible === true);
      // 获取当前选中的中文名称
      for (let index = 0; index < columnList.length; index++) {
        const element = columnList[index];
        if (this.crud.props.tableColumns[element].visible === true) {
          list.push(this.crud.props.tableColumns[element].label);
        }
      }
      this.crud.query.headNameList = list;
    },
    // 刷新表格
    refresh () {
      this.crud.refresh();
    },
    // 绑定终端
    onBindTerminal(row) {
      this.bindDialogVisible = true;
      this.bindFacility = JSON.parse(JSON.stringify(row));
      this.bindFacility.category = this.bindFacility.category.toString();
    },
    // 查看终端
    toTerminal (row) {
      this.facilityId = row.id;
      this.dialogVisible = true;
    },
    // 详情
    toDetail (data) {
      this.isDetail = true;
      this.crud.toEdit(data);
    },
    // 批量导入相关
    // 获取excel的数据
    getBatchData (returnValue) {
      let data = returnValue.data;
      this.importDeptId = returnValue.deptId;
      if (!data?.length) {
        this.$message.error('导入数据不能为空！');
        this.$refs.msgDialog?.handleClose();
        return;
      }
      if (!this.reversalObj) {
        this.reversalObj = {};
        for (let k in this.dataType) {
          this.reversalObj[this.dataType[k]] = k;
        }
      }
      this.tipsKey = [];
      let arr = data.map((item, index) => {
        let obj = {};
        for (let key in item) {
          for (let k in this.dataType) {
            if (key === k) {
              obj[this.dataType[k]] = item[key];
            }
          }
        }
        this.typeTreeTurn(obj);
        this.typeRequiredTurn(obj, index);
        this.handleExtra(obj, index);
        return obj;
      });
      if (this.tipsKey && this.tipsKey.length > 0) {
        let arr = [];
        this.tipsKey.forEach((item, index) => {
          if (item && item.length > 0) {
            const errList = [];
            item.forEach(v => {
              errList.push(v);
            });
            arr.push({
              sort: `第${index + 1}行`,
              details: errList.join(',')
            });
          }
        });
        this.msgData = arr;
        this.$refs.msgDialog.msgVisible = true;
      } else {
        this.addbatchPost(arr, this.importDeptId);
        returnValue.close();
      }
    },

    /**
     * 批量修改组织
     * @param returnValue
     */
    getChoosedDept (returnValue) {
      let choosedDeptId = returnValue.deptId;
      console.log("选择的数据为：", crud.selections);
      const ids =crud.selections?.length ? crud.selections.map( item => item.id) : null;
      let obj = {
        ids: ids,
        deptId: choosedDeptId
      };
      if (choosedDeptId) {
        this.updateDeptPost(obj);
        returnValue.close();
      }
    },
    updateDeptPost (arr) {
      crudFacility.updateDeptBatch(arr).then(res => {
        this.$message({
          showClose: true,
          message: res.msg,
          type: 'success'
        });
        this.crud.refresh();
      });
    },

    //弹出修改组织弹窗
    toUpdateDept(){
      if(!crud.selections || crud.selections.length < 1){
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.updateDeptVisible=true;
    },


    handleExtra (obj, index) {
      if(!/^[\d\. ]+$/.test(obj.geometry)) {
        this.handleErrFun(index, 'geometry');
      }
      Object.keys(this.maxLen).forEach( v => {
        if(obj[v] && (obj[v].length > this.maxLen[v])) {
          this.handleErrFun(index, v);
        }
      });
    },
    handleErrFun (index, v) {
      const pL = this.reversalObj[v];
      if (pL) {
        if (!this.tipsKey[index]) {
          this.tipsKey[index] = [pL];
        } else if (!this.tipsKey[index].includes(pL)) {
          this.tipsKey[index].push(pL);
        }
      }
    },
    // 必填项判断
    typeRequiredTurn (obj, k, dataRequired = this.typeRequired) {
      dataRequired.forEach(v => {
        if (typeof (v) === 'object' && v.mod) {
          this.typeRequiredTurn(obj[v.mod], k, v.required);
        } else if (!obj[v] && obj[v] !== 0) {
          this.handleErrFun(k, v);
        }
      });
    },
    // 字典里的转key/id
    typeTreeTurn (obj) {
      for (let k in this.dict) {
        for (let j in obj) {
          if (k === j) {
            this.treeTurn(this.dict[k], obj[j],j);
            obj[j] = this.numKey;
            this.numKey = undefined;
          }else if (k === this.typeDictName[j]) {
            this.treeTurn(this.dict[this.typeDictName[j]], obj[j],j);
            obj[j] = this.numKey;
            this.numKey = undefined;
          }
        }
      }
    },
    getTreeTurn(str){
      let obj = {
        value:'value',
        label:'label'
      };
      switch (str) {
      case 'deptId':
        obj.value = 'id';
        obj.label = 'title';
        break;
      default:
        break;
      }
      return obj;
    },
    // 递归找key/id
    treeTurn (tree, word, str, iValue = this.getTreeTurn(str).value, iLabel = this.getTreeTurn(str).label, iChildren = 'children') {
      tree.forEach(item => {
        if (!item.disabled && item[iLabel] === word) {
          this.numKey = item[iValue];
        } else if (item[iChildren] && item[iChildren].length > 0) {
          this.treeTurn(item[iChildren], word, str, iValue, iLabel, iChildren);
        }
      });
    },
    // 提交请求
    addbatchPost (arr, importDeptId) {
      crudFacility.addbatch(arr, importDeptId).then(res => {
        this.$message({
          showClose: true,
          message: res.msg,
          type: 'success'
        });
        this.crud.refresh();
      });
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Facility', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('Facility', value);
    },
    handleExport () {
      crud.toQuery();
      const ids =crud.selections?.length ? crud.selections.map( item => item.id) : null;
      crud.doExport(ids);
    },
  }
};
</script>

<style lang="less" scoped>
.xh-container ::v-deep.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
  background-color: #fcf0c1;
}
</style>
