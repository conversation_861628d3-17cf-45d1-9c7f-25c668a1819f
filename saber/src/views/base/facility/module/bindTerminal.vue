<template>
  <el-dialog
    title="绑定终端"
    :close-on-click-modal="false"
    :visible.sync="bindDialogVisible"
    append-to-body
    @close="close"
  >
    <el-form
      ref="form"
      :model="bindFacility"
      size="small"
      class="terminal-form"
      label-width="80px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-8 el-col-md-8">
          <!-- 设施类型 -->
          <el-form-item
            label="设施类型"
            prop="category"
          >
            <xh-select
              v-model="bindFacility.category"
              clearable
              :disabled="true"
            >
              <el-option
                v-for="item in dict.facilityType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-8 el-col-md-8">
          <!-- 设施名称 -->
          <el-form-item
            label="设施名称"
            prop="name"
          >
            <el-input
              v-model.trim="bindFacility.name"
              :disabled="true"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-8 el-col-offset-0 el-col-xs-24 el-col-sm-8 el-col-md-8">
          <!-- 所属机构 -->
          <el-form-item
            label="所属机构"
            prop="deptId"
          >
            <el-input
              v-model.trim="bindFacility.deptName"
              :disabled="true"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <MapWidget
      ref="MapWidget"
      class="liveMap"
      :tool-config="toolConfig"
      @mapLoaded="mapLoaded"
      @leftClickEvent="leftClickEvent"
    />
    <div class="terminal-container">
      <el-button
        v-show="!showCondition"
        size="normal"
        type="primary"
        @click="showCondition = !showCondition"
      >
        >> {{ tabActiveId === '1' ? '未绑定终端列表' : '已绑定终端列表' }}
      </el-button>
      <div
        v-show="showCondition"
        class="terminal-content"
      >
        <div class="terminal-content-header">
          <!-- <div class="terminal-content-header-title">
            未绑定终端列表
          </div> -->
          <el-tabs
            v-model="tabActiveId"
            type="card"
            class="terminal-content-header-tabs"
          >
            <el-tab-pane
              name="1"
              label="未绑定终端列表"
            />
            <el-tab-pane
              name="2"
              label="已绑定终端列表"
            />
          </el-tabs>
          <div
            class="xh-select-component-toggle-video-button common-search-btn"
            @click="showCondition = !showCondition"
          >
            &lt;&lt;
          </div>
        </div>
        <el-form
          ref="form"
          :model="terminalQuery"
          :rules="rules"
          size="small"
          label-width="70px"
        >
          <!-- 终端类型 -->
          <el-form-item label="终端类型">
            <el-cascader
              v-model="categoryTypeLeft"
              :options="deviceOptions"
              :show-all-levels="false"
              clearable
              popper-class="cascader"
            />
          </el-form-item>
          <!-- 所属机构 -->
          <el-form-item label="所属机构">
            <DeptFormSingleSelect
              ref="deptIdRef"
              v-model="terminalQuery.deptId"
              :is-show="bindDialogVisible"
              placeholder="请选择所属机构"
              clearable
              size="small"
            />
          </el-form-item>
        </el-form>
        <div class="terminal-content-btn">
          <el-button
            class="filter-item terminal-search-btn"
            size="small"
            type="primary"
            icon="el-icon-search"
            @click="handleQueryClick"
          >查 询
          </el-button>
        </div>
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="loading"
          class="terminal-content-table"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="tableData"
          :cell-style="{'text-align':'center'}"
          highlight-current-row
          @row-click="handleRowClick"
        >
          <el-table-column
            prop="deviceTypeName"
            label="终端类别"
            show-overflow-tooltip
          />
          <el-table-column
            prop="categoryName"
            label="终端类型"
            show-overflow-tooltip
          />
          <el-table-column
            prop="uniqueId"
            label="序列号"
            min-width="100px"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="tabActiveId !== '1'"
            prop="longitude"
            label="经度"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ handlePosition(scope.row.longitude) }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="tabActiveId !== '1'"
            prop="latitude"
            label="纬度"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ handlePosition(scope.row.latitude) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="deptName"
            label="所属机构"
            show-overflow-tooltip
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
        <el-pagination
          v-if="tabActiveId === '1'"
          layout="total,prev, pager, next"
          :total="total"
          :current-page.sync="terminalQuery.current"
          @current-change="getNoBindTerminal"
        />
      </div>
    </div>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="close"
      >
        取消
      </el-button>
      <el-button
        type="primary"
        size="small"
        @click="handleConfirm"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { facilitySelectBind, facilityConnect, facilitySelect } from '@/api/base/facility.js';
import MapWidget from '@/components/map/MapWidgetAMap.vue';
import { vehicleAddress } from '@/api/monitoring/info.js';
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';
export default {
  name: 'BindTerminal',
  components: {
    DeptFormSingleSelect,
    MapWidget
  },
  props: {
    // 外部控制显隐
    bindDialogVisible: {
      type: Boolean,
      default: false
    },
    // 当前设施信息
    bindFacility: {
      type: Object,
      required: true,
      default: () => {
        return {};
      }
    },
    depts: {
      type: Array,
      default: () => {
        return [];
      }
    },
    dict: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
      toolConfig: {
        routeRegionEdit: false, // 跳转区域编辑
        routePolylineEdit: false, // 跳转路线编辑
        routePointEdit: false, // 跳转标注点编辑
        drawMarkerShow: false, // 标注点
        polylineEditorShow: false, // 绘制直线
        showZoneToolShow: false, // 绘制区域
        searchToolShow: true, // 搜索
        clearBtnShow: false, // 清除按钮
        returnBtnShow: false, // 回到中心
        setCenterShow: false, // 设置中心
        trafficLayerShow: false, // 路况
        layerSelectShow: false, // 卫星图
        drivingLineShow: false, // 路径规划
        mapToolsShow: false // 工具栏
      }, // 控制工具按钮
      // treeSelect自定义键名
      normalizer (node) {
        return {
          id: node.id,
          label: node.title,
          children: (node.children && node.children.length > 0) ? node.children : undefined
        };
      },
      bindTerminalList: [],
      mapEl: null,
      map: null,
      AMap: null,
      rules: {},
      showCondition: true,
      terminalQuery: {
        category: null,
        deviceType: null,
        deptId: null,
        current: 1,
        size: 10
      },
      total: 0,
      categoryTypeLeft: [],
      deviceOptions: [],
      tableData: [],
      loading: false,
      terminalRow: {},
      drawMarker: null,
      markerObj: {},
      tabActiveId: '1',
      unbindTerminalList: []
    };
  },
  computed: {
    categoryLabel () {
      return (dictName, val) => {
        if (this.dict.dict[dictName] && this.dict.dict[dictName][val]) {
          return this.dict.dict[dictName][val].label;
        }
      };
    }
  },
  watch: {
    'dict.bdmDeviceType': {
      handler (val) {
        this.deviceOptions = val.filter(item => {
          return item.value === '4' || item.value === '5';
        });
      },
      deep: true,
      immediate: true
    },
    bindDialogVisible: {
      handler (newVal) {
        if (newVal) {
          this.getBindTerminal();
          this.getNoBindTerminal();
          if (this.bindFacility.geometry) {
            const list = this.bindFacility.geometry.split(' ');
            let pointList = [];
            for (let index = 0; index < list.length;) {
              pointList.push(list.splice(index, 2));
            }
            this.setEditPolygon(pointList);
          }
        }
      },
      deep: true
    },
    tabActiveId: {
      handler (val) {
        if (val === '1') {
          this.tableData = this.unbindTerminalList;
        } else {
          this.tableData = this.bindTerminalList;
        }
      }
    }
  },
  methods: {
    handleQueryClick () {
      if (this.tabActiveId === '1') {
        this.getNoBindTerminal();
      } else {
        this.getBindTerminal('clear');
      }
    },
    leftClickEvent (e) {
      if (Object.keys(this.terminalRow).length === 0) {
        return;
      }
      const longitude = Number(e.coordinate.longitude);
      const latitude = Number(e.coordinate.latitude);
      let index = this.tableData.findIndex(item => item.id === this.terminalRow.id && item.deviceType === this.terminalRow.deviceType);
      if (index >= 0) {
        this.tableData[index].longitude = longitude;
        this.tableData[index].latitude = latitude;
        this.bindTerminalList.push({...this.tableData[index]});
        this.$nextTick(() => {
          this.tableData.splice(index, 1);
        });
      }
      if(e.isFormAlarm) {
        this.map.setZoomAndCenter(14, [longitude ,latitude]);
      }
      let position = new this.AMap.LngLat(longitude, latitude);
      if (this.markerObj[this.terminalRow.deviceType + this.terminalRow.id]) {
        this.markerObj[this.terminalRow.deviceType + this.terminalRow.id].setPosition(position);
      } else {
        this.terminalRow = {
          ...this.terminalRow,
          longitude: longitude,
          latitude: latitude
        };
        this.createTerminalMrker([this.terminalRow]);
      }
    },
    handleRowClick (row) {
      if (this.tabActiveId === '1') {
        this.terminalRow = row;
      } else {
        const obj = {
          longitude: row.longitude,
          latitude: row.latitude
        };
        this.$refs.MapWidget.setZoomAndCenter(obj);
      }
    },
    // 关闭dialog
    close () {
      this.tableData = [];
      this.bindTerminalList = [];
      this.unbindTerminalList = [];
      this.tabActiveId = '1';
      this.markerObj = {};
      this.mapEl.clearAll();
      this.$refs?.MapWidget.clearSearchResult();
      this.$emit('update:bindDialogVisible', false);
    },
    // 获取未绑定终端列表
    getNoBindTerminal () {
      this.terminalQuery.deviceType = this.categoryTypeLeft[0];
      this.terminalQuery.category = this.categoryTypeLeft[1];
      let query = { ...this.terminalQuery };
      facilitySelect(query).then(res => {
        this.unbindTerminalList = res.data.content;
        if (this.tabActiveId === '1') {
          this.tableData = this.unbindTerminalList;
        }
        this.total = res.data.total;
      }).catch(err => {
        console.log('err', err);
      });
    },
    setEditPolygon(pointList) {
      if (this.AMap?.Polygon) {
        const drawShape = new this.AMap.Polygon({
          path: pointList,
          fillColor: '#1791fc',
          fillOpacity: 0.4,
          strokeColor: '#3ca2fa',
          strokeWeight: 2,
          strokeOpacity: 1,
          borderWeight: 2
        });
        this.map.add(drawShape);
        this.$nextTick(() => {
          this.mapEl.setFitView();
        });
      } else {
        setTimeout(() => {
          this.setEditPolygon(pointList);
        }, 200);
      }
    },
    getBindTerminal (type) {
      let query = {
        id: this.bindFacility.id,
        deviceType: this.categoryTypeLeft[0],
        category: this.categoryTypeLeft[1],
        deptId: this.terminalQuery.deptId
      };
      facilitySelectBind(query).then(res => {
        this.bindTerminalList = res.data.content;
        if (this.tabActiveId === '2') {
          this.tableData = this.bindTerminalList;
        }
        this.markerObj = {};
        if (type === 'clear') {
          this.mapEl?.clearAll();
        }
        this.$nextTick(() => {
          this.createTerminalMrker(this.bindTerminalList);
        });
      }).catch(err => {
        console.log('err', err);
      });
    },
    createTerminalMrker (data) {
      if (this.AMap && this.map) {
        for (let index = 0; index < data.length; index++) {
          const element = data[index];
          let labelTypeData = {
            iconUrl: this.judgeTerminalIcon(element),
            iconWidth: 50,
            iconHeight: 50
          };
          let position = new this.AMap.LngLat(element.longitude, element.latitude);
          this.markerObj[element.deviceType + element.id] = new this.AMap.Marker({
            position: position,
            label: {
              content: this.getEnumDictLabel('bdmDeviceType', element.category) + '-' + element.uniqueId,
              direction: 'bottom'
            },
            content: `<div style="position: relative;">
                      <div style="position: absolute; width: 100%; height: 100%; background-image: url('/bdsplatform/static/images/pic/static.png'); background-size: 100%;"></div>
                      <img src="${labelTypeData.iconUrl}" style="display: block; width: ${labelTypeData.iconWidth}px; height: ${labelTypeData.iconHeight}px; padding: 3px; position: inherit;">
                    </div>`,
            draggable: true,
            offset: new this.AMap.Pixel(-(labelTypeData.iconWidth / 2), -(labelTypeData.iconHeight / 2)),
            extData: {
              originalData: element
            },
          });
          this.markerObj[element.deviceType + element.id].on('click', (e) => {
            // 处理定位
            this.setSinglePointText(e, element);
          });
          this.markerObj[element.deviceType + element.id].on('rightclick', (e) => {
            // 右键
            this.handleMarkerRight(e, element);
          });
          this.markerObj[element.deviceType + element.id].on('dragend', (e) => {
            // 拖拽
            this.dragMarker(e, element);
          });
          this.map.add(this.markerObj[element.deviceType + element.id]);
        }
      } else {
        setTimeout(() => {
          this.createTerminalMrker(data);
        }, 200);
      }
    },
    handleMarkerRight (e, data) {
      // 创建一个右键菜单实例
      const contextMenu = new this.AMap.ContextMenu();
      contextMenu.addItem("移除终端", ()=> {
        this.map.remove(e.target);
        delete this.markerObj[data.deviceType + data.id];
        let index = this.tableData.findIndex(item => item.id === data.id && item.deviceType === data.deviceType);
        if (index >= 0) {
          this.tableData[index].longitude = null;
          this.tableData[index].latitude = null;
          this.unbindTerminalList.push({...this.tableData[index]});
          this.$nextTick(() => {
            this.tableData.splice(index, 1);
          });
        }
        contextMenu.close();
      }, 0);
      // 在地图上指定位置打开右键菜单
      contextMenu.open(this.map, [e.lnglat.lng, e.lnglat.lat]);
    },
    dragMarker (e, data) {
      if (this.markerObj[data.deviceType + data.id]) {
        this.markerObj[data.deviceType + data.id].setPosition(e.lnglat);
      }
      let index = this.tableData.findIndex(item => item.id === data.id && item.deviceType === data.deviceType);
      if (index >= 0) {
        this.tableData[index].longitude = e.lnglat.lng;
        this.tableData[index].latitude = e.lnglat.lat;
      }
    },
    async setSinglePointText (e, element) {
      let locAddr = '';
      const query = {
        lon: e.target['_position'].lng,
        lat: e.target['_position'].lat
      };
      await vehicleAddress(query).then(res => {
        if (res.code === 200) {
          locAddr = res.data;
        }
      });
      let position = new this.AMap.LngLat(query.lon, query.lat);
      var info = [];
      info.push("<div class='input-card content-window-card' style='width: 435px;'>");
      info.push("<p class='input-item' style='margin: 10px'>终端类别：" + `${this.getEnumDictLabel('bdmDeviceType', element.deviceType)}`);
      info.push("<p class='input-item' style='margin: 10px'>终端类型：" + `${this.getEnumDictLabel('bdmDeviceType', element.category)}`);
      info.push("<p class='input-item' style='margin: 10px'>所属机构：" + `${ element.deptName || '' }`);
      info.push("<p class='input-item' style='margin: 10px'>型号：" + `${ element.model || '' }`);
      info.push("<p class='input-item' style='margin: 10px'>地址：" + `${locAddr}`);
      let infoWindow = new this.AMap.InfoWindow({
        position: position,
        anchor: 'bottom-center',
        content: info.join(''),
        offset: new this.AMap.Pixel(0, -35)
      });
      this.map.add(infoWindow);
    },
    // 提交
    handleConfirm () {
      let params = {
        id: this.bindFacility.id,
        terminalList: []
      };
      params.terminalList = Object.values(this.markerObj).map(v => {
        let marker = v.getExtData();
        return {
          ...marker.originalData,
          longitude: String(v['_position'].lng),
          latitude: String(v['_position'].lat)
        };
      });
      facilityConnect(params, this.bindFacility.deptId).then((res) => {
        this.$message.success(res.msg);
        this.close();
        this.$emit('refresh');
      });
    },
    mapLoaded(v){
      this.mapEl = v;
      this.map = this.mapEl.map;
      this.AMap = this.mapEl.AMap;
      this.drawMarker = this.mapEl.$refs.DrawMarker;
    },
    /**
     * 根据终端类型判断图标
     */
    judgeTerminalIcon (val) {
      const vehicleModel = ['101', '102', '103', '104', '105'];
      const materialsModel = ['106', '108', '109', '111', '112', '114'];
      const personnelModel = ['201', '202', '301', '107', '110', '113'];
      const shortMessageModel = ['302', '303'];
      const timeServiceModel = ['501', '502', '503'];
      const monitorModel = ['401', '402'];
      let vehicleIcon = '';
      if (vehicleModel.includes(String(val.category))) {
        vehicleIcon = `/bdsplatform/static/images/pic/vehicle.png`; // 车辆
      } else if (materialsModel.includes(String(val.category))) {
        vehicleIcon = `/bdsplatform/static/images/pic/materials.png`; // 物资
      } else if (personnelModel.includes(String(val.category))) {
        vehicleIcon = `/bdsplatform/static/images/pic/personnel.png`; // 人员
      } else if (shortMessageModel.includes(String(val.category))) {
        vehicleIcon = `/bdsplatform/static/images/pic/shortMessage.png`; // 短报文终端
      } else if (timeServiceModel.includes(String(val.category))) {
        vehicleIcon = `/bdsplatform/static/images/pic/timeService.png`; // 授时终端
      } else if (monitorModel.includes(String(val.category))) {
        vehicleIcon = `/bdsplatform/static/images/pic/monitor.png`; // 监测终端
      } else if (String(val.category) === '0') {
        vehicleIcon = `/bdsplatform/static/images/pic/other.png`; // 其他
      }
      return vehicleIcon;
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    }
  }
};
</script>

<style lang="less">
.cascader {
  .el-cascader-panel {
    height: 200px !important;
  }
}
</style>
<style lang="less" scoped>
::v-deep .el-dialog {
  width: 99%;
  height: 98%;
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 20px - 24px - 90px);
    max-height: max-content;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
  }
}
.facility-descriptions {
  margin: 20px 400px;
  &-item {
    color: #909399;
    background-color: #fafafa;
  }
  &-value {
    font-weight: bold;
  }
}
.terminal-container {
  position: absolute;
  top: 85px;
  left: 15px;
  height: calc(100% - 95px);
  .terminal-content {
    width: 450px;
    height: 100%;
    background-color: #FFFFFF;
    padding: 10px 20px;
    display: flex;
    flex-direction: column;
    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 10px;
      &-title {
        font-size: 18px;
      }
      &-tabs {
        /deep/ .el-tabs__header {
          margin: 0;
        }
      }
    }
    &-btn {
      display: flex;
      justify-content: center;
    }
    &-table {
      margin-top: 10px;
      flex: 1;
        ::v-deep .el-table__cell{
          border: none;
        }
        ::v-deep .current-row>td {
          background-color: #98c7fd !important;
        }
    }
  }
}
.tree-disabled {
    ::v-deep .vue-treeselect__single-value {
      background-color: #F5F7FA;
      border-color: #E4E7ED;
      color: #C0C4CC;
      cursor: not-allowed;
    }
  }
.terminal-form {
  padding: 20px 200px 0;
}
.terminal-search-btn {
  padding: 9px 30px;
}
</style>
