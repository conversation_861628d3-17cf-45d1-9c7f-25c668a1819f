<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="isDetail ? '查看基础设施' : crud.status.title"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      class="rewriting-form-disable"
      :hide-required-asterisk="isDetail"
      label-width="120px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 设施类型 -->
          <el-form-item
            :label="getLabel('category')"
            prop="category"
          >
            <single-select
              v-model="form.category"
              :options="dict.facilityType"
              :placeholder="getPlaceholder('category', 'select')"
              clearable
              :disabled="isDetail"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 设施名称 -->
          <el-form-item
            :label="getLabel('name')"
            prop="name"
          >
            <el-input
              v-model.trim="form.name"
              :disabled="isDetail"
              maxLength="50"
              :placeholder="getPlaceholder('name')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 所属机构 -->
          <el-form-item
            :label="getLabel('deptId')"
            prop="deptId"
          >
            <DeptFormSingleSelect
              ref="deptIdRef"
              v-model="form.deptId"
              :detail-name="form.deptName"
              :disabled="isDetail"
              :is-show="crud.status.cu > 0"
              placeholder="请选择所属机构"
              size="small"
              @input="validateTreeSelect('deptId')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('code')"
            prop="code"
          >
            <el-input
              v-model.trim="form.code"
              :disabled="isDetail || crud.status.edit > 0"
              maxLength="20"
              :placeholder="getPlaceholder('code')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <!-- 设施地址 -->
          <el-form-item
            :label="getLabel('address')"
            prop="address"
          >
            <el-input
              v-model.trim="form.address"
              :disabled="isDetail"
              maxLength="100"
              placeholder="点击右侧获取定位按钮后在地图上点击获取并回显对应地址"
            >
              <el-button
                slot="append"
                icon="el-icon-map-location"
                class="address-btn"
                @click="handleAddressClick"
              >
                获取定位
              </el-button>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="!isDetail"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12 form-btn"
        >
          <el-button
            class="filter-item"
            size="small"
            type="primary"
            icon="el-icon-edit"
            @click="drawClick"
          >绘 制
          </el-button>
          <el-button
            class="filter-item"
            size="small"
            icon="el-icon-refresh-right"
            @click="clearClick"
          >清 除
          </el-button>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <MapWidget
            ref="MapWidget"
            class="liveMap"
            :tool-config="toolConfig"
            @mapLoaded="mapLoaded"
            @handleClear="handleClear"
            @leftClickEvent="leftClickEvent"
          />
        </div>
      </el-row>
    </el-form>
    <div
      v-if="!isDetail"
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import MapWidget from '@/components/map/MapWidgetAMapCE';
import { facilitySelectBind } from '@/api/base/facility.js';
import { gnAddress, vehicleAddress } from '@/api/monitoring/info.js';
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';
import SingleSelect from '@/components/select/DictSelect/DictSelectSingle';

const defaultForm = {
  id: null,
  category: null,
  name: '',
  address: null,
  deptId: null,
  deptName: null,
  geometry: null,
  code: ''
};
export default {
  components: {
    DeptFormSingleSelect,
    MapWidget,
    SingleSelect
  },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rules: {
        category: {
          required: true,
          message: '请选择设施类型',
          trigger: 'change'
        }, // 设施类型
        name: {
          required: true,
          message: '请输入设施名称',
          trigger: 'blur'
        }, // 设施名称
        deptId: {
          required: true,
          message: '请选择所属机构',
          trigger: 'change'
        }, // 所属机构
        code: {
          required: true,
          message: '请输入编号',
          trigger: 'blur'
        } // 设施名称
      },
      // treeSelect自定义键名
      normalizer(node) {
        return {
          id: node.id,
          label: node.title,
          children: (node.children && node.children.length > 0) ? node.children : undefined
        };
      },
      toolConfig: {
        drawMarkerShow: false, // 标注点
        polylineEditorShow: false, // 绘制直线
        showZoneToolShow: false, // 绘制区域
        searchToolShow: true, // 搜索
        clearBtnShow: false, // 清除按钮
        returnBtnShow: false, // 回到中心
        setCenterShow: false, // 设置中心
        trafficLayerShow: false, // 路况
        layerSelectShow: false, // 卫星图
        drivingLineShow: false, // 路径规划
        mapToolsShow: false // 工具栏
      }, // 控制工具按钮
      area: null,
      mapEl: null,
      map: null,
      AMap: null,
      bindTerminalList: [],
      shapeEditor: null,
      depts: [],
      isAddress: false,
      geocoder: null,
      layer: null
    };
  },
  methods: {
    // 点击地图后
    leftClickEvent(val) {
      if (this.isAddress) {
        this.isAddress = false;
        const query = {
          postStr: {
            lon: val.coordinate[0],
            lat: val.coordinate[1],
            ver: 1
          },
          type: 'geocode'
        };
        gnAddress(query).then(res => {
          if (res.status === 200) {
            let {
              formatted_address
            } = res.data.result;
            if (Number(res.data.status) === 0) {
              this.$set(this.form, 'address', formatted_address);
            }
          }
        });
      }
    },
    handleAddressClick() {
      // this.mapEl.mousetool.close(false);
      // this.mapEl.mousetool.off('draw', this.getAreaInfo);
      this.isAddress = true;
    },
    // 获取地图元素
    mapLoaded(v) {
      this.mapEl = v;
      this.map = this.mapEl.map;
      this.AMap = this.mapEl.AMap;
      this.layer = this.mapEl.LabelsLayer;
    },
    /** 新建 - 之前 */
    [CRUD.HOOK.beforeToAdd]() {
      this.initMousetoolDraw();
    },
    // 初始化多边形绘制
    initMousetoolDraw() {
      if (this.layer) {
        MayMap.TileLayer.MouseTool({
          layer: this.layer,
          type: 'Polygon',
          // state: true, //绘制完成后是否销毁
          callback: (res) => {
            console.log('绘制', res);
            this.shapeEditor = res;
            this.setCoordinateVal();
          }
        });
      }
      else {
        setTimeout(() => {
          this.initMousetoolDraw();
        }, 100);
      }
    },
    // 绘制图形后触发的事件
    getAreaInfo(e) {
      this.editShape(e);
      // 关闭鼠标工具，不清除覆盖物
      // this.mapEl.mousetool.close(false);
      // 注销事件
      // this.mapEl.mousetool.off('draw', this.getAreaInfo);
    },
    // 编辑图形
    editShape(e) {
      let polygon = MayMap.TileLayer.add({
        layer: this.layer,
        type: 'Polygon',
        coordinate: [e.obj]
      });
      MayMap.TileLayer.MouseToolEditor({
        layer: this.layer,
        feature: polygon,
        callback: (res) => {
          console.log(res);
          this.shapeEditor = res;
          this.setCoordinateVal();
        }
      });
      setTimeout(()=> {
        this.mapEl.setFitView(polygon, [120, 120, 120, 120]);
      }, 200);
    },
    // 获取图形数据
    setCoordinateVal() {
      if(this.shapeEditor) {
        let rectPath = this.shapeEditor.coordinate.flat(2);
        let polygonPoints = [];
        rectPath.length > 0 && rectPath.forEach(item => {
          polygonPoints.push(item);
        });
        this.form.geometry = polygonPoints.join(' ');
      }
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU]() {
      this.$refs?.form?.clearValidate();
    },
    /** 编辑 - 之前 */
    [CRUD.HOOK.beforeToEdit]() {
      if (this.form.category || this.form.category === 0) {
        this.form.category = String(this.form.category);
      }
      this.form.deptId = this.form.deptId ? this.form.deptId : null;
      if (this.form.geometry) {
        const list = this.form.geometry.split(' ');
        let pointList = [];
        for (let index = 0; index < list.length;) {
          pointList.push(list.splice(index, 2));
        }
        this.setEditPolygon(pointList);
      }
      else {
        this.initMousetoolDraw();
      }
    },
    setEditPolygon(pointList) {
      if (this.layer) {
        // 设置图层样式
        this.layer.setStyle((feature, resolution) => {
          return new MayMap.ol.style.Style({
            //边框色
            stroke: new MayMap.ol.style.Stroke({
              color: '#1791fc',
              width: 2
            }),
            //填充色
            fill: new MayMap.ol.style.Fill({
              color: 'rgba(23,145,252,0.4)'
            })
          });
        });
        this.$nextTick(() => {
          if (!this.isDetail) {
            this.editShape({ obj: pointList });
          }
          else {
            this.getBindTerminal();
          }
        });
      }
      else {
        setTimeout(() => {
          this.setEditPolygon(pointList);
        }, 200);
      }
    },
    getBindTerminal() {
      facilitySelectBind({ id: this.form.id }).then(res => {
        this.bindTerminalList = res.data.content;
        for (let index = 0; index < this.bindTerminalList.length; index++) {
          const element = this.bindTerminalList[index];
          let labelTypeData = {
            iconUrl: this.judgeTerminalIcon(element),
            iconWidth: 50,
            iconHeight: 50
          };
          let position = new this.AMap.LngLat(element.longitude, element.latitude);
          let pointMarker = new this.AMap.Marker({
            position: position,
            label: {
              content: this.getEnumDictLabel('bdmDeviceType', element.category) + '-' + element.uniqueId,
              direction: 'bottom'
            },
            content: `<div style="position: relative;">
                      <div style="position: absolute; width: 100%; height: 100%; background-image: url('/bdsplatform/static/images/pic/static.png'); background-size: 100%;"></div>
                      <img src="${labelTypeData.iconUrl}" style="display: block; width: ${labelTypeData.iconWidth}px; height: ${labelTypeData.iconHeight}px; padding: 3px; position: inherit;">
                    </div>`
          });
          pointMarker.on('click', (e) => {
            // 处理定位
            this.setSinglePointText(element);
          });
          this.map.add(pointMarker);
        }
      });
    },
    async setSinglePointText(element) {
      let locAddr = '';
      const query = {
        lon: Number(element.longitude),
        lat: Number(element.latitude)
      };
      await vehicleAddress(query).then(res => {
        if (res.code === 200) {
          locAddr = res.data;
        }
      });
      let position = new this.AMap.LngLat(element.longitude, element.latitude);
      var info = [];
      info.push('<div class=\'input-card content-window-card\' style=\'width: 435px;\'>');
      info.push('<p class=\'input-item\' style=\'margin: 10px\'>终端类别：' + `${this.getEnumDictLabel('bdmDeviceType', element.deviceType)}`);
      info.push('<p class=\'input-item\' style=\'margin: 10px\'>终端类型：' + `${this.getEnumDictLabel('bdmDeviceType', element.category)}`);
      info.push('<p class=\'input-item\' style=\'margin: 10px\'>所属机构：' + `${element.deptName || ''}`);
      info.push('<p class=\'input-item\' style=\'margin: 10px\'>型号：' + `${element.model}`);
      info.push('<p class=\'input-item\' style=\'margin: 10px\'>地址：' + `${locAddr}`);
      let infoWindow = new this.AMap.InfoWindow({
        position: position,
        anchor: 'bottom-center',
        content: info.join(''),
        offset: new this.AMap.Pixel(25, -10)
      });
      this.map.add(infoWindow);
    },
    handleClear() {
      this.layer?.getSource()?.clear();
      this.shapeEditor = null;
    },
    /** "新建/编辑" 验证 - 之前 */
    [CRUD.HOOK.beforeValidateCU]() {
      if (!this.form.geometry) {
        this.$message.warning('请绘制电子围栏');
        return false;
      }
      this.shapeEditor = null;
      this.$refs.form.validate((valid) => {
        if (!valid) {
          // this.validateTreeSelect('deptId');
        }
      });
    },
    // 开始绘制
    drawClick() {
      if (!this.shapeEditor) {
        this.initMousetoolDraw();
      }
      else {
        this.$message.warning('当前已有绘制, 请先清除');
      }
    },
    // 清除绘制
    clearClick() {
      this.mapEl.clearAll();
      // this.mapEl.mousetool.close(false);
      // this.mapEl.mousetool.off('draw', this.getAreaInfo);
      // this.shapeEditor?.close();
      this.shapeEditor = null;
      this.form.geometry = null;
    },
    // 监听关闭事件
    closed() {
      this.clearClick();
      this.$emit('update:isDetail', false);
      this.$refs?.MapWidget.clearSearchResult();
      // let refList = ['deptIdRef'];
      // for (let i = 0; i < refList.length; i++) {
      //   if (this.$refs[refList[i]]) {
      //     this.$refs[refList[i]].$children[0].$el.style.borderColor = '#BFBFBF';
      //   }
      // }
    },
    /**
     * 验证treeSelect项
     * @param {String} item
     */
    // validateTreeSelect (item) {
    //   this.$refs.form.validateField(item, (valid) => {
    //     this.$refs[`${item}Ref`].$children[0].$el.style.borderColor = valid !== '' ? '#F56C6C' : '#BFBFBF';
    //   });
    // },
    /**
     * 根据终端类型判断图标
     */
    judgeTerminalIcon(val) {
      const vehicleModel = [
        '101',
        '102',
        '103',
        '104',
        '105'
      ];
      const materialsModel = [
        '106',
        '108',
        '109',
        '111',
        '112',
        '114'
      ];
      const personnelModel = [
        '201',
        '202',
        '301',
        '107',
        '110',
        '113'
      ];
      const shortMessageModel = [
        '302',
        '303'
      ];
      const timeServiceModel = [
        '501',
        '502',
        '503'
      ];
      const monitorModel = [
        '401',
        '402'
      ];
      let vehicleIcon = '';
      if (vehicleModel.includes(String(val.category))) {
        vehicleIcon = `/bdsplatform/static/images/pic/vehicle.png`; // 车辆
      }
      else if (materialsModel.includes(String(val.category))) {
        vehicleIcon = `/bdsplatform/static/images/pic/materials.png`; // 物资
      }
      else if (personnelModel.includes(String(val.category))) {
        vehicleIcon = `/bdsplatform/static/images/pic/personnel.png`; // 人员
      }
      else if (shortMessageModel.includes(String(val.category))) {
        vehicleIcon = `/bdsplatform/static/images/pic/shortMessage.png`; // 短报文终端
      }
      else if (timeServiceModel.includes(String(val.category))) {
        vehicleIcon = `/bdsplatform/static/images/pic/timeService.png`; // 授时终端
      }
      else if (monitorModel.includes(String(val.category))) {
        vehicleIcon = `/bdsplatform/static/images/pic/monitor.png`; // 监测终端
      }
      else if (String(val.category) === '0') {
        vehicleIcon = `/bdsplatform/static/images/pic/other.png`; // 其他
      }
      return vehicleIcon;
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('Facility', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder(value, type) {
      return getPlaceholder('Facility', value, type);
    },
    /**
     * 验证treeSelect项
     * @param {String} item
     */
    validateTreeSelect(item) {
      this.$refs.form.validateField(item, (valid) => {
        this.$refs[`${item}Ref`].$refs[`${item}StrRef`].$children[0].$el.style.borderColor = valid !== '' ? '#f56c6c' : '#bfbfbf';
      });
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

.tree-disabled {
  ::v-deep .vue-treeselect__single-value {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed;
  }
}

.liveMap {
  height: 500px;
}

.form-btn {
  padding-left: 50px;
}

.address-btn {
  border-color: transparent !important;
  background-color: transparent !important;
  color: inherit !important;
}
</style>
