<template>
  <el-dialog
    :close-on-click-modal="false"
    :before-close="beforeClose"
    :visible="dialogVisible"
    title="绑定终端详情"
    append-to-body
    width="65%"
  >
    <!--表格渲染-->
    <el-table
      ref="table"
      v-loading="loading"
      :data="tableData"
      :cell-style="{'text-align':'center'}"
      height="65vh"
    >
      <el-table-column
        type="index"
        label="#"
      />
      <el-table-column
        label="序列号"
        prop="uniqueId"
        show-overflow-tooltip
        min-width="180"
      />
      <el-table-column
        label="终端类别"
        prop="deviceTypeName"
        show-overflow-tooltip
        min-width="150"
      />
      <el-table-column
        label="终端类型"
        prop="categoryName"
        show-overflow-tooltip
        min-width="220"
      />
      <el-table-column
        :label="getLabel('deviceNum')"
        prop="deviceNum"
        min-width="150"
        show-overflow-tooltip
      />
      <el-table-column
        :label="getLabel('deptName')"
        prop="deptName"
        min-width="150"
        show-overflow-tooltip
      />
      <el-table-column
        :label="getLabel('longitude')"
        prop="longitude"
        min-width="100"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ handlePosition(scope.row.longitude) }}
        </template>
      </el-table-column>
      <el-table-column
        :label="getLabel('latitude')"
        prop="latitude"
        min-width="100"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ handlePosition(scope.row.latitude) }}
        </template>
      </el-table-column>
      <el-empty
        slot="empty"
        :image="require('@/assets/images/nodata.png')"
      />
    </el-table>
  </el-dialog>
</template>
<script>
import { terminalInfo } from '@/api/base/facility';
import getLabel from '@/utils/getLabel';
export default {
  components:{ },
  props:{
    dialogVisible: {
      type: Boolean,
      default: false
    },
    facilityId: {
      type: Number,
      default: null
    },
    dict: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data(){
    return{
      loading: false,
      tableData: []
    };
  },
  watch: {
    dialogVisible: {
      handler(val) {
        if (val) {
          this.getData();
        }
      }
    }
  },
  methods:{
    getData () {
      this.loading = true;
      terminalInfo({
        id: this.facilityId
      }).then(res => {
        this.tableData = res.data;
      }).finally(() => {
        this.loading = false;
      });
    },
    // 判断开始时间小于结束时间
    judge () {
      if (this.query.startTime) {
        if (
          this.$moment(this.query.startTime).valueOf() >
          this.$moment(this.query.endTime).valueOf()
        ) {
          this.$message({
            type: 'warning',
            message: '开始日期时间要小于结束日期时间'
          });
        }
      }
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    beforeClose(){
      this.$emit('update:dialogVisible', false);
      this.tableData = [];
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Facility', value);
    },
  }
};
</script>
<style lang="less" scoped>
</style>
