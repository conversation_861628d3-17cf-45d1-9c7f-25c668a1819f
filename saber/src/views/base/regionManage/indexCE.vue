<template>
  <div class="region">
    <basic-container class="region-tree">
      <div
        v-if="dialogRegionVisible"
        class="my-modal"
      />
      <div class="region-input-container">
        <el-input
          v-model.trim="filterText"
          class="region-input"
          size="small"
          placeholder="输入进行查询"
        />
        <div>
          <el-button
            type="primary"
            size="small"
            @click="dialogVisible = true"
          >电子围栏下发
          </el-button>
        </div>
      </div>
      <el-tree
        ref="tree"
        class="filter-tree"
        :data="treeData"
        node-key="id"
        :props="defaultProps"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        @node-click="handleClick"
      />
      <el-button-group class="btn-group">
        <el-button
          type="primary"
          icon="el-icon-circle-plus-outline"
          size="small"
          @click="handleClassifyClick"
        >分类
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-circle-plus-outline"
          size="small"
          @click="handleRegionClick"
        >电子围栏
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-edit"
          size="small"
          @click="handleEditClick"
        >编辑
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-delete"
          size="small"
          @click="handleDelClick"
        >删除
        </el-button>
      </el-button-group>
    </basic-container>
    <basic-container class="region-map">
      <MapWidget
        ref="MapWidget"
        class="liveMap"
        :tool-config="toolConfig"
        @mapLoaded="mapLoaded"
        @handleClear="handleClear"
        @clearMap="clearMap"
      />
    </basic-container>
    <RegionClassify
      :classify-form="classifyForm"
      :tree-data="treeData"
      :dialog-visible.sync="dialogClassifyVisible"
      @getTreeData="getTreeData"
    />
    <Region
      ref="region"
      :region-type.sync="regionType"
      class="region-dialog"
      :tree-data="treeData"
      :dialog-visible.sync="dialogRegionVisible"
      @getTreeData="getTreeData"
      @clearAll="clearAll"
      @closeShape="closeShape"
      @drawHandle="drawHandle"
    />
    <RegionSend
      ref="regionSend"
      :dialog-visible.sync="dialogVisible"
      :tree-data="treeData"
    />
  </div>
</template>

<script>
import { regionGroups, regionlassifyDel, regionDel, regionDetail } from '@/api/base/regionManage';
import MapWidget from '@/components/map/MapWidgetAMapCE.vue';
import RegionClassify from './module/regionClassify.vue';
import Region from './module/region.vue';
import RegionSend from './module/regionSend.vue';
import { mapGetters } from 'vuex';

export default {
  components: {
    MapWidget,
    RegionClassify,
    Region,
    RegionSend
  },
  data() {
    return {
      filterText: '',
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      toolConfig: {
        drawMarkerShow: false, // 标注点
        polylineEditorShow: false, // 绘制直线
        showZoneToolShow: false, // 绘制区域
        searchToolShow: true, // 搜索
        clearBtnShow: false, // 清除按钮
        returnBtnShow: false, // 回到中心
        setCenterShow: false, // 设置中心
        trafficLayerShow: true, // 路况
        layerSelectShow: true, // 卫星图
        drivingLineShow: false, // 路径规划
        mapToolsShow: true // 工具栏
      }, // 控制工具按钮
      dialogClassifyVisible: false,
      dialogRegionVisible: false,
      nodeData: {},
      classifyForm: {},
      regionType: '2',
      area: null,
      mapEl: null,
      map: null,
      AMap: null,
      shapeEditor: null,
      drawShape: null,
      dialogVisible: false, // 区域下发弹窗
      layer: null
    };
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
    dialogRegionVisible(val) {
      if (!val) {
        // 国能地图没有退出编辑功能 直接取消树选中 清除地图上的矢量形状
        this.layer.getSource().clear();
        this.nodeData = {};
        this.$refs.tree.setCurrentKey(null);
      }
    }
  },
  mounted() {
    this.getTreeData();
  },
  methods: {
    // 调用地图"清除"后
    handleClear() {
      this.shapeEditor = null;
      // 清空保存的图形数据
      this.$refs.region.setFormVal({ coordinate: '' });
      // 重新调用绘制方法
      this.drawHandle(this.regionType);
    },
    // 切换绘制的图形
    drawHandle(type) {
      this.regionType = type;
      this.shapeEditor = null;
      this.startDraw(type);
    },
    // 获取地图元素
    mapLoaded(v) {
      this.mapEl = v;
      this.map = this.mapEl.map;
      this.AMap = this.mapEl.AMap;
      this.layer = this.mapEl.LabelsLayer;
    },
    handleDelClick() {
      if (!this.nodeData.id) {
        this.$message.warning('请选择删除的数据');
        return false;
      }
      if (this.nodeData.data.children && this.nodeData.data.children.length) {
        this.$message.warning('此区域下含有子级区域, 不可直接删除');
        return false;
      }
      this.$confirm(`确定将选择数据删除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (this.nodeData.data.type === 1) {
          regionlassifyDel(this.nodeData.data.id).then((res) => {
            this.$message.success(res.msg);
            this.getTreeData();
            this.nodeData = {};
          });
        }
        else {
          regionDel(this.nodeData.data.id).then((res) => {
            this.$message.success(res.msg);
            this.getTreeData();
            this.clearAll();
            this.nodeData = {};
          });
        }
      }).catch(() => {
      });
    },
    // 点击区域树
    handleClick(node, data) {
      this.nodeData = data;
      // 类型为区域时
      if (this.nodeData.data.type === 2) {
        regionDetail(this.nodeData.data.id).then((res) => {
          this.regionType = res.data.type;
          this.clearAll();
          // 点击区域树时绘制区域
          const list = res.data.coordinate.split(' ').map(item => Number(item));
          let leftTop = [list[0],list[3]];
          let leftBottom = [list[0], list[1]];
          let rightBottom = [list[2], list[1]];
          let rightTop = [list[2], list[3]];
          let actions = {
            '1': () => {
              this.drawShape = MayMap.TileLayer.add({
                layer: this.layer,
                type: 'Rectangle',
                coordinate: [
                  [
                    leftTop,
                    leftBottom,
                    rightBottom,
                    rightTop,
                    leftTop
                  ]
                ]
              });
            },
            '2': () => {
              this.drawShape = MayMap.TileLayer.add({
                layer: this.layer,
                type: 'Circle',
                coordinate: [
                  list[0],
                  list[1]
                ],
                radius: list[2]
              });
            },
            '3': () => {
              let pointList = [];
              for (let index = 0; index < list.length; index += 2) {
                pointList.push([
                  list[index],
                  list[index + 1]
                ]);
              }
              this.drawShape = MayMap.TileLayer.add({
                layer: this.layer,
                type: 'Polygon',
                coordinate: [pointList]
              });
            },
            '4': () => {
              let pointList = [];
              for (let index = 0; index < list.length; index += 2) {
                pointList.push([
                  list[index],
                  list[index + 1]
                ]);
              }
              this.drawShape = MayMap.TileLayer.add({
                layer: this.layer,
                type: 'LineString',
                coordinate: pointList
              });
            }
          };
          actions[this.regionType].call(this);
          this.$nextTick(() => {
            this.$refs.MapWidget.setFitView(this.drawShape);
          });
        });
      }
      else {
        // 类型为分类时
        this.clearAll();
      }

    },
    // setEditRectangle(bounds) {
    //   this.drawShape = new this.AMap.Rectangle({
    //     bounds: bounds,
    //     fillColor: '#1791fc',
    //     fillOpacity: 0.4,
    //     strokeColor: '#3ca2fa',
    //     strokeWeight: 2,
    //     strokeOpacity: 1,
    //     borderWeight: 2
    //   });
    //   this.map.add(this.drawShape);
    // },
    // setEditPolyline(pointList) {
    //   this.drawShape = new this.AMap.Polyline({
    //     path: pointList,
    //     isOutline: true,
    //     outlineColor: '#ffeeff',
    //     strokeColor: '#1791fc',
    //     strokeWeight: 2,
    //     strokeOpacity: 1
    //   });
    //   this.map.add(this.drawShape);
    // },
    // setEditPolygon(pointList) {
    //   this.drawShape = new this.AMap.Polygon({
    //     path: pointList,
    //     fillColor: '#1791fc',
    //     fillOpacity: 0.4,
    //     strokeColor: '#3ca2fa',
    //     strokeWeight: 2,
    //     strokeOpacity: 1,
    //     borderWeight: 2
    //   });
    //   this.map.add(this.drawShape);
    // },
    // setEditCircle(circular) {
    //   this.drawShape = new this.AMap.Circle({
    //     center: new this.AMap.LngLat(circular.longitude, circular.latitude),
    //     radius: circular.radius,
    //     fillColor: '#1791fc',
    //     fillOpacity: 0.4,
    //     strokeColor: '#3ca2fa',
    //     strokeWeight: 2,
    //     strokeOpacity: 1,
    //     borderWeight: 2
    //   });
    //   this.map.add(this.drawShape);
    // },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    getTreeData() {
      regionGroups().then((res) => {
        this.treeData = res.data;
      });
    },
    handleRegionClick() {
      this.regionType = '2';
      this.dialogRegionVisible = true;
      this.startDraw(this.regionType);
      if (this.nodeData.id) {
        let query = {
          pName: this.nodeData.data.type === 1 ? this.nodeData.data.name : this.nodeData.parent.data.name,
          classifyId: this.nodeData.data.type === 1 ? this.nodeData.data.id : this.nodeData.parent.data.id,
          deptId: this.nodeData.data.type === 1 ? this.nodeData.data.deptId : this.nodeData.parent.data.deptId
        };
        this.$refs.region.setFormVal(query);
      }
      this.$nextTick(() => {
        if (this.nodeData.data && this.nodeData.data.type === 2) {
          this.nodeData = {};
          this.$refs.tree.setCurrentKey(null);
        }
      });
    },
    handleClassifyClick() {
      this.dialogClassifyVisible = true;
      const deptList = this.userInfo.dept_id.split(',');
      this.classifyForm = {
        deptId: deptList[0]
      };
      if (this.nodeData.id) {
        if (this.nodeData.data.type === 1) {
          this.classifyForm.pName = this.nodeData.data.name;
          this.classifyForm.pid = this.nodeData.data.id;
        }
        else {
          this.classifyForm.pName = this.nodeData.parent.data.name;
          this.classifyForm.pid = this.nodeData.parent.data.id;
        }
      }
      this.$nextTick(() => {
        if (this.nodeData.data && this.nodeData.data.type === 2) {
          this.clearAll();
          this.nodeData = {};
          this.$refs.tree.setCurrentKey(null);
        }
      });
    },
    handleEditClick() {
      this.classifyForm = {};
      if (!this.nodeData.id) {
        this.$message.warning('请选择编辑的数据');
        return false;
      }
      if (this.nodeData.data.type === 1) {
        this.dialogClassifyVisible = true;
        this.classifyForm.pName = this.nodeData.parent.data.name;
        this.classifyForm.pid = this.nodeData.parent.data.id;
        this.classifyForm.name = this.nodeData.data.name;
        this.classifyForm.id = this.nodeData.data.id;
        this.classifyForm.deptId = this.nodeData.data.deptId;
      }
      else {
        this.dialogRegionVisible = true;
        let query = {
          pName: this.nodeData.parent.data.name,
          classifyId: this.nodeData.parent.data.id,
          name: this.nodeData.data.name,
          id: this.nodeData.data.id,
          type: this.regionType,
          deptId: this.nodeData.data.deptId
        };
        this.$refs.region.setFormVal(query);
        // 开启图形编辑
        console.log('开启图形编辑');
        this.editShape({ obj: this.drawShape });
      }
    },
    clearAll(id) {
      if (!id) {
        // 区域新增
        // this.$refs.MapWidget.mousetool.close(true);
        this.$refs.MapWidget.clearMap();
        // this.closeShape();
      }
      else {
        // 区域编辑
        // this.$refs.MapWidget.mousetool.close(false);
        // this.closeShape();
      }
    },
    // 开始绘制
    startDraw(type) {
      console.log('-> type', type)
      this.layer.getSource().clear();
      let actions = {
        '1': () => {
          console.info('--> 绘制矩形');
          this.$refs.MapWidget.clearMap();
          MayMap.TileLayer.MouseTool({
            layer: this.layer,
            type: 'Rectangle',
            callback: (res) => {
              console.log(res);
              const coordinate = res.coordinate;
              // 矩形按照高德格式传 左下 右上两个坐标 国能地图的矩形顺序为 左上 左下 右下 右上
              this.shapeEditor = {
                ...res,
                coordinate: [
                  [
                    [coordinate[0][1][0]],
                    [coordinate[0][1][1]],
                  ],
                  [
                    [coordinate[0][3][0]],
                    [coordinate[0][3][1]],
                  ]
                ]
              };
              this.setCoordinateVal();
            }
          });
        },
        '2': () => {
          console.info('--> 绘制圆形');
          this.$refs.MapWidget.clearMap();
          MayMap.TileLayer.MouseTool({
            layer: this.layer,
            type: 'Circle',
            callback: (res) => {
              console.log(res);
              this.shapeEditor = res;
              this.setCoordinateVal();
            }
          });
        },
        '3': () => {
          console.info('--> 绘制多边形');
          this.$refs.MapWidget.clearMap();
          MayMap.TileLayer.MouseTool({
            layer: this.layer,
            type: 'Polygon',
            callback: (res) => {
              console.log(res);
              this.shapeEditor = res;
              this.setCoordinateVal();
            }
          });
        },
        '4': () => {
          console.info('--> 绘制线段');
          this.$refs.MapWidget.clearAll();
          MayMap.TileLayer.MouseTool({
            layer: this.layer,
            type: 'LineString',
            callback: (res) => {
              console.log(res);
              this.shapeEditor = res;
              this.setCoordinateVal();
            }
          });
        }
      };
      actions[type].call(this);
    },
    // 绘制图形后触发的事件
    // getAreaInfo(e) {
    //   this.editShape(e, 'draw');
    //   // 关闭鼠标工具，不清除覆盖物
    //   this.$refs.MapWidget.mousetool.close(false);
    //   // 注销事件
    //   this.$refs.MapWidget.mousetool.off('draw', this.getAreaInfo);
    // },
    // 编辑图形
    editShape(e) {
      console.log('-> 编辑图形类型', this.regionType);
      let actions = {
        '1': () => {
          MayMap.TileLayer.MouseToolEditor({
            layer: this.layer,
            feature: e.obj,
            callback: (res) => {
              console.log(res);
              this.shapeEditor = res;
              this.setCoordinateVal();
            }
          });
        },
        '2': () => {
          MayMap.TileLayer.MouseToolEditor({
            layer: this.layer,
            feature: e.obj,
            callback: (res) => {
              console.log(res, '圆形');
              this.shapeEditor = res;
              this.setCoordinateVal();
            }
          });
        },
        '3': () => {
          MayMap.TileLayer.MouseToolEditor({
            layer: this.layer,
            feature: e.obj,
            callback: (res) => {
              console.log(res);
              this.shapeEditor = res;
              this.setCoordinateVal();
            }
          });
        },
        '4': () => {
          MayMap.TileLayer.MouseToolEditor({
            layer: this.layer,
            feature: e.obj,
            callback: (res) => {
              console.log(res);
              this.shapeEditor = res;
              this.setCoordinateVal();
            }
          });
        }
      };
      actions[this.regionType].call(this);
    },
    // 获取图形数据
    setCoordinateVal() {
      let actions = {
        '1': () => {
          console.log('-> this.shapeEditor 矩形', this.shapeEditor);
          if (this.shapeEditor) {
            let rectanglePoints = this.shapeEditor.coordinate.flat(2);
            console.log('-> rectanglePoints', rectanglePoints);
            this.$refs.region.setFormVal({ coordinate: rectanglePoints.join(' ') });
          }
        },
        '2': () => {
          console.log('-> this.shapeEditor 圆形', this.shapeEditor);
          if (this.shapeEditor) {
            let circleParam = this.shapeEditor;
            if (circleParam) {
              let val = circleParam.coordinate[0] + ' ' + circleParam.coordinate[1] + ' ' + circleParam.radius;
              this.$refs.region.setFormVal({ coordinate: val });
            }
          }
        },
        '3': () => {
          console.log('-> this.shapeEditor 多边形', this.shapeEditor);
          if (this.shapeEditor) {
            let rectPath = this.shapeEditor.coordinate.flat(2);
            let polygonPoints = [];
            rectPath.length > 0 && rectPath.forEach(item => {
              polygonPoints.push(item);
            });
            this.$refs.region.setFormVal({ coordinate: polygonPoints.join(' ') });
          }
        },
        '4': () => {
          console.log('-> this.shapeEditor 线', this.shapeEditor);
          if (this.shapeEditor) {
            let rectPath = this.shapeEditor.coordinate.flat(2);
            let polygonPoints = [];
            rectPath.length > 0 && rectPath.forEach(item => {
              polygonPoints.push(item);
            });
            this.$refs.region.setFormVal({ coordinate: polygonPoints.join(' ') });
          }
        }
      };
      actions[this.regionType].call(this);
    },
    // 关闭绘制
    closeShape() {
      this.shapeEditor = null;
      this.$refs.MapWidget.clearAll();
    },
    clearMap() {
      // 右键清除了地图绘制的内容 需要重新调用绘制方法
      // this.startDraw(this.regionType);
    }
  }
};
</script>

<style lang="less" scoped>
.basic-container {
  padding: 0;
}

.region {
  display: flex;
  justify-content: space-between;
  height: 100%;
  // padding: 0 4px 4px;
}

.region-tree {
  width: 340px;
  height: 100%;
  margin-right: 10px;

  .region-input {
    margin: 5px 5px 5px 0;
  }

  .filter-tree {
    height: calc(100% - 80px);
    overflow-y: auto;
  }

  .my-modal {
    width: 340px;
    height: calc(100% - 41px);
    background-color: rgba(0, 0, 0, 0.2);
    position: absolute;
    top: 37px;
    left: 5px;
    z-index: 99;
  }
}

.region-map {
  flex: 1;
  // width: calc(100% - 345px);

  ::v-deep .el-card__body {
    padding: 0 !important;
  }

  // .liveMap {
  //   height: calc(100vh - @newLogoBarHeight - @newTagsViewHeight - @newBottomBlankHeight);
  // }
}

.region-dialog {
  pointer-events: none;

  ::v-deep .el-dialog {
    pointer-events: auto;
    margin-left: 250px;
  }
}

.region-input-container {
  display: flex;
  align-items: center;
}

.btn-group {
  display: flex;
  margin-top: 5px;

  button {
    flex: 1;
  }
}

.el-button-group > .el-button:not(:last-child) {
  margin-right: 1px;
}
</style>
