<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :modal="false"
    :visible="dialogVisible"
    title="电子围栏"
    width="400px"
    top="35vh"
    custom-class="elect-line-dialog"
    @close="close"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="100px"
    >
      <el-row span="24">
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <el-form-item
            label="所属分类"
            prop="pName"
          >
            <xh-select
              ref="select"
              v-model="form.pName"
              placeholder="请选择所属分类"
            >
              <el-option>
                <el-tree
                  ref="tree"
                  :data="data"
                  :props="defaultProps"
                  node-key="id"
                  :expand-on-click-node="false"
                  :check-on-click-node="true"
                  @node-click="handleNodeClick"
                />
              </el-option>
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <el-form-item
            label="电子围栏"
            prop="name"
          >
            <el-input
              v-model.trim="form.name"
              maxlength="20"
              show-word-limit
              placeholder="请输入电子围栏名称"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <el-form-item
            label="电子围栏类型"
            prop="type"
          >
            <el-radio-group
              v-model="form.type"
              @input="radioHandle"
            >
              <el-radio-button label="2">圆形</el-radio-button>
              <el-radio-button label="1">矩形</el-radio-button>
              <el-radio-button label="3">多边形</el-radio-button>
              <el-radio-button label="4">线形</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="$emit('update:dialogVisible', false);"
      >
        取消
      </el-button>
      <el-button
        :loading="loading"
        type="primary"
        size="small"
        @click="handleSubmit"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { regionAdd, regionEdit } from '@/api/base/regionManage';
export default {
  props:{
    dialogVisible: {
      type: Boolean,
      default: false
    },
    treeData: {
      type: Array,
      default: ()=>{return [];}
    }
  },
  data(){
    return{
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      form:{
        pName: '',
        classifyId: null,
        name: '',
        coordinate: '',
        type: '2',
        deptId: '',
        deptName: ''
      },
      rules: {
        name: { required: true, message: '请输入电子围栏名称', trigger: 'blur' },
        pName: { required: true, message: '请选择所属分类', trigger: 'change' }
      },
      loading: false,
      data: []
    };
  },
  watch:{
    treeData:{
      handler(newValue){
        console.log('-> newValue', newValue)
        this.data = JSON.parse(JSON.stringify(newValue));
        this.dispose(this.data);
        console.log('-> this.data', this.data)
      },
      deep: true
    },
    dialogVisible: {
      handler(newValue){
        if (newValue) {
          this.$nextTick(()=>{
            this.$refs.form.clearValidate();
          });
        }
      }
    }
  },
  methods:{
    radioHandle(){
      this.$emit('drawHandle', this.form.type);
      this.$nextTick(() => {
        // 清空保存的图形数据
        this.form.coordinate = '';
      });
    },
    setFormVal(val){
      this.form = {...this.form, ...val};
      if (this.form.classifyId) {
        this.$nextTick(()=>{
          this.$refs.tree.setCurrentKey(this.form.classifyId);
        });
      }
    },
    dispose(data){
      for (let index = 0; index < data.length; index++) {
        const element = data[index];
        if (element.type === 2) {
          data.splice(index, 1);
          index--;
        }
        if (element.children && element.children.length) {
          this.dispose(element.children);
        }
      }
    },
    handleSubmit(){
      // 获取最新的图形数据
      if (!this.form.coordinate) {
        this.$message.warning('请先绘制电子围栏');
        return false;
      }
      this.$emit('closeShape');
      this.$nextTick(()=>{
        this.$refs.form.validate((valid) => {
          if (valid) {
            let form = JSON.parse(JSON.stringify(this.form));
            if (!this.form.id) {
              regionAdd(form).then((res)=>{
                this.$message.success(res.msg);
                this.$emit('update:dialogVisible', false);
                this.$emit('getTreeData');
              });
            }else{
              regionEdit(form).then((res)=>{
                this.$message.success(res.msg);
                this.$emit('update:dialogVisible', false);
                this.$emit('getTreeData');
                // 编辑成功清空地图元素
                this.$emit('clearAll');
              });
            }
          }
        });
      });
    },
    close(){
      this.$emit('clearAll', this.form.id);
      this.$emit('update:dialogVisible', false);
      this.form = {
        pName: '',
        classifyId: null,
        name: '',
        coordinate: '',
        type: '2',
        deptId: '',
        deptName: ''
      };
    },
    handleNodeClick(node){
      if(node.type === 2) {
        return false;
      }
      this.$set(this.form, 'pName', node.name);
      this.$set(this.form, 'classifyId', node.id);
      this.$set(this.form, 'deptId', node.deptId);
      this.$refs.select.blur();
    },
  }
};
</script>
<style lang="less" scoped>
/deep/ .elect-line-dialog{
  left: 405px;
}
</style>
