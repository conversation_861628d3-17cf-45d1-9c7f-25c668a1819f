<template>
  <el-dialog
    :close-on-click-modal="false"
    :before-close="beforeClose"
    :visible="dialogVisible"
    title="电子围栏分类"
    append-to-body
    width="30%"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="100px"
    >
      <el-row span="24">
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <el-form-item
            label="所属分类"
            prop="pName"
          >
            <xh-select
              ref="select"
              v-model="form.pName"
              placeholder="请选择所属分类"
            >
              <el-option>
                <el-tree
                  ref="tree"
                  :data="data"
                  :props="defaultProps"
                  node-key="id"
                  :expand-on-click-node="false"
                  :check-on-click-node="true"
                  @node-click="handleNodeClick"
                />
              </el-option>
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <el-form-item
            label="分类名称"
            prop="name"
          >
            <el-input
              v-model.trim="form.name"
              maxlength="20"
              show-word-limit
              placeholder="请输入分类名称"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <el-form-item
            label="所属部门"
            prop="deptId"
          >
            <DeptFormSingleSelect
              ref="deptIdRef"
              v-model="form.deptId"
              :is-region="true"
              :detail-name="form.deptName"
              :is-show="dialogVisible"
              placeholder="请选择所属机构"
              size="small"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="handleCancel"
      >
        取消
      </el-button>
      <el-button
        :loading="loading"
        type="primary"
        size="small"
        @click="handleSubmit"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { regionlassifyAdd, regionlassifyEdit } from '@/api/base/regionManage';
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';
export default {
  components: {
    DeptFormSingleSelect
  },
  props:{
    dialogVisible: {
      type: Boolean,
      default: false
    },
    treeData: {
      type: Array,
      default: ()=>{return [];}
    },
    classifyForm: {
      type: Object,
      default: ()=>{return {};}
    }
  },
  data(){
    return{
      defaultProps: {
        children: 'children',
        label: 'name',
      },
      form:{
        pName: '',
        pid: null,
        name: '',
        deptId: '',
        deptName: ''
      },
      rules: {
        name: { required: true, message: '请输入分类名称', trigger: 'blur' },
        pName: { required: true, message: '请选择所属分类', trigger: 'change' },
        deptId: { required: true, message: '请选择所属机构', trigger: 'change' }
      },
      loading: false,
      data: [],
    };
  },
  watch:{
    treeData:{
      handler(newValue){
        this.data = JSON.parse(JSON.stringify(newValue));
        this.dispose(this.data);
      },
      deep: true
    },
    classifyForm: {
      handler(newValue){
        this.form = {...this.form, ...newValue};
        if (this.form.pid) {
          this.$nextTick(()=>{
            this.$refs.tree.setCurrentKey(this.form.pid);
          });
        }
      },
      deep: true
    }
  },
  methods:{
    dispose(data){
      for (let index = 0; index < data.length; index++) {
        const element = data[index];
        if (element.type === 2) {
          data.splice(index, 1);
          index--;
        }
        if (element.children && element.children.length) {
          this.dispose(element.children);
        }
      }
    },
    handleSubmit(){
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          let form = JSON.parse(JSON.stringify(this.form));
          if (!this.form.id) {
            regionlassifyAdd(form).then((res)=>{
              this.loading = false;
              this.$message.success(res.msg);
              this.beforeClose();
              this.$emit('getTreeData');
            }).catch((err)=>{
              this.loading = false;
            });
          }else{
            regionlassifyEdit(form).then((res)=>{
              this.loading = false;
              this.$message.success(res.msg);
              this.beforeClose();
              this.$emit('getTreeData');
            }).catch((err)=>{
              this.loading = false;
            });
          }
        }
      });
    },
    handleCancel(){
      this.beforeClose();
    },
    beforeClose(){
      this.$emit('update:dialogVisible', false);
      this.form = {
        pName: '',
        pid: null,
        name: '',
        deptId: '',
        deptName: '',
      };
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    handleNodeClick(node){
      this.$set(this.form, 'pName', node.name);
      this.$set(this.form, 'pid', node.id);
      this.$refs.select.blur();
    },
  }
};
</script>
<style lang="less" scoped>

</style>
