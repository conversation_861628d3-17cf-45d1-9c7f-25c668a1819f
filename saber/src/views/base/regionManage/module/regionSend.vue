<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :append-to-body="true"
      :fullscreen="true"
      class="dialog"
      title="终端电子围栏下发管理"
      @close="closeDialog"
    >
      <div class="car-container">
        <VehicleMultiSelectAllTree
          :is-show="true"
          @checkedVehiclesChange="checkedVehiclesChange"
          @treeNodeClick="treeNodeClick"
        />
      </div>
      <div class="table-container">
        <div class="crud-opts">
          <el-button
            class="filter-item"
            size="small"
            type="primary"
            icon="el-icon-plus"
            @click="handleAdd"
          >
            新 增
          </el-button>
        </div>
        <el-table
          ref="table"
          v-loading="tableLoading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="tableData"
          :cell-style="{'text-align':'center'}"
          class="table"
        >
          <el-table-column
            label="操作"
            width="120"
            align="center"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                class="table-button-edit"
                size="small"
                type="text"
                @click="toEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                class="table-button-del"
                type="text"
                size="small"
                :loading="delLoading"
                @click="toDelete(scope.row.id)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
          <el-table-column
            prop="fence.name"
            :label="getLabel('fenceName')"
            min-width="120"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            prop="fence.type"
            :label="getLabel('fenceType')"
            min-width="120"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.fence.type === '1' ? '矩形' : scope.row.fence.type === '2' ? '圆形' : scope.row.fence.type === '3' ? '多边形' : scope.row.fence.type === '4' ? '线形' : '-' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="enableTime"
            :label="getLabel('enableTime')"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <div
                v-if="scope.row.enableTime && scope.row.enableEveryday"
                class="xh-table-cell-same-line table-date-td"
              >
                [开始: {{ scope.row.everydayStart }}] [结束: {{ scope.row.everydayEnd }}]
              </div>
              <div
                v-else-if="scope.row.enableTime && !scope.row.enableEveryday"
                class="xh-table-cell-same-line table-date-td"
              >
                [开始: {{ parseTime(scope.row.startTime) }}] [结束: {{ parseTime(scope.row.endTime) }}]
              </div>
              <div v-else>
                无
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="enableSpeed"
            :label="getLabel('enableSpeed')"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <div
                v-if="scope.row.enableSpeed"
                class="xh-table-cell-same-line"
              >
                [速度: {{ scope.row.highSpeed }}km/h] [夜间: {{ scope.row.nightSpeed }}km/h] [时长: {{ scope.row.duration }}秒]
              </div>
              <div v-else>
                无
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="enterDriver"
            :label="getLabel('enterDriver')"
            width="80"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.enterDriver ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="outDriver"
            :label="getLabel('outDriver')"
            width="80"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.outDriver ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="enterPlatform"
            :label="getLabel('enterPlatform')"
            width="80"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.enterPlatform ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="outPlatform"
            :label="getLabel('outPlatform')"
            width="80"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.outPlatform ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="enterCloseTcp"
            :label="getLabel('enterCloseTcp')"
            width="80"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.enterCloseTcp ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="enterGatherGnss"
            :label="getLabel('enterGatherGnss')"
            width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.enterGatherGnss ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
    </el-dialog>
    <RegionForm
      ref="regionForm"
      :dialog-form-visible.sync="dialogFormVisible"
      :tree-data="treeData"
      :row-data="rowData"
      :submit-loading="submitLoading"
      @handleSubmit="handleSubmit"
    />
  </div>
</template>

<script>
import VehicleMultiSelectAllTree from '@/components/select/VehicleMultiSelectTree/VehicleMultiSelectAllTree';
import getLabel from '@/utils/getLabel';
import RegionForm from './regionForm';
import { querypolybyvehicle, setpolybyvehicle, polybyVehicleAll, editpolybyvehicle, deletepolybyvehicle } from '@/api/base/regionManage';
import { parseTime } from '@/api/utils/share';
export default {
  components: {
    RegionForm,
    VehicleMultiSelectAllTree
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    treeData: {
      type: Array,
      default: ()=>{return [];}
    }
  },
  data() {
    return {
      tableLoading: false, // 表格加载中
      tableData: [], // 表格数据
      dialogFormVisible: false, // 表单弹窗
      deviceType: null,
      deviceId: null,
      delLoading: false,
      rowData: {},
      submitLoading: false,
      vehicleList: []
    };
  },
  methods: {
    // 点击编辑时
    toEdit(data) {
      this.rowData = JSON.parse(JSON.stringify(data));
      this.dialogFormVisible = true;
    },
    // 点击删除时
    toDelete (data) {
      this.$confirm(`确定将选择数据删除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.delLoading = true;
        let params = {
          id: data
        };
        deletepolybyvehicle(params).then(res => {
          this.$message.success('电子围栏删除成功');
          this.getTableData();
          this.delLoading = false;
        }).catch(err => {
          this.delLoading = false;
        });
      }).catch(() => {
      });
    },
    // 获取表格数据
    getTableData() {
      this.tableLoading = true;
      let params = {
        deviceType: this.deviceType,
        deviceId: BigInt(this.deviceId)
      };
      querypolybyvehicle(params).then(res => {
        this.tableLoading = false;
        this.tableData = res.data;
      }).catch(err => {
        this.tableLoading = false;
      });
    },
    // 表单点击提交时
    handleSubmit(data) {
      let params = { ...data };
      if (data.id) {
        params.deviceType = this.deviceType;
        params.deviceId = BigInt(this.deviceId);
      } else {
        const list = this.vehicleList.map(item => `${item.deviceType}:${item.deviceId}`);
        params.vehicleIds = list;
      }
      let interfaceName = data.id ? editpolybyvehicle : polybyVehicleAll;
      this.submitLoading = true;
      interfaceName(params).then(res => {
        this.$message.success(data.id ? '电子围栏编辑成功' : '电子围栏新增成功');
        if (this.deviceId) {
          this.getTableData();
        }
        this.dialogFormVisible = false;
        this.$nextTick(() => {
          this.submitLoading = false;
        });
      }).catch(err => {
        this.dialogFormVisible = false;
        this.$nextTick(() => {
          this.submitLoading = false;
        });
      });
    },
    // 点击新增时
    handleAdd() {
      if (!this.vehicleList.length) {
        this.$message.warning('请先勾选需要下发的终端');
        return false;
      }
      this.dialogFormVisible = true;
    },
    // 勾选车辆时
    checkedVehiclesChange(data) {
      this.vehicleList = data.map(item => ({
        targetName: item.name,
        deviceId: item.id,
        deviceType: item.deviceType
      }));
    },
    // 点击车辆时
    treeNodeClick(data) {
      if (data && data.type === undefined) {
        this.deviceType = data.deviceType;
        this.deviceId = data.id;
        this.getTableData();
      }
    },
    closeDialog () {
      this.$emit('update:dialogVisible', false);
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('RegionManage', value);
    },
    parseTime
  }
};
</script>

<style lang="less" scoped>
.el-dialog__wrapper {
    // padding: 10px;
}
.dialog {
  /deep/ .el-dialog__body {
    padding: 10px;
    background: #f5f5f5;
    height: calc(100vh - 20px - 70px);
    max-height: max-content;
    display: flex;
    justify-content: space-between;
  }
  /deep/ .el-dialog {
    width: 99%;
    height: 98%;
  }
}
.car-container {
    width: 400px;
}
.table-container {
    width: calc(100% - 410px);
    display: flex;
    flex-direction: column;
    /deep/ .el-table {
      flex: 1;
    }
}
.table-btn {
  height: 36px;
}
</style>
