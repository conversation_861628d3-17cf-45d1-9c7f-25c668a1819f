<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="120px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="batchPer"
          :download="false"
        >
          <template slot="right">
            <el-button
              v-permission="batchPer.imp"
              class="filter-item"
              icon="el-icon-upload2"
              size="small"
              @click="batchvisible = true"
            >
              导入
            </el-button>
            <el-button
              v-permission="batchPer.exp"
              :loading="crud.downloadLoading"
              class="filter-item"
              size="small"
              icon="el-icon-download"
              @click="handleExport"
            >
              导 出
            </el-button>
            <!-- 批量修改组织 -->
            <el-button
              v-permission="permission.updateDept"
              class="filter-item"
              size="small"
              icon="el-icon-download"
              @click="toUpdateDept"
            >
              修改组织
            </el-button>
          </template>
        </crudOperation>
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            v-permission="['admin','shipM:edit','shipM:del', 'shipM:unbind', 'shipM:bind']"
            label="操作"
            width="180"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetails(scope.row)"
                  >
                    详情
                  </el-button>
                  <el-button
                    v-show="!scope.row.uniqueId"
                    v-permission="permission.bind"
                    type="text"
                    size="small"
                    @click="bindTerminal(scope.row)"
                  >
                    绑定终端
                  </el-button>
                  <el-button
                    v-show="scope.row.uniqueId"
                    v-permission="permission.unbind"
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="unbindTerm(scope.row)"
                  >
                    解绑终端
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('number')"
            :label="getLabel('number')"
            :show-overflow-tooltip="true"
            min-width="180"
            prop="number"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('deptName')"
            :label="getLabel('deptName')"
            :show-overflow-tooltip="true"
            min-width="180"
            prop="deptName"
            :resizable="false"
          />
          <!-- 绑定终端类型 -->
          <el-table-column
            v-if="columns.visible('terminalCategories')"
            :label="getLabel('terminalCategories')"
            prop="terminalCategories"
            min-width="220"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 绑定赋码编号 -->
          <el-table-column
            v-if="columns.visible('deviceNum')"
            :label="getLabel('deviceNum')"
            prop="deviceNum"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 绑定序列号 -->
          <el-table-column
            v-if="columns.visible('uniqueId')"
            :label="getLabel('uniqueId')"
            prop="uniqueId"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('createTime')"
            :label="getLabel('createTime')"
            :show-overflow-tooltip="true"
            min-width="180"
            prop="createTime"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.createTime }}
              </span>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
      <!--表单渲染-->
      <eForm
        :dict="dict"
        :regTurn="regTurn"
        :regTurnM="regTurnM"
        :btnShow="btnShow"
        @cancelCU="cancel"
      />
      <BatchImportWithDept
        :visible="batchvisible"
        mod="ship"
        @close="batchvisible = false"
        @getBatchData="getBatchData"
      />
      <!-- 批量修改组织 -->
      <DeptDialog
        :visible="updateDeptVisible"
        mod="driver"
        @close="updateDeptVisible = false"
        @getBatchData="getChoosedDept"
      />
      <msgDialog
        ref="msgDialog"
        :msg-data="msgData"
      />
      <UniqueIdSelect
        :dialog-visible.sync="dialogVisible"
        :current-data="currentData"
        @toQuery="crud.toQuery"
      />
    </div>
  </basic-container>
</template>

<script>
import crudVehicle, {
  addbatch,
  unbind, updateDeptBatch
} from '@/api/base/shipManagement';
import eForm from './module/form';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';
import udOperation from '@/components/Crud/UD.operation';
import getLabel from '@/utils/getLabel';
import { getDeptPerInit } from '@/api/base/dept';
import BatchImport from '@/components/upload/batchImport.vue';
import msgDialog from '@/components/importErr';
import HeadCommon from '@/components/formHead/headCommon.vue';
import UniqueIdSelect from '@/components/select/uniqueIdSelect/index.vue';
import BatchImportWithDept from "@/components/upload/batchImportWithDept.vue";
import DeptDialog from "@/components/deptDialog/deptDialog.vue";

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('Vehicle', 'uniName'), // 车辆
  // query: {
  //   deptIdStr: getParamDeptId()
  // },
  crudMethod: { ...crudVehicle }
});

export default {
  name: 'Vehicle',
  components: {
    DeptDialog,
    BatchImportWithDept, eForm, crudOperation, pagination, udOperation, BatchImport, msgDialog, HeadCommon, UniqueIdSelect },
  mixins: [presenter(crud)],
  // 数据字典
  dicts: [
    'shipType'
  ],
  data () {
    this.regTurn = {
      number: /^[a-zA-z0-9]{1,13}$/,
      name: /^[\u4e00-\u9fa50-9]{1,10}$/,
      nameEn: /^[A-Za-z0-9 ]{1,18}$/,
      imoNumber: /^(UN)?\d{7}$/,
    };
    this.regTurnM = {
      number: '货船编号只能输入字母和数字，最多13个',
      name: '中文船名只能输入汉字和数字，最多10个',
      nameEn: '英文船名只能输入英文，数字和空格，最多18个',
      imoNumber: '国际海事组织编号IMO：默认开头为字母UN，后面加7个数字'
    };
    this.maxLen = {
      mmsi: 9,
      // imoNumber: 7,
      callSign: 7,
    };
    return {
      batchPer: {
        add: ['admin', 'shipM:add'],
        imp: ['admin', 'shipM:imp'],
        exp: ['admin', 'shipM:exp'],
        del: ['admin', 'shipM:del']
      },
      permission: {
        add: ['admin', 'shipM:add'],
        edit: ['admin', 'shipM:edit'],
        del: ['admin', 'shipM:del'],
        unbind: ['admin', 'shipM:unbind'],
        bind: ['admin', 'shipM:bind'],
        updateDept: ['admin', 'shipM:updateDept']
      },
      // 批量引入相关
      batchvisible: false,
      //批量修改组织
      updateDeptVisible: false,
      dataType: { // excel里文字对应的key
        '货船编号': 'number',
        '所属机构': 'deptId',
        '货船类型': 'category',
        '中文船名': 'name',
        '英文船名': 'nameEn',
        '海上移动通信业务标识MMSI': 'mmsi',
        '国际海事组织编号IMO': 'imoNumber',
        '呼号': 'callSign',
        '总重(t)': 'maxGross',
        '载重/净重(t)': 'net',
        "排水量(t)": 'displcement',
        '船长(mm)': 'length',
        '船宽(mm)': 'breadth',
        '船深(mm)': 'depth',
        '吃水(mm)': 'draught',
        '航速(km)': 'cruiseSpeed',
      },
      // 必填项
      typeRequired: ['number'],
      // 表单名称对应字典(表单名称与字典名称不一致时)
      typeDictName: {
        category: 'shipType',
      },
      headConfig: {
        item: {
          1: {
            name: '货船编号',
            type: 'input',
            value: 'number',
          },
          2: {
            name: '所属机构',
            type: 'extra',
            value: 'deptId'
          },
          3: {
            name: '绑定序列号',
            type: 'input',
            value: 'uniqueId'
          },
          4: {
            name: '绑定赋码编号',
            type: 'input',
            value: 'deviceNum'
          }
        },
        button: {
        }
      },
      //批量导入时选择的组织id
      importDeptId: 0,
      isHomeVehicle: false,
      btnShow: true, // 显示确认取消按钮
      msgData: [],
      dialogVisible: false,
      currentData: {}
    };
  },
  computed: {
    getTerminalType () {
      return (dictName, val) => {
        if (this.dict.dict[dictName] && val) {
          let str = '';
          let list = [...new Set(val.split(','))];
          for (let index = 0; index < list.length; index++) {
            const element = list[index];
            let name = this.dict.dict[dictName][element]?.label;
            str = str + name + ',';
          }
          return str.slice(0, -1);
        }
      };
    }
  },
  watch: {
    batchvisible(val) {
      if (val) {
        getDeptPerInit().then(({ data }) => {
          this.dict['deptId'] = data;
        });
      }
    }
  },
  beforeDestroy () {
    this.crud.resetQuery(false);
  },
  methods: {
    [CRUD.HOOK.beforeExport]() {
      // 获取当前选中的列
      const columnList = Object.keys(this.crud.props.tableColumns);
      let list = [];
      // 获取当前选中的字段名
      this.crud.query.columnNameList = columnList.filter((key) => this.crud.props.tableColumns[key].visible === true);
      // 获取当前选中的中文名称
      for (let index = 0; index < columnList.length; index++) {
        const element = columnList[index];
        if (this.crud.props.tableColumns[element].visible === true) {
          list.push(this.crud.props.tableColumns[element].label);
        }
      }
      this.crud.query.headNameList = list;
    },
    /** 刷新 - 之前 */
    [CRUD.HOOK.beforeRefresh] () {
      if (this.crud.query.ids) {
        this.crud.query.ids = null;
      }
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('ShipManagement', value);
    },
    /**
     * 获取某种字典类型的文字货船编号
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    // 获取excel的数据
    getBatchData (returnValue) {
      let data = returnValue.data;
      this.importDeptId = returnValue.deptId;
      if (!data?.length) {
        this.$message.error('导入数据不能为空！');
        this.$refs.msgDialog?.handleClose();
        return;
      }
      if (!this.reversalObj) {
        this.reversalObj = {};
        for (let k in this.dataType) {
          this.reversalObj[this.dataType[k]] = k;
        }
      }
      this.tipsKey = [];
      let arr = data.map((item, index) => {
        let obj = {};
        for (let key in item) {
          for (let k in this.dataType) {
            if (key === k) {
              obj[this.dataType[k]] = item[key];
            }
          }
        }
        this.typeTreeTurn(obj);
        this.typeRequiredTurn(obj, index);
        this.typeRegTurn(obj, index);
        this.handleExtra(obj, index);
        if(obj.imoNumber && !obj.imoNumber.startsWith('UN')) {
          obj.imoNumber = 'UN' + obj.imoNumber;
        }
        return obj;
      });
      if (this.tipsKey && this.tipsKey.length > 0) {
        let arr = [];
        this.tipsKey.forEach((item, index) => {
          if (item && item.length > 0) {
            const errList = [];
            item.forEach(v => {
              errList.push(v);
            });
            arr.push({
              sort: `第${index + 1}行`,
              details: errList.join(',')
            });
          }
        });
        this.msgData = arr;
        this.$refs.msgDialog.msgVisible = true;
      } else {
        this.addbatchPost(arr, this.importDeptId);
        returnValue.close();
      }
    },
    /**
     * 批量修改组织
     * @param returnValue
     */
    getChoosedDept(returnValue) {
      let choosedDeptId = returnValue.deptId;
      console.log("选择的数据为：", crud.selections);
      const ids = crud.selections?.length ? crud.selections.map(item => item.id) : null;
      let obj = {
        ids: ids,
        deptId: choosedDeptId
      };
      if (choosedDeptId) {
        this.updateDeptPost(obj);
        returnValue.close();
      }
    },
    updateDeptPost(arr) {
      updateDeptBatch(arr).then(res => {
        this.$message({
          showClose: true,
          message: res.msg,
          type: 'success'
        });
        this.crud.refresh();
      });
    },

    //弹出修改组织弹窗
    toUpdateDept() {
      if (!crud.selections || crud.selections.length < 1) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.updateDeptVisible = true;
    },
    handleExtra (obj, index) {
      const cur = {
        net: 0,
        length: 0,
        breadth: 0,
        depth: 0,
        maxGross: 3,
        net: 3,
        displcement: 3,
        cruiseSpeed: 3,
      };
      Object.keys(cur).forEach(key => {
        if (obj[key]) {
          if (!/^(\d)+(.\d+)?$/.test(obj[key])) {
            this.handleErrFun(index, key);
          } else {
            obj[key] = +obj[key].toFixed(cur[key]);
          }
        }
      });
      Object.keys(this.maxLen).forEach( v => {
        if(obj[v] && (obj[v].length > this.maxLen[v])) {
          this.handleErrFun(index, v);
        }
      });
    },
    typeRegTurn (obj, k) {
      Object.keys(this.regTurn).forEach(v => {
        const reg = this.regTurn[v];
        if (obj[v] && !reg.test(obj[v])) {
          this.handleErrFun(k, v);
        }
      });
    },
    handleErrFun (index, v) {
      const pL = this.reversalObj[v];
      if (pL) {
        if (!this.tipsKey[index]) {
          this.tipsKey[index] = [pL];
        } else if (!this.tipsKey[index].includes(pL)) {
          this.tipsKey[index].push(pL);
        }
      }
    },
    // 必填项判断
    typeRequiredTurn (obj, k, dataRequired = this.typeRequired) {
      dataRequired.forEach(v => {
        if (typeof (v) === 'object' && v.mod) {
          this.typeRequiredTurn(obj[v.mod], k, v.required);
        } else if (!obj[v] && obj[v] !== 0) {
          this.handleErrFun(k, v);
        }
      });
    },
    // 字典里的转key/id
    typeTreeTurn (obj) {
      for (let k in this.dict) {
        for (let j in obj) {
          if (k === j) {
            this.treeTurn(this.dict[k], obj[j], j);
            obj[j] = this.numKey;
            this.numKey = undefined;
          } else if (k === this.typeDictName[j]) {
            this.treeTurn(this.dict[this.typeDictName[j]], obj[j], j);
            obj[j] = this.numKey;
            this.numKey = undefined;
          }
        }
      }
    },
    getTreeTurn (str) {
      let obj = {
        value: 'value',
        label: 'label'
      };
      switch (str) {
      case 'deptId':
        obj.value = 'id';
        obj.label = 'title';
        break;
      default:
        break;
      }
      return obj;
    },
    // 递归找key/id
    treeTurn (tree, word, str, iValue = this.getTreeTurn(str).value, iLabel = this.getTreeTurn(str).label, iChildren = 'children') {
      tree.forEach(item => {
        if (!item.disabled && item[iLabel] === word) {
          this.numKey = item[iValue];
        } else if (item[iChildren] && item[iChildren].length > 0) {
          this.treeTurn(item[iChildren], word, str, iValue, iLabel, iChildren);
        }
      });
    },
    // 提交请求
    addbatchPost (arr, importDeptId) {
      addbatch(arr, importDeptId).then(res => {
        this.$message({
          showClose: true,
          message: res.msg,
          type: 'success'
        });
        this.crud.refresh();
      });
    },
    unbindTerm({id, number, uniqueId, targetType, deptId}) {
      this.$confirm(`${number} 是否解绑 ${uniqueId}终端？解绑成功后，若终端重新绑定，需要重新注册。`, '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      }).then(() => {
        unbind({id, targetType, deptId}).then( res => {
          this.crud.toQuery();
        });
      });
    },
    toDetails (param) {
      crud.toEdit(param);
      this.btnShow = false;
    },
    bindTerminal(row) {
      this.dialogVisible = true;
      this.currentData = JSON.parse(JSON.stringify(row));
    },
    cancel () {
      this.btnShow = true;
    },
    handleExport () {
      crud.toQuery();
      const ids =crud.selections?.length ? crud.selections.map( item => item.id) : null;
      crud.doExport(ids);
    },
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-input-number .el-input__inner {
  text-align: left;
}
.xh-container
  /deep/.el-table--enable-row-hover
  .el-table__body
  tr:hover
  > td.el-table__cell {
  background-color: #fcf0c1;
}
</style>
