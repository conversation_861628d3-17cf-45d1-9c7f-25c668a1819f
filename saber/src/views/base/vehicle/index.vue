<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="120px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="batchPer"
          :download="false"
        >
          <template slot="right">
            <el-button
              v-permission="batchPer.imp"
              class="filter-item"
              icon="el-icon-upload2"
              size="small"
              @click="batchvisible = true"
            >
              导入
            </el-button>
            <el-button
              v-permission="batchPer.exp"
              :loading="crud.downloadLoading"
              class="filter-item"
              size="small"
              icon="el-icon-download"
              @click="handleExport"
            >
              导 出
            </el-button>
          </template>
        </crudOperation>
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            v-permission="['admin','vehicle:edit','vehicle:del', 'vehicle:unbind', 'vehicle:bind']"
            label="操作"
            width="180"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetails(scope.row)"
                  >
                    详情
                  </el-button>
                  <el-button
                    v-show="!scope.row.uniqueId"
                    v-permission="permission.bind"
                    type="text"
                    size="small"
                    @click="bindTerminal(scope.row)"
                  >
                    绑定终端
                  </el-button>
                  <el-button
                    v-show="scope.row.uniqueId"
                    v-permission="permission.unbind"
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="unbindTerm(scope.row)"
                  >
                    解绑终端
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('number')"
            :label="getLabel('number')"
            :show-overflow-tooltip="true"
            min-width="120"
            prop="number"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('deptName')"
            :label="getLabel('deptName')"
            :show-overflow-tooltip="true"
            min-width="180"
            prop="deptName"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('category')"
            :label="getLabel('category')"
            :show-overflow-tooltip="true"
            min-width="100"
            prop="categoryName"
            :resizable="false"
          />
          <!-- 绑定终端类型 -->
          <el-table-column
            v-if="columns.visible('terminalCategories')"
            :label="getLabel('terminalCategories')"
            prop="terminalCategories"
            min-width="220"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 绑定赋码编号 -->
          <el-table-column
            v-if="columns.visible('deviceNum')"
            :label="getLabel('deviceNum')"
            prop="deviceNum"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 绑定序列号 -->
          <el-table-column
            v-if="columns.visible('uniqueId')"
            :label="getLabel('uniqueId')"
            prop="uniqueId"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('createTime')"
            :label="getLabel('createTime')"
            :show-overflow-tooltip="true"
            min-width="180"
            prop="createTime"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.createTime }}
              </span>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
      <!--表单渲染-->
      <eForm
        :vehicle-owner-id="vehicleOwnerId"
        :dict="dict"
        :rulebindrealtime="rulebindrealtime"
        :btnShow="btnShow"
        @cancelCU="cancel"
      />
      <BatchImport
        :visible="batchvisible"
        :rulebindrealtime="rulebindrealtime"
        mod="vehicle"
        @close="batchvisible = false"
        @getBatchData="getBatchData"
      />
      <msgDialog
        ref="msgDialog"
        :msg-data="msgData"
      />
      <UniqueIdSelect
        :dialog-visible.sync="dialogVisible"
        :current-data="currentData"
        @toQuery="crud.toQuery"
      />
    </div>
  </basic-container>
</template>

<script>
import crudVehicle, {
  addbatch,
  unbind
} from '@/api/base/vehicle';
import eForm from './module/form';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';
import udOperation from '@/components/Crud/UD.operation';
import getLabel from '@/utils/getLabel';
import { getDeptPerInit } from '@/api/base/dept';
import BatchImport from '@/components/upload/batchImport.vue';
import msgDialog from '@/components/importErr';
import HeadCommon from '@/components/formHead/headCommon.vue';
import UniqueIdSelect from '@/components/select/uniqueIdSelect/index.vue';

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('Vehicle', 'uniName'), // 车辆
  // query: {
  //   deptIdStr: getParamDeptId()
  // },
  crudMethod: { ...crudVehicle }
});

export default {
  name: 'Vehicle',
  components: { eForm, crudOperation, pagination, udOperation, BatchImport, msgDialog, HeadCommon, UniqueIdSelect },
  mixins: [presenter(crud)],
  // 数据字典
  dicts: [
    'vehicleModel', 'transportType', 'operatingStatus', 'bdmCarCategory', 'licenceColor', 'bdmDeviceType'
  ],
  data () {
    return {
      batchPer: {
        add: ['admin', 'vehicle:add'],
        imp: ['admin', 'vehicle:imp'],
        exp: ['admin', 'vehicle:exp'],
        del: ['admin', 'vehicle:del']
      },
      permission: {
        add: ['admin', 'vehicle:add'],
        edit: ['admin', 'vehicle:edit'],
        del: ['admin', 'vehicle:del'],
        unbind: ['admin', 'vehicle:unbind'],
        bind: ['admin', 'vehicle:bind']
      },
      vehicleOwnerId: [],
      // 批量引入相关
      batchvisible: false,
      dataType: { // excel里文字对应的key
        '车辆类型': 'category',
        '车辆编号/车牌号码': 'number',
        '所属机构': 'deptId',
        '车牌号码':'licencePlate',
        '车牌颜色':'licenceColor',
        '车架号':'vin',
        '最大马力':'maxPower',
        '制造商':'manufacturer',
        '额定载重':'ratedLoad',
        '车辆型号':'model',
      },
      // 必填项
      typeRequired: ['number', 'deptId', 'category'],
      // 表单名称对应字典(表单名称与字典名称不一致时)
      typeDictName: {
        category: 'bdmCarCategory',
      },
      headConfig: {
        item: {
          1: {
            name: '车辆编号',
            type: 'input',
            value: 'number',
          },
          2: {
            name: '车辆类型',
            type: 'select',
            value: 'category',
            dictOptions: 'bdmCarCategory'
          },
          3: {
            name: '所属机构',
            type: 'extra',
            value: 'deptId'
          },
          4: {
            name: '绑定序列号',
            type: 'input',
            value: 'uniqueId'
          },
          5: {
            name: '绑定赋码编号',
            type: 'input',
            value: 'deviceNum'
          }
        },
        button: {
        }
      },
      rulebindrealtime: [],
      btnShow: true, // 显示确认取消按钮
      msgData: [],
      dialogVisible: false,
      currentData: {}
    };
  },
  computed: {
    getTerminalType () {
      return (dictName, val) => {
        if (this.dict.dict[dictName] && val) {
          let str = '';
          let list = [...new Set(val.split(','))];
          for (let index = 0; index < list.length; index++) {
            const element = list[index];
            let name = this.dict.dict[dictName][element]?.label;
            str = str + name + ',';
          }
          return str.slice(0, -1);
        }
      };
    }
  },
  watch: {
    batchvisible(val) {
      if (val) {
        getDeptPerInit().then(({ data }) => {
          this.dict['deptId'] = data;
        });
      }
    }
  },
  beforeDestroy () {
    this.crud.resetQuery(false);
  },
  methods: {
    /** 导出 - 之前 */
    [CRUD.HOOK.beforeExport]() {
      // 获取当前选中的列
      const columnList = Object.keys(this.crud.props.tableColumns);
      let list = [];
      // 获取当前选中的字段名
      this.crud.query.columnNameList = columnList.filter((key) => this.crud.props.tableColumns[key].visible === true);
      // 获取当前选中的中文名称
      for (let index = 0; index < columnList.length; index++) {
        const element = columnList[index];
        if (this.crud.props.tableColumns[element].visible === true) {
          list.push(this.crud.props.tableColumns[element].label);
        }
      }
      this.crud.query.headNameList = list;
    },
    /** 刷新 - 之前 */
    [CRUD.HOOK.beforeRefresh]() {
      if (this.crud.query.ids) {
        this.crud.query.ids = null;
      }
    },
    // 查询对应车辆归属label
    getVehicleOwnerId (v) {
      let l;
      this.vehicleOwnerId.forEach(item => {
        if (v === item.value) {
          l = item.label;
        }
      });
      return l;
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Vehicle', value);
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    // 批量导入相关
    // 获取excel的数据
    // 批量导入相关
    // 获取excel的数据
    getBatchData (returnValue) {
      let data = returnValue.data;
      if(!data?.length) {
        this.$message.error('导入数据不能为空！');
        this.$refs.msgDialog?.handleClose();
        return;
      }
      if (!this.reversalObj) {
        this.reversalObj = {};
        for (let k in this.dataType) {
          this.reversalObj[this.dataType[k]] = k;
        }
      }
      this.tipsKey = [];
      let arr = data.map((item, index) => {
        let obj = {};
        for (let key in item) {
          for (let k in this.dataType) {
            if (key === k) {
              obj[this.dataType[k]] = item[key];
            }
          }
        }
        this.typeTreeTurn(obj);
        this.typeRequiredTurn(obj, index);
        this.handleExra(obj, index, item);
        return obj;
      });
      if (this.tipsKey && this.tipsKey.length > 0) {
        let arr = [];
        this.tipsKey.forEach((item, index) => {
          if (item && item.length > 0) {
            const errList = [];
            item.forEach(v => {
              errList.push(v);
            });
            arr.push({
              sort: `第${index + 1}行`,
              details: errList.join(',')
            });
          }
        });
        this.msgData = arr;
        this.$refs.msgDialog.msgVisible = true;
      } else {
        this.addbatchPost(arr);
        returnValue.close();
      }
    },
    handleErrFun (index, v) {
      const pL = this.reversalObj[v];
      if (pL) {
        if (!this.tipsKey[index]) {
          this.tipsKey[index] = [pL];
        } else if (!this.tipsKey[index].includes(pL)) {
          this.tipsKey[index].push(pL);
        }
      }
    },
    handleExra(obj, index, item) {
      if([1, 2].includes(+obj.category)) {
        if(obj.number?.length > 8) {
          this.handleErrFun(index, 'number');
        }
        if(obj.licenceColor) {
          obj.number += `-${item[this.reversalObj.licenceColor]}`;
        } else {
          this.handleErrFun(index, 'licenceColor');
        }
      } else {
        if(obj.number?.length > 15) {
          this.handleErrFun(index, 'number');
        }
      }
      const max = {
        vin: 15,
        manufacturer: 50,
        model: 15
      };
      Object.keys(max).forEach( key => {
        if(obj[key]?.length > max[key]) {
          this.handleErrFun(index, key);
        }
      });
      const num = ['maxPower', 'ratedLoad'];
      num.forEach(key => {
        if (obj[key]) {
          if (!/^(\d)+(.\d+)?$/.test(obj[key])) {
            this.handleErrFun(index, key);
          } else {
            obj[key] = +obj[key].toFixed(3);
          }
        }
      });
    },
    // 必填项判断
    typeRequiredTurn (obj, k, dataRequired = this.typeRequired) {
      dataRequired.forEach(v => {
        if (typeof (v) === 'object' && v.mod) {
          this.typeRequiredTurn(obj[v.mod], k, v.required);
        } else if (!obj[v] && obj[v] !== 0) {
          this.handleErrFun(k, v);
        }
      });
    },
    // 字典里的转key/id
    typeTreeTurn (obj) {
      for (let k in this.dict) {
        for (let j in obj) {
          if (k === j) {
            this.treeTurn(this.dict[k], obj[j],j);
            obj[j] = this.numKey;
            this.numKey = undefined;
          }else if (k === this.typeDictName[j]) {
            this.treeTurn(this.dict[this.typeDictName[j]], obj[j],j);
            obj[j] = this.numKey;
            this.numKey = undefined;
          }
        }
      }
    },
    getTreeTurn(str){
      let obj = {
        value:'value',
        label:'label'
      };
      switch (str) {
      case 'deptId':
        obj.value = 'id';
        obj.label = 'title';
        break;
      default:
        break;
      }
      return obj;
    },
    // 递归找key/id
    treeTurn (tree, word,str, iValue = this.getTreeTurn(str).value, iLabel = this.getTreeTurn(str).label, iChildren = 'children') {
      tree.forEach(item => {
        if (!item.disabled && item[iLabel] === word) {
          this.numKey = item[iValue];
        } else if (item[iChildren] && item[iChildren].length > 0) {
          this.treeTurn(item[iChildren], word, str, iValue,iLabel, iChildren);
        }
      });
    },
    // 提交请求
    addbatchPost (arr) {
      addbatch(arr).then(res => {

        this.$message({
          showClose: true,
          message: res.msg,
          type: 'success'
        });
        this.crud.refresh();
      });
    },

    toDetails(param){
      crud.toEdit(param);
      this.btnShow = false;
    },
    bindTerminal(row) {
      this.dialogVisible = true;
      this.currentData = JSON.parse(JSON.stringify(row));
    },
    unbindTerm({id, number, uniqueId, targetType, deptId}) {
      this.$confirm(`${number} 是否解绑 ${uniqueId}终端？解绑成功后，若终端重新绑定，需要重新注册。`, '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      }).then(() => {
        unbind({id, targetType, deptId}).then( res => {
          this.crud.toQuery();
        });
      });
    },
    cancel(){
      this.btnShow = true;
    },
    handleExport () {
      crud.toQuery();
      const ids =crud.selections?.length ? crud.selections.map( item => item.id) : null;
      crud.doExport(ids);
    },
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  .xh-container ::v-deep.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
    background-color: #fcf0c1;
  }
</style>
