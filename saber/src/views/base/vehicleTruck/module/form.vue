<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="btnShow?crud.status.title:'查看'"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      class="rewriting-form-disable"
      :hide-required-asterisk="!btnShow"
      label-width="150px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 车辆类型 -->
          <el-form-item
            :label="getLabel('category')"
            prop="category"
          >
            <el-cascader
              v-model="form.category"
              :options="dict.bdmTruckCategory"
              separator="/"
              :props="{
                expandTrigger: 'hover',
                emitPath: false
              }"
              :placeholder="getPlaceholder('category', 'select')"
              filterable
              :disabled="!btnShow || crud.status.edit"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('number')"
            prop="number"
          >
            <el-input
              v-model.trim="form.number"
              :placeholder="getPlaceholder('number')"
              maxlength="15"
              :disabled="!btnShow || crud.status.edit"
              @input="e => form.number = validInput(e)"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('deptId')"
            prop="deptId"
          >
            <DeptFormSingleSelect
              ref="deptIdRef"
              v-model="form.deptId"
              :detail-name="form.deptName"
              :disabled="!btnShow"
              :is-show="crud.status.cu > 0"
              placeholder="请选择所属机构"
              size="small"
              @input="validateTreeSelect('deptId')"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('vin')"
            prop="vin"
          >
            <el-input
              v-model.trim="form.vin"
              :placeholder="getPlaceholder('vin')"
              :disabled="!btnShow"
              maxlength="15"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('maxPower')+'(kw)'"
            prop="maxPower"
          >
            <el-input-number
              v-model="form.maxPower"
              style="width: 100%"
              :placeholder="getPlaceholder('maxPower')"
              :precision="3"
              controls-position="right"
              :min="0"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('manufacturer')"
            prop="manufacturer"
          >
            <el-input
              v-model.trim="form.manufacturer"
              :placeholder="getPlaceholder('manufacturer')"
              :disabled="!btnShow"
              maxlength="50"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('ratedLoad')+'(t)'"
            prop="ratedLoad"
          >
            <el-input-number
              v-model="form.ratedLoad"
              style="width: 100%"
              :placeholder="getPlaceholder('ratedLoad')"
              :precision="3"
              controls-position="right"
              :min="0"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('model')"
            prop="model"
          >
            <el-input
              v-model.trim="form.model"
              :placeholder="getPlaceholder('model')"
              :disabled="!btnShow"
              maxlength="15"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        v-if="btnShow"
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="btnShow"
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="submit"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import { validateFn } from '@/validate/vehicle/vehicleRules';
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';

const defaultForm = {
  number: null,
  category: null,
  deptId: null,
  deptName: null,
  vin: null,
  maxPower: null,
  manufacturer: null,
  ratedLoad: null,
  model: null,
  id: null
};
export default {
  components: { DeptFormSingleSelect },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      default: ()=>{return {};}
    },
    fromTerminal: {
      type: Boolean,
      default: false
    },
    bindVehicle: {
      type: String,
      default: ''
    },
    vehicleOwnerId: {
      type: Array,
      required: true
    },
    rulebindrealtime: {
      type: Array,
      required: true
    },
    btnShow: {
      type: Boolean,
      default: true
    },
  },
  data () {
    return {
      rules: {
        number: { required: true, validator: validateFn('Cap', 'number'), trigger: 'input' }, // 车辆编号
        category: { required: true, validator: validateFn('Cap', 'category'), trigger: 'change' }, // 车辆类型
        deptId: { required: true, validator: validateFn('Cap', 'deptId'), trigger: 'change' }, // 所属机构
      },
      oldForm: {},
      depts: []
    };
  },
  methods: {
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Vehicle', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value, pre) {
      return getPlaceholder('Vehicle', value, pre);
    },
    /** 新建 - 之前 */
    [CRUD.HOOK.beforeToAdd] () {
      this.$nextTick(()=>{
        this.form.terminalCategories = undefined;
      });
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
          this.$refs[`deptIdRef`].$refs[`deptIdStrRef`].$children[0].$el.style.borderColor = '#BFBFBF';
        }
      });
    },
    /** 点击取消之前*/
    [CRUD.HOOK.afterEditCancel] () {
      this.$emit('cancelCU');
    },
    /** 编辑 - 之后 */
    [CRUD.HOOK.afterToEdit] () {
      if (this.form.category !== null) this.form.category = this.form.category.toString();
      if (this.form.terminalCategories) {
        this.oldForm = JSON.parse(JSON.stringify(this.form));
      }
    },
    /** 提交 */
    submit () {
      this.$refs.form.validate((valid)=>{
        if (valid) {
          if (this.form.terminalCategories && (this.form.number !== this.oldForm.number)) {
            this.$confirm(`该车辆已绑定终端，修改后可能导致车辆在平台离线，请谨慎操作确认要修改?`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.crud.submitCU();
            });
          } else {
            this.crud.submitCU();
          }
        }
      });
    },
    /**
     * 验证treeSelect项
     * @param {String} item
     */
    validateTreeSelect (item) {
      this.$refs.form.validateField(item, (valid) => {
        this.$refs[`${item}Ref`].$refs[`${item}StrRef`].$children[0].$el.style.borderColor = valid !== '' ? '#F56C6C' : '#BFBFBF';
      });
    },
  }
};
</script>

<style lang="less" scoped>
  /deep/ .el-input__inner {
    text-align: left;
  }
  .tree-disabled {
    /deep/ .vue-treeselect__single-value {
      background-color: #F5F7FA;
      border-color: #E4E7ED;
      color: #C0C4CC;
      cursor: not-allowed;
    }
  }
</style>
