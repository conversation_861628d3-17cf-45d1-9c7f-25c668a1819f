<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="120px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="batchPer"
          :download="false"
        >
          <template slot="right">
            <el-button
              v-permission="batchPer.imp"
              class="filter-item cyan-btn"
              plain
              icon="el-icon-upload2"
              size="small"
              @click="batchvisible = true"
            >
              导入
            </el-button>
            <el-button
              v-permission="batchPer.exp"
              :loading="crud.downloadLoading"
              class="filter-item"
              size="small"
              plain
              icon="el-icon-download"
              @click="handleExport"
            >
              导 出
            </el-button>
            <!-- 批量修改组织 -->
            <el-button
              v-permission="permission.updateDept"
              class="filter-item"
              size="small"
              icon="el-icon-download"
              @click="toUpdateDept"
            >
              修改组织
            </el-button>
          </template>
        </crudOperation>
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <!--          <el-table-column-->
          <!--            type="index"-->
          <!--            label="#"-->
          <!--          />-->
          <el-table-column
            v-permission="['admin','visitor:edit','visitor:del']"
            width="190"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    v-permission="permission.bind"
                    type="text"
                    size="mini"
                    class="table-button-add"
                    @click="onBind(scope.row)"
                  >
                    绑定终端
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetails(scope.row)"
                  >
                    详情
                  </el-button>
                </template>
              </udOperation>

            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('name')"
            label="人员姓名"
            :show-overflow-tooltip="true"
            min-width="100"
            prop="name"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('idNumber')"
            label="身份证号"
            :show-overflow-tooltip="true"
            prop="idNumber"
            min-width="180"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('deptName')"
            label="所属机构"
            :show-overflow-tooltip="true"
            min-width="150"
            prop="deptName"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('industryName')"
            :show-overflow-tooltip="true"
            label="从业类型"
            min-width="90"
            prop="industryName"
            :resizable="false"
          >
          </el-table-column>
          <el-table-column
            v-if="columns.visible('postName')"
            label="岗位类型"
            :show-overflow-tooltip="true"
            min-width="150"
            prop="postName"
            :resizable="false"
          >
          </el-table-column>
          <el-table-column
            v-if="columns.visible('phone')"
            label="联系电话"
            :show-overflow-tooltip="true"
            min-width="120"
            prop="phone"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('company')"
            label="所属公司名称"
            :show-overflow-tooltip="true"
            min-width="150"
            prop="company"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('validFrom')"
            label="生效日期"
            :show-overflow-tooltip="true"
            min-width="130"
            prop="validFrom"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.validFrom }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('validTo')"
            label="终止日期"
            :show-overflow-tooltip="true"
            min-width="130"
            prop="validTo"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.validTo }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('statusName')"
            label="账号状态"
            :show-overflow-tooltip="true"
            min-width="100"
            prop="statusName"
            :resizable="false"
          />
          <!-- 绑定终端类型 -->
          <el-table-column
            v-if="columns.visible('terminalCategories')"
            :label="getLabel('terminalCategories')"
            prop="terminalCategories"
            min-width="220"
            show-overflow-tooltip
            :resizable="false"
          >
          </el-table-column>
          <el-table-column
            v-if="columns.visible('bindTerminal')"
            :label="getLabel('bindTerminal')"
            min-width="90"
            prop="bindTerminal"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span
                class="active-label"
                @click="read(scope.row)"
              >
                查看终端
              </span>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination/>
      <!--表单渲染-->
      <eForm
        :dict="dict"
        :regTurn="regTurn"
        :regTurnM="regTurnM"
        :maxLen="maxLen"
        :btnShow="btnShow"
        @cancelCU="cancel"
      />
      <bindVehicle
        :bind-dialog-visible.sync="bindDialogVisible"
        :bind-user="bindUser"
        :dict="dict"
        @closeBind="closeBind"
        @refresh="refresh"
      />
      <bindInfo
        v-if="bindInfoDialogVisible"
        :bindInfoDialogVisible="bindInfoDialogVisible"
        :dict="dict"
        :bind-user="bindUser"
        @closeBind="closeBind"
      />
      <BatchImportWithDept
        :visible="batchvisible"
        mod="visitor"
        @close="batchvisible = false"
        @getBatchData="getBatchData"
      />
      <!-- 批量修改组织 -->
      <DeptDialog
        :visible="updateDeptVisible"
        mod="driver"
        @close="updateDeptVisible = false"
        @getBatchData="getChoosedDept"
      />
      <msgDialog
        ref="msgDialog"
        :msg-data="msgData"
      />
    </div>
  </basic-container>
</template>

<script>
import crudVisitor, { addbatch } from '@/api/base/visitor';
import crudDistrict from '@/api/base/district';
import eForm from './module/form';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';
import udOperation from '@/components/Crud/UD.operation';
import getLabel from '@/utils/getLabel';
import BindVehicle from './module/bindVehicle.vue';
import bindInfo from './module/bindInfo.vue';
import BatchImport from '@/components/upload/batchImport.vue';
import msgDialog from '@/components/importErr'
import { getDeptPerInit } from '@/api/base/dept';
import HeadCommon from '@/components/formHead/headCommon.vue';
import { deviceType } from '@/api/base/visitor.js';
import { idNum, phoneNum } from "@/utils/validate";
import BatchImportWithDept from "@/components/upload/batchImportWithDept.vue";
import DeptDialog from "@/components/deptDialog/deptDialog.vue";
// crud交由presenter持有
const crud = CRUD({
  title: '',
  crudMethod: { ...crudVisitor }
});

export default {
  name: 'Visitor',
  components: {
    DeptDialog,
    BatchImportWithDept,
    eForm,
    crudOperation,
    pagination,
    udOperation,
    BindVehicle,
    BatchImport,
    msgDialog,
    HeadCommon,
    bindInfo,
  },
  mixins: [presenter(crud)],
  // 数据字典
  dicts: [
    'bdmWokerPost',
    "accStatus",
    'bdmDeviceType'
  ],
  data() {
    this.regTurn = {
      idNumber: idNum,
      phone: phoneNum,
      validFrom: /^\d[-|\d]+\d$/,
      validTo: /^\d[-|\d]+\d$/,
    }
    this.regTurnM = {
      idNumber: '请输入正确的身份证号码',
      phone: '请输入正确的电话号码或者固话号码',
    }
    this.maxLen = {
      name: 20,
      idNumber: 50,
      phone: 20,
      company: 50
    }
    return {
      batchPer: {
        add: ['admin', 'visitor:add'],
        imp: ['admin', 'visitor:imp'],
        exp: ['admin', 'visitor:exp'],
        del: ['admin', 'visitor:del']
      },
      permission: {
        add: [
          'admin',
          'visitor:add'
        ],
        edit: [
          'admin',
          'visitor:edit'
        ],
        del: [
          'admin',
          'visitor:del'
        ],
        bind: [
          'admin',
          'visitor:bind'
        ],
        updateDept: ['admin', 'visitor:updateDept']
      },
      bindDialogVisible: false,
      bindInfoDialogVisible: false,
      bindUser: {},
      //批量导入时选择的组织id
      importDeptId: 0,
      // 批量引入相关
      batchvisible: false,
      //批量修改部门
      updateDeptVisible: false,
      dataType: {
        // excel里文字对应的key
        人员姓名: "name",
        身份证号: "idNumber",
        所属机构: "deptId",
        从业类型: "industry",
        岗位类型: "post",
        联系电话: "phone",
        所属公司名称: "company",
        生效日期: "validFrom",
        终止日期: "validTo",
      },
      // 必填项
      typeRequired: [
        "name",
        "idNumber",
        "industry",
        "post",
        "phone",
        "company",
        "validFrom",
        "validTo",
      ],
      // 表单名称对应字典(表单名称与字典名称不一致时)
      typeDictName: {
      },
      cascade:['industry','post'],
      cascadeDictName:{
        industry: 'bdmWokerIndustry',
      },//级联对应字典
      headConfig: {
        item: {
          1: {
            name: '人员姓名',
            type: 'input',
            value: 'name'
          },
          2: {
            name: '身份证号',
            type: 'input',
            value: 'idNumber'
          },
          3: {
            name: '绑定终端类型',
            type: 'extra',
            value: 'terminalType',
            dictOptions: 'bdmDeviceType'
          },
          4: {
            name: '绑定序列号',
            type: 'input',
            value: 'terminalId'
          },
          5: {
            name: '从业类型',
            type: 'select',
            value: 'industry',
            dictsOptions: 'bdmWokerPost'
          },
          6: {
            name: '账号状态',
            type: 'select',
            value: 'status',
            dictOptions: 'accStatus'
          },
          7: {
            name: '绑定赋码编号',
            type: 'input',
            value: 'deviceNum'
          },
          8: {
            name: '所属机构',
            type: 'extra',
            value: 'deptId'
          }
        },
        button: {}

      },
      post: [],
      btnShow: true, // 显示确认取消按钮
      msgData: [],
    };
  },
  watch: {
    batchvisible(val) {
      if (val) {
        getDeptPerInit().then(({ data }) => {
          this.dict['deptId'] = data;
        });
      }
    }
  },
  computed: {
    getTerminalType() {
      return (dictName, val) => {
        if (this.dict.dict[dictName] && val) {
          let str = '';
          let list = [...new Set(val.split(','))];
          for (let index = 0; index < list.length; index++) {
            const element = list[index];
            let name = this.dict.dict[dictName][element]?.label;
            str = str + name + ',';
          }
          return str.slice(0, -1) || ''
        }
      };
    },
  },
  created() {
    this.crud.optShow = {
      add: true,
      edit: false,
      del: true,
      download: true
    };
    // deviceType().then(res => {
    //   if (res?.data) {
    //     let bdmDeviceType = res.data['0']?.children.filter(item => {
    //       return item.value === '1' || item.value === '2' || item.value === '3';
    //     });
    //     this.$set(this.dict, 'bdmDeviceType__', res.data['0']?.children || []);
    //     this.$set(this.dict.dict, 'bdmDeviceType__', {});
    //     this.$set(this.dict, 'bdmDeviceType', bdmDeviceType);
    //     this.$set(this.dict.dict, 'bdmDeviceType', {});
    //     let list = this.flattenList(res.data['0'].children);
    //     for (let index = 0; index < list.length; index++) {
    //       const element = list[index];
    //       this.$set(this.dict.dict['bdmDeviceType'], element.value, element);
    //     }
    //   }
    // });

  },
  methods: {
    [CRUD.HOOK.beforeExport]() {
      // 获取当前选中的列
      const columnList = Object.keys(this.crud.props.tableColumns);
      let list = [];
      // 获取当前选中的字段名
      this.crud.query.columnNameList = columnList.filter((key) => this.crud.props.tableColumns[key].visible === true);
      // 获取当前选中的中文名称
      for (let index = 0; index < columnList.length; index++) {
        const element = columnList[index];
        if (this.crud.props.tableColumns[element].visible === true) {
          list.push(this.crud.props.tableColumns[element].label);
        }
      }
      this.crud.query.headNameList = list;
    },
    refresh () {
      this.crud.refresh();
    },
    /**
     * 将多级嵌套的列表展平为单级列表。
     * @param {Array} data - 嵌套结构的列表数据，每个元素包含`label`和`value`属性，可能还有`children`子元素数组。
     * @returns {Array} 返回一个展平后的列表，其中每个元素只包含`label`和`value`属性。
     */
    flattenList(data) {
      var result = [];
      data.forEach((item)=> {
        result.push({ label: item.label, value: item.value });
        if (item.children && item.children.length > 0) {
          var childItems = this.flattenList(item.children);
          result = result.concat(childItems);
        }
      });
      return result;
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('Driver', value);
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    },
    // 关闭绑定车辆dialog
    closeBind() {
      this.bindInfoDialogVisible = false;
    },
    // 打开绑定车辆dialog
    onBind(row) {
      this.bindDialogVisible = true;
      this.bindUser = row;

    },
    // 批量导入相关
    // 获取excel的数据
    getBatchData (returnValue) {
      let data = returnValue.data;
      this.importDeptId = returnValue.deptId;
      if(!this.reversalObj) {
        this.reversalObj = {};
        for (let k in this.dataType) {
          this.reversalObj[this.dataType[k]] = k;
        }
      }
      this.tipsKey = [];
      let arr = data.map((item, index) => {
        let obj = {};
        for (let key in item) {
          for (let k in this.dataType) {
            if (key === k) {
              obj[this.dataType[k]] = item[key];
            }
          }
        }
        this.typeTimeTurn(obj, index);
        this.typeTreeTurn(obj);
        this.getCascadeKey(obj);
        this.typeRequiredTurn(obj, index);
        this.typeRegTurn(obj, index)
        this.handleExtra(obj, index, data)
        return obj;
      });
      if (this.tipsKey && this.tipsKey.length > 0) {
        let arr = [];
        this.tipsKey.forEach((item, index) => {
          if (item && item.length > 0) {
            const errList = []
            item.forEach(v => {
              errList.push(v)
            });
            arr.push({
              sort: `第${index + 1}行`,
              details: errList.join(',')
            });
          }
        });
        this.msgData = arr;
        this.$refs.msgDialog.msgVisible = true;
      } else {
        this.addbatchPost(arr, this.importDeptId);
        returnValue.close();
      }
    },


    /**
     * 批量修改组织
     * @param returnValue
     */
    getChoosedDept(returnValue) {
      let choosedDeptId = returnValue.deptId;
      console.log("选择的数据为：", crud.selections);
      const ids = crud.selections?.length ? crud.selections.map(item => item.id) : null;
      let obj = {
        ids: ids,
        deptId: choosedDeptId
      };
      if (choosedDeptId) {
        this.updateDeptPost(obj);
        returnValue.close();
      }
    },
    updateDeptPost(arr) {
      crudVisitor.updateDeptBatch(arr).then(res => {
        this.$message({
          showClose: true,
          message: res.msg,
          type: 'success'
        });
        this.crud.refresh();
      });
    },

    //弹出修改组织弹窗
    toUpdateDept() {
      if (!crud.selections || crud.selections.length < 1) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.updateDeptVisible = true;
    },


    handleExtra(obj, index, data) {
      const rList = ['industry', 'post', 'deptId']
        rList.forEach( v => {
          const pL = this.reversalObj[v]
          if(obj[v] && data[index][pL] === obj[v]) {
            this.handleErrFun(index, v)
          }
        })
    },
    typeRegTurn (obj, k) {
      Object.keys(this.regTurn).forEach(v => {
        const reg = this.regTurn[v]
        if (obj[v] && !reg.test(obj[v])) {
          this.handleErrFun(k, v)
        }
      })
      Object.keys(this.maxLen).forEach( v => {
        if(obj[v] && (obj[v].length > this.maxLen[v])) {
          this.handleErrFun(k, v)
        }
      })
    },
    handleErrFun(index, v) {
      const pL = this.reversalObj[v]
      if(pL) {
        if (!this.tipsKey[index]) {
          this.tipsKey[index] = [pL];
        } else if(!this.tipsKey[index].includes(pL)) {
          this.tipsKey[index].push(pL);
        }
      }
    },
    // 必填项判断
    typeRequiredTurn (obj, k, dataRequired = this.typeRequired) {
      dataRequired.forEach(v => {
        if (typeof (v) === 'object' && v.mod) {
          this.typeRequiredTurn(obj[v.mod], k, v.required);
        } else if (!obj[v] && obj[v] !== 0) {
          this.handleErrFun(k, v)
        }
      });
    },
    // 字典里的转key/id
    typeTreeTurn (obj) {
      for (let k in this.dict) {
        for (let j in obj) {
          if (k === j) {
            this.treeTurn(this.dict[k], obj[j],j);
            obj[j] = this.numKey;
            this.numKey = undefined;
          }else if (k === this.typeDictName[j]) {
            this.treeTurn(this.dict[this.typeDictName[j]], obj[j],j);
            obj[j] = this.numKey;
            this.numKey = undefined;
          }
        }
      }
    },
    getTreeTurn(str){
      let obj = {
        value:'value',
        label:'label'
      };
      switch (str) {
      case 'deptId':
        obj.value = 'id';
        obj.label = 'title';
        break;
      default:
        break;
      }
      return obj;
    },
    // 递归找key/id
    treeTurn (tree, word,str, iValue = this.getTreeTurn(str).value, iLabel = this.getTreeTurn(str).label, iChildren = 'children') {
      tree.forEach(item => {
        if (!item.disabled && item[iLabel] === word) {
          this.numKey = item[iValue];
        } else if (item[iChildren] && item[iChildren].length > 0) {
          this.treeTurn(item[iChildren], word, str, iValue,iLabel, iChildren);
        }
      });
    },

    // 时间转化
    typeTimeTurn (obj, index) {
      if(+new Date(obj.validFrom) > +new Date(obj.validTo)) {
        this.handleErrFun(index, 'validFrom')
        this.handleErrFun(index, 'validTo')
      }
    },
    // 提交请求
    addbatchPost (arr, importDeptId) {
      crudVisitor.addbatch(arr, importDeptId).then(res => {
        this.$message({
          showClose: true,
          message: res.msg,
          type: 'success'
        });
        this.crud.refresh();
      });
    },
    read(row) {
      this.bindUser = row;
      this.bindInfoDialogVisible = true;
    },
    getModel(model, industry) {
      const list = this.dict['bdmWokerPost'].find( item => item.value == industry)?.children || []
      return list.find( item => item.value == model)?.label || ''
    },
    toDetails(param) {
      crud.toEdit(param);
      this.btnShow = false;
    },
    cancel() {
      this.btnShow = true;
    },
    // 监听关闭事件
    closed () {
      this.$emit('update:isDetail', false);
      // let refList = ['deptIdRef'];
      // for (let i = 0; i < refList.length; i++) {
      //   if (this.$refs[refList[i]]) {
      //     this.$refs[refList[i]].$children[0].$el.style.borderColor = '#BFBFBF';
      //   }
      // }
    },
    handleExport () {
      crud.toQuery()
      const ids =crud.selections?.length ? crud.selections.map( item => item.id) : null
      crud.doExport(ids);
    },
    //获取级联项key
    getCascadeKey(obj){
      if(this.cascade.length>0) this.getIndustryKey(obj,0);
    },
    //获取从业类型key
    getIndustryKey(obj,position){
      for (let item of this.dict['bdmWokerPost']) {
        if(item.label === obj[this.cascade[position]]){
          obj[this.cascade[position]] = item.value;
          this.getPostKey(obj,item.children,1);
          break;
        }
      }
    },
    //获取岗位类型key
    getPostKey(obj,wokerPost=[],position){
      for (let item of wokerPost) {
        if(item.label === obj[this.cascade[position]]){
          obj[this.cascade[position]] = item.value;
          break;
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-input-number .el-input__inner {
  text-align: left;
}

.xh-container /deep/ .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background-color: #fcf0c1;
}

.underline-text {
  text-decoration: underline;
}
.active-label {
  color: blue;
  cursor: pointer;
}
</style>
