<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible.sync="bindDialogVisible"
    :modal-append-to-body="false"
    title="绑定终端"
    class="terminal-relation"
    width="90%"
    @close="closeDialog"
  >
    <!-- 表头用户信息 -->
    <el-form
      :model="bindUser"
      :inline="true"
    >
      <div class="current-user">
        当前人员信息
      </div>
      <el-row>
        <el-col :span="5">
          <el-form-item
            label="姓名"
            label-width="40px"
          >
            <el-input
              v-model="bindUser.name"
              placeholder="姓名"
              disabled
              width="70px"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="所属机构">
            <el-input
              v-model="bindUser.deptName"
              placeholder="所属机构"
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 穿梭框头部搜索 -->
    <div class="terminal-transfer-header">
      <div class="transfer-header">
        <div class="transfer-header-title">
          <TableTitleSlot title="未绑定终端列表" />
        </div>
        <el-form
          size="small"
          label-width="80px"
        >
          <el-row>
            <el-col :span="11">
              <el-form-item label="终端类型">
                <el-cascader
                  v-model="search.categoryTypeLeft"
                  :options="dict.bdmDeviceType"
                  :show-all-levels="false"
                />
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item
                label="序列号"
                label-width="100px"
              >
                <el-input
                  v-model="search.uniqueIdLeft"
                  placeholder="请输入序列号"
                  prefix-icon="el-icon-search"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col
              :span="3"
              class="form-left-search"
            >
              <el-button
                class="filter-item"
                size="small"
                type="primary"
                plain
                icon="el-icon-search"
                @click="searchLeftClick"
              >查 询
              </el-button>
            </el-col>
            <el-col :span="11">
              <el-form-item
                label="所属机构"
                prop="deptLeft"
              >
                <DeptFormSingleSelect
                  ref="deptIdRef"
                  v-model="search.deptLeft"
                  :is-show="true"
                  placeholder="请选择所属机构"
                  size="small"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="transfer-header-middle"/>
      <div class="transfer-header">
        <div class="transfer-header-title">
          <TableTitleSlot title="已绑定终端列表" />
        </div>
        <el-form
          size="small"
          label-width="80px"
        >
          <el-row>
            <el-col :span="11">
              <el-form-item label="终端类型">
                <el-cascader
                  v-model="search.categoryTypeRight"
                  :options="dict.bdmDeviceType"
                  :show-all-levels="false"
                  clearable
                  :props="terminalProps"
                />
              </el-form-item>
            </el-col>
            <el-col :span="2">
              &nbsp;
            </el-col>
            <el-col :span="11">
              <el-button
                class="filter-item"
                size="small"
                type="primary"
                plain
                icon="el-icon-search"
                @click="searchFront"
              >查 询
              </el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <!-- 双table穿梭框 -->
    <div class="terminal-transfer">
      <el-table
        v-loading="loadingLeft"
        :data="table.left"
        :header-cell-style="{background:'#ebf5ff',color:'#606266'}"
        style="width: 100%"
        @selection-change="(val)=>{
          currentTable.left = val
        }"
      >
        <el-table-column
          type="selection"
          label-class-name="selection-label"
          width="50px"
        />
        <el-table-column
          prop="deviceTypeName"
          label="终端类别"
          show-overflow-tooltip
        />
        <el-table-column
          prop="categoryName"
          label="终端类型"
          show-overflow-tooltip
        />
        <el-table-column
          prop="uniqueId"
          label="序列号"
          show-overflow-tooltip
        />
        <el-table-column
          prop="deptName"
          label="使用单位"
          show-overflow-tooltip
        />
        <el-empty
          slot="empty"
          :image="require('@/assets/images/nodata.png')"
        />
      </el-table>

      <div class="btns">
        <el-button
          :disabled="currentTable.left.length === 0"
          type="primary"
          @click="toWhere('right')"
        >
          添加
        </el-button>
        <el-button
          :disabled="currentTable.right.length === 0"
          type="primary"
          @click="toWhere('left')"
        >
          移除
        </el-button>
      </div>
      <el-table
        v-loading="loadingRight"
        :data="table.right"
        :header-cell-style="{background:'#ebf5ff',color:'#606266'}"
        style="width:100%"
        height="140%"
        @selection-change="(val)=>{
          currentTable.right = val;
        }"
      >
        <el-table-column
          type="selection"
          label-class-name="selection-label"
          width="50px"
        />
        <el-table-column
          prop="deviceTypeName"
          label="终端类别"
          show-overflow-tooltip
        />
        <el-table-column
          prop="categoryName"
          label="终端类型"
          show-overflow-tooltip
        />
        <el-table-column
          prop="uniqueId"
          label="序列号"
          show-overflow-tooltip
        />
        <el-table-column
          prop="deptName"
          label="使用单位"
          show-overflow-tooltip
        />
        <el-empty
          slot="empty"
          :image="require('@/assets/images/nodata.png')"
        />
      </el-table>
    </div>
    <!-- 穿梭框底部分页 -->
    <div class="terminal-transfer-footer">
      <div class="transfer-footer">
        <el-pagination
          :total="page.leftTotal"
          :current-page.sync="page.leftCurrent"
          layout="total,prev, pager, next"
          @current-change="(val)=>{
            searchHandle('left', val, {uniqueId: search.uniqueIdLeft, category: search.categoryTypeLeft[1], deviceType: search.categoryTypeLeft[0], deptId: search.deptLeft});
          }"
        />
      </div>
      <div class="transfer-footer-middle"/>
      <div class="transfer-footer">
        <div class="footer-tips">注: 手表只能绑定一个设备，手持终端可绑定多个设备。</div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { Transfer } from 'element-ui';
import {
  driverBindVehicleEdit,
  driverUnbindVehicleDetail,
  driverBindVehicleDetailList
} from '@/api/base/visitor.js';
import getPlaceholder from '@/utils/getPlaceholder';
import TableTitleSlot from '@/components/pageHead/tableTitleSlot.vue';
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';

export default {
  name: 'BindVehicle',
  components: {
    DeptFormSingleSelect,
    [Transfer.name]: Transfer,
    TableTitleSlot,
  },
  props: {
    // 外部控制显隐
    bindDialogVisible: {
      type: Boolean,
      default: false
    },
    // props当前用户信息
    bindUser: {
      type: Object,
      required: true,
      default: () => {
        return {};
      }
    },
    dict: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      // table内容
      table: {
        left: [],
        right: []
      },
      // 选中table内容
      currentTable: {
        left: [],
        right: []
      },
      // 搜索
      search: {
        uniqueIdLeft: '',
        deptLeft: null,
        categoryTypeLeft: [],
        categoryTypeRight: null
      },
      // 分页
      page: {
        leftTotal: 0,
        rightTotal: 0,
        leftCurrent: 1,
        rightCurrent: 1
      },
      // 拷贝的提交数据
      submitData: [],
      rightDataSearch: [], // 为搜索data池
      category: null,//绑定终端类型数据
      deviceType: null,//绑定终端类型数据
      terminalProps: {
        emitPath: false
      },
      loadingLeft: false,
      loadingRight: false,
      bindTerminalLength: 0,
      deviceOptions: []
    };
  },
  watch: {
    'dict.bdmDeviceType': {
      handler (val) {
        // this.deviceOptions = val.filter(item => item.value === '2' || item.value === '3').map(element => {
        //   let obj = { ...element };
        //   if (obj.children) {
        //     obj.children = element.children.filter(it => it.value !== '202');
        //   }
        //   return obj;
        // });
      },
      deep: true
    },
    bindDialogVisible(newValue) {
      if (newValue) {
        this.searchHandle('left', 1, {category: this.search.categoryTypeLeft[1], deviceType: this.search.categoryTypeLeft[0], deptId: this.search.deptLeft, uniqueId: this.search.uniqueIdLeft}, true);
        this.searchHandle('right', 1, {id: this.bindUser.id}, null, (data) => {
          this.bindTerminalLength = data.length;
          [...this.submitData] = data;
          [...this.rightDataSearch] = data;
        });
      }
    }
  },
  methods: {
    searchLeftClick () {
      this.searchHandle('left', 1, {category: this.search.categoryTypeLeft[1], deviceType: this.search.categoryTypeLeft[0], deptId: this.search.deptLeft, uniqueId: this.search.uniqueIdLeft}, true);
    },
    // 关闭dialog
    closeDialog() {
      if (this.bindTerminalLength !== this.submitData.length) {
        this.$emit('refresh');
      }
      this.search = {
        uniqueIdLeft: '',
        deptLeft: null,
        categoryTypeLeft: [],
        categoryTypeRight: null
      };
      this.table.left = [];
      this.table.right = [];
      this.submitData = [];
      this.rightDataSearch = [];
      this.$refs?.deptIdRef?.clear();
      this.$emit('update:bindDialogVisible', false);
    },

    // 绑定项增减
    toWhere(to) {
      let from = to === 'right' ? 'left' : 'right';
      let terminalData = [...this.submitData];
      from === 'left' ? this.addCar(this.currentTable[from], terminalData) : this.cutCar(this.currentTable[from], terminalData);
      const list = terminalData.map(item => ({
        ...item,
        terminalId: item.id
      }));
      console.log(list, '-----list');
      driverBindVehicleEdit(this.bindUser.id, list, this.bindUser.deptId).then((res) => {
        this.$notify({
          title: res.msg,
          type: 'success',
          duration: 2500
        });
        from === 'left' ? this.addCar(this.currentTable[from], this.submitData) : this.cutCar(this.currentTable[from], this.submitData);
        this.toWhereSearch(from);
        this.addCar(this.currentTable[from], this.table[to]);
        this.cutCar(this.table[to], this.table[from]);
        this.currentTable[from] = [];
        this.$nextTick(() => {
          if (this.table['left'].length === 0) {
            this.searchHandle('left', 1, {category: this.search.categoryTypeLeft[1], deviceType: this.search.categoryTypeLeft[0], deptId: this.search.deptLeft, uniqueId: this.search.uniqueIdLeft});
          }
          // this.searchHandle('right', 1, {id: this.bindUser.id}, null, (data) => {
          //   [...this.submitData] = data;
          //   [...this.rightDataSearch] = data;
          // });
        });
      }).catch(err => {
        console.log('绑定时出错', err);
      });
    },
    // 搜索池也增减
    toWhereSearch (from) {
      from === 'left' ? this.addCar(this.currentTable[from], this.rightDataSearch) : this.cutCar(this.currentTable[from], this.rightDataSearch);
    },

    // 分页查询[绑定right]or[未绑定left]车辆
    searchHandle(way, current = 1, val = {}, back, callback) {
      let fn = way === 'right' ? driverBindVehicleDetailList : driverUnbindVehicleDetail;
      let params = {
        current: current,
        size: 10
      };
      if (way === 'right') this.loadingRight = true;
      else this.loadingLeft = true;
      fn({ ...params, ...val }).then((res) => {
        if (way === 'right') {
          this.table[way] = res.data || [];
          callback && callback(res.data || []);
        }
        else {
          this.page[way + 'Total'] = res.data?.total || 0;
          back && (this.page[way + 'Current'] = 1);
          this.table[way] = res.data?.records || [];
          callback && callback(res.data?.records || []);

        }
      }).finally(() => {
        if (way === 'right') this.loadingRight = false;
        else this.loadingLeft = false;
      });
    },

    // 增项
    addCar(add, total) {
      add.length > 0 && add.forEach(item => {
        let flag = true;
        total.length > 0 && total.forEach(v => {
          if (v.id === item.id) {
            flag = false;
          }
        });
        flag && total.push(item);
      });
    },
    // 减项
    cutCar(cut, total) {
      cut.length > 0 && cut.forEach(item => {
        total.length > 0 && total.forEach((v, index) => {
          if (item.id === v.id) {
            total.splice(index, 1);
          }
        });
      });
    },
    // 总data池搜索已绑
    searchFront () {
      this.loadingRight = true;
      if (!this.search.categoryTypeRight) {
        this.table.right = [...this.rightDataSearch];
      } else {
        let arr = [];
        this.rightDataSearch.forEach(item => {
          if (item.category === Number(this.search.categoryTypeRight)) {
            arr.push(item);
          }
        });
        this.table.right = arr;
      }
      this.loadingRight = false;
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    },
    getPlaceholder(value) {
      return getPlaceholder('Driver', value);
    },
  }
};
</script>

<style lang="less" scoped>
.terminal-relation {
  /deep/ .el-dialog__body {
    padding: 0 20px;
  }

  .current-user {
    font-size: 16px;
    padding: 10px 0;
    font-weight: bold;
  }

  /deep/ .el-input.is-disabled .el-input__inner {
    background-color: #ffffff;
    color: #606266;
    cursor: default;
  }

  .terminal-transfer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .el-table {
      border-right: 1px solid #e4e7ed;
      border-left: 1px solid #e4e7ed;
      min-height: 450px;
      /deep/ .el-table__cell {
        border: none;
      }
    }

    .btns {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 120px;

      .el-button {
        margin: 10px 0;
      }
    }
  }

  .terminal-transfer-header {
    display: flex;

    .transfer-header {
      padding: 0 10px;
      flex: 1;
      border: 1px solid #e4e7ed;
      border-bottom: none;
      border-radius: 4px 4px 0 0;

      .transfer-header-title {
        padding: 5px 0;
      }

      /deep/ .el-form-item {
        margin-bottom: 5px;
      }
    }

    .transfer-header-middle {
      width: 120px;
    }
  }

  .terminal-transfer-footer {
    display: flex;

    .transfer-footer {
      padding: 5px 10px 0 ;
      flex: 1;
      border: 1px solid #e4e7ed;
      border-top: none;
      border-radius: 0 0 4px 4px;
      margin-bottom: 10px;

      .transfer-footer-title {
        padding: 10px;
      }
    }

    .transfer-footer-middle {
      width: 120px;
    }
  }
  .footer-tips {
    padding: 10px 0;
  }
  .form-left-search {
    display: flex;
    justify-content: end;
  }
}

</style>
