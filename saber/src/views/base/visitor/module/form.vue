<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="btnShow ? crud.status.title : '查看'"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      class="rewriting-form-disable"
      :hide-required-asterisk="!btnShow"
      label-width="120px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="人员姓名"
            prop="name"
          >
            <el-input
              v-model.trim="form.name"
              placeholder="请输入人员姓名"
              :maxlength="maxLen.name"
              style="width: 100%"
              :disabled="!btnShow"
              @input="(e) => (form.name = validInput(e))"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="身份证号"
            prop="idNumber"
          >
            <el-input
              v-model.trim="form.idNumber"
              placeholder="请输入身份证号"
              :maxlength="maxLen.idNumber"
              style="width: 100%"
              :disabled="!crud.status.title.includes('新增')"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="所属机构"
            prop="deptId"
          >
            <DeptFormSingleSelect
              ref="deptIdRef"
              v-model="form.deptId"
              :detail-name="form.deptName"
              :disabled="!btnShow"
              :is-show="crud.status.cu > 0"
              placeholder="请选择所属机构"
              size="small"
              @input="validateTreeSelect('deptId')"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="从业类型"
            prop="industry"
          >
            <single-select
              :options="dict.bdmWokerPost"
              v-model="form.industry"
              placeholder="请选择从业类型"
              clearable
              :disabled="!btnShow"
              @change="form.post=''"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.industry"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="岗位类型"
            prop="post"
          >
            <single-select
              :options="wokerPost"
              v-model="form.post"
              placeholder="请选择岗位类型"
              clearable
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="联系电话"
            prop="phone"
          >
            <el-input
              v-model.trim="form.phone"
              placeholder="请输入联系电话"
              :maxlength="maxLen.phone"
              style="width: 100%"
              :disabled="!btnShow"
              @input="(e) => (form.phone = validInput(e))"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="所属公司名称"
            prop="company"
          >
            <el-input
              v-model.trim="form.company"
              placeholder="请输入所属公司名称"
              :maxlength="maxLen.company"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <!-- 安装日期 -->
          <el-form-item
            label="生效日期"
            prop="validFrom"
          >
            <el-date-picker
              v-model="form.validFrom"
              type="date"
              placeholder="请选择生效日期"
              value-format="yyyy-MM-dd"
              :disabled="!btnShow"
              @blur="judge"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <!-- 安装日期 -->
          <el-form-item
            label="终止日期"
            prop="validTo"
          >
            <el-date-picker
              v-model="form.validTo"
              type="date"
              placeholder="请选择终止日期"
              value-format="yyyy-MM-dd"
              :disabled="!btnShow"
              @blur="judge"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        v-if="btnShow"
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="btnShow"
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from "@/components/Crud/crud";
import getLabel from "@/utils/getLabel";
import getPlaceholder from "@/utils/getPlaceholder";
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';
import SingleSelect from '@/components/select/DictSelect/DictSelectSingle';
const defaultForm = {
  idNumber: "",
  company: "",
  validFrom: "",
  validTo: "",
  name: "",
  post: null,
  industry: null,
  phone: null,
  deptId: null,
  deptName: null,
};
export default {
  components: { DeptFormSingleSelect, SingleSelect },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true,
    },
    btnShow: {
      type: Boolean,
      default: true,
    },
    maxLen: {
      type: Object,
      default: () => {
        return {};
      },
    },
    regTurn: {
      type: Object,
      default: () => {
        return {};
      },
    },
    regTurnM: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      rules: {
        name: {
          required: true,
          trigger: "blur",
        },
        idNumber: [
          { required: true, trigger: "blur" },
        ],
        deptId: { required: true, trigger: "change" }, // 车组
        industry: {
          required: true,
          trigger: "change",
        },
        post: {
          required: true,
          trigger: "blur",
        },
        phone: [
          {
            required: true,
            trigger: "blur",
          },
        ], // 终端电话
        company: { required: true, trigger: "blur" }, // 车组
        validFrom: { required: true, trigger: "change" }, // 车组
        validTo: { required: true, trigger: "change" }, // 车组
      },
      wokerPost: [],
      depts: []
    };
  },
  watch: {
    "form.industry": {
      handler(newVal) {
        const list = this.dict['bdmWokerPost'];
        this.wokerPost = list.find( item => item.value === newVal)?.children || [];
      },
      deep: true,
      immediate: true,
    }
  },
  methods: {
    judge () {
      if (this.form.validFrom && this.form.validTo) {
        if ( +new Date(this.form.validFrom) > +new Date(this.form.validTo) ) {
          this.$message({
            type: 'warning',
            message: '生效日期不能大于终止日期'
          });
          return true;
        }
      }
    },
    /** 提交 - 之前 */
    [CRUD.HOOK.beforeSubmit] () {
      if (this.judge()) {
        return false;
      }
      return true;
    },
    /** 新建/编辑" 验证 - 之后 */
    [CRUD.HOOK.afterValidateCU] () {
      const list = Object.keys(this.regTurn);
      for (let i = 0; i < list.length; i++) {
        const key = list[i];
        if (this.form[key] && !this.regTurn[key].test(this.form[key])) {
          this.$message.error(this.regTurnM[key]);
          return false;
        }
      }
      return true;
    },
    /** 开始 "新建/编辑" - 之后 */
    [CRUD.HOOK.afterToCU]() {
      if (this.form.industry != null)
        this.form.industry = this.form.industry.toString();
      if (this.form.post != null) this.form.post = this.form.post.toString();
    },
    /** 点击取消之前*/
    [CRUD.HOOK.afterEditCancel]() {
      this.$emit("cancelCU");
    },
    // 新增/编辑前
    [CRUD.HOOK.beforeToCU]() {
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
          this.$refs[`deptIdRef`].$refs[`deptIdStrRef`].$children[0].$el.style.borderColor = '#BFBFBF';
        }
      });
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel(value) {
      return getLabel("Driver", value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder(value) {
      return getPlaceholder("Driver", value);
    },
    /**
     * 验证treeSelect项
     * @param {String} item
     */
    validateTreeSelect (item) {
      this.$refs.form.validateField(item, (valid) => {
        this.$refs[`${item}Ref`].$refs[`deptIdStrRef`].$children[0].$el.style.borderColor = valid !== '' ? '#F56C6C' : '#BFBFBF';
      });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-input__inner {
  text-align: left;
}

.subTitle /deep/ .el-divider__text {
  font-size: 14px;
  font-weight: 700;
  color: #606266;
}

/deep/ .el-form-item__label {
  font-weight: 400;
}

/deep/ .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

/deep/ .avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

/deep/ .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 128px;
  height: 128px;
  line-height: 128px;
  text-align: center;
}

/deep/ .avatar {
  width: 128px;
  height: 128px;
  display: block;
}

/deep/ .el-upload-list__item-actions:hover {
  opacity: 1;
}

/deep/ .el-upload-list__item-actions {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  cursor: default;
  text-align: center;
  color: #ffffff;
  opacity: 0;
  font-size: 20px;
  background-color: rgba(255, 255, 255, 0.5);
  transition: opacity 0.3s;
}

/deep/ .el-upload-list__item-actions:hover span {
  display: inline-block;
}

/deep/ .upload-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -5px;
  margin-top: -15px;
  width: 30px;
  height: 30px;
}

/deep/ .el-icon-delete {
  font-size: 30px;
}
</style>
