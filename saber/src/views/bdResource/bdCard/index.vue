<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :permission="permission"
          :head-config="headConfig"
          label-width="130px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
        >
          <el-button
            slot="right"
            class="filter-item"
            icon="el-icon-upload2"
            size="small"
            @click="batchvisible = true"
          >
            导入
          </el-button>
        </crudOperation>
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <!--          <el-table-column-->
          <!--            type="index"-->
          <!--            label="#"-->
          <!--          />-->
          <el-table-column
            v-permission="['admin','simCardManage:edit','simCardManage:del']"
            width="90"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              />
            </template>
          </el-table-column>

          <!-- 北斗卡号 -->
          <el-table-column
            v-if="columns.visible('bdCard')"
            :label="getLabel('bdCard')"
            prop="bdCard"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
         <!-- 所属机构 -->
         <el-table-column
            v-if="columns.visible('conpany')"
            :label="getLabel('conpany')"
            prop="conpany"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <div>
                {{ getEnumDictLabel("conpany", scope.row.conpany) }}
              </div>
            </template>
          </el-table-column>
          <!-- 所属用户 -->
          <el-table-column
            v-if="columns.visible('user')"
            :label="getLabel('user')"
            prop="user"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 联系电话 -->
          <el-table-column
            v-if="columns.visible('phone')"
            :label="getLabel('phone')"
            prop="phone"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 状态 -->
          <el-table-column
            v-if="columns.visible('status')"
            :label="getLabel('status')"
            prop="status"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <div>
                {{ getEnumDictLabel("status", scope.row.status) }}
              </div>
            </template>
          </el-table-column>
          <!-- 短报文序列号 -->
          <el-table-column
            v-if="columns.visible('messageTerminalId')"
            :label="getLabel('messageTerminalId')"
            prop="messageTerminalId"
            min-width="130"
            show-overflow-tooltip
            :resizable="false"
          >
          </el-table-column>
          <!-- 发卡日期 -->
          <el-table-column
            v-if="columns.visible('startDate')"
            :label="getLabel('startDate')"
            prop="startDate"
            min-width="130"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <div class="table-date-td">
                {{ parseTimes(scope.row.startDate) }}
              </div>
            </template>
          </el-table-column>
          <!-- 服务期止 -->
          <el-table-column
            v-if="columns.visible('endDate')"
            :label="getLabel('endDate')"
            prop="endDate"
            min-width="130"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <div class="table-date-td">
                {{ parseTimes(scope.row.endDate) }}
              </div>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
      <!--表单渲染-->
      <eForm :dict="dict"/>
      <BatchImport
        :visible="batchvisible"
        mod="simCard"
        @close="batchvisible = false"
        @getBatchData="getBatchData"
      />
      <msgDialog
        ref="msgDialog"
        :msg-data="msgData"
      />
    </div>
  </basic-container>
</template>

<script>
import crudSimCardManage from '@/api/base/simCardManage';
import eForm from './module/form';
import CRUD, { presenter, header } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import pagination from '@/components/Crud/Pagination';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import HeadCommon from '@/components/formHead/headCommon.vue';
import BatchImport from '@/components/upload/batchImport.vue';
import msgDialog from './module/msgDialog.vue';

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('SimCardManage', 'uniName'),
  crudMethod: { ...crudSimCardManage }
});

export default {
  name: 'SimCardManage',
  components: { eForm, crudOperation, udOperation, pagination, HeadCommon, BatchImport, msgDialog },
  mixins: [presenter(crud), header()],
  // 数据字典
  dicts: [
    'cardType',
    'package',
    'cardState',
    'operator',
    'licenceColor'
  ],
  data () {
    return {
      permission: {
        add: ['admin', 'simCardManage:add'],
        edit: ['admin', 'simCardManage:edit'],
        del: ['admin', 'simCardManage:del'],
      },
      headConfig: {
        item: {
          1: {
            name: '北斗卡号',
            type: 'input',
            value: 'bdCard',
          },
          2: {
            name: '登记用户',
            type: 'input',
            value: 'user',
          },
          3: {
            name: '短报文序列号',
            type: 'input',
            value: 'messageTerminalId',
          },
          4: {
            name: '发卡日期',
            type: 'date',
            value: 'date',
            defaultValue: this.$moment().subtract(1, 'day').format('YYYY-MM-DD')
          }
        },
        button: {
        }
      },
      // 批量引入相关
      batchvisible: false,
      msgData: [], // 批量导入提示消息
      numKey: undefined, // 递归中继值
      dataType: { // excel里文字对应的key
        '卡号': 'bdCard',
        '实名登记用户': 'user',
        '短报文序列号': 'messageTerminalId',
        '所属机构': 'conpany',
        '发卡日期': 'startDate',
        '到期时间': 'endDate',
      },
      // 时间类型，统一转换时间
      typeTime: ['startDate','endDate'],
      // 必填项
      typeRequired: ['bdCard', 'user', 'messageTerminalId', 'conpany', 'startDate', 'endDate'],
      // 字符串项
      typeString: ['messageTerminalId', 'bdCard'],
      // 表单名称对应字典(表单名称与字典名称不一致时)
      typeDictName: {},
      tipsKey: [], // 提示点集合
    };
  },
  methods: {
    // 批量导入相关
    // 获取excel的数据
    getBatchData (returnValue) {
      let data = returnValue.data;
      let reversalObj = {};
      for (let k in this.dataType) {
        reversalObj[this.dataType[k]] = k;
      }
      this.tipsKey = [];
      let arr = data.map((item, index) => {
        let obj = {};
        for (let key in item) {
          for (let k in this.dataType) {
            if (key === k) {
              obj[this.dataType[k]] = item[key];
            }
          }
        }
        if (obj['isAi']) {
          obj['isAi'] = obj['isAi'] === '是' ? 1 : 0;
        }
        this.typeTimeTurn(obj);
        this.typeTreeTurn(obj);
        this.typeRequiredTurn(obj, index, reversalObj);
        this.typeStringTurn(obj);
        return obj;
      });
      if (this.tipsKey && this.tipsKey.length > 0) {
        let str = '';
        this.tipsKey.forEach((item, index) => {
          if (item && item.length > 0) {
            let s = '';
            item.forEach(v => {
              s += `[${v}]`;
            });
            str += `【第${index + 1}条数据${s}】`;
          }
        });
        let arr = [];
        this.tipsKey.forEach((item, index) => {
          if (item && item.length > 0) {
            let str = `共${item.length}条`;
            item.forEach(v => {
              str += `[${v}]`;
            });
            arr.push({
              sort: `第${index + 1}条`,
              details: str
            });
          }
        });
        this.msgData = arr;
        this.$refs.msgDialog.msgVisible = true;
      } else {
        this.addbatchPost(arr);
        returnValue.close();
      }
    },
    // 转化字符串
    typeStringTurn (obj, dataType = this.typeString) {
      dataType.forEach(v => {
        if (obj[v]) {
          obj[v] = obj[v] + '';
        }
      });
    },
    // 必填项判断
    typeRequiredTurn (obj, k, reversalObj, dataRequired = this.typeRequired) {
      dataRequired.forEach(v => {
        if (typeof (v) === 'object' && v.mod) {
          this.typeRequiredTurn(obj[v.mod], k, reversalObj, v.required);
        } else if (!obj[v] && obj[v] !== 0) {
          if (!this.tipsKey[k]) {
            this.tipsKey[k] = [reversalObj[v]];
          } else {
            this.tipsKey[k].push(reversalObj[v]);
          }
        }
      });
    },
    // 字典里的转key/id
    typeTreeTurn (obj) {
      for (let k in this.dict) {
        for (let j in obj) {
          if (k === j) {
            this.treeTurn(this.dict[k], obj[j]);
            obj[j] = this.numKey;
            this.numKey = undefined;
          }else if (k === this.typeDictName[j]) {
            this.treeTurn(this.dict[this.typeDictName[j]], obj[j]);
            obj[j] = this.numKey;
            this.numKey = undefined;
          }
        }
      }
    },
    // 递归找key/id
    treeTurn (tree, word, iKey = 'value', iValue = 'label', iChildren = 'children') {
      tree.forEach(item => {
        if (item[iValue] === word) {
          this.numKey = item[iKey];
        } else if (item[iChildren] && item[iChildren].length > 0) {
          this.treeTurn(item[iChildren], word, iKey, iValue, iChildren);
        }
      });
    },
    // 时间转化
    typeTimeTurn (obj) {
      for (let k in obj) {
        this.typeTime.forEach(v => {
          if (k === v && obj[k]) {
            obj[k] = this.timeTurnDate(obj[k]);
          }
        });
      }
    },
    // excel表里时间转化日期格式
    timeTurnDate (numb) {
      let yeraData = new Date(1900, 0, numb - 1);
      let year = yeraData.getFullYear();
      let month = yeraData.getMonth() + 1;
      month = month < 10 ? '0' + month : month;
      let day = yeraData.getDate();
      day = day < 10 ? '0' + day : day;
      let t = `${year}-${month}-${day}`;
      t = new Date(t);
      t.setHours(t.getHours() - 8);
      return t;
    },
    // excel表里时间转化时间戳
    timeTurn (numb) {
      let yeraData = new Date(1900, 0, numb - 1);
      let year = yeraData.getFullYear();
      let month = yeraData.getMonth() + 1;
      month = month < 10 ? '0' + month : month;
      let day = yeraData.getDate();
      day = day < 10 ? '0' + day : day;
      let t = `${year}-${month}-${day}`;
      t = new Date(t).getTime() / 1000;
      return t;
    },
    // 提交请求
    addbatchPost (arr) {
      crudSimCardManage.addbatch(arr).then(res => {
        this.$message({
          showClose: true,
          message: res.msg,
          type: 'success'
        });
        this.crud.refresh();
      });
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('bdCard', value);
    },
    parseTimes (time) {
      if (time) {
        return this.$moment(time).format('YYYY-MM-DD');
      }
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('bdCard', value);
    }
  }
};
</script>

<style lang="less" scoped>
.xh-container ::v-deep.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
    background-color: #fcf0c1;
  }
</style>
