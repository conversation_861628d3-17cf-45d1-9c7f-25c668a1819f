<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="100px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="batchPer"
          :download="false"
        >
          <template slot="right">
            <el-button
              v-permission="batchPer.imp"
              class="filter-item"
              icon="el-icon-upload2"
              size="small"
              @click="batchvisible = true"
            >
              导入
            </el-button>

            <el-button
              v-permission="batchPer.exp"
              :loading="crud.downloadLoading"
              class="filter-item"
              size="small"
              icon="el-icon-download"
              @click="handleExport"
            >
              导 出
            </el-button>
          </template>
        </crudOperation>
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            v-permission="['admin','locTerminalManage:edit','locTerminalManage:del']"
            width="120"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
                :hide-edit="true"
                :hide-del="true"
              >
                <template slot="right">
                  <el-button
                    v-permission="permission.edit"
                    type="text"
                    size="small"
                    class="table-button-edit"
                    @click="toDetails(scope.row, true)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    v-permission="permission.del"
                    type="text"
                    class="table-button-del"
                    size="small"
                    @click="handleDelClick(scope.row)"
                  >删除
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetails(scope.row, false)"
                  >
                    详情
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <!-- 应用方向 -->
          <el-table-column
            v-if="columns.visible('classCode')"
            :label="'应用方向'"
            prop="classCode"
            min-width="120"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 终端类型 -->
          <el-table-column
            v-if="columns.visible('subClassCode')"
            :label="getLabel('category')"
            prop="subClassCode"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 归属单位名称 -->
          <el-table-column
            v-if="columns.visible('deptName')"
            label="归属单位"
            prop="deptName"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 序列号 -->
          <el-table-column
            v-if="columns.visible('uniqueId')"
            :label="getLabel('uniqueId')"
            prop="uniqueId"
            min-width="160"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- IMEI -->
          <el-table-column
            v-if="columns.visible('imei')"
            :label="getLabel('imei')"
            prop="imei"
            min-width="160"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.imei || '-' }}
            </template>
          </el-table-column>
          <!-- 入网方式 -->
          <el-table-column
            v-if="columns.visible('inNetType')"
            :label="'入网方式'"
            prop="inNetTypeName"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 入网运营商 -->
          <el-table-column
            v-if="columns.visible('inNetProvider')"
            :label="'入网运营商'"
            prop="inNetProvider"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 北斗卡号 -->
          <el-table-column
            v-if="columns.visible('bdCardNumber')"
            :label="'北斗卡号'"
            prop="bdCardNumber"
            min-width="120"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.bdCardNumber || '-' }}
            </template>
          </el-table-column>
          <!-- 终端型号 -->
          <el-table-column
            v-if="columns.visible('model')"
            :label="getLabel('model')"
            prop="model"
            min-width="120"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.model || scope.row.otherTypes || '-' }}
            </template>
          </el-table-column>
          <!-- 物联网卡 -->
          <el-table-column
            v-if="columns.visible('iotNumber')"
            label="物联网卡号"
            prop="iotNumber"
            min-width="160"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.iotNumber || '-' }}
            </template>
          </el-table-column>
          <!-- 终端编号 -->
          <el-table-column
            v-if="columns.visible('deviceNo')"
            label="终端编号"
            prop="deviceNo"
            min-width="120"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.deviceNo || '-' }}
            </template>
          </el-table-column>
          <!-- 视频通道数 -->
          <el-table-column
            v-if="columns.visible('channelNum')"
            :label="getLabel('channelNum')"
            prop="channelNum"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.channelNum || '0' }}
            </template>
          </el-table-column>
          <!-- 联系人 -->
          <el-table-column
            v-if="columns.visible('contact')"
            label="联系人"
            prop="contact"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 联系方式 -->
          <el-table-column
            v-if="columns.visible('contactPhone')"
            label="联系方式"
            prop="contactPhone"
            min-width="120"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 厂商名称 -->
          <el-table-column
            v-if="columns.visible('manufacturerName')"
            :label="getLabel('vendor')"
            prop="manufacturerName"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.manufacturerName || '-' }}
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination/>
      <!--表单渲染-->
      <eForm
        ref="from"
        :dict="dict"
        :regTurn="regTurn"
        :regTurnM="regTurnM"
        :btnShow="btnShow"
        @cancelCU="cancel"
      />
      <BatchImport
        :visible="batchvisible"
        mod="existingDevice"
        @close="batchvisible = false"
        @getBatchData="getBatchData"
      />
    </div>
  </basic-container>
</template>

<script>
import crudTerminalManage, {
  addbatch,
  listMainDeviceClasses,
  listSubClassesByMainClass,
  toDetail
} from '@/api/base/existingTerminal';
import eForm from './module/form';
import CRUD, { presenter, header } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import pagination from '@/components/Crud/Pagination';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import HeadCommon from '@/components/formHead/headCommon.vue';
import BatchImport from '@/components/upload/batchImportBackground.vue';
import { getDeptPerInit } from '@/api/base/dept';
// crud交由presenter持有
const crud = CRUD({
  crudMethod: { ...crudTerminalManage }
});

export default {
  name: 'ExistingTerminal',
  components: {
    eForm,
    crudOperation,
    udOperation,
    pagination,
    HeadCommon,
    BatchImport
  },
  mixins: [
    presenter(crud),
    header()
  ],
  // 数据字典
  dicts: [
    'bdmDeviceModel',
    'inNetType',
    'inNetProvider'
  ],
  data() {
    this.installdateReg = {
      installdate: /^(\d+)-(\d+)-(\d+)/
    };
    this.regTurn = {
      imei: /^\d+$/,
      bdChipSn: /^[a-zA-z0-9]+$/,
      uniqueId: /^[a-zA-z0-9]+$/,
      numbers: /^[a-zA-z0-9]+$/,
      model: /^[^\u4E00-\u9FA5]+$/,
      channelNum: /^\d+$/
    };
    this.regTurnM = {
      bdChipSn: '北斗芯片序列号只能输入数字跟字母',
      model: '终端型号不允许输入中文',
      imei: 'IMEI只允许输入数字',
      uniqueId: '序列号只能输入数字跟字母',
      numbers: '物联网卡只能输入数字跟字母',
      channelNum: '视频通道数只允许输入数字'
    };
    return {
      batchPer: {
        add: [
          'admin',
          'existingTerminal:add'
        ],
        imp: [
          'admin',
          'existingTerminal:imp'
        ],
        exp: [
          'admin',
          'existingTerminal:exp'
        ],
        del: [
          'admin',
          'existingTerminal:del'
        ]
      },
      permission: {
        add: [
          'admin',
          'existingTerminal:add'
        ],
        del: [
          'admin',
          'existingTerminal:del'
        ],
        edit: [
          'admin',
          'existingTerminal:edit'
        ]
      },
      headConfig: {
        noReset: true,
        item: {
          1: {
            name: '应用方向',
            type: 'select',
            value: 'classCode',
            options: []
          },
          2: {
            name: '终端类型',
            type: 'select',
            value: 'subClassCode',
            options: []
          },
          3: {
            name: '序列号',
            type: 'input',
            value: 'uniqueId'
          },
          4: {
            name: '联系人',
            type: 'input',
            value: 'contact'
          },
          5: {
            name: '归属单位',
            type: 'extra',
            value: 'deptId'
          }
        },
        button: {}
      },
      // 批量引入相关
      batchvisible: false,
      tipsKey: [], // 提示点集合
      btnShow: true, // 显示确认取消按钮
      alterationData: {}
    };
  },
  watch: {
    batchvisible(val) {
      if (val) {
        getDeptPerInit().then(({ data }) => {
          this.dict['deptId'] = data;
        });
      }
    },
    'crud.query.classCode'(val) {
      if (!val) {
        crud.query.subClassCode = '';
        this.$set(this.headConfig.item[2], 'options', []);
        return false;
      }
      this.getListSubClassesByMainClass(val);
      crud.query.subClassCode = '';
    },
    'crud.query.subClassCode'(val) {
      console.log(val);
    }
  },
  created() {
    this.getListMainDeviceClasses();
  },
  methods: {
    getListMainDeviceClasses() {
      listMainDeviceClasses().then(res => {
        const formatList = res.data.map(item => {
          return {
            label: item.className,
            value: item.code
          };
        });
        this.$set(this.headConfig.item[1], 'options', formatList);
        this.$set(this.dict.dict, 'bdmDeviceClasses', formatList);
        this.$set(this.dict, 'bdmDeviceClasses', formatList);
      });
    },
    getListSubClassesByMainClass(code) {
      listSubClassesByMainClass(code).then(res => {
        const formatList = res.data.map(item => {
          return {
            label: item.className,
            value: item.code
          };
        });
        this.headConfig.item[2].options = formatList;
        this.$set(this.dict.dict, 'bdmSubClassesByMainClass', formatList);
        this.$set(this.dict, 'bdmSubClassesByMainClass', formatList);
      });
    },
    [CRUD.HOOK.beforeExport]() {
      // 获取当前选中的列
      const columnList = Object.keys(this.crud.props.tableColumns);
      let list = [];
      // 获取当前选中的字段名
      this.crud.query.columnNameList = columnList.filter((key) => this.crud.props.tableColumns[key].visible === true);
      // 获取当前选中的中文名称
      for (let index = 0; index < columnList.length; index++) {
        const element = columnList[index];
        if (this.crud.props.tableColumns[element].visible === true) {
          list.push(this.crud.props.tableColumns[element].label);
        }
      }
      this.crud.query.headNameList = list;
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('bdResourceDevice', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder(value) {
      return getPlaceholder('bdResourceDevice', value);
    },
    cancel() {
      this.btnShow = true;
    },
    toDetails(param, isEdit) {
      toDetail(param.id).then(res => {
        crud.toEdit(res.data);
        this.btnShow = isEdit;
      });
    },
    handleDelClick(row) {
      this.$confirm(`确定将选择数据删除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.crud.delAllLoading = true;
        this.crud.doDelete([row]);
      });
    },
    handleExport() {
      crud.toQuery();
      const ids = crud.selections?.length ? crud.selections.map(item => item.id) : null;
      crud.doExport(ids);
    },
    getBatchData(data) {
      const {
        deptId,
        file,
        close
      } = data;
      const formData = new FormData();
      formData.append('file', file.raw);
      formData.append('deptId', deptId);
      addbatch(formData).then(res => {
        const {
          code,
          data,
          msg
        } = res;
        if (code === 200 && data !== 207) {
          close();
          this.$message.success('导入成功');
          this.crud.refresh();
        }
        else if (data === 207) {
          this.$message.error('导入失败, 请查看下载文件中的说明');
          const hasHttp = msg.indexOf('http') > -1 || msg.indexOf('https') > -1;
          if (hasHttp) {
            window.open(msg);
          }
          else {
            window.open(`${window.location.origin}${msg}`);
          }
        }
        else {
          this.$message.error('导入失败');
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.xh-container ::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background-color: #fcf0c1;
}
</style>
