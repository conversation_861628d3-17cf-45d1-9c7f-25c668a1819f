<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="btnShow?crud.status.title:'查看'"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      class="rewriting-form-disable"
      :hide-required-asterisk="!btnShow"
      label-width="120px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 应用方向 -->
          <el-form-item
            key="classCode"
            label="应用方向"
            prop="classCode"
          >
            <single-select
              v-model="form.classCode"
              :options="dict.bdmDeviceClasses"
              placeholder="请选择应用方向"
              clearable
              :disabled="!btnShow"
              @change="handleClassCodeChange"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 终端类型 -->
          <el-form-item
            key="subClassCode"
            label="终端类型"
            prop="subClassCode"
          >
            <single-select
              v-model="form.subClassCode"
              :options="dict.bdmSubClassesByMainClass"
              placeholder="请选择终端类型"
              clearable
              :disabled="!btnShow"
              @change="handleSubClassCodeChange"
            />
          </el-form-item>
        </div>
        <div
          v-if="isShowOtherTypes"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <!-- 终端类型选择其他时 显示终端类型名称输入框 -->
          <el-form-item
            key="otherTypes"
            label="终端类型名称"
            prop="otherTypes"
          >
            <el-input
              v-model.trim="form.otherTypes"
              placeholder="请输入终端类型名称"
              :disabled="!btnShow"
              maxlength="50"
              @input="e => form.otherTypes = validInput(e)"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 序列号 -->
          <el-form-item
            key="uniqueId"
            :label="getLabel('uniqueId')"
            prop="uniqueId"
          >
            <el-input
              v-model.trim="form.uniqueId"
              :placeholder="getPlaceholder('uniqueId')"
              :disabled="!btnShow"
              maxlength="20"
              @input="e => form.uniqueId = e.replace(/[^a-zA-Z0-9]/g, '')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- IMEI号 -->
          <el-form-item
            key="imei"
            :label="getLabel('imei')"
            prop="imei"
          >
            <el-input
              v-model.trim="form.imei"
              :placeholder="getPlaceholder('imei')"
              :disabled="!btnShow"
              maxlength="17"
              oninput="value=value.replace(/[^\d]/g,'')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 终端型号 -->
          <el-form-item
            key="model"
            label="终端型号"
            prop="model"
          >
            <el-input
              v-model.trim="form.model"
              placeholder="请输入终端型号"
              maxlength="50"
              :disabled="!btnShow"
              @input="e => form.model = validInput(e)"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 入网方式 -->
          <el-form-item
            key="inNetType"
            label="入网方式"
            prop="inNetType"
          >
            <single-select
              v-model="form.inNetType"
              :options="dict.inNetType"
              placeholder="请选择入网方式"
              clearable
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 入网运营商 -->
          <el-form-item
            key="inNetProvider"
            label="入网运营商"
            prop="inNetProvider"
          >
            <single-select
              v-model="form.inNetProvider"
              :options="dict.inNetProvider"
              placeholder="请选择入网运营商"
              clearable
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 北斗卡号 -->
          <el-form-item
            key="bdCardNumber"
            label="北斗卡号"
            prop="bdCardNumber"
          >
            <el-input
              v-model.trim="form.bdCardNumber"
              placeholder="请输入北斗卡号"
              maxlength="7"
              :disabled="!btnShow"
              show-word-limit
              oninput="value=value.replace(/[^\d]/g,'')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 物联网卡号 -->
          <el-form-item
            key="iotNumber"
            label="物联网卡号"
            prop="iotNumber"
          >
            <el-input
              v-model="form.iotNumber"
              placeholder="请输入物联网卡号"
              maxlength="50"
              :disabled="!btnShow"
              oninput="value=value.replace(/[^\d]/g,'')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 终端编号 -->
          <el-form-item
            key="deviceNo"
            label="终端编号"
            prop="deviceNo"
          >
            <el-input
              v-model="form.deviceNo"
              placeholder="请输入终端编号"
              :disabled="!btnShow"
              maxlength="50"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 经度 -->
          <el-form-item
            key="longitude"
            label="经度"
            prop="longitude"
          >
            <el-input
              v-model="form.longitude"
              placeholder="请输入经度"
              :disabled="!btnShow"
              maxlength="20"
              type="number"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 纬度 -->
          <el-form-item
            key="latitude"
            label="纬度"
            prop="latitude"
          >
            <el-input
              v-model="form.latitude"
              placeholder="请输入纬度"
              :disabled="!btnShow"
              maxlength="20"
              type="number"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 设备安装位置 -->
          <el-form-item
            key="deviceAddr"
            label="设备安装位置"
            prop="deviceAddr"
          >
            <el-input
              v-model.trim="form.deviceAddr"
              placeholder="请输入设备安装位置"
              maxlength="50"
              :disabled="!btnShow"
              @input="e => form.deviceAddr = validInput(e)"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            key="deptId"
            label="归属单位"
            prop="deptId"
          >
            <DeptFormSingleSelect
              ref="deptIdRef"
              v-model="form.deptId"
              :detail-name="form.deptName"
              :disabled="!btnShow"
              placeholder="请选择归属单位"
              :is-show="crud.status.cu > 0"
              size="small"
              @input="validateTreeSelect('deptId')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 联系人 -->
          <el-form-item
            key="contact"
            label="联系人"
            prop="contact"
          >
            <el-input
              v-model.trim="form.contact"
              placeholder="请输入联系人"
              maxlength="20"
              :disabled="!btnShow"
              @input="e => form.contact = validInput(e)"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 联系方式 -->
          <el-form-item
            key="contactPhone"
            label="联系方式"
            prop="contactPhone"
          >
            <el-input
              v-model.trim="form.contactPhone"
              placeholder="请输入联系方式"
              type="number"
              maxlength="20"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 厂商名称 -->
          <el-form-item
            key="manufacturerName"
            label="厂商名称"
            prop="manufacturerName"
          >
            <el-input
              v-model.trim="form.manufacturerName"
              placeholder="请输入厂商名称"
              maxlength="50"
              :disabled="!btnShow"
              @input="e => form.manufacturerName = validInput(e)"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 安装日期 -->
          <el-form-item
            key="installdate"
            label="安装日期"
            prop="installdate"
          >
            <el-date-picker
              v-model="form.installdate"
              type="date"
              placeholder="请选择安装日期"
              value-format="yyyy-MM-dd"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 视频通道数 -->
          <el-form-item
            key="channelNum"
            label="视频通道数"
            prop="channelNum"
          >
            <el-input
              v-model.trim="form.channelNum"
              placeholder="请输入视频通道数"
              :disabled="!btnShow"
              maxlength="4"
              oninput="value=value.replace(/[^\d]/g,'')"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        v-if="btnShow"
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="btnShow"
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';
import SingleSelect from '@/components/select/DictSelect/DictSelectSingle';
import { listSubClassesByMainClass } from '@/api/base/existingTerminal';

const defaultForm = {
  deptId: null,
  deptName: null,
  classCode: null,
  subClassCode: null,
  uniqueId: null,
  imei: null,
  inNetType: null,
  inNetProvider: null,
  bdCardNumber: null,
  model: null,
  iotNumber: null,
  deviceNo: null,
  longitude: null,
  latitude: null,
  deviceAddr: null,
  contact: null,
  contactPhone: null,
  manufacturerName: null,
  installdate: null,
  channelNum: null,
  otherTypes: null
};
export default {
  components: {
    DeptFormSingleSelect,
    SingleSelect
  },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    btnShow: {
      type: Boolean,
      default: true
    },
    regTurn: {
      type: Object,
      default: () => ({})
    },
    regTurnM: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      rules: {
        classCode: {
          required: true,
          trigger: 'change'
        }, // 应用方向
        subClassCode: {
          required: true,
          trigger: 'change'
        }, // 终端类型
        uniqueId: {
          required: true,
          trigger: 'blur'
        }, // 序列号
        imei: {
          required: false, // 定位设备时必填 用户自行判断 前端不做校验
          trigger: 'blur'
        }, // IMEI
        longitude: {
          required: false, // 时频同步、安全监测类终端必填
          trigger: 'blur'
        },
        latitude:  {
          required: false, // 时频同步、安全监测类终端必填
          trigger: 'blur'
        },
        deviceAddr: {
          required: false, // 时频同步、安全监测类终端必填
          trigger: 'blur'
        },
        deptId: {
          required: true,
          trigger: 'change'
        }, // 所属机构
        inNetType: {
          required: false,
          trigger: 'change'
        }, // 入网方式
        inNetProvider: {
          required: false,
          trigger: 'change'
        }, // 入网运营商
        contact:{
          required: true,
          trigger: 'blur'
        }, // 联系人
        contactPhone:{
          required: true
        }, // 联系方式
        channelNum: {
          required: false, // 视频终端必填 用户自行判断 前端不做校验
          trigger: 'blur'
        } // 视频通道数
      },
      loading: false
    };
  },
  computed: {
    isShowOtherTypes() {
      // 其他类型的code值为1000 2000 3000整数
      return this.form.subClassCode?.includes?.('000');
    }
  },
  watch: {
    'form.classCode'(val){
      const isCheckLocItem = ['4', '7'].includes(val);
      this.$set(this.rules.longitude, 'required', isCheckLocItem,);
      this.$set(this.rules.latitude, 'required', isCheckLocItem,);
      this.$set(this.rules.deviceAddr, 'required', isCheckLocItem,);
    }
  },
  methods: {
    getListSubClassesByMainClass(code) {
      listSubClassesByMainClass(code).then(res => {
        const formatList = res.data.map(item => {
          return {
            label: item.className,
            value: item.code
          };
        });
        this.$set(this.dict.dict, 'bdmSubClassesByMainClass', formatList);
        this.$set(this.dict, 'bdmSubClassesByMainClass', formatList);
      });
    },
    handleClassCodeChange(val) {
      if (!val) {
        this.form.subClassCode = '';
        this.$set(this.dict, 'bdmSubClassesByMainClass', []);
        return false;
      }
      this.getListSubClassesByMainClass(val);
      this.form.subClassCode = '';
    },
    handleSubClassCodeChange(val) {
      if (!val?.includes?.('000')) {
        this.form.otherTypes = null;
      }
    },
    /** 新建/编辑" 验证 - 之后 */
    [CRUD.HOOK.afterValidateCU]() {
      const list = Object.keys(this.regTurn);
      for (let i = 0; i < list.length; i++) {
        const key = list[i];
        if (this.form[key] && !this.regTurn[key].test(this.form[key])) {
          this.$message.error(this.regTurnM[key]);
          return false;
        }
      }
      return true;
    },
    [CRUD.HOOK.afterToAdd](){
      //清空form中的id
      this.form.id = null;
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU]() {
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
          this.$refs[`deptIdRef`].$refs[`deptIdStrRef`].$children[0].$el.style.borderColor = '#bfbfbf';
        }
      });
    },
    /** 点击取消之前*/
    [CRUD.HOOK.afterEditCancel]() {
      this.$emit('cancelCU');
    },
    /** 开始 "新建/编辑" - 之后 */
    [CRUD.HOOK.afterToCU]() {
      if (this.form.classCode) {
        this.getListSubClassesByMainClass(this.form.classCode);
      }
    },
    /** 提交之前 */
    [CRUD.HOOK.beforeSubmit]() {
      if (this.form.channelNum != null) this.form.channelNum = Number(this.form.channelNum);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('bdResourceDevice', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder(value, type) {
      return getPlaceholder('bdResourceDevice', value, type);
    },
    // 监听关闭事件
    closed() {
      this.$emit('update:isDetail', false);
    },
    /**
     * 验证treeSelect项
     * @param {String} item
     */
    validateTreeSelect(item) {
      this.$refs.form.validateField(item, (valid) => {
        this.$refs[`${item}Ref`].$refs[`${item}StrRef`].$children[0].$el.style.borderColor = valid !== '' ? '#f56c6c' : '#bfbfbf';
      });
    },
    /** "新建/编辑" 验证 - 之前 */
    [CRUD.HOOK.beforeValidateCU]() {
      this.$refs.form.validate((valid) => {
        if (!valid) {
          this.validateTreeSelect('deptId');
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-input-number .el-input__inner {
  text-align: left;
}

::v-deep .el-form-item.is-error .el-input__inner, .el-form-item.is-error .el-input__inner:focus, .el-form-item.is-error .el-textarea__inner, .el-form-item.is-error .el-textarea__inner:focus{
  border-color: #bfbfbf!important;
}
</style>
