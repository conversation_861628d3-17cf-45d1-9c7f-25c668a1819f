<template>
  <el-dialog
    :visible.sync="msgVisible"
    :append-to-body="true"
    :modal="false"
    title="错误提示"
    width="25%"
    class="msg-dialog"
  >
    <el-table
      :data="msgData"
      :header-cell-style="{'text-align':'center'}"
      :cell-style="{'text-align':'center'}"
      style="width: 100%"
    >
      <el-table-column
        prop="sort"
        label="序号"
      />
      <el-table-column
        prop="details"
        label="详情"
        :resizable="false"
      >
        <template slot-scope="scope">
          <el-popover
            :content="scope.row.details"
            placement="right"
            width="200"
            trigger="hover"
          >
            <el-tag
              slot="reference"
              type="danger"
              effect="plain"
              class="tag"
            >
              详情
            </el-tag>
          </el-popover>
        </template>
      </el-table-column>
      <el-empty slot="empty" :image="require('@/assets/images/nodata.png')" />
</el-table>
    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="msgVisible = false"
      >关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    msgData: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      msgVisible: false
    };
  },
  methods: {
  }
};
</script>

<style scoped>
.tag{
  cursor: pointer;
}
.msg-dialog ::v-deep.el-dialog__body{
  padding: 5px;
  max-height: 335px;
  overflow: overlay;
}
</style>
