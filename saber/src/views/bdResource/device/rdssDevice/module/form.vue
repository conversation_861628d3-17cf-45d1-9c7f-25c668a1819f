<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="crud.status.title"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="form.specificity === '2' ? rules : otherRules"
      size="small"
      class="rewriting-form-disable"
      :hide-required-asterisk="!btnShow"
      label-width="150px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 设备 -->
          <el-form-item
            :label="getLabel('specificity')"
            prop="specificity"
          >
            <single-select
              :options="dict.bdmType"
              v-model="form.specificity"
              :placeholder="getPlaceholder('specificity', 'select')"
              clearable
              :disabled="crud.status.title!=='新增'"
              @change="handleSpecificityChange"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="使用单位"
            prop="deptId"
          >
            <DeptFormSingleSelect
              ref="deptIdRef"
              v-model="form.deptId"
              :detail-name="form.deptName"
              :disabled="!btnShow"
              :is-show="crud.status.cu > 0"
              placeholder="请选择使用单位"
              size="small"
              @input="validateTreeSelect('deptId')"
            />
          </el-form-item>
        </div>
        <div
          v-if="!crud.status.title.includes('新增') || form.specificity === '2'"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <!-- 赋码编号 -->
          <el-form-item
            :label="getLabel('deviceNum')"
            prop="deviceNum"
          >
            <DeviceNumSelect
              :source-form="form"
              :value="form.deviceNum"
              :dept-id="form.deptId"
              :category-dict="getCategoryDict"
              type-dict-value="3"
              :disabled="!crud.status.title.includes('新增')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 序列号 -->
          <el-form-item
            :label="getLabel('uniqueId')"
            prop="uniqueId"
          >
            <el-input
              v-model.trim="form.uniqueId"
              :placeholder="getPlaceholder('uniqueId')"
              :disabled="deviceDisabled || !btnShow"
              maxlength="50"
              @input="e => form.uniqueId = validInput(e)"
            />
          </el-form-item>
        </div>
        <div
          v-if="['1','3'].includes(form.specificity)"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <!-- 终端编号 -->
          <el-form-item
            :label="getLabel('terminalId')"
            prop="terminalId"
          >
            <el-input
              v-model="form.terminalId"
              :placeholder="getPlaceholder('terminalId')"
              :disabled="!btnShow"
              maxlength="50"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 北斗卡号 -->
          <el-form-item
            :label="getLabel('bdCard')"
            prop="bdCard"
          >
            <el-input
              v-model.trim="form.bdCard"
              :placeholder="getPlaceholder('bdCard')"
              :disabled="!btnShow"
              maxlength="50"
              @input="e => form.bdCard = validInput(e)"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 终端类别 -->
          <el-form-item
            :label="getLabel('category')"
            prop="category"
          >
            <single-select
              :options="getCategoryDict"
              v-model="form.category"
              :placeholder="getPlaceholder('category', 'select')"
              clearable
              :disabled="deviceDisabled || !btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 终端型号 -->
          <el-form-item
            :label="getLabel('model')"
            prop="model"
          >
            <el-input
              v-if="['1','3'].includes(form.specificity)"
              v-model="form.model"
              :placeholder="getPlaceholder('model')"
              :disabled="deviceDisabled || !btnShow"
            />
            <xh-select
              v-else
              v-model="form.model"
              placeholder="请选择终端型号"
              clearable
              filterable
              :disabled="deviceDisabled || !btnShow"
            >
              <el-option
                v-for="item in dict.bdmDeviceModel"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- IMEI号 -->
          <el-form-item
            :label="getLabel('imei')"
            prop="imei"
          >
            <el-input
              v-model.trim="form.imei"
              :placeholder="getPlaceholder('imei')"
              :disabled="deviceDisabled || !btnShow"
              maxlength="17"
              show-word-limit
              @input="e => form.imei = validInput(e)"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 厂商名称 -->
          <el-form-item
            :label="getLabel('vendor')"
            prop="vendor"
          >
            <xh-select
              v-model="form.vendor"
              :placeholder="getPlaceholder('vendor', 'select')"
              clearable
              filterable
              :disabled="deviceDisabled || !btnShow"
            >
              <el-option
                v-for="item in dict.vendor"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 北斗芯片序列号 -->
          <el-form-item
            :label="getLabel('bdChipSn')"
            prop="bdChipSn"
          >
            <el-input
              v-model.trim="form.bdChipSn"
              :placeholder="getPlaceholder('bdChipSn')"
              :disabled="deviceDisabled || !btnShow"
              maxlength="50"
              @input="e => form.bdChipSn = validInput(e)"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 物联网卡绑定 -->
          <el-form-item
            :label="getLabel('numbers')"
            prop="numbers"
          >
            <el-input
              v-model="form.numbers"
              :placeholder="getPlaceholder('numbers')"
              maxlength="50"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          v-if="['1','3'].includes(form.specificity)"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <!-- 旧设备定位模式 -->
          <el-form-item
            :label="getLabel('gnssMode')"
            prop="gnssMode"
          >
            <single-select
              :options="dict.bdmGnssMode"
              v-model="form.gnssMode"
              :placeholder="getPlaceholder('gnssMode', 'select')"
              clearable
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 应用方向 -->
          <el-form-item
            :label="getLabel('domain')"
            prop="domain"
          >
            <single-select
              :options="dict.bdmTerminalApp"
              v-model="form.domain"
              :placeholder="getPlaceholder('domain', 'select')"
              clearable
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 应用场景 -->
          <el-form-item
            :label="getLabel('scenario')"
            prop="scenario"
          >
            <el-cascader
              v-model="form.scenario"
              :options="dict.bdmAppSense"
              separator="/"
              :props="{
                expandTrigger: 'hover',
                emitPath: false
              }"
              :placeholder="getPlaceholder('scenario')"
              filterable
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 安装日期 -->
          <el-form-item
            :label="getLabel('installdate')"
            prop="installdate"
          >
            <el-date-picker
              v-model="form.installdate"
              type="date"
              :placeholder="getPlaceholder('installdate')"
              value-format="yyyy-MM-dd"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 卡类型 -->
          <el-form-item
            :label="getLabel('bdCardLevel')"
            prop="bdCardLevel"
          >
            <single-select
              :options="dict.bdmBdcardLevel"
              v-model="form.bdCardLevel"
              :placeholder="getPlaceholder('bdCardLevel', 'select')"
              clearable
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 资产类型 -->
          <el-form-item
            :label="getLabel('assetType')"
            prop="assetType"
          >
            <xh-select
              v-model="form.assetType"
              placeholder="请选择资产类型"
              clearable
              :disabled="!btnShow"
            >
              <el-option
                v-for="item in dict.assetType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 归属单位类型 -->
          <el-form-item
            :label="getLabel('ownDeptType')"
            prop="ownDeptType"
          >
            <xh-select
              v-model="form.ownDeptType"
              placeholder="请选择归属单位类型"
              clearable
              :disabled="!btnShow"
              @change="form.ownDeptName = null"
            >
              <el-option
                v-for="item in dict.ownDeptType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div
          v-if="form.ownDeptType === '2'"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <!-- 归属单位名称 -->
          <el-form-item
            :label="getLabel('ownDeptName')"
            prop="ownDeptName"
          >
            <el-input
              v-model.trim="form.ownDeptName"
              :placeholder="getPlaceholder('ownDeptName')"
              :disabled="!btnShow"
              @input="e => form.ownDeptName = validInput(e)"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        v-if="btnShow"
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="btnShow"
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import { validateFn } from '@/validate/vehicle/vehicleRules';
import getPlaceholder from '@/utils/getPlaceholder';
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';
import DeviceNumSelect from '@/components/deviceNumSelect/deviceNumSelect.vue';
import SingleSelect from '@/components/select/DictSelect/DictSelectSingle';

const defaultForm = {
  deptId: null,
  deptName: '',
  vendor: '',
  model: null,
  id: null,
  imei: '',
  bdChipSn: '',
  address: null,
  installdate: null,
  refSource: null,
  clockSignal: null,
  numbers: null,
  dataPlan: null,
  cardCategory: null,
  expire: null,
  bdCard: null,
  bdCardLevel: null,
  uniqueId: null,
  domain: null,
  assetType: null,
  ownDeptType: null,
  ownDeptName: null,
  scenario: null,
  specificity: null,
  deviceNum: null,
  gnssMode: null,
  category: null,
  targetId: null,
  terminalId: null
};
export default {
  components: {
    DeviceNumSelect,
    DeptFormSingleSelect,
    SingleSelect
  },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    btnShow: {
      type: Boolean,
      default: true
    },
    regTurn: {
      type: Object,
      default: () => ({})
    },
    regTurnM: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      rules: {
        deptId: {
          required: true,
          validator: validateFn('Cap', 'deptId'),
          trigger: 'change'
        }, // 所属机构
        specificity: {
          required: true,
          validator: validateFn('Cap', 'specificity'),
          trigger: 'change'
        }, // 设备
        gnssMode: {
          required: true,
          validator: validateFn('Cap', 'gnssMode'),
          trigger: 'change'
        }, // 旧设备定位模式
        deviceNum: {
          required: true,
          validator: validateFn('Cap', 'deviceNum'),
          trigger: 'blur'
        }, // 赋码编号
        vendor: {
          required: true,
          validator: validateFn('Cap', 'vendor'),
          trigger: 'blur'
        }, // 设备id
        model: {
          required: true,
          validator: validateFn('Cap', 'model'),
          trigger: 'change'
        }, // 终端型号
        category: {
          required: true,
          validator: validateFn('Cap', 'category'),
          trigger: 'blur'
        }, // 终端类别
        imei: {
          required: true,
          validator: validateFn('Cap', 'imei'),
          trigger: 'blur'
        }, // imei
        uniqueId: {
          required: true,
          validator: validateFn('Cap', 'uniqueId'),
          trigger: 'blur'
        }, // imei
        bdChipSn: {
          required: true,
          validator: validateFn('Cap', 'bdChipSn'),
          trigger: 'blur'
        }, // bdChipSn
        bdCard: {
          required: true,
          validator: validateFn('Cap', 'bdCard'),
          trigger: 'blur'
        }, // bdCard
        // numbers: {
        //   required: true,
        //   validator: validateFn('Cap', 'numbers'),
        //   trigger: 'blur'
        // } // iccid
      },
      otherRules: {
        deptId: {
          required: true,
          validator: validateFn('bdResourceDevice', 'deptId'),
          trigger: 'change'
        }, // 所属机构
        specificity: {
          required: true,
          validator: validateFn('bdResourceDevice', 'specificity'),
          trigger: 'change'
        }, // 设备
        category: {
          required: true,
          validator: validateFn('bdResourceDevice', 'category'),
          trigger: 'blur'
        }, // 终端类别
        uniqueId: {
          required: true,
          validator: validateFn('bdResourceDevice', 'uniqueId'),
          trigger: 'blur'
        }, // uniqueId
        model: {
          required: true,
          validator: validateFn('bdResourceDevice', 'model'),
          trigger: 'change'
        }, // 终端型号
        bdCard: {
          required: true,
          validator: validateFn('Cap', 'bdCard'),
          trigger: 'blur'
        }, // bdCard
        terminalId: {
          required: true,
          validator: validateFn('bdResourceDevice', 'terminalId'),
          trigger: 'blur'
        }, // 终端编号
        // numbers: {
        //   required: true,
        //   validator: validateFn('bdResourceDevice', 'numbers'),
        //   trigger: 'blur'
        // } // iccid
      },
      iotCards: [],
      loading: false,
      deviceDisabled: false,
      depts: []
    };
  },
  computed: {
    getCategoryDict() {
      for (const item of this.dict?.bdmDeviceType) {
        if (item.value === '3') {
          return item.children;
        }
      }
      return [];
    }
  },
  watch: {
    'form.specificity'() {
      if (this.crud.status.title.includes('新增')) {
        this.setData({});
      }
      if (this.form.specificity) this.deviceDisabled = this.form.specificity === '2';
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });
    },
    'form.deptId'(newVal, oldVal){
      // if (!!oldVal !== false && newVal !== oldVal) {
      //   this.form.deviceNum = '';
      //   this.form.vendor = '';
      //   this.form.model = '';
      //   this.form.category = '';
      //   this.form.imei = '';
      //   this.form.uniqueId = '';
      //   this.form.bdChipSn = '';
      // }
    }
  },
  methods: {
    /** 新建/编辑" 验证 - 之后 */
    [CRUD.HOOK.afterValidateCU]() {
      const list = Object.keys(this.regTurn);
      for (let i = 0; i < list.length; i++) {
        const key = list[i];
        if (this.form[key] && !this.regTurn[key].test(this.form[key])) {
          this.$message.error(this.regTurnM[key]);
          return false;
        }
      }
      return true;
    },
    setData(item) {
      this.form.model = item.deviceModel || '';
      this.form.vendor = item.manufacturer || '';
      this.form.imei = item.imei || '';
      this.form.bdChipSn = item.chipSeq || '';
      this.form.uniqueId = item.deviceSeq || '';
    },
    handleSpecificityChange() {
      this.form.deviceNum = null;
      this.form.gnssMode = null;
      this.form.category = null;
      this.$refs['form'].clearValidate([
        'deviceNum',
        'gnssMode'
      ]);
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU]() {
      if (this.crud.form.id) {
        this.defaultValue = this.form.deviceNum;
      }
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
          this.$refs[`deptIdRef`].$refs[`deptIdStrRef`].$children[0].$el.style.borderColor = '#BFBFBF';
        }
      });
    },
    /** 点击取消之前*/
    [CRUD.HOOK.afterEditCancel]() {
      this.$emit('cancelCU');
    },
    /** 开始 "新建/编辑" - 之后 */
    [CRUD.HOOK.afterToCU]() {
      if (this.form.type != null) this.form.type = this.form.type.toString();
      if (this.form.category != null) this.form.category = this.form.category.toString();
      if (this.form.bdCardLevel != null) this.form.bdCardLevel = this.form.bdCardLevel.toString();
      if (this.form.domain != null) this.form.domain = this.form.domain.toString();
      if (this.form.scenario != null) this.form.scenario = this.form.scenario.toString();
      if (this.form.specificity != null) this.form.specificity = this.form.specificity.toString();
      if (this.form.gnssMode != null) this.form.gnssMode = this.form.gnssMode.toString();
      if (this.form.assetType) this.form.assetType = this.form.assetType.toString();
      if (this.form.ownDeptType) this.form.ownDeptType = this.form.ownDeptType.toString();
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('bdResourceDevice', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder(value, type) {
      return getPlaceholder('bdResourceDevice', value, type);
    },
    // 监听关闭事件
    closed() {
      this.$emit('update:isDetail', false);
      this.deviceDisabled = false;
    },
    /**
     * 验证treeSelect项
     * @param {String} item
     */
    validateTreeSelect (item) {
      this.$refs.form.validateField(item, (valid) => {
        this.$refs[`${item}Ref`].$refs[`${item}StrRef`].$children[0].$el.style.borderColor = valid !== '' ? '#F56C6C' : '#BFBFBF';
      });
    },
    /** "新建/编辑" 验证 - 之前 */
    [CRUD.HOOK.beforeValidateCU] () {
      this.$refs.form.validate((valid)=>{
        if (!valid) {
          this.validateTreeSelect('deptId');
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-input-number .el-input__inner {
  text-align: left;
}
</style>
