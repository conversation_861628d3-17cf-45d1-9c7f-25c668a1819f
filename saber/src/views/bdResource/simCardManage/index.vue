<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="100px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="batchPer"
          :download="false"
        >
          <template slot="right">
            <el-button
              v-permission="batchPer.imp"
              class="filter-item"
              icon="el-icon-upload2"
              size="small"
              @click="batchvisible = true"
            >
              导入
            </el-button>
            <el-button
              v-permission="batchPer.exp"
              :loading="crud.downloadLoading"
              class="filter-item"
              size="small"
              icon="el-icon-download"
              @click="handleExport"
            >
              导 出
            </el-button>
          </template>
        </crudOperation>
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            v-permission="['admin','simCardManage:edit','simCardManage:del']"
            width="120"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetails(scope.row)"
                  >
                    详情
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <!-- 设备id -->
          <!--          <el-table-column-->
          <!--            v-if="columns.visible('deviceId')"-->
          <!--            :label="getLabel('deviceId')"-->
          <!--            prop="deviceId"-->
          <!--            min-width="100"-->
          <!--            show-overflow-tooltip-->
          <!--          />-->
          <!-- 物联网卡号 -->
          <el-table-column
            v-if="columns.visible('number')"
            :label="getLabel('number')"
            prop="number"
            min-width="200"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 所属机构 -->
          <el-table-column
            v-if="columns.visible('deptName')"
            :label="getLabel('deptName')"
            prop="deptName"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 本机号 -->
          <el-table-column
            v-if="columns.visible('cardNumber')"
            :label="getLabel('cardNumber')"
            prop="cardNumber"
            min-width="200"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- ICCID -->
          <el-table-column
            v-if="columns.visible('iccid')"
            :label="getLabel('iccid')"
            prop="iccid"
            min-width="200"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- IMSI号 -->
          <el-table-column
            v-if="columns.visible('imsi')"
            :label="getLabel('imsi')"
            prop="imsi"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 客户名称 -->
          <el-table-column
            v-if="columns.visible('holder')"
            :label="getLabel('holder')"
            prop="holder"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 运营商 -->
          <el-table-column
            v-if="columns.visible('operatorName')"
            :label="getLabel('operator')"
            prop="operatorName"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          >
          </el-table-column>
          <!-- 卡状态 -->
          <el-table-column
            v-if="columns.visible('statusName')"
            :label="getLabel('status')"
            prop="statusName"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
          </el-table-column>
          <!-- 发卡日期 -->
          <el-table-column
            v-if="columns.visible('issueTime')"
            :label="getLabel('issueTime')"
            prop="issueTime"
            min-width="130"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <div class="table-date-td">
                {{ parseTimes(scope.row.issueTime) }}
              </div>
            </template>
          </el-table-column>
          <!-- 激活日期 -->
          <el-table-column
            v-if="columns.visible('activationTime')"
            :label="getLabel('activationTime')"
            prop="activationTime"
            min-width="130"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <div class="table-date-td">
                {{ parseTimes(scope.row.activationTime) }}
              </div>
            </template>
          </el-table-column>
          <!-- 服务期止 -->
          <el-table-column
            v-if="columns.visible('expire')"
            :label="getLabel('expire')"
            prop="expire"
            min-width="130"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <div class="table-date-td">
                {{ parseTimes(scope.row.expire) }}
              </div>
            </template>
          </el-table-column>
          <!-- 流量套餐 -->
          <el-table-column
            v-if="columns.visible('dataPlanName')"
            :label="getLabel('dataPlan')"
            prop="dataPlanName"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
          </el-table-column>
          <!-- 流量套餐总量(MB) -->
          <el-table-column
            v-if="columns.visible('packetSize')"
            :label="getLabel('packetSize')"
            prop="packetSize"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 卡类型 -->
          <el-table-column
            v-if="columns.visible('categoryName')"
            :label="getLabel('category')"
            prop="categoryName"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 设备类型 -->
          <el-table-column
            v-if="columns.visible('deviceTypeName')"
            :label="getLabel('deviceType')"
            prop="deviceTypeName"
            min-width="150"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
      <!--表单渲染-->
      <eForm
        :dict="dict"
        :regTurn="regTurn"
        :regTurnM="regTurnM"
        :btnShow="btnShow"
        @cancelCU="cancel"
      />
      <BatchImport
        :visible="batchvisible"
        mod="simCardManager"
        @close="batchvisible = false"
        @getBatchData="getBatchData"
      />
      <msgDialog
        ref="msgDialog"
        :msg-data="msgData"
      />
    </div>
  </basic-container>
</template>

<script>
import crudSimCardManage from '@/api/base/simCardManage';
import eForm from './module/form';
import CRUD, { presenter, header } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import pagination from '@/components/Crud/Pagination';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import HeadCommon from '@/components/formHead/headCommon.vue';
import BatchImport from '@/components/upload/batchImport.vue';
import msgDialog from '@/components/importErr';
import { getDeptPerInit } from '@/api/base/dept';

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('SimCardManage', 'uniName'),
  crudMethod: { ...crudSimCardManage },
});

export default {
  name: 'SimCardManage',
  components: { eForm, crudOperation, udOperation, pagination, HeadCommon, BatchImport, msgDialog },
  mixins: [presenter(crud), header()],
  // 数据字典
  dicts: [
    'bdmIotcardCategory',
    'bdmIotcardDataPlan',
    'bdmIotcardStatus',
    'bdmIotcardOperator',
    'licenceColor',
  ],
  data () {
    this.regTurn = {
      number: /^[a-zA-z0-9]+$/,
      iccid: /^[a-zA-z0-9]+$/,
      imsi: /^[a-zA-z0-9]+$/,
      cardNumber: /^\d+$/,
    };
    this.regTurnM = {
      number: '物联网卡号只能输入数字跟字母',
      iccid: 'ICCID只能输入数字跟字母',
      imsi: 'IMSI号只能输入数字跟字母',
      cardNumber: '本机号只能输入数字',
    };
    return {
      batchPer: {
        add: ['admin', 'simCardManage:add'],
        imp: ['admin', 'simCardManage:imp'],
        exp: ['admin', 'simCardManage:exp'],
        del: ['admin', 'simCardManage:del']
      },
      permission: {
        add: ['admin', 'simCardManage:add'],
        edit: ['admin', 'simCardManage:edit'],
        del: ['admin', 'simCardManage:del'],
      },
      headConfig: {
        item: {
          1: {
            name: '物联网卡号',
            type: 'input',
            value: 'number',
          },
          2: {
            name: 'ICCID',
            type: 'input',
            value: 'iccid',
          }
        },
        button: {
        }
      },
      // 批量引入相关
      batchvisible: false,
      msgData: [], // 批量导入提示消息
      numKey: undefined, // 递归中继值
      dataType: { // excel里文字对应的key
        '所属机构': 'deptId',
        '物联网卡号': 'number',
        '本机号': 'cardNumber',
        'ICCID': 'iccid',
        'IMSI号': 'imsi',
        '客户名称': 'holder',
        '运营商': 'operator',
        '卡状态': 'status',
        '发卡日期': 'issueTime',
        '激活日期': 'activationTime',
        '有效期': 'expire',
        '流量套餐': 'dataPlan',
        '流量套餐总量(MB)': 'packetSize',
        '卡类型': 'category',
      },
      // 必填项
      typeRequired: ['deptId', 'number', 'operator'],
      // 表单名称对应字典(表单名称与字典名称不一致时)
      typeDictName: {
        category: 'bdmIotcardCategory',
        dataPlan: 'bdmIotcardDataPlan',
        operator: 'bdmIotcardOperator',
        status: 'bdmIotcardStatus',
      },
      tipsKey: [], // 提示点集合
      btnShow: true, // 显示确认取消按钮
      regulatesList: [], // 监管部门
      deptDataObj: {}
    };
  },
  watch: {
    batchvisible(val) {
      if (val) {
        getDeptPerInit().then(({ data }) => {
          // 机构树为嵌套对象数组, 导入数据过多时遍历循环获取机构id会导致卡顿, 因此将机构树转为对象
          this.deptDataObj = this.flattenToObj(data);
        });
      }
    }
  },
  methods: {
    flattenToObj(arr, result = {}) {
      arr.forEach((item) => {
        result[item.title] = item.id;
        if (item.children && item.children.length) {
          this.flattenToObj(item.children, result);
        }
      });
      return result;
    },
    [CRUD.HOOK.beforeExport]() {
      // 获取当前选中的列
      const columnList = Object.keys(this.crud.props.tableColumns);
      let list = [];
      // 获取当前选中的字段名
      this.crud.query.columnNameList = columnList.filter((key) => this.crud.props.tableColumns[key].visible === true);
      // 获取当前选中的中文名称
      for (let index = 0; index < columnList.length; index++) {
        const element = columnList[index];
        if (this.crud.props.tableColumns[element].visible === true) {
          list.push(this.crud.props.tableColumns[element].label);
        }
      }
      this.crud.query.headNameList = list;
    },
    // 批量导入相关
    // 获取excel的数据
    getBatchData (returnValue) {
      let data = returnValue.data;
      if(!this.reversalObj) {
        this.reversalObj = {};
        for (let k in this.dataType) {
          this.reversalObj[this.dataType[k]] = k;
        }
      }
      this.tipsKey = [];
      let arr = data.map((item, index) => {
        let obj = {};
        for (let key in item) {
          for (let k in this.dataType) {
            if (key === k) {
              obj[this.dataType[k]] = item[key];
            }
          }
        }
        this.typeTreeTurn(obj);
        this.typeDeptTurn(obj);
        this.typeRequiredTurn(obj, index);
        this.typeRegTurn(obj, index, item);
        return obj;
      });
      if (this.tipsKey && this.tipsKey.length > 0) {
        let arr = [];
        this.tipsKey.forEach((item, index) => {
          if (item && item.length > 0) {
            const errList = [];
            item.forEach(v => {
              errList.push(v);
            });
            arr.push({
              sort: `第${index + 1}行`,
              details: errList.join(',')
            });
          }
        });
        this.msgData = arr;
        this.$refs.msgDialog.msgVisible = true;
      } else {
        this.addbatchPost(arr);
        returnValue.close();
      }
    },
    handleErrFun(index, v) {
      const pL = this.reversalObj[v];
      if(pL) {
        if (!this.tipsKey[index]) {
          this.tipsKey[index] = [pL];
        } else if(!this.tipsKey[index].includes(pL)) {
          this.tipsKey[index].push(pL);
        }
      }
    },
    typeRegTurn(obj, k, data) {
      Object.keys(this.regTurn).forEach( v => {
        const reg = this.regTurn[v];
        if(obj[v] && !reg.test(obj[v])) {
          this.handleErrFun(k, v);
        }
      });
      const rList = ['operator', 'status', 'dataPlan', 'category'];
      rList.forEach(v => {
        const pL = this.reversalObj[v];
        if (data[pL] && (data[pL] === obj[v] || !obj[v])) {
          this.handleErrFun(k, v);
        }
      });
      const LenObj = {
        number: 50,
        holder: 50,
        iccid: 50,
        imsi: 50,
        cardNumber: 50
      };
      Object.keys(LenObj).forEach( key => {
        if(`${obj[key]}`.length > LenObj[key]) {
          this.handleErrFun(k, key);
        }
      });
      if(obj.packetSize && !/^1?\d{1,6}(\.\d+)?$/.test(obj.packetSize)) {
        this.handleErrFun(k, 'packetSize');
      }
      const dataList = ['expire', 'activationTime', 'issueTime'];
      dataList.forEach( key => {
        if(obj[key] && !/^(\d+)-(\d+)-(\d+)/.test(obj[key])) {
          this.handleErrFun(k, key);
        }
      });
    },
    // 必填项判断
    typeRequiredTurn (obj, k, dataRequired = this.typeRequired) {
      dataRequired.forEach(v => {
        if (typeof (v) === 'object' && v.mod) {
          this.typeRequiredTurn(obj[v.mod], k, v.required);
        } else if (!obj[v] && obj[v] !== 0) {
          this.handleErrFun(k, v);
        }
      });
    },
    // 机构名称转id
    typeDeptTurn (obj) {
      if (obj?.deptId) {
        obj.deptId = this.deptDataObj[obj.deptId];
      }
    },
    // 字典里的转key/id
    typeTreeTurn (obj) {
      for (let k in this.dict) {
        for (let j in obj) {
          if (k === j) {
            this.treeTurn(this.dict[k], obj[j],j);
            obj[j] = this.numKey;
            this.numKey = undefined;
          }else if (k === this.typeDictName[j]) {
            this.treeTurn(this.dict[this.typeDictName[j]], obj[j],j);
            obj[j] = this.numKey;
            this.numKey = undefined;
          }
        }
      }
    },
    getTreeTurn(str){
      let obj = {
        value:'value',
        label:'label'
      };
      return obj;
    },
    // 递归找key/id
    treeTurn (tree, word,str, iValue = this.getTreeTurn(str).value, iLabel = this.getTreeTurn(str).label, iChildren = 'children') {
      tree.forEach(item => {
        if (!item.disabled && item[iLabel] === word) {
          this.numKey = item[iValue];
        } else if (item[iChildren] && item[iChildren].length > 0) {
          this.treeTurn(item[iChildren], word, str, iValue,iLabel, iChildren);
        }
      });
    },
    // 提交请求
    addbatchPost (arr) {
      crudSimCardManage.addbatch(arr).then(res => {
        this.$message({
          showClose: true,
          message: res.msg,
          type: 'success'
        });
        this.crud.refresh();
      });
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('SimCardManage', value);
    },
    parseTimes (time) {
      if (time) {
        return this.$moment(time).format('YYYY-MM-DD');
      }
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('SimCardManage', value);
    },
    toDetails(param){
      crud.toEdit(param);
      this.btnShow = false;
    },
    cancel(){
      this.btnShow = true;
    },
    handleExport () {
      crud.toQuery();
      const ids =crud.selections?.length ? crud.selections.map( item => item.id) : null;
      crud.doExport(ids);
    },
  }
};
</script>

<style lang="less" scoped>
.xh-container ::v-deep.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
    background-color: #fcf0c1;
  }
</style>
