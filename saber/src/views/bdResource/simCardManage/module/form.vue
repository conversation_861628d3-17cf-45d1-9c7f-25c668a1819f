<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="btnShow?crud.status.title:'查看'"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      class="rewriting-form-disable"
      :hide-required-asterisk="!btnShow"
      label-width="130px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('deptName')"
            prop="deptId"
          >
            <DeptFormSingleSelect
              ref="deptIdRef"
              v-model="form.deptId"
              :detail-name="form.deptName"
              :disabled="!btnShow"
              :is-show="crud.status.cu > 0"
              placeholder="请选择所属机构"
              size="small"
              @input="validateTreeSelect('deptId')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- sim卡号 -->
          <el-form-item
            :label="getLabel('number')"
            prop="number"
          >
            <el-input
              v-model.trim="form.number"
              :placeholder="getPlaceholder('number')"
              :disabled="!btnShow || crud.status.edit"
              maxLength="50"
              @change="handleChange($event, 'number')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 运营商 -->
          <el-form-item
            :label="getLabel('operator')"
            prop="operator"
          >
            <single-select
              :options="dict.bdmIotcardOperator"
              v-model="form.operator"
              :placeholder="getPlaceholder('operator', 'select')"
              clearable
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 本机号 -->
          <el-form-item
            :label="getLabel('cardNumber')"
            prop="cardNumber"
          >
            <el-input
              v-model.trim="form.cardNumber"
              :placeholder="getPlaceholder('cardNumber')"
              :disabled="!btnShow"
              maxLength="50"
              @change="handleChange($event, 'cardNumber')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- iccid -->
          <el-form-item
            :label="getLabel('iccid')"
            prop="iccid"
          >
            <el-input
              v-model.trim="form.iccid"
              :placeholder="getPlaceholder('iccid')"
              :disabled="!btnShow"
              maxLength="50"
              @change="handleChange($event, 'iccid')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- imsi -->
          <el-form-item
            :label="getLabel('imsi')"
            prop="imsi"
          >
            <el-input
              v-model.trim="form.imsi"
              :placeholder="getPlaceholder('imsi')"
              maxlength="50"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 客户名称 -->
          <el-form-item
            :label="getLabel('holder')"
            prop="holder"
          >
            <el-input
              v-model.trim="form.holder"
              maxlength="50"
              :placeholder="getPlaceholder('holder')"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 卡状态 -->
          <el-form-item
            :label="getLabel('status')"
            prop="status"
          >
            <single-select
              :options="dict.bdmIotcardStatus"
              v-model="form.status"
              :placeholder="getPlaceholder('status', 'select')"
              clearable
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 发卡日期 -->
          <el-form-item
            :label="getLabel('issueTime')"
            prop="issueTime"
          >
            <el-date-picker
              v-model="form.issueTime"
              type="date"
              :placeholder="getPlaceholder('issueTime')"
              value-format="yyyy-MM-dd"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 激活日期 -->
          <el-form-item
            :label="getLabel('activationTime')"
            prop="activationTime"
          >
            <el-date-picker
              v-model="form.activationTime"
              type="date"
              value-format="yyyy-MM-dd"
              :placeholder="getPlaceholder('activationTime')"
              :picker-options="pickerOptions"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 服务期止 -->
          <el-form-item
            :label="getLabel('expire')"
            prop="expire"
          >
            <el-date-picker
              v-model="form.expire"
              type="date"
              :placeholder="getPlaceholder('expire')"
              value-format="yyyy-MM-dd"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 流量套餐 -->
          <el-form-item
            :label="getLabel('dataPlan')"
            prop="dataPlan"
          >
            <single-select
              :options="dict.bdmIotcardDataPlan"
              v-model="form.dataPlan"
              :placeholder="getPlaceholder('dataPlan', 'select')"
              clearable
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 流量套餐总量(MB) -->
          <el-form-item
            :label="getLabel('packetSize')"
            prop="packetSize"
          >
            <el-input-number
              v-model.number.trim="form.packetSize "
              :max="1000000"
              :min="0"
              :controls="false"
              type="number"
              :placeholder="getPlaceholder('packetSize')"
              :disabled="!btnShow"
              style="width: 100%;"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 卡类型 -->
          <el-form-item
            :label="getLabel('category')"
            prop="category"
          >
            <single-select
              :options="dict.bdmIotcardCategory"
              v-model="form.category"
              :placeholder="getPlaceholder('category', 'select')"
              clearable
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        v-if="btnShow"
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="btnShow"
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import { getDeptListByName } from "@/api/base/dept";
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';
import SingleSelect from '@/components/select/DictSelect/DictSelectSingle';
const defaultForm = {
  id:null,
  number: null,
  cardNumber: null,
  deviceId: null,
  deviceType: null,
  iccid: null,
  imsi: null,
  holder: null,
  operator: null,
  status: null,
  issueTime: null,
  activationTime: null,
  expire: null,
  dataPlan: null,
  packetSize: null,
  category: null,
  deptId: null,
  deptName: null
};
export default {
  components: { DeptFormSingleSelect, SingleSelect },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    btnShow: {
      type: Boolean,
      default: true
    },
    regTurn: {
      type: Object,
      default: () => {
        return {};
      },
    },
    regTurnM: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data () {
    return {
      pickerOptions: {
        disabledDate(time) {
          // 禁止选择未来的日期
          return time.getTime() > Date.now();
        }
      },
      rules: {
        deptId: { required: true, message: '请选择所属机构', trigger: 'change' }, // 所属机构
        number: { required: true, message: '请输入物联网卡号', trigger: 'blur' }, // sim卡号
        // iccid: { required: true, message: '请输入iccid', trigger: 'blur' }, // iccid
        // category: { required: true, message: '请选择卡类型', trigger: 'change' }, // 卡类型
        operator: { required: true, message: '请选择运营商', trigger: 'change' }, // 运营商
        // dataPlan: { required: true, message: '请选择流量套餐', trigger: 'change' }, // 流量套餐
        // status: { required: true, message: '请选择卡状态', trigger: 'change' }, // 卡状态
        // expire: { required: true, message: '请选择有效期', trigger: 'change' }, // 服务期止
      },
      depts: []
    };
  },
  watch: {
    // 'crud.status.cu'(val) {
    //   if(val > 0) {
    //     if(this.form.deptId) {
    //       this.oldValue = this.form.deptName;
    //       this.depts = [{
    //         deptName: this.form.deptName,
    //         id: this.form.deptId
    //       }];
    //     } else {
    //       this.depts = [];
    //       this.oldValue = "";
    //     }
    //   }
    // },
  },
  methods: {
    handleChange(val, key) {
      this.form[key] = val;
    },
    /** 新建/编辑" 验证 - 之后 */
    [CRUD.HOOK.afterValidateCU] () {
      const list = Object.keys(this.regTurn)
      for (let i = 0; i < list.length; i++) {
        const key = list[i]
        if (this.form[key] && !this.regTurn[key].test(this.form[key])) {
          this.$message.error(this.regTurnM[key]);
          return false
        }
      }
      return true
    },
    // 相差8小时, 后台存储不会加上8小时, 直接往数据库存储, 因此前端自行加上8小时
    handleDateChange(time) {
      this.form[time] = new Date(new Date(this.form[time]).getTime() + 8 * 60 * 60 * 1000);
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.$nextTick(()=>{
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
          this.$refs[`deptIdRef`].$refs[`deptIdStrRef`].$children[0].$el.style.borderColor = '#BFBFBF'
        }
      });
    },
    /** 开始 "新建/编辑" - 之后 */
    [CRUD.HOOK.afterToCU] () {
      if (this.form.category != null) this.form.category = this.form.category.toString();
      if (this.form.dataPlan != null) this.form.dataPlan = this.form.dataPlan.toString();
      if (this.form.operator != null) this.form.operator = this.form.operator.toString();
      if (this.form.status != null) this.form.status = this.form.status.toString();
    },
    /** 点击取消之前*/
    [CRUD.HOOK.afterEditCancel] () {
      this.$emit('cancelCU');
    },
    // 监听关闭事件
    closed () {
      this.$emit('update:isDetail', false);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('SimCardManage', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value, type) {
      return getPlaceholder('SimCardManage', value, type);
    },
    /**
     * 验证treeSelect项
     * @param {String} item
     */
    validateTreeSelect (item) {
      this.$refs.form.validateField(item, (valid) => {
        this.$refs[`${item}Ref`].$refs[`${item}StrRef`].$children[0].$el.style.borderColor = valid !== '' ? '#F56C6C' : '#BFBFBF';
      });
    },
    /** "新建/编辑" 验证 - 之前 */
    [CRUD.HOOK.beforeValidateCU] () {
      this.$refs.form.validate((valid)=>{
        if (!valid) {
          this.validateTreeSelect('deptId');
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
</style>
