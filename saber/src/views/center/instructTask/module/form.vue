<template>
  <div>
    <el-dialog
      ref="dialogForm"
      v-dialog-drag
      :append-to-body="true"
      :close-on-click-modal="false"
      :before-close="crud.cancelCU"
      :visible="crud.status.cu > 0"
      :title="crud.status.title"
      width="1000px"
      @closed="closed"
    >
      <div class="leftTop">
        <VehicleMultiSelectAllTree
          ref="VehicleMultiSelectAllTree"
          :is-show="true"
          @checkedVehiclesChange="checkedVehiclesChange"
        />
      </div>
      <!-- 表单 -->
      <div class="notice-form">
        <el-form
          ref="form"
          label-width="230px"
          :model="form"
          :rules="rules"
          size="small"
        >
          <div class="form-header">
            <el-divider
              content-position="left"
              class="divider-title"
            >
              任务设置
            </el-divider>
            <BtnMore
              class="divider-btn"
              :is-down="taskDialog"
              @click="taskDialog = !taskDialog"
            />
          </div>
          <div v-show="taskDialog">
            <el-form-item
              :label="getLabel('taskName')"
              prop="taskName"
            >
              <el-input
                v-model="form.taskName"
                :placeholder="getPlaceholder('taskName')"
              />
            </el-form-item>
            <el-form-item
              :label="getLabel('interval')"
              prop="interval"
            >
              <el-select
                v-model="form.interval"
                placeholder="请选择任务频次"
                @change="handleFrequencyChange"
              >
                <el-option
                  v-for="item in frequencyOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="form.interval !== 4"
              :label="getLabel('planExecuteTime')"
            >
              <el-select
                v-if="form.interval === 1"
                v-model="form.day"
                class="date-select"
              >
                <el-option
                  v-for="item in 31"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
              <span
                v-if="form.interval === 1"
                class="data-text"
              >日</span>
              <el-select
                v-if="form.interval === 1 || form.interval === 2"
                v-model="form.hour"
                class="date-select"
              >
                <el-option
                  v-for="(item, index) in 24"
                  :key="index"
                  :label="index"
                  :value="index"
                />
              </el-select>
              <span
                v-if="form.interval === 1 || form.interval === 2"
                class="data-text"
              >时</span>
              <el-select
                v-model="form.minute"
                class="date-select"
              >
                <el-option
                  v-for="(item, index) in 60"
                  :key="index"
                  :label="index"
                  :value="index"
                />
              </el-select>
              <span class="data-text">分</span>
            </el-form-item>
            <el-form-item
              :label="getLabel('executeMaxCount')"
              prop="executeMaxCount"
            >
              <el-input
                v-model.number="form.executeMaxCount"
                :placeholder="getPlaceholder('executeMaxCount')"
              />
            </el-form-item>
            <el-form-item
              :label="getLabel('startTime')"
              prop="startTime"
            >
              <el-date-picker
                v-model="form.startTime"
                size="small"
                type="datetime"
                default-time="00:00:00"
                placeholder="选择开始日期时间"
                value-format="timestamp"
                :picker-options="pickerOptions"
              />
            </el-form-item>
            <el-form-item
              :label="getLabel('endTime')"
              prop="endTime"
            >
              <el-date-picker
                v-model="form.endTime"
                size="small"
                type="datetime"
                default-time="23:59:59"
                placeholder="选择结束日期时间"
                value-format="timestamp"
                :picker-options="pickerOptions"
              />
            </el-form-item>
            <el-form-item
              :label="getLabel('taskState')"
              prop="taskState"
            >
              <el-radio-group v-model="form.taskState">
                <el-radio :label="1">
                  开启
                </el-radio>
                <el-radio :label="2">
                  关闭
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              :label="getLabel('vehicleState')"
              prop="vehicleState"
            >
              <el-checkbox-group
                v-model="form.vehicleState"
                :min="1"
              >
                <el-checkbox label="1">
                  行驶
                </el-checkbox>
                <el-checkbox label="2">
                  停止
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>
          <div class="form-header">
            <el-divider
              content-position="left"
              class="divider-title form-divider"
            >
              指令设置
            </el-divider>
            <BtnMore
              class="divider-btn"
              :is-down="instructDialog"
              @click="instructDialog = !instructDialog"
            />
          </div>
          <div v-show="instructDialog">
            <el-form-item
              :label="getLabel('distributeType')"
              prop="distributeType"
            >
              <el-radio-group
                v-model="form.distributeType"
                @change="HandleTaskType"
              >
                <el-radio :label="1">
                  内容
                </el-radio>
                <el-radio :label="2">
                  指令
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              v-if="form.distributeType === 1"
              :label="getLabel('text')"
              prop="content"
            >
              <el-input
                v-model="form.content"
                type="textarea"
                maxlength="140"
                :autosize="{ minRows: 3}"
                show-word-limit
                placeholder="请输入指令内容"
              />
            </el-form-item>
            <el-form-item
              v-if="form.distributeType === 2"
              :label="getLabel('paramConfigId')"
              prop="paramConfigId"
            >
              <el-select
                v-model="form.paramConfigId"
                placeholder="请选择指令"
                filterable
                @change="handleInstructChange"
              >
                <el-option
                  v-for="(item, index) in instructOptions"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <div v-if="form.distributeType === 2 && form.paramConfigId">
              <el-form-item
                v-for="(item, index) in paramList"
                :key="index"
                :label="item.name"
              >
                <el-input
                  v-if="item.componentType === 1 && item.paramType === 2"
                  v-model.number="dataForm[item.param]"
                  :placeholder="`请输入${item.name}`"
                />
                <el-input
                  v-if="item.componentType === 1 && item.paramType === 1"
                  v-model="dataForm[item.param]"
                  :placeholder="`请输入${item.name}`"
                />
                <el-radio-group
                  v-if="item.componentType === 2"
                  v-model="dataForm[item.param]"
                >
                  <el-radio
                    v-for="(element, index) in item.children"
                    :key="element.id"
                    :label="index + 1"
                  >
                    {{ element.name }}
                  </el-radio>
                </el-radio-group>
                <div v-if="item.componentType === 3">
                  <el-time-picker
                    v-model="paramStartTime"
                    value-format="HH:mm"
                    format="HH:mm"
                    placeholder="起始时间"
                    size="mini"
                    style="width: 30%"
                    @change="changeTime($event,item.children, 'startTime')"
                  />
                  -
                  <el-time-picker
                    v-model="paramEndTime"
                    value-format="HH:mm"
                    format="HH:mm"
                    placeholder="结束时间"
                    size="mini"
                    style="width: 30%"
                    @change="changeTime($event,item.children, 'endTime')"
                  />
                </div>
                <el-checkbox-group
                  v-if="item.componentType === 4"
                  v-model="checkboxData"
                >
                  <el-checkbox
                    v-for="(element) in item.children"
                    :key="element.id"
                    :label="element.param"
                    @change="handleCheckChange($event, item.children)"
                  >
                    {{ element.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </div>
          </div>
          <!-- <el-form-item
            :label="getLabel('commandList')"
            prop="commandList"
          >
            <div class="main">
              <div class="item_label main_size">
                模板
              </div>
              <div class="item_label main_size">
                参数
              </div>
              <div class="item_label">
                <span
                  class="add_item"
                  @click="addHandle"
                >添加</span>
              </div>
            </div>
            <a
              class="message-info"
              @click="addTemplateMessage"
            >
              模板管理
            </a>
            <div
              v-for="(item,index) in form.commandList"
              :key="index"
              class="main"
            >
              <div class="main_item main_size">
                <div class="main_item_select">
                  <el-select
                    v-model="form.commandList[index].commandTemplateId"
                    placeholder="请选择模板"
                    @change="handleTemplateChange(index)"
                  >
                    <el-option
                      v-for="subItem in templateOptions"
                      :key="subItem.id"
                      :label="subItem.terminalModel + '-' + subItem.templateName"
                      :value="subItem.id"
                    />
                  </el-select>
                </div>
              </div>
              <div class="main_item main_size main_parameter">
                <div>
                  <span
                    v-for="element in form.commandList[index].parameterOptions"
                    :key="element.id"
                  >
                    <el-input
                      v-if="element.isInput"
                      v-model="form.commandList[index].ipAddress"
                      placeholder="输入IP"
                      :class="['main_item_input', 'main_item_input' + index]"
                      clearable
                    />
                    <el-tag
                      v-else
                      class="main_item_tag"
                    >
                      {{ element.value }}
                    </el-tag>
                  </span>
                </div>
              </div>
              <div class="main_item">
                <div
                  class="reduce_item"
                >
                  <i
                    class="el-icon-remove-outline reduce_icon"
                    @click="reduceHandle(index)"
                  />
                </div>
              </div>
            </div>
          </el-form-item> -->
        </el-form>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="crud.cancelCU"
        >
          取消
        </el-button>
        <el-button
          :loading="crud.status.cu === 2"
          type="primary"
          size="small"
          @click="crud.submitCU"
        >
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import { instruction, getParameterEdit, queryParamConfig } from '@/api/center/instructTask';
import VehicleMultiSelectAllTree from '@/components/select/VehicleMultiSelectTree/VehicleMultiSelectAllTree';
import BtnMore from '@/components/BtnMore.vue';

const defaultForm = {
  taskName: '',
  executeMaxCount: null,
  startTime: null,
  endTime: null,
  // commandList: [{
  //   commandTemplateId: '',
  //   param: '',
  //   parameterOptions: [],
  //   ipAddress: ''
  // }],
  devicesInfo: [],
  interval: 1,
  day: 1,
  hour: 0,
  minute: 0,
  taskState: 1,
  vehicleState: ['1', '2'],
  distributeType: 1,
  paramConfigId: null,
  content: null,
  param: {}
};
export default {
  components: { VehicleMultiSelectAllTree, BtnMore },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      rules: {
        taskName: { required: true, message: '请输入任务名称', trigger: 'blur' },
        executeMaxCount: { required: true, message: '请输入任务最大执行次数', trigger: 'blur' },
        startTime: { required: true, message: '请选择开始日期时间', trigger: 'change' },
        endTime: { required: true, message: '请选择结束日期时间', trigger: 'change' },
        interval: { required: true, message: '请选择任务频次', trigger: 'change' },
        paramConfigId: { required: true, message: '请选择指令', trigger: 'change' },
        content: { required: true, message: '请输入指令内容', trigger: 'blur' }
      },
      templateOptions: [],
      formItem: {
        commandTemplateId: '',
        param: '',
        parameterOptions: [],
        ipAddress: ''
      },
      frequencyOptions: [
        {label: '每月', value: 1},
        {label: '每日', value: 2},
        {label: '每时', value: 3},
        {label: '每分', value: 4}
      ],
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() < Date.now() - 8.64e7;
        }
      },
      instructOptions: [],
      paramList: [],
      dataForm: {},
      paramStartTime: null,
      paramEndTime: null,
      checkboxData: [],
      taskDialog: true,
      instructDialog: true
    };
  },
  created () {
    // this.getInstructionData();
    this.getParamConfig();
  },
  mounted () {
    // 首次进入页面时使VehicleMultiSelectTreeInForm直接缓存, 否则首次点击打开弹窗会有卡顿
    this.$nextTick(() => {
      // this.$refs.dialogForm.rendered = true;
    });
  },
  methods: {
    handleCheckChange (val, list) {
      list.forEach((item) => {
        this.dataForm[item.param] = false;
      });
      this.checkboxData.forEach((item) => {
        this.dataForm[item] = true;
      });
    },
    // 时间类型字段
    changeTime (value, list, type) {
      if (type === 'startTime') {
        this.dataForm[list[0].param] = value ? value.split(':')[0] : '';
        this.dataForm[list[1].param] = value ? value.split(':')[1] : '';
      } else {
        this.dataForm[list[2].param] = value ? value.split(':')[0] : '';
        this.dataForm[list[3].param] = value ? value.split(':')[1] : '';
      }
      if (this.dataForm[list[0].param] > this.dataForm[list[2].param] || ((this.dataForm[list[0].param] === this.dataForm[list[2].param]) &&
        (this.dataForm[list[1].param] >= this.dataForm[list[3].param]))) {
        this.$message({
          message: '禁行时段结束时间不能晚于开始时间！',
          type: 'warning'
        });
        this.paramEndTime = '';
        this.dataForm[list[2].param] = '';
        this.dataForm[list[3].param] = '';
      }
    },
    getParamConfig () {
      queryParamConfig({parentId: 0}).then(res => {
        this.instructOptions = res.data.content;
      });
    },
    HandleTaskType () {
      this.form.content = null;
      this.dataForm = {};
      this.form.paramConfigId = null;
      this.$nextTick(() => {
        this.$refs.form.clearValidate(['content', 'paramConfigId']);
      });
    },
    handleInstructChange (val) {
      this.dataForm = {};
      this.paramStartTime = null;
      this.paramEndTime = null;
      this.checkboxData = [];
      let data = this.instructOptions.find((item) => item.id === val);
      this.paramList = data && data.children;
      this.paramList.forEach(element => {
        if (element.param) {
          this.$set(this.dataForm, element.param, '');
        }
      });
    },
    handleFrequencyChange (val) {
      this.form.day = 1;
      this.form.hour = 0;
      this.form.minute = 0;
    },
    checkedVehiclesChange (data) {
      this.form.devicesInfo = data.map(item => ({
        deviceType: item.deviceType,
        deviceId: item.id
      }));
    },
    handleTemplateChange (index) {
      getParameterEdit({templateId: this.form.commandList[index].commandTemplateId}).then(res => {
        this.$delete(this.form.commandList[index], 'parameterOptions'); // 防止视图不更新, 先删除再添加
        this.$set(this.form.commandList[index], 'parameterOptions', res.data.resData);
        this.form.commandList[index].parameterOptions.forEach(item => {
          if (item.isInput) {
            this.$nextTick(() => {
              let input = document.querySelector('.main_item_input' + index).querySelector('.el-input__inner');
              input.focus();
            });
          }
        });
      });
    },
    reduceHandle (index) {
      this.form.commandList.splice(index, 1);
    },
    addHandle () {
      let obj = JSON.parse(JSON.stringify(this.formItem));
      this.form.commandList.push(obj);
    },
    /**
     * 跳转模板管理
     */
    addTemplateMessage () {
      this.$router.push({
        path: '/system/templateManage',
        query: {
          isRouter: this.$route.fullPath
        }
      });
    },
    getInstructionData () {
      instruction({start: 0, count: 99999}).then(res => {
        this.templateOptions = res.data.content;
      });
    },
    [CRUD.HOOK.afterToAdd] (crud, form) {
      this.form.id = null;
      this.form.taskId = null;
    },
    // 提交之前做的操作
    [CRUD.HOOK.beforeSubmit] () {
      if (!this.form.devicesInfo?.length) {
        this.$message.error('请选择下发车辆');
        return false;
      }
      // let isWrite = false;
      // this.form.commandList.forEach((item) => {
      //   if (!item.commandTemplateId) {
      //     isWrite = true;
      //   }
      // });
      // if (isWrite) {
      //   this.$message.error('请选择指令模板');
      //   return false;
      // }
      // this.form.commandList.forEach((item) => {
      //   let str = '';
      //   item.parameterOptions.forEach((element) => {
      //     str += element.value + ',';
      //   });
      //   item.param = str.slice(0, str.length - 1);
      //   if (item.param.indexOf('IP') !== -1) {
      //     item.param = item.param.replace('IP', item.ipAddress);
      //   }
      // });
      if (this.form.interval !== 1) {
        this.form.day = 0;
      }
      this.form.param = this.dataForm;
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.$refs.form && this.$refs.form.clearValidate();
    },
    /** 编辑 - 之前 */
    [CRUD.HOOK.beforeToEdit] () {
      this.form.startTime = this.form.startTime * 1000;
      this.form.endTime = this.form.endTime * 1000;
      this.form.taskId = this.form.id;
      if (this.form.distributeType === 2) {
        let data = this.instructOptions.find((item) => item.id === this.form.paramConfigId);
        this.paramList = data && data.children;
        this.dataForm = JSON.parse(this.form.param);
        this.paramList.forEach((item) => {
          if (item.componentType === 4) {
            item.children.forEach((element) => {
              if (this.dataForm[element.param]) {
                this.checkboxData.push(element.param);
              }
            });
          }
          if (item.componentType === 3) {
            this.paramStartTime = this.dataForm[item.children[0].param] + ':' + this.dataForm[item.children[1].param];
            this.paramEndTime = this.dataForm[item.children[2].param] + ':' + this.dataForm[item.children[3].param];
          }
        });
      }
      // this.form.commandList.forEach((item) => {
      //   item.parameterOptions = item.params;
      //   let options = item.param.split(',');
      //   for (let index = 0; index < item.parameterOptions.length; index++) {
      //     const element = item.parameterOptions[index];
      //     if (element.value === 'IP') {
      //       // item.ipAddress = options[index];
      //       this.$set(item, 'ipAddress', options[index]);
      //       break;
      //     }
      //   }
      // });
      this.form.vehicleState = this.form.vehicleState.split(',');
      // 避免第一次进入弹窗时触发子组件的onRefresh方法
      setTimeout(() => {
        this.$refs.VehicleMultiSelectAllTree?.setDefaultCheckedKeys(this.form.devicesInfo);
      }, 0);
    },
    closed () {
      this.$nextTick(() => {
        this.$refs.VehicleMultiSelectAllTree.onClear();
      });
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('InstructTask', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('InstructTask', value);
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep.el-dialog__body {
  padding: 15px;
}
.notice-form {
    width: calc(100% - 300px);
  }
.history-file {
  display: inline;
  margin-right: 10px;
  &-download {
    text-decoration: underline;
    cursor: pointer;
    color: #409eff;
  }
  &-del {
    cursor: pointer;
  }
}

::v-deep.el-form-item__label{
  font-weight: 400;
}
.message-info{
  margin-left: 10px;
  color: @menuActiveText;
  position: absolute;
  right: 30px;
  top: 3px;
}
.notice-form{
  ::v-deep .el-select, ::v-deep .el-input{
    width: 300px;
  }
}
.main{
  display: flex;
  width: 80%;
  text-align: center;
}
.main_item{
  flex: 1;
  height: 45px;
  line-height: 45px;
  border: 1px solid #c1c9da;
}
.main_size{
  flex: 3 !important;
}
.item_label{
  flex: 1;
  height: 40px;
  line-height: 40px;
  background-color: #e1e5ee;
  border: 1px solid #c1c9da;
}
.main_item ::v-deep.el-select, .main_item ::v-deep.el-input{
  width: 100%;
}
.add_item{
  border: 1px solid #aebac5;
  padding: 3px 5px;
  background-color: #fff;
  margin: 5px;
  cursor: pointer;
}
.main_item_input{
  width: 100px !important;
  ::v-deep .el-input__inner{
    background-color: #ecf5ff;
    border-color: #d9ecff;
    display: inline-block;
    height: 32px;
    line-height: 30px;
    color: #409EFF;
    border-width: 1px;
    border-style: solid;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    white-space: nowrap;
  }
}
.main_item_tag{
  margin-left: 3px;
}
::v-deep .el-dialog__body{
  display: flex;
}
.main_parameter{
  overflow: auto;
}
.main_item_select{
  ::v-deep .el-input__inner{
    height: 45px;
  }
}
.date-select{
  width: 80px !important;
  ::v-deep .el-input{
    width: 80px;
  }
}
.data-text{
  margin: 0 4px;
}
.form-header{
  position: relative;
}
.divider-title{
  width: 90%;
  ::v-deep .el-divider__text{
    left: 0;
    padding-left: 60px;
  }
}
.divider-btn{
  position: absolute;
  right: 0;
  top: -15px;
}
.form-divider{
  margin-top: 40px;
}
.leftTop {
  width: 300px;
  height: 650px;
}
</style>
