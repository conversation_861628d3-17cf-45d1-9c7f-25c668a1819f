<template>
  <div class="instruction">
    <div class="instruction-list">
      <el-radio-group
        v-model="tabPosition"
        size="small"
        @change="radioChange"
      >
        <el-radio-button :label="getLabel('TextInstruction', 'uniName')">
          {{ getLabel('TextInstruction', 'uniName') }}
        </el-radio-button>
        <el-radio-button :label="getLabel('PhotoInstruction', 'uniName')">
          {{ getLabel('PhotoInstruction', 'uniName') }}
        </el-radio-button>
        <el-radio-button :label="getLabel('MonitorInstruction', 'uniName')">
          {{ getLabel('MonitorInstruction', 'uniName') }}
        </el-radio-button>
      </el-radio-group>
    </div>
    <div class="instruction-content">
      <div class="car-list media-terminal-tree-container">
        <InstructionMultiSelect
          v-show="tabPosition === getLabel('TextInstruction', 'uniName')"
          ref="InstructionMultiSelect"
          :is-show="tabPosition === getLabel('TextInstruction', 'uniName')"
          @checkedVehiclesChange="checkedVehiclesChange"
        />
        <VideoMultiSelectTree
          v-show="tabPosition !== getLabel('TextInstruction', 'uniName') && tabPosition !== getLabel('PhotoInstruction', 'uniName')"
          ref="InstructionSingleSelect"
          :is-show="tabPosition !== getLabel('TextInstruction', 'uniName') && tabPosition !== getLabel('PhotoInstruction', 'uniName')"
          :is-voice="true"
          :is-intercom="false"
          class="single-with-channel-tree"
          @selectedRowSingle="selectedRowSingle"
        />
        <VideoMultiSelectTree
          v-show="tabPosition === getLabel('PhotoInstruction', 'uniName')"
          ref="videoMultiSelectTree"
          :is-show="tabPosition === getLabel('PhotoInstruction', 'uniName')"
          :is-voice="true"
          :is-intercom="false"
          class="single-with-channel-tree"
          @selectedRowSingle="getSingleCarOriInfo"
        />
      </div>
      <div class="car-content">
        <el-divider content-position="left">
          {{ tabPosition }}
        </el-divider>
        <TextInstructionPage
          v-show="tabPosition === getLabel('TextInstruction', 'uniName')"
          ref="TextInstructionPage"
        />
        <PhotoInstructionPage
          v-show="tabPosition === getLabel('PhotoInstruction', 'uniName')"
          ref="PhotoInstructionPage"
        />
        <MonitorInstructionPage
          v-show="tabPosition === getLabel('MonitorInstruction', 'uniName')"
          ref="MonitorInstructionPage"
        />
      </div>
    </div>
  </div>
</template>

<script>
import InstructionMultiSelect from '@/components/select/VehicleMultiSelectTree/VehicleMultiSelectAllTree';
// import InstructionSingleSelect from '@/components/select/VehicleSingleSelectTree/VehicleSingleSelectTree';
import VideoMultiSelectTree from '@/components/select/VideoMultiSelect/VideoMultiSelectTreeNew';
import TextInstructionPage from './textInstruction/index.vue';
import PhotoInstructionPage from './photoInstruction/index.vue';
import MonitorInstructionPage from './monitorInstruction/index.vue';
import getLabel from '@/utils/getLabel';
import { rnssChannelTree, rnssTree } from '@/api/base/vehicle';
export default {
  name: 'SendCmd',
  components: {
    TextInstructionPage,
    PhotoInstructionPage,
    MonitorInstructionPage,
    InstructionMultiSelect,
    // InstructionSingleSelect,
    VideoMultiSelectTree
  },
  mixins: [],
  // 数据字典
  dicts: [],
  data () {
    return {
      tabPosition: getLabel('TextInstruction', 'uniName'),
      textInstructionList: []
    };
  },
  mounted () {
    // 处理车辆面板的跳转
    if (this.$route.query.from === 'photoInstruction') {
      this.tabPosition = getLabel('PhotoInstruction', 'uniName');
      // this.$refs.InstructionSingleSelect.setSelectedCar(this.$route.query.licencePlate);
    } else if (this.$route.query.from !== 'photoInstruction' && this.$route.query.id) {
      this.tabPosition = getLabel('TextInstruction', 'uniName');
      // this.$refs.InstructionMultiSelect.setSelectedVehicle(this.$route.query.licencePlate, 'textInstruction');
    }
  },
  methods: {
    rnssTree,
    rnssChannelTree,
    checkedVehiclesChange (data) {
      this.textInstructionList = data.map(item => ({
        targetName: item.name,
        deviceId: item.id,
        deviceType: item.deviceType
      }));
      this.$refs.TextInstructionPage.setTextCars(this.textInstructionList);
    },
    // 获取车辆信息（原数据带通道）
    getSingleCarOriInfo (data) {
      this.$refs.PhotoInstructionPage.getSingleCarOriInfo(data);
    },
    selectedRowSingle (data) {
      // if (this.tabPosition === getLabel('PhotoInstruction', 'uniName')) {
      //   this.$refs.PhotoInstructionPage.setPhotoCar(data);
      // }

      if (this.tabPosition === getLabel('MonitorInstruction', 'uniName')) {
        if (!data) return;
        this.$refs.MonitorInstructionPage.setPhotoCar(data);
      }
    },
    radioChange (val) {
      if(val !== '文字指令') {
        this.textInstructionList = [];
        this.$refs.TextInstructionPage.setTextCars([]);
      }
      this.$refs.InstructionSingleSelect.updateStyle();
      this.$refs.InstructionMultiSelect.clear();
      this.$refs.videoMultiSelectTree.updateStyle();
    },
    /**
     * 获取标签
     * @param {String} className 数据的类型
     * @param {String} value 字段名称
     * @return {String}
     */
    getLabel (className, value) {
      return getLabel(className, value);
    }
  }
};
</script>

<style lang="less" scoped>
@import '../../../assets/less/variables.less';
  .instruction{
    display: flex;
    flex-direction: column;
  }
  .instruction-list{
    padding: 5px;
    background-color: @xhBackgroundColor2;
    // margin: @xhSpacingBase;
    margin: 0 0 @xhSpacingBase 0;
    border-radius: @xhSpacingBase;
    box-shadow: @xh-button-shadow;
  }
  .instruction-content{
    height: 85vh;
    display: flex;
    flex-direction: row;
  }
  .car-list{
    width: 300px;
    background-color: @xhBackgroundColor2;
    // margin: @xhSpacingBase;
    margin: @xhSpacingBase @xhSpacingBase 0 0;
    border-radius: @xhSpacingBase;
    box-shadow: @xh-button-shadow;
  }
  .instruction-single-select, .single-with-channel-tree {
    // width: 100%;
  }
  .single-with-channel-tree {
    height: 100%;
  }
  .car-content{
    flex: 1;
    padding: 5px;
    background-color: @xhBackgroundColor2;
    margin: @xhSpacingBase;
    border-radius: @xhSpacingBase;
    box-shadow: @xh-button-shadow;
  }
</style>
