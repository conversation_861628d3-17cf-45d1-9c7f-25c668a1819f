<template>
  <div class="text-instruction-dialog">
    <el-form
      ref="form"
      :model="form"
      size="small"
      label-width="140px"
      :rules="rules"
    >
      <el-form-item
        :label="getLabel('message')"
        prop="message"
        @blur="getMessage()"
      >
        <MessageTemplateSingleSelectInForm
          v-model="form.message"
          :show-info="true"
        />
      </el-form-item>
      <el-form-item
        :label="getLabel('text')"
        prop="text"
      >
        <el-input
          v-model="form.text"
          :placeholder="getPlaceholder('text')"
          type="textarea"
          :rows="4"
          style="width: 370px;"
        />
      </el-form-item>
      <el-form-item
        :label="getLabel('urgent')"
        prop="urgent"
      >
        <el-radio-group v-model="form.urgent">
          <el-radio :label="1">
            是
          </el-radio>
          <el-radio :label="0">
            否
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        :label="getLabel('ttsRead')"
        prop="ttsRead"
      >
        <el-radio-group v-model="form.ttsRead">
          <el-radio :label="1">
            是
          </el-radio>
          <el-radio :label="0">
            否
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        :label="getLabel('display')"
        prop="display"
      >
        <el-radio-group v-model="form.display">
          <el-radio :label="1">
            是
          </el-radio>
          <el-radio :label="0">
            否
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- <el-form-item
        :label="getLabel('infoFlag')"
        prop="infoFlag"
      >
        <xh-select
          v-model="infoType"
          size="small"
          filterable
          clearable
          style="width: 370px;"
          :placeholder="getPlaceholder('infoFlag')"
        >
          <el-option
            v-for="(item, index) in infoTypeOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </xh-select>
      </el-form-item>
      <el-form-item
        :label="getLabel('infoType')"
        prop="infoType"
      >
        <xh-select
          v-model="infoFlag"
          size="small"
          filterable
          clearable
          style="width: 370px;"
          :placeholder="getPlaceholder('infoType')"
        >
          <el-option
            v-for="(item, index) in infoFlagOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </xh-select>
      </el-form-item> -->
      <span class="text-footer">
        <el-button
          size="small"
          type="primary"
          @click="sendTextMsg"
        >
          发布指令
        </el-button>
      </span>
    </el-form>
  </div>
</template>
<script>
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import { sendTextMsg } from '@/api/center/instruction.js';
import MessageTemplateSingleSelectInForm
  from '@/components/select/MessageTemplateSingleSelectInForm/MessageTemplateSingleSelectInForm';
import { validateRules } from '@/utils/validate';

export default {
  name: 'TextInstruction',
  components: {
    MessageTemplateSingleSelectInForm
  },
  data() {
    return {
      form: {
        message: '',
        text: '',
        ttsRead: 0,
        urgent: 0,
        display: 0
      },
      infoType: null,
      infoFlag: null,
      showOn: 4,
      broadcastOn: 8,
      infoTypeOptions: [
        {
          value: 1,
          label: '通知'
        },
        {
          value: 2,
          label: '服务'
        }
      ],
      infoFlagOptions: [
        {
          value: 1,
          label: '紧急'
        },
        {
          value: 2,
          label: '服务'
        },
        {
          value: 3,
          label: '通知'
        }
      ],
      selectedCars: [],
      rules: {
        text: {
          required: true,
          validator: validateRules('TextInstruction', 'text'),
          trigger: 'change'
        }
      }

    };
  },
  watch: {
    'form.message': function (val) {
      if (val) {
        this.form.text = val;
      }
    }
  },
  methods: {
    setTextCars(val) {
      this.selectedCars = val;
    },
    sendTextMsg() {
      let selectedCars = this.selectedCars;
      let ids = selectedCars.map(item => {
        return {
          device_type: item.deviceType,
          device_id: BigInt(item.deviceId)
        };
      });
      // let licencePlates = selectedCars.map(item => {
      //   return item.licencePlate;
      // });
      if (ids.length < 1) {
        this.$message({
          type: 'error',
          message: '请选择车辆！'
        });
        return;
      }
      console.log(selectedCars);
      let parme = {
        ttsRead: this.form.ttsRead,
        urgent: this.form.urgent,
        display: this.form.display,
        text: this.form.text,
        alarm: ids
      };
      this.$refs['form'].validate((valid) => {
        if (valid) {
          sendTextMsg(parme).then(res => {
            this.$message({
              type: 'success',
              message: '操作成功'
            });
          });
        }
      });
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('TextInstruction', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder(value) {
      return getPlaceholder('TextInstruction', value);
    }
  }
};
</script>
<style lang="less" scoped>
.text-instruction-dialog {
  display: flex;
  justify-content: center;
  align-items: center;
}

.text-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 8px;
}
</style>
