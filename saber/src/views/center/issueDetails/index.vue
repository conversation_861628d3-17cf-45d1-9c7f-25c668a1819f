<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :permission="permission"
          :head-config="headConfig"
          label-width="80px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          style="width: 100%; height: calc(100% - 47px);"
          :cell-style="{'text-align':'center'}"
        >
          <!-- <el-table-column
        type="selection"
        width="50"
      /> -->
          <el-table-column
            prop="licencePlate"
            :label="getLabel('licencePlate')"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            prop="licenceColor"
            :label="getLabel('licenceColor')"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>{{ EnumerationTypeHandling('licenceColor',scope.row.licenceColor) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="deptName"
            :label="getLabel('deptName')"
            show-overflow-tooltip
            min-width="180"
            :resizable="false"
          />
          <!-- <el-table-column
        prop="name"
        :label="getLabel('name')"
        show-overflow-tooltip
      /> -->
          <el-table-column
            prop="templateName"
            :label="getLabel('templateName')"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            prop="terminalModel"
            :label="getLabel('terminalModel')"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            prop="content"
            :label="getLabel('content')"
            show-overflow-tooltip
            min-width="180"
            :resizable="false"
          />
          <el-table-column
            prop="param"
            :label="getLabel('param')"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            prop="executeTime"
            :label="getLabel('executeTime')"
            width="180"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">{{ scope.row.executeTime ? parseTimes1000(scope.row.executeTime) : '未执行' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="executeCount"
            :label="getLabel('executeCount')"
            show-overflow-tooltip
            min-width="100"
            :resizable="false"
          />
          <el-table-column
            prop="executeResult"
            :label="getLabel('executeResult')"
            show-overflow-tooltip
            min-width="100"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>{{ parseState(scope.row.executeResult) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="commandExecuteResult"
            :label="getLabel('commandExecuteResult')"
            show-overflow-tooltip
            min-width="150"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.commandExecuteResult === 1 ? '执行成功' : scope.row.commandExecuteResult === 2 ? '执行失败' : scope.row.commandExecuteResult === 3 ? '部分成功' : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="commandExecuteResultDetail"
            :label="getLabel('commandExecuteResultDetail')"
            show-overflow-tooltip
            min-width="150"
            :resizable="false"
          />
          <!-- <el-table-column
        prop="createTime"
        :label="getLabel('createTime')"
        width="180"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span class="table-date-td">{{ parseTimes1000(scope.row.createTime) }}</span>
        </template>
      </el-table-column> -->
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
    </div>
  </basic-container>
</template>

<script>
import crudIssueDetails from '@/api/center/issueDetails';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';
import HeadCommon from '@/components/formHead/headCommon.vue';
import getLabel from '@/utils/getLabel';
import { queryparamconfigtype } from '@/api/center/templateIssue';

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('IssueDetails', 'uniName'),
  crudMethod: { ...crudIssueDetails },
  queryOnPresenterCreated: false
});

export default {
  name: 'IssueDetails',
  components: {
    HeadCommon,
    crudOperation,
    pagination
  },
  mixins: [presenter(crud)],
  // 数据字典
  dicts: ['licenceColor'],
  data () {
    return {
      permission: {
        add: ['admin', 'IssueDetails:add'],
        edit: ['admin', 'IssueDetails:edit'],
        del: ['admin', 'IssueDetails:del']
      },
      headConfig: {
        item: {
          1: {
            name: '车牌号码',
            type: 'input',
            value: 'licencePlate'
          },
          2: {
            name: '车组',
            type: 'extra',
            value: 'deptId'
          },
          3: {
            name: '下发结果',
            type: 'select',
            value: 'executeResult',
            options: [
              {
                value: 1,
                label: '下发成功'
              },
              {
                value: 2,
                label: '下发失败'
              },
              {
                value: 3,
                label: '未执行'
              }
            ]
          },
          4: {
            name: '执行结果',
            type: 'select',
            value: 'commandExecuteResult',
            options: [
              {
                value: 1,
                label: '执行成功'
              },
              {
                value: 2,
                label: '执行失败'
              },
              {
                value: 3,
                label: '部分成功'
              }
            ]
          },
          5: {
            name: '指令类型',
            type: 'select',
            value: 'commandType',
            options: []
          },
          6: {
            name: '车牌颜色',
            type: 'select',
            value: 'licenceColor',
            dictOptions: 'licenceColor'
          },
        },
        button: {}
      },
    };
  },
  // 枚举类型处理
  computed: {
    EnumerationTypeHandling () {
      return (dictName, val) => {
        if (this.dict.dict[dictName] && this.dict.dict[dictName][val]) {
          return this.dict.dict[dictName][val].label;
        }
      };
    }
  },
  mounted () {
    this.getParameterData();
    if (this.$route.query.id) {
      this.$set(this.crud.query, 'commandTaskId', Number(this.$route.query.id));
      this.crud.toQuery();
    }
  },
  methods: {
    async getParameterData () {
      const { code, data } = await queryparamconfigtype();
      if (code === 200 && data) {
        this.headConfig.item[5].options = data.content;
      }
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('IssueDetails', value);
    },
    parseState (value) {
      return value === 1 ? '下发成功' : value === 2 ? '下发失败' : '未执行';
    },
    parseTimes (time) {
      return this.$moment(time).format('YYYY-MM-DD HH:mm:ss');
    },
    parseTimes1000 (time) {
      if (time) {
        return this.$moment(time * 1000).format('YYYY-MM-DD HH:mm:ss');
      }
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
 .platformRecord{
  margin: 10px;
  border: 1px solid #e5e7e9;
  box-shadow: 1px 1px 5px #88888850;
  background: #fff;
 }
 ::v-deep.el-table th{
  text-align: center;
}
::v-deep.el-table td{
  text-align: center;
}
.app-container ::v-deep.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
  background-color: #fcf0c1;
}
</style>
