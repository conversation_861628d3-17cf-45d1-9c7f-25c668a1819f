<template>
  <div class="app-container reportform-online" style="padding: 0;">
    <basic-container style="padding-bottom: 6px;">
      <div class="xh-container">
        <!--工具栏-->
        <div class="head-container">
          <eHeader :permission="permission" />
        </div>
        <div class="xh-crud-table-container">
          <crudOperation :permission="permission" :show-edit="false" />
          <!--表格渲染-->
          <el-table ref="table" v-loading="crud.loading"
            :header-cell-style="{background:'#e1e5ee','text-align':'center'}" :data="crud.data"
            :max-height="tableMaxHeight" style="width: 100%; height: calc(100% - 47px);"
            :cell-style="{'text-align':'center'}" @selection-change="crud.selectionChangeHandler">
            <el-table-column :resizable="false" v-permission="['admin', 'platformannouncement:edit']" label="操作" width="120" align="center"
              fixed="right">
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :edit-name="editName" icon="el-icon-view" />
              </template>
            </el-table-column>
            <el-table-column :resizable="false" v-if="columns.visible('timeFormat')" prop="updateTime" :label="getLabel('timeFormat')">
              <template slot-scope="scope">
                <span class="table-date-td">{{ parseTime(scope.row.updateTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column :resizable="false" v-if="columns.visible('title')" prop="title" :label="getLabel('title')" />
            <el-table-column :resizable="false"  v-if="columns.visible('content')" prop="content" :label="getLabel('content')"
              show-overflow-tooltip />
            <el-table-column :resizable="false" v-if="columns.visible('state')" prop="state" :label="getLabel('state')" />
            <el-empty slot="empty" :image="require('@/assets/images/nodata.png')" />
          </el-table>
        </div>
        <!--分页组件-->
        <pagination />
        <!--表单渲染-->
        <eForm />
      </div>
    </basic-container>
  </div>
</template>

<script>
import crudAnnouncement from '@/api/center/announcement.js';
import eHeader from './module/header';
import eForm from './module/form';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';
import udOperation from '@/components/Crud/UD.operation';
import getLabel from '@/utils/getLabel';
// import queryToObject from '@/utils/core/queryToObject';
// crud交由presenter持有
const crud = CRUD({
  title: getLabel('receiveAnnouncement', 'uniName'),
  crudMethod: { ...crudAnnouncement },
  editTitle: '查看'
});

export default {
  name: 'ReceiveAnnouncement',
  components: {
    eHeader,
    eForm,
    crudOperation,
    udOperation,
    pagination
  },
  mixins: [presenter(crud)],
  data () {
    return {
      permission: {
        add: ['admin', 'receiveAnnouncement:add'],
        edit: ['admin', 'platformannouncement:edit'],
        del: ['admin', 'receiveAnnouncement:del']
      },
      editName: '详情'
    };
  },
  methods: {
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('receiveAnnouncement', value);
    },
    [CRUD.HOOK.beforeRefresh] (hook, item) {
      crud.query.receive = 1;
    },
    [CRUD.HOOK.beforeExport] (hook, item) {
      crud.query.receive = 1;
    },
    [CRUD.HOOK.afterRefresh] () {
      for (let i = 0; i < crud.data.length; i++) {
        crud.data[i].state = crud.data[i].state ? '已读' : '未读';
      }
    },
    /**
     * 编辑beforeToEdit
     */
    [CRUD.HOOK.beforeToEdit] (hook, item) {
      let parme = {
        id: item.id
      };
      crudAnnouncement.detail(parme).then(res => {
        setTimeout(() => {
          crud.toQuery();
        }, 200);
      }).catch(msg => {
        console.log(msg);
      });
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
 .Announcement{
  margin: 10px;
  border: 1px solid #e5e7e9;
  box-shadow: 1px 1px 5px #88888850;
  background: #fff;
 }
 ::v-deep.el-table th{
  text-align: center;
}
::v-deep.el-table td{
  text-align: center;
}
.app-container ::v-deep.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
  background-color: #fcf0c1;
}
</style>
