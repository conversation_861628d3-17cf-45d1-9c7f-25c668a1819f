<template>
  <el-dialog
    append-to-body
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="crud.status.title"
    :draggable="true"
    width="600px"
    custom-class="new_notice_dialog"
  >
    <div class="new_notice_dialog_body">
      <!-- <el-tree
        ref="newNoticeTree"
        :data="list"
        show-checkbox
        node-key="id"
        empty-text=" "
        class="new_notice_dialog_tree"
      /> -->
      <div class="new_notice_dialog_form">
        <el-form
          ref="form"
          label-width="90px"
          :model="form"
          :rules="rules"
        >
          <el-form-item
            :label="getLabel('title')"
            prop="title"
            :rules="rules.title"
          >
            <el-input
              v-model="form.title"
              type="textarea"
              maxlength="160"
              show-word-limit
              :disabled="true"
              :autosize="{minRows: 4, maxRows: 6}"
              :placeholder="getPlaceholder('title')"
            />
          </el-form-item>
          <el-form-item
            :label="getLabel('content')"
            :rules="rules.content"
            prop="content"
          >
            <el-input
              v-model="form.content"
              type="textarea"
              maxlength="1600"
              show-word-limit
              :disabled="true"
              :autosize="{minRows: 8, maxRows: 10}"
              :placeholder="getPlaceholder('content')"
            />
          </el-form-item>
          <!-- <el-form-item label="公告附件">
            <FileUpload
              ref="fileUpload"
              :current-history-files-length="filesArray.length || 0"
            />
          </el-form-item> -->
          <div v-show="isShowDownloadFile">
            <el-form-item
              v-for="(item, index) in filesArray"
              :key="index"
              :label="'附件'+(index+1)"
            >
              <div class="history_files">
                <span
                  class="download_file_name"
                  @click="downloadFile(item.fileUrl)"
                >{{ item.fileName }}</span>
                <!-- <span
                  class="del_history_file"
                  @click="delHistoryFile(index)"
                >
                  X
                </span> -->
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>
    <span
      v-show="false"
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取 消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确 定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import { getEnabledDepts, getDepts } from '@/api/base/dept';
// import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
// import FileUpload from '@/components/upload/FileUpload/FileUpload';

const defaultForm = {
  deptList: '',
  title: '',
  content: '',
  fileUrl: ''
};
export default {
  // components: { FileUpload },
  mixins: [form(defaultForm)],
  props: {
  },
  data () {
    return {
      depts: [],
      list: [],
      filesArray: [],
      rules: {
        title: { required: true, message: '请输入公告标题', trigger: 'blur' },
        content: { required: true, message: '请输入公告内容', trigger: 'blur' }
      }
    };
  },
  computed: {
    isShowDownloadFile () {
      return this.filesArray.length > 0 && this.filesArray[0];
    }
  },
  methods: {
    [CRUD.HOOK.beforeToCU] () {
      // getEnabledDepts().then(res => {
      //   this.list = res.data.content;
      // });
      if (this.form.fileUrl) {
        let detailsInfoFileArray = [];
        let fileArray = this.form.fileUrl.split(',');
        for (let i = 0; i < fileArray.length; i++) {
          detailsInfoFileArray.push({
            fileUrl: fileArray[i],
            fileName: fileArray[i].split('/')[fileArray[i].split('/').length - 1]
          });
        }
        this.filesArray = detailsInfoFileArray;
      }
    },
    [CRUD.HOOK.afterValidateCU] () {
      if (!this.form.title) {
        this.$notify({
          title: `所属${getLabel('receiveAnnouncement', 'title')}不能为空`,
          type: 'warning'
        });
        return false;
      }
      if (!this.form.content) {
        this.$notify({
          title: `所属${getLabel('receiveAnnouncement', 'content')}不能为空`,
          type: 'warning'
        });
        return false;
      }
      return true;
    },
    [CRUD.HOOK.beforeSubmit] () {
      // 处理公告
      // 处理添加了一个[新疆]的根节点的处理(id为undefined,所以需要处理)
      var departmentList = this.getNewNoticeTreeCheckedNodes();
      if (departmentList[0] === undefined) {
        departmentList.splice(0, 1);
      }
      this.form.deptList = departmentList;

      // 处理附件
      let urlStr = '';
      let newFilesArray = this.filesArray;
      let historyFilesArray = this.$refs.fileUpload.getFilesArray();
      let concatFilesArray = newFilesArray.concat(historyFilesArray);
      for (let i = 0; i < concatFilesArray.length; i++) {
        if (i > 0) {
          urlStr += ',';
        }
        urlStr += concatFilesArray[i].fileUrl;
      }
      this.form.fileUrl = urlStr;
    },
    getNewNoticeTreeCheckedNodes () {
      var arr = this.$refs.newNoticeTree.getCheckedNodes();
      var returnArr = [];
      for (let i = 0; i < arr.length; i++) {
        returnArr.push(arr[i].id);
      }
      return returnArr;
    },
    delHistoryFile (index) {
      this.filesArray.splice(index, 1);
    },
    downloadFile (url) {
      this.$download(url, 'path');
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('receiveAnnouncement', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('receiveAnnouncement', value);
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  .new_notice_dialog {
    min-width: 300px;
    /* .new_notice_dialog_body {
      display: flex;
      .new_notice_dialog_tree {
        min-width: 250px;
        max-height: 500px;
        overflow-y: auto;
        > span {
          font-size: 18px;
          color: black;
        }
        .el-tree{
          background: transparent;
          display: inline-block !important;
        }
      }
      .new_notice_dialog_form {
        width: 400px;
      }
    } */
    .el-dialog__footer {
      text-align: center;
    }
  }
  .history_files {
    display: flex;
    justify-content: space-between;
    .del_history_file {
      cursor: pointer;
    }
  }
  .download_file_name {
    text-decoration: underline;
    cursor: pointer;
    color: blue;
  }
  .el-textarea ::v-deep.el-input__count {
    opacity: 0.5;
  }
</style>
