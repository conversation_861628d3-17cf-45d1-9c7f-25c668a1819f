<template>
  <div
    v-if="crud.props.searchToggle"
    class="xh-custom-header"
  >
    <!-- <DeptSingleSelect
      v-model="query.deptId"
      class="filter-item"
      @keyup.enter.native="crud.toQuery"
    /> -->
    <!-- <el-date-picker
      v-model="query.startTime"
      size="small"
      type="datetime"
      class="filter-item"
      placeholder="选择开始日期时间"
      @blur="judge()"
    />
    <el-date-picker
      v-model="query.endTime"
      size="small"
      type="datetime"
      class="filter-item"
      placeholder="选择结束日期时间"
      @blur="judge()"
    /> -->
    <div class="xh-page-searchItem-content-auto">
      <el-input
        v-model="query.title"
        clearable
        size="small"
        :placeholder="getPlaceholder('title')"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="crud.toQuery"
      />
    </div>
    <div class="xh-page-searchItem-content-auto">
      <el-input
        v-model="query.content"
        clearable
        size="small"
        :placeholder="getPlaceholder('content')"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="crud.toQuery"
      />
    </div>
    <!-- <xh-select
      v-model="query.receive"
      clearable
      size="small"
      :placeholder="getPlaceholder('receive')"
      class="filter-item"
      @change="crud.toQuery"
    >
      <el-option
        v-for="item in typeOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </xh-select> -->
    <rrOperation
      :crud="crud"
    />
  </div>
</template>

<script>
import { header } from '@/components/Crud/crud';
import rrOperation from '@/components/Crud/RR.operation';
import getPlaceholder from '@/utils/getPlaceholder';
// import DeptSingleSelect from '@/components/select/DeptSingleSelect/DeptSingleSelect';
export default {
  components: { rrOperation },
  mixins: [header()],
  props: {
    permission: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      typeOptions: [
        {
          value: 0,
          label: '发布公告'
        },
        {
          value: 1,
          label: '接收公告'
        }
      ]
    };
  },
  created () {
    this.query.receive = 1;
  },
  methods: {
    // 判断开始时间小于结束时间
    judge () {
      if (this.query.startTime) {
        if (
          this.$moment(this.query.startTime).valueOf() >
          this.$moment(this.query.endTime).valueOf()
        ) {
          this.$message({
            type: 'warning',
            message: '开始日期时间要小于结束日期时间'
          });
        }
      }
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('Announcement', value);
    }
  }
};
</script>
