<template>
  <div>
    <el-dialog
      ref="dialogForm"
      v-dialog-drag
      :append-to-body="true"
      :close-on-click-modal="false"
      :before-close="crud.cancelCU"
      :visible="crud.status.cu > 0"
      :title="crud.status.title"
      width="1100px"
      @closed="closed"
    >
      <div class="leftTop">
        <VehicleMultiSelectAllTree
          ref="VehicleMultiSelectAllTree"
          :is-show="true"
          @checkedVehiclesChange="checkedVehiclesChange"
        />
      </div>
      <!-- 表单 -->
      <div class="notice-form">
        <el-form
          ref="form"
          label-width="140px"
          :model="form"
          :rules="rules"
          size="small"
        >
          <div class="form-header">
            <el-divider
              greetings-position="left"
              class="divider-title"
            >
              任务设置
            </el-divider>
            <BtnMore
              class="divider-btn"
              :is-down="taskDialog"
              @click="taskDialog = !taskDialog"
            />
          </div>
          <div v-show="taskDialog">
            <el-form-item
              :label="getLabel('taskName')"
              prop="taskName"
            >
              <el-input
                v-model="form.taskName"
                :placeholder="getPlaceholder('taskName')"
              />
            </el-form-item>
            <el-form-item
              :label="getLabel('taskState')"
              prop="taskState"
            >
              <el-radio-group v-model="form.taskState">
                <el-radio :label="1">
                  开启
                </el-radio>
                <el-radio :label="2">
                  关闭
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div>
            <el-form-item
              label="问候语内容"
              prop="greetings"
              required
            >
              <el-input
                v-model="form.greetings"
                type="textarea"
                maxlength="140"
                :autosize="{ minRows: 3}"
                show-word-limit
                placeholder="请输入问候语内容"
              />
            </el-form-item>
            <el-form-item
              label="天气预报"
              prop="weatherForecast"
            >
              <el-radio-group v-model="form.weatherForecast">
                <el-radio :label="1">开启</el-radio>
                <el-radio :label="2">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="crud.cancelCU"
        >
          取消
        </el-button>
        <el-button
          :loading="crud.status.cu === 2"
          type="primary"
          size="small"
          @click="crud.submitCU"
        >
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import VehicleMultiSelectAllTree from '@/components/select/VehicleMultiSelectTree/VehicleMultiSelectAllTree';
import BtnMore from '@/components/BtnMore.vue';

const defaultForm = {
  taskName: '',
  taskState: 1,
  weatherForecast: 1,
  taskId: '',
  id: '',
  greetings: '',
  devicesInfo: []
};
export default {
  components: {
    VehicleMultiSelectAllTree,
    BtnMore
  },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      rules: {
        taskName: {
          required: true,
          message: '请输入任务名称',
          trigger: 'blur'
        },
        greetings: {
          required: true,
          message: '请输入问候语内容',
          trigger: 'blur'
        }
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        }
      },
      instructOptions: [],
      paramList: [],
      dataForm: {},
      paramStartTime: null,
      paramEndTime: null,
      checkboxData: [],
      taskDialog: true,
      instructDialog: true
    };
  },
  created() {
  },
  mounted() {
  },
  activated() {
  },
  methods: {
    checkedVehiclesChange(data) {
      this.form.devicesInfo = data.map(item => ({
        deviceType: item.deviceType,
        deviceId: item.id
      }));
    },
    [CRUD.HOOK.afterToAdd](crud, form) {
      this.form.id = null;
      this.form.taskId = null;
    },
    // 提交之前做的操作
    [CRUD.HOOK.beforeSubmit]() {
      if (!this.form.devicesInfo || this.form.devicesInfo.length === 0) {
        this.$message.error('请选择下发终端');
        return false;
      }
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU]() {
      this.$refs.form && this.$refs.form.clearValidate();
    },
    /** 编辑 - 之前 */
    [CRUD.HOOK.beforeToEdit]() {
      this.form.taskId = this.form.id;
      // 避免第一次进入弹窗时触发子组件的onRefresh方法
      setTimeout(() => {
        this.$refs.VehicleMultiSelectAllTree?.setDefaultCheckedKeys(this.form.devicesInfo);
      }, 0);
    },
    closed() {
      this.$nextTick(() => {
        this.$refs.VehicleMultiSelectAllTree.onClear();
      });
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('TemplateIssue', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder(value) {
      return getPlaceholder('TemplateIssue', value);
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .el-dialog__body {
  padding: 15px;
}

.notice-form {
  width: calc(100% - 300px);
}

.history-file {
  display: inline;
  margin-right: 10px;

  &-download {
    text-decoration: underline;
    cursor: pointer;
    color: #409eff;
  }

  &-del {
    cursor: pointer;
  }
}

::v-deep .el-form-item__label {
  font-weight: 400;
}

.message-info {
  margin-left: 10px;
  color: @menuActiveText;
  position: absolute;
  right: 30px;
  top: 3px;
}

.notice-form {
  ::v-deep .el-select, ::v-deep .el-input {
    width: 300px;
  }
}

.main {
  display: flex;
  width: 85%;
  text-align: center;
}

.main_item {
  flex: 1;
  height: 45px;
  line-height: 45px;
  border: 1px solid #c1c9da;
}

.main_size {
  flex: 2 !important;
}

.item_label {
  flex: 1;
  height: 40px;
  line-height: 40px;
  background-color: #e1e5ee;
  border: 1px solid #c1c9da;
}

.main_item ::v-deep .el-select, .main_item ::v-deep .el-input {
  width: 100%;
}

.add_item {
  border: 1px solid #aebac5;
  padding: 3px 5px;
  background-color: #ffffff;
  margin: 5px;
  cursor: pointer;
}

.main_item_input {
  width: 140px !important;

  ::v-deep .el-input__inner {
    background-color: #ecf5ff;
    border-color: #d9ecff;
    display: inline-block;
    height: 32px;
    line-height: 30px;
    color: #409eff;
    border-width: 1px;
    border-style: solid;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    white-space: nowrap;
  }
}

.main_item_tag {
  margin-left: 3px;
}

::v-deep .el-dialog__body {
  display: flex;
}

.main_parameter {
  flex: 3 !important;
  overflow: auto;
}

.main_item_select {
  ::v-deep .el-input__inner {
    height: 45px;
  }
}

.date-select {
  width: 80px !important;

  ::v-deep .el-input {
    width: 80px;
  }
}

.data-text {
  margin: 0 4px;
}

.form-header {
  position: relative;
}

.divider-title {
  width: 90%;

  ::v-deep .el-divider__text {
    left: 0;
    padding-left: 60px;
  }
}

.divider-btn {
  position: absolute;
  right: 0;
  top: -15px;
}

.form-divider {
  margin-top: 40px !important;
}

.leftTop {
  width: 300px;
  height: 650px;
}
</style>
