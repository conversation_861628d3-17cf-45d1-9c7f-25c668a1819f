<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="110px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <!-- 升级任务名称 -->
          <el-table-column
            v-if="columns.visible('taskName')"
            :label="getLabel('taskName')"
            prop="taskName"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 终端序列号 -->
          <el-table-column
            v-if="columns.visible('deviceUid')"
            :label="getLabel('deviceUid')"
            prop="deviceUid"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 升级包类型 -->
          <el-table-column
            v-if="columns.visible('otaPackageCategory')"
            :label="getLabel('otaPackageCategory')"
            prop="otaPackageCategory"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>{{ getEnumDictLabel('upgradeCategory', scope.row.otaPackageCategory) }}</span>
            </template>
          </el-table-column>
          <!-- 升级包名称 -->
          <el-table-column
            v-if="columns.visible('otaPackageName')"
            :label="getLabel('otaPackageName')"
            prop="otaPackageName"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 升级前版本 -->
          <el-table-column
            v-if="columns.visible('oldVersion')"
            :label="getLabel('oldVersion')"
            prop="oldVersion"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 升级后版本 -->
          <el-table-column
            v-if="columns.visible('newVersion')"
            :label="getLabel('newVersion')"
            prop="newVersion"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 创建时间 -->
          <el-table-column
            v-if="columns.visible('createTime')"
            :label="getLabel('createTime')"
            prop="createTime"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <!-- 升级时间 -->
          <el-table-column
            v-if="columns.visible('upgradeTime')"
            :label="getLabel('upgradeTime')"
            prop="upgradeTime"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">{{ parseTime(scope.row.upgradeTime) }}</span>
            </template>
          </el-table-column>
          <!-- 终端升级状态 -->
          <el-table-column
            v-if="columns.visible('state')"
            :label="getLabel('state')"
            prop="state"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.state === 1 ? '已完成' : '-' }}</span>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
    </div>
  </basic-container>
</template>

<script>
import crudUpgradePatch from '@/api/center/upgradeLog';
import CRUD, { presenter, header } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import HeadCommon from '@/components/formHead/headCommon.vue';

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('UpgradeLog', 'uniName'), // 升级记录
  crudMethod: { ...crudUpgradePatch }
});

export default {
  name: 'UpgradeLog',
  components: { crudOperation, pagination, HeadCommon },
  mixins: [presenter(crud), header()],
  // 数据字典
  dicts: [
    'upgradeCategory'
  ],
  data () {
    return {
      permission: {
        add: ['admin', 'upgradeLog:add'],
        edit: ['admin', 'upgradeLog:edit'],
        del: ['admin', 'upgradeLog:del'],
        view: ['admin', 'upgradeLog:view']
      },
      headConfig: {
        item: {
          1: {
            name: '升级任务名称',
            type: 'input',
            value: 'taskName',
          },
          2: {
            name: '开始日期',
            type: 'datetime',
            value: 'startTime',
            defaultFn: '7DS'
          },
          3: {
            name: '结束日期',
            type: 'datetime',
            value: 'endTime',
            defaultFn: 'toDE'
          },
          4: {
            name: '终端序列号',
            type: 'input',
            value: 'deviceUid'
          },
          5: {
            name: '升级包名称',
            type: 'input',
            value: 'otaPackageName',
          }
        },
        button: {
        }
      },
      isDetail: false
    };
  },
  methods: {
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('UpgradeLog', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('UpgradeLog', value);
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    }
  }
};
</script>

<style lang="less" scoped>

</style>
