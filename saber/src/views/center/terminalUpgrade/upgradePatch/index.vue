<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="110px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            width="190"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :disabledDle="scope.row.immutable === 1"
                :disabledEdit="scope.row.immutable === 1"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetail(scope.row)"
                  >
                    详情
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <!-- MD5校验码 -->
          <el-table-column
            v-if="columns.visible('md5Hash')"
            :label="getLabel('md5Hash')"
            prop="md5Hash"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 文件名 -->
          <el-table-column
            v-if="columns.visible('name')"
            :label="getLabel('name')"
            prop="name"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 软件类型 -->
          <el-table-column
            v-if="columns.visible('category')"
            :label="getLabel('category')"
            prop="category"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>{{ getEnumDictLabel('upgradeCategory', scope.row.category) }}</span>
            </template>
          </el-table-column>
          <!-- 版本名称 -->
          <el-table-column
            v-if="columns.visible('version')"
            :label="getLabel('version')"
            prop="version"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 文件大小 -->
          <el-table-column
            v-if="columns.visible('size')"
            :label="getLabel('size')"
            prop="size"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 终端型号 -->
          <el-table-column
            v-if="columns.visible('deviceModel')"
            :label="getLabel('deviceModel')"
            prop="deviceModel"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 制造商编号 -->
          <el-table-column
            v-if="columns.visible('vendor')"
            :label="getLabel('vendor')"
            prop="vendor"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 上传用户 -->
          <el-table-column
            v-if="columns.visible('uploader')"
            :label="getLabel('uploader')"
            prop="uploader"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 上传时间 -->
          <el-table-column
            v-if="columns.visible('uploadTime')"
            :label="getLabel('uploadTime')"
            prop="uploadTime"
            width="180"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">{{ parseTime(scope.row.uploadTime) }}</span>
            </template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column
            v-if="columns.visible('remark')"
            :label="getLabel('remark')"
            prop="remark"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
      <!--表单渲染-->
      <eForm
        :dict="dict"
        :is-detail.sync="isDetail"
      />
    </div>
  </basic-container>
</template>

<script>
import crudUpgradePatch from '@/api/center/upgradePatch';
import eForm from './module/form';
import CRUD, { presenter, header } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import pagination from '@/components/Crud/Pagination';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import HeadCommon from '@/components/formHead/headCommon.vue';

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('UpgradePatch', 'uniName'), // 升级包
  crudMethod: { ...crudUpgradePatch },
  queryOnPresenterCreated: false
});

export default {
  name: 'UpgradePatch',
  components: { eForm, crudOperation, udOperation, pagination, HeadCommon },
  mixins: [presenter(crud), header()],
  // 数据字典
  dicts: [
    'upgradeCategory',
    'bdmDeviceModel'
  ],
  data () {
    return {
      permission: {
        add: ['admin', 'upgradePatch:add'],
        edit: ['admin', 'upgradePatch:edit'],
        del: ['admin', 'upgradePatch:del'],
        view: ['admin', 'upgradePatch:view']
      },
      headConfig: {
        initQuery: true,
        item: {
          1: {
            name: '版本名称',
            type: 'input',
            value: 'version',
          },
          2: {
            name: '开始日期',
            type: 'datetime',
            value: 'startTime',
            defaultFn: '7DS'
          },
          3: {
            name: '结束日期',
            type: 'datetime',
            value: 'endTime',
            defaultFn: 'toDE'
          },
          4: {
            name: '终端型号',
            type: 'input',
            value: 'deviceModel',
          }
        },
        button: {
        }
      },
      isDetail: false
    };
  },
  methods: {
    // 详情
    toDetail (data) {
      this.isDetail = true;
      this.crud.toEdit(data);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('UpgradePatch', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('UpgradePatch', value);
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    }
  }
};
</script>

<style lang="less" scoped>

</style>
