<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="110px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        >
          <el-button
            slot="right"
            v-permission="permission.execute"
            class="filter-item"
            icon="el-icon-video-play"
            size="small"
            @click="executeHandle"
          >
            执行
          </el-button>
        </crudOperation>
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            width="120"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetail(scope.row)"
                  >
                    详情
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    :disabled="scope.row.state === 2 || scope.row.state === 3"
                    @click="cancelHandle(scope.row)"
                  >
                    取消
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <!-- 任务名称 -->
          <el-table-column
            v-if="columns.visible('name')"
            :label="getLabel('name')"
            prop="name"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 升级包类型 -->
          <el-table-column
            v-if="columns.visible('upgradeMode')"
            :label="getLabel('upgradeMode')"
            prop="upgradeMode"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>{{ getEnumDictLabel('upgradeCategory', scope.row.upgradeMode) }}</span>
            </template>
          </el-table-column>
          <!-- 升级包名称 -->
          <el-table-column
            v-if="columns.visible('otaPackageName')"
            :label="getLabel('otaPackageName')"
            prop="otaPackageName"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 升级包版本 -->
          <el-table-column
            v-if="columns.visible('version')"
            :label="getLabel('version')"
            prop="version"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 终端型号 -->
          <el-table-column
            v-if="columns.visible('deviceModel')"
            :label="getLabel('deviceModel')"
            prop="deviceModel"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 创建时间 -->
          <el-table-column
            v-if="columns.visible('createTime')"
            :label="getLabel('createTime')"
            prop="createTime"
            width="180"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <!-- 升级成功终端数/总终端数 -->
          <el-table-column
            v-if="columns.visible('upgradeTotal')"
            :label="getLabel('upgradeTotal')"
            prop="upgradeTotal"
            width="180"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span
                class="upgrade-num"
                @click="terminalHandle(scope.row)"
              >
                {{ scope.row.upgradeNum }} / {{ scope.row.upgradeTotal }}
              </span>
            </template>
          </el-table-column>
          <!-- 任务状态 -->
          <el-table-column
            v-if="columns.visible('state')"
            :label="getLabel('state')"
            prop="state"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>{{ stateObj[scope.row.state] }}</span>
            </template>
          </el-table-column>
          <!-- 创建者 -->
          <el-table-column
            v-if="columns.visible('creator')"
            :label="getLabel('creator')"
            prop="creator"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 备注 -->
          <el-table-column
            v-if="columns.visible('remark')"
            :label="getLabel('remark')"
            prop="remark"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
      <!--表单渲染-->
      <eForm
        :dict="dict"
        :is-detail.sync="isDetail"
      />
      <UpgradeTerminal
        :dict="dict"
        :task-id="taskId"
        :state-obj="stateObj"
        :dialog-visible.sync="dialogTerminalVisible"
      />
    </div>
  </basic-container>
</template>

<script>
import crudUpgradePatch from '@/api/center/upgradeTask';
import eForm from './module/form';
import CRUD, { presenter, header } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import pagination from '@/components/Crud/Pagination';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import HeadCommon from '@/components/formHead/headCommon.vue';
import UpgradeTerminal from './module/upgradeTerminal';

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('UpgradeTask', 'uniName'), // 升级任务
  crudMethod: { ...crudUpgradePatch }
});

export default {
  name: 'UpgradeTask',
  components: { eForm, crudOperation, udOperation, pagination, HeadCommon, UpgradeTerminal },
  mixins: [presenter(crud), header()],
  // 数据字典
  dicts: [
    'upgradeCategory'
  ],
  data () {
    return {
      permission: {
        add: ['admin', 'upgradeTask:add'],
        edit: ['admin', 'upgradeTask:edit'],
        del: ['admin', 'upgradeTask:del'],
        view: ['admin', 'upgradeTask:view'],
        execute: ['admin', 'upgradeTask:execute'],
        cancel: ['admin', 'upgradeTask:cancel']
      },
      headConfig: {
        item: {
          1: {
            name: '任务名称',
            type: 'input',
            value: 'name',
          },
          2: {
            name: '开始日期',
            type: 'datetime',
            value: 'startTime',
            defaultFn: '7DS'
          },
          3: {
            name: '结束日期',
            type: 'datetime',
            value: 'endTime',
            defaultFn: 'toDE'
          },
          4: {
            name: '升级包类型',
            type: 'select',
            value: 'category',
            dictOptions: 'upgradeCategory'
          },
          5: {
            name: '升级包名称',
            type: 'input',
            value: 'otaPackageName',
          },
          6: {
            name: '任务状态',
            type: 'select',
            value: 'state',
            options: [
              { label: '未开始', value: 0 },
              { label: '执行中', value: 1 },
              { label: '已完成', value: 2 },
              { label: '已取消', value: 3 }
            ]
          },
        },
        button: {
        }
      },
      isDetail: false,
      dialogTerminalVisible: false,
      stateObj: {
        0: '未开始',
        1: '执行中',
        2: '已完成',
        3: '已取消',
        4: '失败'
      },
      taskId: null
    };
  },
  methods: {
    // 查看任务中的终端升级状态
    terminalHandle (data) {
      this.taskId = data.id;
      this.dialogTerminalVisible = true;
    },
    // 升级任务点击执行
    executeHandle () {
      if (!this.crud.selections.length) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      const result = this.crud.selections.some(item => item.state === 1 || item.state === 2);
      if (result) {
        this.$message.warning("请选择状态为未开始或已取消的任务执行升级");
        return;
      }
      this.$confirm(`确定开始执行选择的升级任务?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          ids: this.crud.selections.map(item => item.id),
          state: 1
        };
        crudUpgradePatch.editState(params).then(res => {
          if (res.code === 200) {
            this.$message.success('操作成功!');
            this.crud.toQuery();
          }
        });
      }).catch(() => {
      });
    },
    // 取消升级任务
    cancelHandle (data) {
      this.$confirm(`确定取消当前升级任务?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          ids: [data.id],
          state: 3
        };
        crudUpgradePatch.editState(params).then(res => {
          if (res.code === 200) {
            this.$message.success('操作成功!');
            this.crud.toQuery();
          }
        });
      }).catch(() => {
      });
    },
    // 详情
    toDetail (data) {
      this.isDetail = true;
      this.crud.toEdit(data);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('UpgradeTask', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('UpgradeTask', value);
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    }
  }
};
</script>

<style lang="less" scoped>
.upgrade-num {
  color: #027DB4;
  text-decoration:underline;
  cursor:pointer;
}
</style>
