<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="!isDetail ? crud.status.title : '查看' "
    append-to-body
    width="90%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      class="rewriting-form-disable"
      :hide-required-asterisk="isDetail"
      label-width="100px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('name')"
            prop="name"
          >
            <el-input
              v-model.trim="form.name"
              :placeholder="getPlaceholder('name')"
              :disabled="isDetail"
              @input="e => form.name = validInput(e)"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model.trim="form.remark"
              type="textarea"
              :placeholder="getPlaceholder('remark')"
              :disabled="isDetail"
              @input="e => form.remark = validInput(e)"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div class="dialog-container">
      <div class="container-left">
        <div class="left-header-title">
          <TableTitleSlot title="升级包列表" />
        </div>
        <el-form
          v-if="!isDetail"
          size="small"
          label-width="90px"
        >
          <el-row>
            <el-col :span="10">
              <el-form-item
                label="升级包名"
              >
                <el-input
                  v-model="upgradeForm.name"
                  placeholder="请输入升级包名"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item
                label="升级包类型"
              >
                <single-select
                  v-model="upgradeForm.category"
                  :options="dict.upgradeCategory"
                  placeholder="请输入升级包类型"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col
              :span="3"
              class="form-left-search"
            >
              <el-button
                class="filter-item"
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="leftSearchClick"
              >查 询
              </el-button>
            </el-col>
          </el-row>
        </el-form>
        <div class="left-table-container">
          <el-table
            v-loading="leftLoading"
            :data="upgradeList"
            :header-cell-style="{background:'#ebf5ff',color:'#606266'}"
            highlight-current-row
            height="50vh"
            @row-click="leftRowClick"
          >
            <el-table-column
              prop="name"
              label="升级包名"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              prop="category"
              label="升级包类型"
              show-overflow-tooltip
              :resizable="false"
            >
              <template slot-scope="scope">
                {{ getEnumDictLabel('upgradeCategory', scope.row.category) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="version"
              label="升级包版本"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              prop="deviceModel"
              label="终端型号"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-empty
              slot="empty"
              :image="require('@/assets/images/nodata.png')"
            />
          </el-table>
          <div class="left-pagination-container">
            <el-pagination
              background
              layout="total, prev, pager, next, sizes"
              :current-page.sync="upgradeForm.current"
              :page-size.sync="upgradeForm.size"
              :page-sizes="[10, 20, 30, 40, 50, 100]"
              :total="leftTotal"
              @size-change="leftSearchClick"
              @current-change="leftSearchClick"
            />
          </div>
        </div>
      </div>

      <div class="container-right">
        <div class="right-header-title">
          <TableTitleSlot title="终端列表" />
        </div>
        <el-form
          v-if="!isDetail"
          size="small"
          label-width="90px"
        >
          <el-row>
            <el-col :span="10">
              <el-form-item
                label="终端序列号"
              >
                <el-input
                  v-model="terminalForm.uniqueId"
                  placeholder="请输入终端序列号"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item
                label="所属机构"
              >
                <DeptFormSingleSelect
                  v-model="terminalForm.deptId"
                  :is-show="true"
                  placeholder="请选择所属机构"
                  size="small"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col
              :span="3"
              class="form-right-search"
            >
              <el-button
                class="filter-item"
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="rightSearchClick"
              >查 询
              </el-button>
            </el-col>
          </el-row>
        </el-form>
        <div class="right-table-container">
          <el-table
            ref="multipleTable"
            v-loading="rightLoading"
            :data="terminalList"
            row-key="uniqueId"
            :header-cell-style="{background:'#ebf5ff',color:'#606266'}"
            height="50vh"
            @selection-change="selectionChangeHandler"
          >
            <el-table-column
              type="selection"
              width="50px"
              reserve-selection
            />
            <el-table-column
              prop="deptName"
              label="所属部门"
              show-overflow-tooltip
              :resizable="false"
            >
              <template slot-scope="scope">
                {{ scope.row.deviceDeptName || scope.row.deptName }}
              </template>
            </el-table-column>
            <el-table-column
              prop="model"
              label="终端型号"
              show-overflow-tooltip
              :resizable="false"
            >
              <template slot-scope="scope">
                {{ scope.row.deviceModel || scope.row.model }}
              </template>
            </el-table-column>
            <el-table-column
              prop="uniqueId"
              label="终端序列号"
              show-overflow-tooltip
              :resizable="false"
            >
              <template slot-scope="scope">
                {{ scope.row.deviceUniqueId || scope.row.uniqueId }}
              </template>
            </el-table-column>
            <el-table-column
              prop="deviceNum"
              label="终端赋码号"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-empty
              slot="empty"
              :image="require('@/assets/images/nodata.png')"
            />
          </el-table>
          <div class="right-pagination-container">
            <el-pagination
              background
              layout="total, prev, pager, next, sizes"
              :current-page.sync="terminalForm.current"
              :page-size.sync="terminalForm.size"
              :page-sizes="[10, 20, 30, 40, 50, 100]"
              :total="rightTotal"
              @size-change="rightSearchClick"
              @current-change="rightSearchClick"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        v-if="!isDetail"
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="!isDetail"
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import SingleSelect from '@/components/select/DictSelect/DictSelectSingle';
import TableTitleSlot from '@/components/pageHead/tableTitleSlot.vue';
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';
import { upgradePageData, upgradeTerminalPageData, upgradeTerminalState } from '@/api/center/upgradeTask';
const defaultForm = {
  name: null,
  remark: null,
  otaPackageId: null,
  deviceList: null
};
export default {
  components: {
    SingleSelect,
    TableTitleSlot,
    DeptFormSingleSelect
  },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rules: {
        name: {
          required: true,
          message: '请输入任务名称',
          trigger: 'blur'
        }
      },
      upgradeForm: {
        name: '',
        category: null,
        current: 1,
        size: 10
      },
      terminalForm: {
        uniqueId: null,
        deptId: null,
        current: 1,
        size: 10,
        model: null
      },
      leftLoading: false,
      rightLoading: false,
      leftTotal: 0,
      rightTotal: 0,
      upgradeList: [],
      terminalList: [],
      leftRowData: null,
      selections: []
    };
  },
  methods: {
    // 勾选终端时
    selectionChangeHandler (val) {
      this.form.deviceList = val;
    },
    /** 开始 "新建/编辑" - 之后 */
    [CRUD.HOOK.afterToCU] () {
      this.$nextTick(() => {
        this.leftSearchClick();
        if (this.isDetail) {
          this.rightSearchClick();
        }
      });
    },
    /** 提交 - 之前 */
    [CRUD.HOOK.beforeSubmit] () {
      if (!this.leftRowData) {
        this.$message.warning('请选择安装包');
        return false;
      }
      if (!this.form.deviceList || !this.form.deviceList.length) {
        this.$message.warning('请选择要升级的终端');
        return false;
      }
    },
    // 左侧表格查询
    leftSearchClick () {
      this.leftLoading = true;
      upgradePageData({
        ...JSON.parse(JSON.stringify(this.upgradeForm)),
        id: this.isDetail ? this.form.otaPackageId : undefined
      }).then(res => {
        this.upgradeList = res.data.content;
        this.leftTotal = res.data.total;
      }).catch(err => {
        this.upgradeList = [];
        this.leftTotal = 0;
      }).finally(() => {
        this.leftLoading = false;
      });
    },
    // 右侧表格查询
    rightSearchClick () {
      if (!this.leftRowData && !this.isDetail) {
        this.$message.warning('请先选择左侧升级包');
        return;
      }
      this.rightLoading = true;
      const fn = this.isDetail ? upgradeTerminalState : upgradeTerminalPageData;
      fn({
        ...JSON.parse(JSON.stringify(this.terminalForm)),
        taskId: this.isDetail ? this.form.id : undefined
      }).then(res => {
        this.terminalList = res.data.content;
        this.rightTotal = res.data.total;
      }).finally(() => {
        this.rightLoading = false;
      });
    },
    // 点击左侧表格行时
    leftRowClick (data) {
      if (this.isDetail) {
        return;
      }
      this.leftRowData = data;
      this.form.otaPackageId = data.id;
      this.terminalForm.model = data.deviceModel;
      this.$refs.multipleTable.clearSelection();
      this.rightSearchClick();
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel(value) {
      return getLabel('UpgradeTask', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder(value, pre) {
      return getPlaceholder('UpgradeTask', value, pre);
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    // 监听关闭事件
    closed () {
      this.$refs.multipleTable.clearSelection();
      this.leftRowData = null;
      this.upgradeList = [];
      this.leftTotal = 0;
      this.terminalList = [];
      this.rightTotal = 0;
      this.upgradeForm = {
        name: '',
        category: null,
        current: 1,
        size: 10,
      };
      this.terminalForm = {
        uniqueId: null,
        deptId: null,
        current: 1,
        size: 10
      };
      this.$emit('update:isDetail', false);
    }
  }
};
</script>
<style lang="less" scoped>
.dialog-container {
  display: flex;
  justify-content: space-between;
  .container-left, .container-right {
    width: calc(50% - 10px);
    border: 1px solid #e4e7ed;
    border-radius: 4px 4px 0 0;
  }
  .container-left {
    /deep/ .el-table__row.current-row > td {
      background-color: rgba(var(--gn-color-rgb), .4) !important;
    }
  }
}
</style>
