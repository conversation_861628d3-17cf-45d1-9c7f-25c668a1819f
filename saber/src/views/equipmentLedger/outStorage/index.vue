<template>
  <div
    class="app-container out-storage"
    style="padding: 0"
  >
    <basic-container style="padding-bottom: 6px">
      <div class="box-container">
        <div class="box-item left">
          <div class="box-item-title">
            <div class="text">库存列表<el-tooltip
              placement="bottom-start"
              content="勾选数据移入出库列表"
            >
              <i
                class="el-icon-info"
                style="color: #3d6af6;margin-left: 6px;font-size: 18px;position: relative;top:1px;"/>
            </el-tooltip></div>
            <div>
              <el-input
                v-model="deviceNum"
                placeholder="请输入赋码编号"
                maxlength="50"
                style="width: 170px; margin-right: 20px;"
                clearable
                size="small"
              />
              <el-input
                v-model="uniqueId"
                placeholder="请输入序列号"
                maxlength="50"
                style="width: 170px; margin-right: 20px;"
                clearable
                size="small"
              />
              <el-button
                type="primary"
                icon="el-icon-search"
                size="small"
                @click="refresh"
              >查 询</el-button>
            </div>
          </div>
          <div class="table-box">
            <el-table
              ref="multipleTableLeft"
              :data="tableDataLeft"
              tooltip-effect="dark"
              row-key="id"
              current-row-key="id"
              resizable
              style="width: 100%"
              @selection-change="handleSelectionChangeLeft"
            >
              <el-table-column
                :resizable="false"
                type="selection"
                width="50"
                reserve-selection
              />
              <el-table-column
                :resizable="false"
                label="序列号"
                min-width="150"
                show-overflow-tooltip
              >
                <template slot-scope="scope">{{ scope.row.uniqueId }}</template>
              </el-table-column>
              <el-table-column
                label="终端类型"
                prop="categoryName"
                min-width="140"
                :resizable="false"
                show-overflow-tooltip
              />
              <el-table-column
                prop="model"
                label="终端型号"
                min-width="100"
                show-overflow-tooltip
                :resizable="false"
              />
              <el-table-column
                prop="deviceNum"
                label="赋码编号"
                min-width="170"
                show-overflow-tooltip
                :resizable="false"
              />
            </el-table>
          </div>
          <div class="bottom">
            <el-pagination
              layout="total, sizes, prev, pager, next, jumper"
              :page-size="size"
              :page-sizes="[10, 20, 30, 40, 50, 100]"
              :current-page="curPage"
              :total="totalPage"
              @current-change="pageChange"
              @size-change="sizeChangeHandler"
            />
            <!-- <el-button type="primary" @click="transferData">移入</el-button> -->
          </div>
        </div>
        <el-divider
          class="box-divider"
          direction="vertical"
        />
        <div class="box-item right">
          <div class="box-item-title">
            <div class="text">
              出库列表<span>{{ checkedNum }}</span>
            </div>
            <div style="display: flex;">
              <el-button
                type="primary"
                class="top-btn"
                size="small"
                @click="batchvisible = true"
              >导入</el-button>
              <DeptFormSingleSelect
                ref="deptIdRef"
                v-model="userDeptId"
                :is-show="true"
                placeholder="请选择使用单位"
              />
            </div>
          </div>
          <div class="table-box">
            <el-table
              ref="multipleTableRight"
              v-loading="loading"
              :data="tableDataRight"
              tooltip-effect="dark"
              current-row-key="id"
              row-key="id"
              resizable
              style="width: 100%"
            >
              <el-table-column
                label="序列号"
                min-width="170"
                show-overflow-tooltip
                :resizable="false"
              >
                <template slot-scope="scope">{{ scope.row.uniqueId }}</template>
              </el-table-column>
              <el-table-column
                label="终端类型"
                prop="categoryName"
                min-width="150"
                :resizable="false"
                show-overflow-tooltip
              />
              <el-table-column
                prop="model"
                label="终端型号"
                min-width="100"
                show-overflow-tooltip
                :resizable="false"
              />
              <el-table-column
                prop="deviceNum"
                label="赋码编号"
                min-width="180"
                show-overflow-tooltip
                :resizable="false"
              />
              <el-table-column
                label="操作"
                :resizable="false"
              >
                <template slot-scope="{ row }">
                  <span
                    class="del"
                    size="mini"
                    @click="deleteItem(row)"
                  >移除</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="bottom">
            <el-button
              type="primary"
              size="small"
              @click="handleOut"
            >出库</el-button>
          </div>
        </div>
      </div>
    </basic-container>
    <BatchImport
      :visible="batchvisible"
      mod="equipmentLedger"
      @close="batchvisible = false"
      @getBatchData="getBatchData"
    />
  </div>
</template>

<script>
import crudOperation from "@/components/Crud/CRUD.operation";
import pagination from "@/components/Crud/Pagination";
import { pagination as getOutList } from "@/api/bdTest/manuFactor";
import api, {
  getOutlmportList,
  outBatch,
} from "@/api/equipmentLedger/equipmentManage.js";
import BatchImport from "@/components/upload/batchImport.vue";
import DeptSingleSelect from '@/components/select/DeptSingleSelect/DeptSingleSelectNoDefault';
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';

export default {
  name: "OutStorage",
  components: {
    DeptFormSingleSelect,
    crudOperation,
    pagination,
    BatchImport,
    DeptSingleSelect
  },
  mixins: [],
  // 数据字典
  dicts: ["bdmDeviceType", "testDeviceType"],
  data() {
    return {
      deviceNum: "",
      uniqueId: '',
      tableDataLeft: [],
      tableDataRight: [],
      seleNum: 0,
      total: 0,
      userDeptId: "",
      totalPage: 0,
      curPage: 1,
      size: 20,
      manufacturerList: [],
      batchvisible: false,
      loading: false,
    };
  },
  activated() {
    this.getTableList();
    this.getManufacturerList();
  },
  methods: {
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return "";
      }
    },
    getManufacturerList() {
      getOutList({
        page: 0,
        size: 9999,
      }).then((res) => {
        this.manufacturerList = res.data.content.map((item) => {
          return {
            label: item.name,
            value: item.code,
          };
        });
      });
    },
    handleSelectionChangeLeft(val) {
      this.tableDataRight = val;
    },
    deleteItem({id}) {
      let index = this.tableDataLeft.findIndex( item => item.id === id);
      let row = this.tableDataLeft[index];
      if(index === -1) {
        const index = this.tableDataRight.findIndex((i) => i.id === id);
        row = this.tableDataRight[index];
      }
      row && this.$refs.multipleTableLeft.toggleRowSelection(row);
    },
    handleOut() {
      if (!this.userDeptId) {
        this.$message.warning("请先选择所属机构");
        return;
      }
      if (!this.tableDataRight.length) {
        this.$message.warning("请先勾选出库数据");
        return;
      }
      const ids = this.tableDataRight.reduce((list, item) => {
        list.push(item.id);
        return list;
      }, []);
      outBatch(ids, this.userDeptId).then((res) => {
        this.$refs.multipleTableLeft.clearSelection();
        this.tableDataRight = [];
        this.userDeptId = "";
        this.deviceNum = "";
        this.uniqueId = "";
        this.$refs.deptIdRef.clear();
        this.refresh();
      });
    },
    pageChange(val) {
      this.curPage = val;
      this.getTableList();
    },
    sizeChangeHandler(val) {
      this.size = val;
      this.refresh();
    },
    getTableList() {
      api
        .pagination({
          storageState: 0,
          page: this.curPage - 1,
          size: this.size,
          deviceNum: this.deviceNum,
          uniqueId: this.uniqueId
        })
        .then((res) => {
          this.tableDataLeft = res.data.content;
          this.totalPage = res.data.total;
        });
    },
    refresh() {
      this.curPage = 1;
      this.getTableList();
    },
    // 批量导入相关
    // 获取excel的数据
    getBatchData(returnValue) {
      returnValue.close();
      let data = returnValue.data;
      if (!data?.length) {
        this.$message.warning("导入数据不能为空！");
        return;
      }
      const strList = data.reduce((str, item) => {
        const val = item["赋码编号"];
        if (val) str += `${val},`;
        return str;
      }, "");
      this.loading = true;
      getOutlmportList(strList.substr(0, strList.length - 1)).then((res) => {
        const list = res.data;
        if (list?.length) {
          if (data.length !== list.length) {
            const h = this.$createElement;
            const newList = data.reduce((arr, item) => {
              if (!list.find((each) => each.deviceNum === item["赋码编号"])) {
                arr.push(h("p", null, item["赋码编号"]));
              }
              return arr;
            }, []);
            if(newList.length) {
              this.$msgbox({
                title: "以下数据导入失败：",
                message: h("p", null, newList),
                confirmButtonText: "确定",
              });
            }
          }
          list.forEach((data) => {
            if (!this.tableDataRight.find((item) => item.id === data.id)) {
              this.$refs.multipleTableLeft.toggleRowSelection(data);
            }
          });
          this.$nextTick(() => {
            this.tableDataLeft = JSON.parse(JSON.stringify(this.tableDataLeft));
          }, 100);
        } else {
          this.$message.error("导入数据的赋码编号不存在");
        }
      }).finally(() => {
        this.loading = false;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.box-container {
  display: flex;
  height: 100%;
  //overflow-x: auto;
  width: 100%;
  .box-item {
    flex: 1;
    //min-width: 780px;
    padding: 15px;
    // border: 1px solid #409eff;
    display: flex;
    flex-direction: column;
    &.left {
      width: calc(50% - 16px);
      box-sizing: border-box;
    }
    &.right {
      width: 50%;
      box-sizing: border-box;
    }
    .del {
      color: #409eff;
      cursor: pointer;
    }

    .box-item-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0 18px;
      border-bottom: 1px solid #ebeef5;
      .text {
        font-size: 16px;
        font-weight: 600;
        span {
          margin-left: 8px;
          color: #ccc;
          font-weight: normal;
          font-size: 12px;
        }
      }
      .top-btn {
        margin-right: 20px;
      }
    }
    .table-box {
      flex: 1;
      overflow-y: auto;
    }
    .bottom {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
    }
  }
}
.ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.box-divider {
  height: 100%;
  background-color: #cccccc;
}
</style>
