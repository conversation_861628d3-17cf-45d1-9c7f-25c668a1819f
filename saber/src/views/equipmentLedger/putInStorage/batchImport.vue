<template>
  <div class="batch-page">
    <el-upload
      ref="uploadBatch"
      :auto-upload="false"
      :on-change="handleChange"
      :on-exceed="handleLimit"
      :limit="1"
      class="uploadBatch"
      drag
      action="#"
      accept=".xlsx,.xls"
    >
      <i class="el-icon-upload" />
      <div class="el-upload__text">将Excel拖到此处，或<em class="upload-text">点击上传</em></div>
      <div slot="tip" class="el-upload__tip">
        <a
          :href="`/bdsplatform/static/excelTemplate/终端设备入库导入模板.xlsx`"
          class="downloadFile"
          >下载模板</a
        >
      </div>
    </el-upload>
    <div class="dialog-footer">
      <el-button size="small" @click="handleClose"> 取消 </el-button>
      <el-button type="primary" size="small" @click="handleconfirm"> 确认 </el-button>
    </div>
    <msgDialog ref="msgDialog" :msg-data="msgData" :isParent="true" @clearFileErr="handleClose" />
  </div>
</template>

<script>
import XLSX from "xlsx";
import msgDialog from '@/components/importErr'
import { addBatch } from "@/api/equipmentLedger/equipmentManage.js";
export default {
  components: {
    msgDialog,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    dict: {
      type: Array,
      default: () => [],
    },
  },
  data() {
  this.maxLen = {
    uniqueId: 50,
    model: 50,
    imei: 50,
    bdChipSn: 50,
  }
    return {
      fileList: [],
      file: "",
      upData: undefined,
      ruleIds: [],
      // 批量引入相关
      batchvisible: false,
      msgData: [], // 批量导入提示消息
      numKey: undefined, // 递归中继值
      dataType: {
        // excel里文字对应的key
        终端类型: "category",
        序列号: "uniqueId",
        终端型号: "model",
        赋码编号: "deviceNum",
        设备类别: "type",
        北斗芯片序列号: "bdChipSn",
        IMEI号: "imei",
        采购日期: "buytime",
        生产厂商: "vendor",
        产品序列号: "serial"
      },
      // 必填项
      typeRequired: [
        "category",
        // "uniqueId",
        // "model",
        "deviceNum",
        // "type",
        // "bdChipSn",
        // "imei",
        // "buytime",
        // "vendor",
        // "serial",
      ],
      // 表单名称对应字典(表单名称与字典名称不一致时)
      typeDictName: {
        type: "testDeviceType",
      },
      strArr: [],
      cascade: ["category"],
      cascadeDictName: {
        category: "bdmDeviceType",
      }, //级联对应字典
      tipsKey: [], // 提示点集合
    };
  },
  methods: {
    handleconfirm() {
      if (this.upData) {
        this.getBatchData();
      }
    },
    handleLimit() {
      this.$message.warning('只能上传1个文件')
    },
    handleClose() {
      this.upData = undefined;
      this.$refs.uploadBatch.clearFiles();
      this.ruleIds = [];
      // this.$emit("close");
    },
    handleChange(file, fileList) {
      if (!this.beforeUp(file)) {
        this.$refs.uploadBatch.clearFiles();
        return;
      }
      this.fileList = [fileList[fileList.length - 1]]; // 只能上传一个Excel，重复上传会覆盖之前的
      this.file = file.raw;
      let reader = new FileReader();
      let _this = this;
      reader.readAsArrayBuffer(this.file);
      reader.onload = function () {
        let buffer = reader.result;
        let bytes = new Uint8Array(buffer);
        let length = bytes.byteLength;
        let binary = "";
        for (let i = 0; i < length; i++) {
          binary += String.fromCharCode(bytes[i]);
        }
        let XLSX = require("xlsx");
        // wb 是核心
        let wb = XLSX.read(binary, {
          type: "binary",
        });
        // 拿到了xlsx中的第一个sheet 如果还有其他的sheet 还可以继续拿到其他sheet
        let outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]]);
        // 拿到xlsx中的第2个sheet ,同理以此类推
        // let outdata1 = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[1]]);

        _this.upData = outdata;
      };
    },
    // 上传格式做限制
    beforeUp(file) {
      let testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      if (testmsg !== "xls" && testmsg !== "xlsx") {
        this.$message({
          message: "上传文件格式错误!",
          type: "warning",
        });
        return false;
      } else {
        return true;
      }
    },
    // 批量导入相关
    // 获取excel的数据
    getBatchData() {
      let data = this.upData;
      if(!this.reversalObj) {
        this.reversalObj = {};
        for (let k in this.dataType) {
          this.reversalObj[this.dataType[k]] = k;
        }
      }
      this.tipsKey = [];
      let arr = data.map((item, index) => {
        let obj = {};
        for (let key in item) {
          for (let k in this.dataType) {
            if (key === k) {
              obj[this.dataType[k]] = item[key];
            }
          }
        }
        // this.typeTreeTurn(obj);
        this.typeRequiredTurn(obj, index);
        this.getCascadeKey(obj);
        this.handleExtra(obj ,index, item)
        return obj;
      });
      if (this.tipsKey && this.tipsKey.length > 0) {
        let arr = [];
        this.tipsKey.forEach((item, index) => {
          if (item && item.length > 0) {
            const errList = []
            item.forEach(v => {
              errList.push(v)
            });
            arr.push({
              sort: `第${index + 1}行`,
              details: errList.join(',')
            });
          }
        });
        this.msgData = arr;
        this.$refs.msgDialog.msgVisible = true;
      } else {
        arr = arr.map( item => {
          const {category: val, ...obj} = item
          const [deviceType, category] = val
          return {
            ...obj,
            category: Number(category),
            deviceType: Number(deviceType)
          }
        })
        this.addbatchPost(arr);
        this.handleClose();
      }
    },
    handleExtra (obj, index, data) {
      this.cascade.forEach( k => {
        if(obj[k] === data[this.reversalObj[k]]) {
          this.handleErrFun(index, k)
        }
      })
      if(obj.buytime && !/^\d[-|\d]+\d$/.test(obj.buytime)) {
        this.handleErrFun(index, 'buytime')
      }
      // Object.keys(this.maxLen).forEach( v => {
      //   if(obj[v] && (obj[v].length > this.maxLen[v])) {
      //     this.handleErrFun(index, v)
      //   }
      // })
    },
    handleErrFun(index, v) {
      const pL = this.reversalObj[v]
      if(pL) {
        if (!this.tipsKey[index]) {
          this.tipsKey[index] = [pL];
        } else if(!this.tipsKey[index].includes(pL)) {
          this.tipsKey[index].push(pL);
        }
      }
    },
    // 必填项判断
    typeRequiredTurn(obj, k, dataRequired = this.typeRequired) {
      dataRequired.forEach((v) => {
        if (typeof v === "object" && v.mod) {
          this.typeRequiredTurn(obj[v.mod], k, v.required);
        } else if (!obj[v] && obj[v] !== 0) {
          this.handleErrFun(k, v)
        }
      });
    },
    // 字典里的转key/id
    typeTreeTurn(obj) {
      for (let k in this.dict) {
        for (let j in obj) {
          if (k === j) {
            this.treeTurn(this.dict[k], obj[j], j);
            obj[j] = this.numKey;
            this.numKey = undefined;
          } else if (k === this.typeDictName[j]) {
            this.treeTurn(this.dict[this.typeDictName[j]], obj[j], j);
            obj[j] = this.numKey;
            this.numKey = undefined;
          }
        }
      }
    },
    getTreeTurn(str) {
      let obj = {
        value: "value",
        label: "label",
      };
      switch (str) {
        case "deptId":
          obj.value = "id";
          obj.label = "title";
          break;
        default:
          break;
      }
      return obj;
    },
    // 递归找key/id
    treeTurn(
      tree,
      word,
      str,
      iValue = this.getTreeTurn(str).value,
      iLabel = this.getTreeTurn(str).label,
      iChildren = "children"
    ) {
      tree.forEach((item) => {
        if (!item.disabled && item[iLabel] === word) {
          this.numKey = item[iValue];
        } else if (item[iChildren] && item[iChildren].length > 0) {
          this.treeTurn(item[iChildren], word, str, iValue, iLabel, iChildren);
        }
      });
    },
    // 提交请求
    addbatchPost(arr) {
      addBatch(arr, 1).then((res) => {
        this.$message({
          showClose: true,
          message: res.msg,
          type: "success",
        });
        this.handleClose()
      })
    },
    //获取级联项key
    getCascadeKey(obj) {
      this.cascade.forEach((item) => {
        if (obj[item]) {
          let arr = obj[item]?.split?.("-");
          if (arr?.length > 0)
            this.cascadeRecursion(
              obj,
              this.dict[this.cascadeDictName[item]],
              arr,
              0,
              item
            );
        }
      });
    },
    //递归
    cascadeRecursion(obj, dict, arr, position, type) {
      for (let index = position; index < arr.length; index++) {
        let element = arr[index];
        for (let item of dict) {
          if (element === item.label) {
            arr[position] = item.value.toString()
            if (item.children) {
              this.cascadeRecursion(obj, item.children, arr, index + 1, type);
            } else {
              obj[type] = arr
            }
          }
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
.batch-page {
  width: 360px;
  a {
      outline:none;
  }
  .downloadFile {
    color: var(--gn-color);
    font-size: 14px;
    padding: 30px;
  }
  .upload-text {
    color: var(--gn-color);
  }
  .el-upload__text {
    font-size: 14px;
  }
  .rule-config {
    margin-top: 12px;
    display: flex;

    .rule-label {
      height: 40px;
      display: flex;
      align-items: center;
      margin-right: 6px;
      color: #3c3c3c;
    }
  }
  .el-upload__tip {
    text-align: right;
  }
  .dialog-footer {
    display: flex;
    justify-content: center;
  }
}
</style>
