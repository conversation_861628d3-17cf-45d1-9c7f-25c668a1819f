<template>
  <div class="select-content">
    <div
      v-if="!disabled"
      class="mask-operate"
      @click="handleTableShow"
    />
    <el-input
      v-model="value"
      :disabled="disabled"
      placeholder="请点击选择赋码编号"
    >
      <el-button
        slot="append"
        icon="el-icon-search"
        :style="{cursor: disabled ? 'default' : 'pointer'}"
        type="info"
      />
    </el-input>
    <el-dialog
      :visible.sync="isShowTable"
      append-to-body
      title="入库终端列表"
      custom-class="device-num-select-dialog"
      :fullscreen="false"
      :before-close="beforeClose"
      width="90%"
    >
      <div class="xh-container">
        <!--工具栏-->
        <div class="head-container">
          <HeadCommon
            :dict="dict"
            :head-config="headConfig"
            :can-set-remove-tabs="false"
            label-width="120px"
          />
        </div>
        <div class="xh-crud-table-container">
          <el-table
            ref="table"
            v-loading="crud.loading"
            :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
            :data="crud.data"
            :cell-style="{'text-align':'center'}"
            style="width: 100%;height: calc(100% - 27px)"
            :highlight-current-row="true"
            @current-change="currentChange"
          >
            <el-table-column
              label="赋码编号"
              :show-overflow-tooltip="true"
              min-width="100"
              prop="deviceNum"
            />
            <el-table-column
              prop="deviceSeq"
              min-width="100"
              label="序列号"
            />
            <el-table-column
              prop="deviceCateName"
              label="设备类别"
              min-width="120"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="IMEI"
              prop="imei"
              min-width="100"
              show-overflow-tooltip
            />
            <el-table-column
              prop="chipSeq"
              label="北斗芯片序列号"
              min-width="120"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              prop="manufacturerName"
              label="生产厂商"
              min-width="120"
              :show-overflow-tooltip="true"
            />
            <el-empty
              slot="empty"
              :image="require('@/assets/images/nodata.png')"
            />
          </el-table>
          <!--分页组件-->
          <pagination/>
        </div>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="cancel"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="submit()"
        >
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import HeadCommon from '@/components/formHead/headCommon.vue';
import CRUD, { presenter } from '@/components/Crud/crud';
import modalTable from '@/api/equipmentLedger/modalTable';
import pagination from '@/components/Crud/Pagination';
// crud交由presenter持有
const crud = CRUD({
  title: '',
  props: {
    searchToggle: true
  },
  queryOnPresenterCreated: false,
  crudMethod: { ...modalTable }
});
export default {
  name: 'DeviceNumSelect',
  components: {
    HeadCommon,
    pagination
  },
  mixins: [presenter(crud)],
  props: {
    // 绑定值
    value: {
      type: String,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 终端类别(大类)字典值 用于后端进行列表类别筛选
    typeDictValue: {
      type: String,
      default: '',
      required: true
    },
    dict: {
      type: Array,
      default: () => []
    },
    codeNumber: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isShowTable: false,
      headConfig: {
        item: {
          1: {
            name: '赋码编号',
            type: 'input',
            value: 'deviceNum'
          },
          2: {
            name: '序列号',
            type: 'input',
            value: 'deviceSeq'
          },
          3: {
            name: 'IMEI',
            type: 'input',
            value: 'imei'
          },
          5: {
            name: '生产厂商',
            type: 'input',
            value: 'manufacturer'
          },
          6: {
            name: '终端型号',
            type: 'input',
            value: 'deviceCate'
          }
        },
        button: {}
      },
      selectRow: {},
      dict: {
        dict: {}
      }
    };
  },
  watch: {
    isShowTable(val) {
      if (val) {
        if(crud.query.codeNumber !== this.codeNumber) {
          this.$set(crud.query, 'codeNumber', this.codeNumber);
        }
        this.crud.toQuery();
      }
    },
  },
  methods: {
    beforeClose () {
      this.isShowTable = false;
      crud.resetQuery(false);
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label || this.dict.dict[dictName][value];
      } else {
        return value;
      }
    },
    handleTableShow() {
      this.isShowTable = true;
    },
    [CRUD.HOOK.beforeRefresh]() {
      // 出库状态的查询条件
      this.crud.query.deviceType = this.typeDictValue;
    },
    cancel() {
      this.isShowTable = false;
    },
    submit() {
      if (Object.keys(this.selectRow).length === 0) {
        this.$message.error('请选择终端');
      }
      else {
        this.$emit('handleChange', this.selectRow);
        this.isShowTable = false;
      }
    },
    currentChange(row) {
      this.selectRow = row || {};
    },
  }
};
</script>
<style scoped lang="less">
.select-content {
  width: 100%;
  height: 100%;
  position: relative;
}

.mask-operate {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  //background-color: #ff000066;
  cursor: pointer;
  z-index: 1;
}

</style>
<style lang="scss">
.device-num-select-dialog {
  color: #ffffff;
  .el-dialog__body {
    padding-top: 8px !important;
    padding-bottom: 8px !important;
    height: 70vh;
    .el-table__row.current-row > td {
      background-color: rgba(var(--gn-color-rgb), .4) !important;
    }
  }
}
</style>
