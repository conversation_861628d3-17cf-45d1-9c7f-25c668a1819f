<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="btnShow?crud.status.title:'查看'"
    append-to-body
    width="50%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="btnShow ? rules : {}"
      size="small"
      label-width="150px"
      class="rewriting-form-disable"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="企业名称"
            prop="companyName"
          >
            <el-input
              v-model.trim="form.companyName"
              placeholder="请输入企业名称"
              maxlength="20"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="所属机构"
            prop="deptId"
            :required="btnShow"
          >
            <DeptFormSingleSelect
              ref="deptIdRef"
              v-model="form.deptId"
              :detail-name="form.deptName"
              :disabled="!btnShow"
              :is-show="crud.status.cu > 0"
              placeholder="请选择所属机构"
              size="small"
              @input="validateTreeSelect('deptId')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="地址"
            prop="address"
          >
            <el-input
              v-model.trim="form.address"
              placeholder="请输入地址"
              maxlength="200"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="联系人"
            prop="contactPerson"
          >
            <el-input
              v-model.trim="form.contactPerson"
              placeholder="请输入联系人"
              maxlength="20"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="联系电话"
            prop="contactPhone"
          >
            <el-input
              v-model.trim="form.contactPhone"
              placeholder="请输入联系电话"
              maxlength="15"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        v-if="btnShow"
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="btnShow"
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import { validatePhoneTwo } from '@/utils/validate';
import DeptFormSingleSelect from '@/components/select/DeptSingleSelect/DeptFormSingleSelect.vue';

const defaultForm = {
  companyName: '',
  contactPerson: '',
  address: '',
  contactPhone: '',
  deptId: null,
  id: '',
  deptName: ''
};
export default {
  components: {
    DeptFormSingleSelect
  },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      default: () => {
        return {};
      }
    },
    btnShow: {
      type: Boolean,
      default: true
    },
    depts: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      rules: {
        companyName: {
          required: true,
          trigger: 'blur'
        },
        contactPerson: {
          required: true,
          trigger: 'blur'
        },
        address: {
          required: true,
          trigger: 'blur'
        },
        contactPhone: {
          required: true,
          validator: validatePhoneTwo,
          trigger: 'blur'
        }
      }
    };
  },
  methods: {
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU]() {
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
          this.$refs[`deptIdRef`].$refs[`deptIdStrRef`].$children[0].$el.style.borderColor = '#BFBFBF';
        }
      });
    },
    [CRUD.HOOK.beforeSubmit]() {
      this.form.deptName = '';
    },
    /** 点击取消之前*/
    [CRUD.HOOK.afterEditCancel]() {
      this.form.deptName = '';
      this.$emit('cancelCU');
    },
    /** "新建/编辑" 验证 - 之前 */
    [CRUD.HOOK.beforeValidateCU]() {
      const reg = /^((0\d{2,3}-\d{7,8})|(1\d{10}))$/;
      this.$refs.form.validate((valid) => {
        if (!valid) {
          if (!reg.test(this.form.contactPhone)) {
            this.$message.warning('请填写正确的电话号码');
          }
          else {
            this.$message.warning('请确认必填项已全部正确填写');
          }
          this.validateTreeSelect('deptIdStr');
        }
      });
    },
    /** 提交- 之后 */
    [CRUD.HOOK.afterSubmit]() {
      // this.form = defaultForm;
    },
    /** 新建/编辑" 验证 - 之后 */
    [CRUD.HOOK.afterValidateCU]() {
    },

    // 监听关闭事件
    closed() {
      let refList = [
        'deptIdStrRef'
      ];
      for (let i = 0; i < refList.length; i++) {
        if (this.$refs[refList[i]]) {
          this.$refs[refList[i]].$children[0].$el.style.borderColor = '#bfbfbf';
        }
      }
    },
    /**
     * 验证treeSelect项
     * @param {String} item
     */
    validateTreeSelect(item) {
      this.$refs.form.validateField(item, (valid) => {
        this.$refs[`${item}Ref`].$refs[`${item}StrRef`].$children[0].$el.style.borderColor = valid !== '' ? '#f56c6c' : '#bfbfbf';
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-input__inner {
  text-align: left;
}

</style>
