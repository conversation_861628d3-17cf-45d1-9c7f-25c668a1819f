<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="100px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :download="false"
          width="190"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :data="crud.data"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          :cell-style="{'text-align':'center'}"
        >
          <el-table-column
            label="操作"
            width="80"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <div class="operation">
                <el-button
                  size="small"
                  type="text"
                  @click="showDetail(scope.row.deviceNum)"
                >
                  查看详情
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('deviceNum')"
            prop="deviceNum"
            label="赋码编号"
            :show-overflow-tooltip="true"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('terminalType')"
            prop="terminalType"
            label="终端类别"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>
                {{ getEnumDictLabel('bdmDeviceType', scope.row.terminalType) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('terminalModel')"
            prop="terminalModel"
            label="终端型号"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('deptName')"
            prop="deptName"
            label="所属机构"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('totalCountNear')"
            prop="totalCountNear"
            label="最近识别结果"
            :resizable="false"
          >
            <template slot="header">
              <el-tooltip
                content="总检测量 /  识别为非北斗数据量"
              >
                <i class="el-icon-info"/>
              </el-tooltip>
              <span style="margin-left: 4px;">最近识别结果</span>
            </template>
            <template slot-scope="scope">
              <span>
                {{ scope.row.totalCountNear }} / {{ scope.row.totalNonDBCountNear }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('totalCount')"
            prop="totalCount"
            label="累计识别数据量"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('totalNonDBCount')"
            prop="totalNonDBCount"
            label="累计识别非北斗数据量"
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination/>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      :modal="false"
      class="dialog"
      title="详情"
      width="75vw"
    >
      <detailPage
        :deviceNum="dialogDetailId"
        :dialogVisible="dialogVisible"
      />
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="dialogVisible = false"
        >
          关闭
        </el-button>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
import { pagination as bDReport } from '@/api/bdTest/realtime.js';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';
import detailPage from './module/detailPage';
import HeadCommon from '@/components/formHead/headCommon.vue';

// crud交由presenter持有
const crud = CRUD({
  title: '北斗识别检测',
  optShow: {
    add: false,
    edit: false,
    download: false
  },
  crudMethod: {
    pagination: bDReport
  }
});

export default {
  name: 'BDReport',
  components: {
    crudOperation,
    pagination,
    detailPage,
    HeadCommon
  },
  mixins: [presenter(crud)],
  dicts: [
    'bdmDeviceType'
  ],
  data() {
    return {
      firstOpen: true,
      dialogVisible: false,
      dialogDetailId: null,
      headConfig: {
        item: {
          1: {
            name: '赋码编号',
            type: 'input',
            value: 'deviceNum',
          },
          2: {
            name: '终端类别',
            type: 'select',
            value: 'terminalType',
            dictOptions: 'bdmDeviceType'
          },
          3: {
            name: '终端型号',
            type: 'input',
            value: 'terminalModel'
          },
          4: {
            name: '所属机构',
            type: 'extra',
            value: 'deptId'
          }
        },
        button: {
        }
      }
    };
  },
  methods: {
    showDetail(deviceNum) {
      this.dialogDetailId = deviceNum;
      this.dialogVisible = true;
    },
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}
</style>
