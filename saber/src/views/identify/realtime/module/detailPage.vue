<template>
  <div
    class="bd-report-detail"
    style="padding: 0;"
  >
    <el-tabs
      v-model="activeName"
    >
      <el-tab-pane
        label="最近识别情况"
        name="1"
      >
        <recently-Table
          :deviceNum="deviceNum"
          :dialogVisible="dialogVisible"
        />
      </el-tab-pane>
      <el-tab-pane
        label="识别非北斗信息"
        name="2"
      >
        <noBdTable
          :deviceNum="deviceNum"
          :dialogVisible="dialogVisible"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import recentlyTable from '@/views/identify/realtime/module/recentlyTable.vue';
import noBdTable from '@/views/identify/realtime/module/noBdTable.vue';

export default {
  name: 'BDReportDetail',
  components: {
    recentlyTable,
    noBdTable
  },
  props: {
    deviceNum: {
      type: String,
      default: ''
    },
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeName: '1'
    };
  },
  watch: {
    deviceNum: {
      handler(newVal) {
        console.log('-> 详情页ID', newVal);
      },
      immediate: true
    }
  }
};
</script>

<style lang="less" scoped>
.bd-report-detail {
  height: 100%;
}

.detail-table {
  ::v-deep .el-table__header-wrapper .el-table__cell {
    background-color: #f2f7ff !important;
  }
}

.detail-container {
  height: 460px;
}

.xh-container {
  height: 100%;
  padding: 12px;
}
</style>
