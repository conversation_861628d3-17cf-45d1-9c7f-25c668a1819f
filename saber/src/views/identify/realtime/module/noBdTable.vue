<template>
  <!--工具栏-->
  <div class="recently-table">
    <div class="detail-container">
      <crudOperation :download="false"/>
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="crud.data"
        style="width: 100%;height: calc(100% - 47px);"
        :cell-style="{'text-align':'center'}"
      >
        <el-table-column
          v-if="columns.visible('deviceNum')"
          prop="deviceNum"
          label="赋码编号"
          width="200"
          :resizable="false"
        />
        <el-table-column
          v-if="columns.visible('terminalType')"
          prop="terminalType"
          label="终端类别"
          width="120"
          :resizale="false"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          v-if="columns.visible('terminalModel')"
          prop="terminalModel"
          label="终端型号"
          :resizable="false"
          width="120"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          v-if="columns.visible('deptName')"
          prop="deptName"
          label="所属机构"
          :resizable="false"
        />
        <el-table-column
          v-if="columns.visible('locTime')"
          prop="locTime"
          label="定位时间"
          width="180"
          :resizable="false"
        >
          <template slot-scope="scope">
            <span class="table-date-td">{{ parseTime(scope.row.locTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns.visible('longitude')"
          prop="longitude"
          label="经度"
          :show-overflow-tooltip="true"
          :resizable="false"
          >
          <template slot-scope="scope">
            {{ handlePosition(scope.row.longitude) }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns.visible('latitude')"
          prop="latitude"
          label="纬度"
          :show-overflow-tooltip="true"
          :resizable="false"
          >
          <template slot-scope="scope">
            {{ handlePosition(scope.row.latitude) }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns.visible('locationAddr')"
          prop="locationAddr"
          label="物理位置"
          :show-overflow-tooltip="true"
          :resizable="false"
        />
        <el-table-column
          v-if="columns.visible('checkRes')"
          prop="checkRes"
          label="识别结果"
          :resizable="false"
        >
          <template slot-scope="scope">
            <span>{{ Number(scope.row.checkRes) === 1 ? '北斗识别' : '疑似非北斗识别' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns.visible('checkResMessage')"
          prop="checkResMessage"
          label="结果描述"
          :show-overflow-tooltip="true"
          :resizable="false"
        />
        <el-empty
          slot="empty"
          :image="require('@/assets/images/nodata.png')"
        />
      </el-table>
    </div>
    <!--分页组件-->
    <pagination/>
  </div>
</template>
<script>
import { noBDPagination } from '@/api/bdTest/realtime.js';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation.vue';
import pagination from '@/components/Crud/Pagination';
import { batchAddr } from '@/api/monitoring/track';
import { parseTime } from '@/utils';

// crud交由presenter持有
const crud = CRUD({
  title: '北斗识别检测',
  optShow: {
    add: false,
    edit: false,
    download: false
  },
  crudMethod: {
    pagination: noBDPagination
  },
  queryOnPresenterCreated: false
});

export default {
  name: 'NoBdTable',
  components: {
    crudOperation,
    pagination
  },
  mixins: [presenter(crud)],
  dicts: [
    'bdmDeviceType'
  ],
  props: {
    deviceNum: {
      type: String,
      default: ''
    },
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  watch: {
    deviceNum: {
      type: String,
      default: ''
    },
    dialogVisible: {
      handler(val) {
        if (val) {
          this.crud.query.deviceNum = this.deviceNum;
          this.$nextTick(() => {
            crud.toQuery();
          });
        }
      },
      immediate: true
    }
  },
  methods: {
    parseTime,
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    },
    [CRUD.HOOK.afterRefresh]() {
      if (this.crud.data.length) {
        const params = [];
        this.crud.data.forEach((item, index) => {
          params.push({
            latitude: item.latitude,
            longitude: item.longitude,
            id: index
          });
        });
        batchAddr(params).then(res => {
          this.crud.data.forEach((item, index) => {
            res.data.forEach(dItem => {
              if (dItem.id === index) {
                item.locationAddr = dItem.locAddr;
              }
            });
          });
          this.$forceUpdate();
        });
      }
    }
  }
};
</script>
<style scoped lang="less">
.recently-table {
  height: 100%;
}

.detail-container {
  height: 500px;
  margin-bottom: 4px;
}
</style>
