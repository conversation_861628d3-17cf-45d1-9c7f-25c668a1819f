<!--原北斗识别页面, 现修改为入网检测页面, 集成接口检测功能-->
<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="100px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :download="false"
          width="190"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          :cell-style="{'text-align':'center'}"
          :expand-row-keys="expands"
          row-key="id"
          @expand-change="expandChange"
        >
          <el-table-column
            type="expand"
            label="详情"
            width="60px"
            fixed="left"
            :resizable="false"
          >
            <template slot-scope="props">
              <el-card style="padding: 12px 6px;margin: 4px 184px 4px 64px;">
                <el-table
                  :data="props.row.terminalCheckData"
                  class="detail-table"
                >
                  <el-table-column
                    label=""
                    width="130"
                    align="center"
                    fixed="right"
                  >
                    <template slot-scope="scope">
                      <div class="operation">
                        <el-button
                          size="small"
                          type="text"
                          @click="showDetail(scope.row.terminalNo)"
                        >
                          查看北斗识别详情
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="terminalNo"
                    label="序列号"
                    :show-overflow-tooltip="true"
                    :resizable="false"
                  />
                  <el-table-column
                    prop="testResult"
                    label="接口检测结论"
                    width="140"
                    :show-overflow-tooltip="true"
                    :resizable="false"
                  >
                    <template slot-scope="scope">
                      <span>
                        {{ getCheckRes(scope.row.testResult) }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="testResMessage"
                    label="接口检测详情"
                    width="120"
                    :resizable="false"
                  >
                    <template slot-scope="scope">
                      <div
                        v-if="scope.row.testResMessage"
                        class="active-label"
                        @click="showTestResultDialog(scope.row.testResMessage)"
                      >
                        查看详情
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="checkResMessage"
                    label="北斗识别检测结论"
                    min-width="140"
                    :resizable="false"
                  />
                  <el-table-column
                    prop="checkDataCount"
                    label="数据校验总数"
                    :show-overflow-tooltip="true"
                    :resizable="false"
                  />
                  <el-table-column
                    prop="checkBDDataCount"
                    label="北斗定位数据总数"
                    :show-overflow-tooltip="true"
                    :resizable="false"
                  />
                </el-table>
              </el-card>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="180"
            align="center"
            fixed="right"
            style="z-index: 999;"
            :resizable="false"
          >
            <template slot-scope="scope">
              <div class="operation">
                <el-button
                  v-if="Number(scope.row.checkProcess) === 0"
                  size="small"
                  type="text"
                  :loading="startBtnLoading[`${scope.row.id}`]"
                  @click="showProtocolCheckDialog(scope.row)"
                >
                  开始检测
                </el-button>
                <el-button
                  v-if="Number(scope.row.checkProcess) === 2"
                  size="small"
                  type="text"
                  @click="finished(scope.row)"
                >
                  结束检测
                </el-button>
                <el-button
                  v-if="Number(scope.row.checkProcess) === 3"
                  :loading="reportBtnLoading[scope.row.id]"
                  size="small"
                  type="text"
                  @click="downloadReport(scope.row)"
                >
                  下载北斗识别报告
                </el-button>
                <el-button
                  size="small"
                  type="text"
                  @click="refresh(scope.row)"
                >
                  重置
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('terminalType')"
            prop="terminalType"
            label="设备类型"
            width="120"
            :show-overflow-tooltip="true"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>
                {{ getEnumDictLabel('testDeviceType', scope.row.terminalType) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('terminalModel')"
            prop="terminalModel"
            label="设备型号"
            width="130"
            :show-overflow-tooltip="true"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('protocol')"
            prop="protocol"
            label="协议"
            width="120"
            :show-overflow-tooltip="true"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>
                {{ scope.row.terminalCheckData[0] && scope.row.terminalCheckData[0].protocol }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('batchNo')"
            prop="batchNo"
            label="批次"
            width="100"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.batchNo }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('companyName')"
            prop="companyName"
            label="委托企业"
            width="120"
            :show-overflow-tooltip="true"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('checkProcess')"
            prop="checkProcess"
            label="检测进度"
            width="120"
          >
            <template slot-scope="scope">
              <span>
                {{ getEnumDictLabel('checkProcess', scope.row.checkProcess) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('createTime')"
            prop="createTime"
            label="创建时间"
            width="180"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.createTime }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('checkStartTime')"
            prop="checkStartTime"
            label="检测开始时间"
            width="170"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.checkStartTime }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('testResult')"
            prop="testResult"
            label="批次接口检测结论"
            min-width="140"
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ getTestRes(scope.row.testResult) }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('checkResMessage')"
            prop="checkResMessage"
            label="批次北斗识别检测结论"
            min-width="180"
            :show-overflow-tooltip="true"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('reportTime')"
            prop="reportTime"
            width="180"
            label="生成北斗识别报告时间"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.reportTime }}
              </span>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination/>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :modal="false"
      class="dialog"
      title="检测详情"
      width="75vw"
      @closed="closedDetailDialog"
      @open="openDetailDialog"
    >
      <detailPage
        :id="dialogDetailId"
        :dialog-com-visible="dialogComVisible"
        :dict="dict"
      />
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="dialogVisible = false"
        >
          关闭
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="isShowTestResultDialog"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      :modal="false"
      class="dialog"
      title="接口检测详情"
      width="50vw"
    >
      <pre class="test-result-content">{{ testResultDialogDetail }}</pre>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="isShowTestResultDialog = false"
        >
          关闭
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="isShowProtocolDialog"
      :close-on-click-modal="false"
      :modal="false"
      title="选择检测功能"
      width="40vw"
      @close="closeProtocolDialog"
    >
      <div class="protocol-check-list">
        <el-checkbox-group
          v-model="checkedProtocol"
          class="checked-group"
        >
          <el-checkbox
            v-for="item in currentProtocolDict"
            :key="item.value"
            class="checked-item"
            :label="item.value"
            :checked="item.remark === '1'"
          >
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="closeProtocolDialog"
        >
          关闭
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="startProtocolCheck"
        >确认
        </el-button>
      </div>
    </el-dialog>
  </basic-container>

</template>

<script>
import bdTest, { finishedCheck, getReport, startCheck, refreshCheck, startCheckMQTT } from '@/api/bdTest/report.js';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';
import detailPage from './module/detailPage';
import HeadCommon from '@/components/formHead/headCommon.vue';
import { allCompanyName } from '@/api/bdTest/report';

// crud交由presenter持有
const crud = CRUD({
  title: '入网检测',
  optShow: {
    add: false,
    edit: false,
    download: false
  },
  crudMethod: { ...bdTest }
});

export default {
  name: 'BDReport',
  components: {
    crudOperation,
    pagination,
    detailPage,
    HeadCommon
  },
  mixins: [presenter(crud)],
  dicts: [
    'testDeviceType',
    'checkProcess',
    'JttProtocol',
    'MQTTProtocol',
    'bdmDeviceType'
  ],
  data() {
    return {
      firstOpen: true,
      dialogVisible: false,
      dialogDetailId: null,
      expands: [],
      reportBtnLoading: {},
      testResultDialogDetail: '',
      startBtnLoading: {},
      isShowTestResultDialog: false,
      dialogComVisible: true,
      protocolDictEnum: {
        'JT/T-808': 'JttProtocol',
        'MQTT': 'MQTTProtocol'
      },
      currentProtocolDict: null,
      isShowProtocolDialog: false,
      checkedProtocol: [],
      currentRow: null,
      headConfig: {
        item: {
          1: {
            name: '委托企业',
            type: 'select',
            value: 'companyId',
            options: []
          },
          2: {
            name: '设备类型',
            type: 'select',
            value: 'terminalType',
            dictOptions: 'testDeviceType'
          },
          3: {
            name: '设备型号',
            type: 'input',
            value: 'terminalModel'
          },
          4: {
            name: '批次',
            type: 'date',
            value: 'batchNo',
            valueFormat: 'yyyyMMdd'
          },
          5: {
            name: '检测进度',
            type: 'select',
            value: 'checkProcess',
            dictsOptions: 'checkProcess'
          },
          6: {
            name: '检测结论',
            type: 'select',
            value: 'checkResult',
            options: [
              {
                label: '检测未开始或检测中',
                value: 0
              },
              {
                label: '通过',
                value: 1
              },
              {
                label: '不通过',
                value: 2
              }
            ]
          }
        },
        button: {
        }
      }
    };
  },
  created() {
    // 获取委托企业
    this.getCompanyList();
  },
  methods: {
    getCompanyList() {
      allCompanyName().then(res => {
        this.headConfig.item['1'].options = res.data.map(item => ({
          label: item.companyName,
          value: item.id
        }));
      });
    },
    showDetail(terminalNo) {
      this.dialogDetailId = terminalNo;
      this.dialogVisible = true;
    },
    startCheckAll(row) {
      const terminalCheckData = row.terminalCheckData;
      this.$set(this.startBtnLoading, `${row.id}`, true);
      const ids = this.checkedProtocol.map(item => {
        return parseInt(item, 16);
      }).filter(item => item !== 0);
      const promises = terminalCheckData.map(async item => {
        switch (item.protocol) {
        case 'JT/T-808':
          await this.startCheck808(item.terminalNo, ids);
          break;
        case 'MQTT':
          await this.startCheckMQTT(item.terminalNo, this.checkedProtocol);
          break;
        default:
          await this.startCheck808(item.terminalNo, ids);
          break;
        }
      });
      Promise.allSettled(promises).then(() => {
        this.startBtnLoading[`${row.id}`] = false;
        this.crud.refresh();
      }).catch(() => {
        this.startBtnLoading[`${row.id}`] = false;
        this.crud.refresh();
      });
    },
    startProtocolCheck() {
      if (this.checkedProtocol.length === 0) {
        this.$message.error('请至少选择一条协议');
        return false;
      }
      this.$confirm('是否确定开始设备入网检测?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.startCheckAll(this.currentRow);
        this.closeProtocolDialog();
      });
    },
    showProtocolCheckDialog(row) {
      const terminalCheckData = row.terminalCheckData;
      if (terminalCheckData.length === 0) {
        this.$message.error('抽检终端列表为空');
        return false;
      }
      const protocol = terminalCheckData[0].protocol;
      const enumStr = this.protocolDictEnum[protocol];
      this.checkedProtocol = [];
      this.currentProtocolDict = this.dict[enumStr];
      this.isShowProtocolDialog = true;
      this.currentRow = row;
    },
    closeProtocolDialog() {
      this.isShowProtocolDialog = false;
      this.checkedProtocol = [];
      this.currentProtocolDict = [];
      this.currentRow = {};
    },
    /**
     * 开始检测 808协议
     * @param terminalNo
     * @param ids
     * @returns {Promise<boolean|Error>}
     */
    async startCheck808(terminalNo, ids) {
      return await new Promise((resolve, reject) => {
        startCheck(terminalNo, ids).then(res => {
          if (res.code === 200) {
            resolve(true);
          }
          else {
            reject(false);
          }
        }).catch(err => {
          reject(err);
        });
      });
    },
    /**
     * 开始检测 MQTT协议
     * @param terminalNo
     * @param ids
     * @returns {Promise<boolean|Error>}
     */
    async startCheckMQTT(terminalNo, ids) {
      return await new Promise((resolve, reject) => {
        startCheckMQTT(terminalNo, ids).then(res => {
          if (res.code === 200) {
            resolve(true);
          }
          else {
            reject(false);
          }
        }).catch(err => {
          reject(err);
        });
      });
    },
    finished(row) {
      this.$confirm('是否确定结束检测?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        finishedCheck(row.id).then(res => {
          if (res.code === 200) {
            this.crud.refresh();
          }
        });
      });
    },
    downloadReport(row) {
      if (row.reportFile) {
        window.open(row.reportFile);
      }
      else {
        this.$set(this.reportBtnLoading, `${row.id}`, true);
        getReport(row.id).then(res => {
          // 后端设计为数组 预备后续批量下载需求 前端目前只有单独下载
          const {
            success,
            filePath,
            message
          } = res.data[0];
          if (!success) {
            this.$message.error(message || '生成报告失败');
            return false;
          }
          else {
            window.open(filePath);
          }
        }).finally(() => {
          this.reportBtnLoading[`${row.id}`] = false;
        });
      }
    },
    /**
     * 测试用重置检测流程
     * @param row
     */
    refresh(row) {
      refreshCheck(row.id).then(() => {
        // 后端设计为数组 预备后续批量下载需求 前端目前只有单独下载
        this.$message.success('重置成功!');
        Object.keys(this.startBtnLoading).forEach(key => {
          this.startBtnLoading[key] = false;
        });
        this.crud.refresh();
      });
    },
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    },
    getCheckRes(checkRes) {
      if (Number(checkRes) === 0) {
        return '未检测';
      }
      else if (Number(checkRes) === 1) {
        return '通过';
      }
      else if (Number(checkRes) === 2) {
        return '未通过';
      }
      else if (!checkRes) {
        return '';
      }
    },
    getTestRes(testRes) {
      if (testRes === 0) {
        return '未通过';
      }
      else if (testRes === 1) {
        return '通过';
      }
      else {
        return '';
      }
    },
    expandChange(_expandedRows, expanded) {
      this.expands = expanded.map(item => item.id);
    },
    showTestResultDialog(testResult) {
      this.testResultDialogDetail = JSON.parse(testResult);
      this.isShowTestResultDialog = true;
    },
    closedDetailDialog() {
      this.dialogComVisible = false;
    },
    openDetailDialog() {
      this.dialogComVisible = true;
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

.detail-table {
  width: calc(100 - 120px);
  ::v-deep .el-table__header-wrapper .el-table__cell {
    background-color: #f2f7ff !important;
  }
}

.test-result-content {
  height: 400px;
  overflow-y: auto;
  font-size: 16px;
}

.checked-group {
  display: flex;
  flex-wrap: wrap;
}

.checked-item {
  width: 45%;
}
</style>
