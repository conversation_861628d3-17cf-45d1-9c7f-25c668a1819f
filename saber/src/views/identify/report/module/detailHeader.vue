<template>
  <div
    v-if="crud.props.searchToggle"
    class="xh-custom-header"
  >
    <el-row
      :gutter="20"
      style="width: 100%;margin: 0;"
    >
      <el-col :span="6">
        <span style="width: 130px;">定位数据识别结果:</span>
        <xh-select
          v-model="query.checkRes"
          clearable
          size="small"
          placeholder="请选择"
        >
          <el-option
            v-for="item in checkResArr"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </xh-select>
      </el-col>
      <el-col :span="12">
        <span>定位时间:</span>
        <el-date-picker
          v-model="query.time"
          type="datetimerange"
          range-separator="至"
          size="small"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="开始时间"
          end-placeholder="结束时间" />
      </el-col>
      <el-col :span="6">
        <rrOperation
          :crud="crud"
          :clear-query="false"
          @handleClear="handleClear"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import CRUD, { header } from '@/components/Crud/crud';
import rrOperation from '@/components/Crud/RR.operation';

export default {
  components: {
    rrOperation,
  },
  mixins: [header()],
  props: {
    dict: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: true,
      moreSearch: false,
      checkResArr: [
        {
          label: '北斗定位',
          value: 1,
        },
        {
          label: '疑似非北斗定位',
          value: 0
        }
      ]
    };
  },
  created() {

  },
  methods: {
    [CRUD.HOOK.beforeRefresh]() {

    },
    handleClear() {

    },
  }
};
</script>
<style lang="less" scoped>
.xh-custom-header {
  /deep/ .el-range-separator {
    width: 30px;
  }
}
</style>
