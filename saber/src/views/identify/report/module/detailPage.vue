<template>
  <div
    class="bd-report-detail"
    style="padding: 0;"
  >

    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <eHeader
          :dict="dict"
        />
      </div>
      <div class="detail-container">
        <crudOperation :download="false"/>
        <el-table
          ref="table"
          v-loading="crud.loading"
          :data="crud.data"
          style="width: 100%;height: calc(100% - 47px);"
          :cell-style="{'text-align':'center'}"
        >
          <el-table-column
            v-if="columns.visible('terminalNo')"
            prop="terminalNo"
            label="设备编号"
            width="180"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('terminalType')"
            prop="terminalType"
            label="设备类型"
            width="160"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>
                {{ getEnumDictLabel('testDeviceType', scope.row.terminalType) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('terminalModel')"
            prop="terminalModel"
            label="设备型号"
            width="180"
            :show-overflow-tooltip="true"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('locTimeStr')"
            prop="locTimeStr"
            label="定位时间"
            width="180"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.locTimeStr }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('checkRes')"
            prop="checkRes"
            label="检测结果"
            width="180"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>
                {{ Number(scope.row.checkRes) === 1 ? '北斗定位' : '疑似非北斗定位' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('checkResMessage')"
            prop="checkResMessage"
            label="结果描述"
            :show-overflow-tooltip="true"
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination/>
    </div>
  </div>
</template>

<script>
import { detailPagination } from '@/api/bdTest/report.js';
import eHeader from './detailHeader';
import CRUD, { header, presenter } from '@/components/Crud/crud';
import pagination from '@/components/Crud/Pagination';
import crudOperation from '@/components/Crud/CRUD.operation.vue';
// crud交由presenter持有
const crud = CRUD({
  title: '北斗识别检测详情',
  optShow: {
    add: false,
    edit: false,
    export: false
  },
  crudMethod: {
    pagination: detailPagination
  },
  queryOnPresenterCreated: false
});

export default {
  name: 'BDReportDetail',
  components: {
    crudOperation,
    eHeader,
    pagination
  },
  mixins: [
    presenter(crud),
    header()
  ],
  props: {
    id: {
      type: String,
      default: ''
    },
    dialogComVisible: {
      type: Boolean,
      default: false
    },
    dict: {
      type: Object,
      default: ()=> ({})
    }
  },
  data() {
    return {
      terminalNo: '',
      permission: {
        add: ['admin'],
        edit: ['admin'],
        del: ['admin']
      }
    };
  },
  watch: {
    dialogComVisible: {
      handler(newVal) {
        if(newVal && this.id) {
          this.crud.resetQuery()
          this.crud.query.terminalNo = this.id;
          crud.toQuery();
        } else {
          this.crud.data = [];
        }
      },
      immediate: true
    }
  },
  methods: {
    [CRUD.HOOK.beforeRefresh](hook, item) {
      this.crud.query.terminalNo = this.id;
      if (this.crud.query?.time?.length > 0) {
        this.crud.query.startTime = new Date(this.crud.query.time[0]).getTime() / 1000;
        this.crud.query.endTime = new Date(this.crud.query.time[1]).getTime() / 1000;
      }
      else {
        this.crud.query.startTime = undefined;
        this.crud.query.endTime = undefined;
      }
      console.log(this.crud.query);
    },
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        console.log(this.dict.dict[dictName][value].label);
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    }
  }
};
</script>

<style lang="less" scoped>

.detail-table {
  ::v-deep .el-table__header-wrapper .el-table__cell {
    background-color: #f2f7ff !important;
  }
}

.detail-container {
  height: 400px;
  margin-bottom: 4px;
}

.xh-container {
  height: 100%;
}
</style>
