<template>
  <div id="singleBdMonitor">
    <div class="control-view">
      <div class="control-item">
        <el-checkbox-group
          v-model="typeList"
          class="type-group"
        >
          <el-checkbox label="GEO">
            <span style="color: rgb(253,127,144, 255)">GEO</span>
          </el-checkbox>
          <el-checkbox label="MEO">
            <span style="color: rgb(239,209,66)">MEO</span>
          </el-checkbox>
          <el-checkbox label="IGSO">
            <span style="color: rgb(47,196,197, 255)">IGSO</span>
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="control-item">
        <el-checkbox-group
          v-model="stateList"
          class="type-group"
        >
          <!--          <el-checkbox :label="1">全部卫星</el-checkbox>-->
          <el-checkbox :label="2"><span class="state1">可视卫星</span></el-checkbox>
          <el-checkbox :label="3"><span class="state2">定位卫星</span></el-checkbox>
          <!--          <el-checkbox :label="4">屏蔽其他卫星</el-checkbox>-->
        </el-checkbox-group>
      </div>
      <div class="control-item check-item">
        <el-checkbox
          v-model="isShowTrack"
        >显示轨道
        </el-checkbox>
        <el-checkbox
          v-model="isShowText"
        >卫星编号
        </el-checkbox>
      </div>
      <div class="control-item">
        <el-button
          size="mini"
          type="primary"
          @click="resetControl"
        >重置
        </el-button>
      </div>
    </div>
    <div class="timeline-control">
      <el-tooltip content="恢复默认">
        <div
          class="timeline-btn"
          @click="resetTimeline"
        >
          <i class="el-icon-refresh-right"/>
        </div>
      </el-tooltip>
    </div>
    <div
      class="fullscreen-btn"
      :style="{right: showInfoBox ? 'calc(45% + 20px)' : '22px'}"
      @click="full"
    >
      <i :class="[fullscreen ? 'icon-tuichuquanping' :'icon-quanping']"/>
    </div>
    <div id="canvas-frame"/>
    <div
      class="collapse-vertical"
    >
      <span
        class="collapse-btn"
        @click="showInfoBox = !showInfoBox"
      >
        <i :class="`el-icon-arrow-${showInfoBox ? 'right':'left'}`"/>
      </span>
    </div>
    <div
      v-show="showInfoBox"
      class="info-content"
    >
      <div class="info-text-title">
        <div class="info-title-satellite">
          <div class="page-title">北斗识别</div>
        </div>
        <div class="info-title-locality">
          <div class="search-content">
            <el-input
              v-model="deviceNum"
              class="input-wrap"
              placeholder="请输入赋码编号"
            />
            <el-button
              :loading="submitLoading"
              type="primary"
              class="submit-btn"
              icon="el-icon-search"
              @click="searchData"
            >
              查 询
            </el-button>
            <el-tooltip content="定位到终端">
              <div class="location-btn" @click="flyTo">
                <i class="el-icon-location-information"></i>
              </div>
            </el-tooltip>
          </div>
          <div class="location-info">
            <el-tooltip :content="locationAddr">
              <div class="location-addr">当前位置：{{ locationAddr || '-' }}</div>
            </el-tooltip>
            <div class="location-info-con">
              <div style="width: 50%;">经度<span class="yellow-text"> {{ longitude || '-' }}</span></div>
              <div style="width: 50%;">纬度<span class="yellow-text"> {{ latitude || '-' }}</span></div>
            </div>
          </div>
        </div>
      </div>
      <div class="info-satellite-a">
        <div class="satellite-room">
          <span class="satellite-title">
            卫星信息
          </span>
          <div class="satellite-box">
            <template v-if="satelliteList.length">
              <SatelliteIcon
                v-for="(item , i) in satelliteList"
                :id="item.id"
                :key="i"
                :flag="item.flag"
                :type="item.type"
                :is-select="selectPickId ? selectPickId.split('/')[1] === item.id : false"
                :select-pick-id="selectPickId"
                @selectSatellite="selectSatellite"
              />
            </template>
            <el-empty
              v-else
              image-size="0"
              style="width: 100%;"
            />
          </div>
        </div>
      </div>
      <div class="info-chart-gsv">
        <span class="satellite-title">信噪比SNR(GSV)</span>
        <div
          ref="glnChart"
          class="charts-body"
        />
      </div>
      <div class="table-content">
        <el-table
          ref="table"
          :header-cell-style="{'text-align':'center'}"
          :data="tableData"
          :cell-style="{'text-align':'center'}"
          style="height: 100%;overflow-x:hidden;"
          :row-class-name="tableRowClassName"
          empty-text=" "
        >
          <!-- 车牌号码 -->
          <el-table-column
            label="赋码编号"
            prop="deviceNum"
            width="180px"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            label="定位时间"
            prop="locTime"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.locTime }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="终端类型"
            min-width="100px"
            prop="terminalType"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            label="所属机构"
            prop="deptName"
            min-width="100px"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-table-column
            label="识别结果"
            prop="checkResName"
            min-width="100px"
            show-overflow-tooltip
            :resizable="false"
          />
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import SatelliteIcon from './module/satelliteIcon';
import { checkDeviceOnlineState, getAuthCodeForBD, getBDSatelliteData } from '@/api/center/beidouVerification';
import { getSystemParam } from '@/api/user';
import ReconnectingWebSocket from '@/utils/rabbitmq/RealTimeProtocol/ReconnectingWebsocket';
import { vehicleAddress } from '@/api/monitoring/info';
import { mapGetters } from 'vuex';
import dayjs from 'dayjs';

const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJmNGVkOWJiMC1lM2RlLTRmYjEtYWZkZi02MjgyYTVlNTFlZTUiLCJpZCI6MjIxOTQ1LCJpYXQiOjE3MTgyNjMxMjd9.PGG_sZU6IaH3X9trYOtKfLuDLGRcZsU2UGZzPi6PG6s';
const Cesium = window.Cesium;
Cesium.Ion.defaultAccessToken = token;
Cesium.Camera.DEFAULT_VIEW_RECTANGLE = Cesium.Rectangle.fromDegrees(90, -20, 110, 90);
let __singleBDViewer__ = null;
let __czmlDataSourcePromise__ = null;

const GEO_IDS = [
  5,
  2,
  60,
  3,
  59,
  1,
  62,
  4
];
const MEO_IDS = [
  11,
  12,
  14,
  19,
  20,
  21,
  22,
  23,
  24,
  25,
  26,
  27,
  28,
  29,
  30,
  32,
  33,
  34,
  35,
  36,
  37,
  41,
  42,
  43,
  44,
  45,
  46,
  48,
  50
];
const IGSO_IDS = [
  9,
  6,
  16,
  39,
  13,
  8,
  38,
  7,
  40,
  10
];

const imgEnum = {
  MEO: 'hdmeo.png',
  GEO: 'hdgeo.png',
  IGSO: 'hdigso.png'
};
const showPathId = [
  '23',
  '14',
  '28',
  '13',
  '10',
  '03',
  '09'
];
const defaultCZMLHeader = (headerParams) => {
  return {
    id: 'document',
    name: 'BDSatellite',
    version: '1.0',
    clock: {
      interval: headerParams.interval,
      currentTime: headerParams.currentTime,
      multiplier: 1,
      range: 'LOOP_STOP',
      step: 'SYSTEM_CLOCK_MULTIPLIER'
    }
  };
};
const defaultCZMLDes = () => {
  return {
    id: '9927edc4-e87a-4e1f-9b8b-0bfb3b05b22722',
    name: 'Accesses',
    description: 'List of BD'
  };
};
const dataStyle = {
  MEO: {
    fillColor: [
      239,
      209,
      66,
      255
    ],
    solidColor: [
      195,
      176,
      100,
      255
    ]
  },
  IGSO: {
    fillColor: [
      47,
      196,
      197,
      255
    ],
    solidColor: [
      35,
      124,
      125,
      255
    ]
  },
  GEO: {
    fillColor: [
      253,
      127,
      144,
      255
    ],
    solidColor: [
      223,
      146,
      158,
      255
    ]
  }
};

export default {
  name: 'SingleBdMonitor',
  components: {
    SatelliteIcon
  },
  data() {
    return {
      chartBD: null,
      satelliteList: [],
      deviceNum: '',
      defaultDeviceNum: '', // 默认请求的赋码编号 用来展示初始化数据
      tableData: [],
      checkResObj: {
        0: '疑似非单北斗定位',
        1: '北斗定位'
      },
      longitude: '',
      latitude: '',
      locationAddr: '',
      submitLoading: false,
      fullscreen: false,
      websock: null,
      czml: null,
      czmlDataSourcePromise: null,
      showInfoBox: false,
      isShowTrack: true,
      isShowText: true,
      stateList: [
        1,
        2,
        3
      ],
      typeList: [
        'GEO',
        'MEO',
        'IGSO'
      ],
      visibleIds: [],
      positionIds: [],
      selectPickId: null,
      selectPickFlag: false,
      GSVList: [],
      layerType: 1,
      devicePosition: []
    };
  },
  computed: {
    ...mapGetters(['removeTags'])
  },
  watch: {
    isShowTrack(val) {
      this.showTrackChange(val);
    },
    isShowText(val) {
      this.showTextChange(val);
    },
    stateList: {
      handler(val) {
        this.stateListChange(val);
      },
      deep: true
    },
    typeList: {
      handler(val) {
        this.typeListChange(val);
      },
      deep: true
    },
    visibleIds: {
      handler() {
        this.stateListChange(this.stateList);
      },
      deep: true
    },
    positionIds: {
      handler() {
        this.stateListChange(this.stateList);
      },
      deep: true
    },
    selectPickId: {
      handler(val) {
        this.highlightBar(val);
      }
    },
    showInfoBox(val) {
      if (val && !this.chartBD) {
        this.$nextTick(() => {
          this.initChart();
        });
      }
    }
  },
  activated() {
    // this.getDefaultDeviceNum();
    const path = this.$route.fullPath;
    const removeIndex = this.removeTags.indexOf(path);
    if (removeIndex !== -1) {
      this.removeTags.splice(removeIndex, 1);
      this.$store.commit('SET_REMOVE_TAGS', this.removeTags);
      this.deviceNum = '';
      this.closeWebsocket();
      this.devicePosition = [];
      this.chartBD.clear();
      this.satelliteList = [];
      this.showInfoBox = false;
      __singleBDViewer__?.entities?.removeById?.('deviceObjId');
    }
  },
  async mounted() {
    await this.initBDSatelliteData();
    this.$nextTick(() => {
      window.addEventListener('resize', this.resize);
      window.addEventListener('fullscreenchange', this.fullscreenchange);
    });
  },
  beforeDestroy() {
    this.chartBD = null;
  },
  methods: {
    flyTo(){
      if (this.devicePosition.length === 0) {
        return false;
      }
      const position = Cesium.Cartesian3.fromDegrees(this.devicePosition[0], this.devicePosition[1], 1800);
      __singleBDViewer__.camera.flyTo({
        destination: position
      });
    },
    resetTimeline() {
      __singleBDViewer__.clockViewModel.multiplier = 1;
      const day2 = new Date(new Date().getTime() - 40 * 3600 * 1000);
      __singleBDViewer__.clock.currentTime = Cesium.JulianDate.fromDate(day2);
      __singleBDViewer__.clock.shouldAnimate = true;
    },
    resetControl() {
      this.isShowTrack = true;
      this.isShowText = true;
      this.stateList = [
        // 1, // 保留字段 全部卫星
        2, // 显示可见星
        3 // 显示可用星
      ];
      this.typeList = [
        'GEO',
        'MEO',
        'IGSO'
      ];
    },
    typeListChange(typeList) {
      __czmlDataSourcePromise__.then(czmlData => {
        czmlData.entities.values.forEach(item => {
          const idNum = item.id.split('/')[1];
          const isVisible = this.visibleIds.includes(idNum) && this.stateList.includes(2);
          const isPosition = this.positionIds.includes(idNum) && this.stateList.includes(3);
          const realShow = isVisible || isPosition;
          if (item.id.includes('MEO')) {
            item.billboard.show = realShow ? true : typeList.includes('MEO');
            if (this.isShowText) {
              item.label.show = realShow ? true : typeList.includes('MEO');
            }
          }
          if (item.id.includes('GEO')) {
            item.billboard.show = realShow ? true : typeList.includes('GEO');
            if (this.isShowText) {
              item.label.show = realShow ? true : typeList.includes('GEO');
            }
          }
          if (item.id.includes('IGSO')) {
            item.billboard.show = realShow ? true : typeList.includes('IGSO');
            if (this.isShowText) {
              item.label.show = realShow ? true : typeList.includes('IGSO');
            }
          }
          // if (typeList.length !== 3) {
          //   this.stateList = [];
          // }
          // else {
          //   // this.stateList = [1, 2, 3];
          // }
        });
      });
    },
    stateListChange(stateList) {
      __czmlDataSourcePromise__.then(czmlData => {
        czmlData.entities.values.forEach(item => {
          if (this.isSatellite(item.id)) {
            const dataSource = __singleBDViewer__.dataSources.get(0);
            const entity = dataSource.entities.getById(item.id);
            const type = item.id.split('/')[0];
            const id = item.id.split('/')[1];
            const {
              fillColor
            } = dataStyle[type];
            // 重置所有卫星样式
            entity.label.outlineColor = new Cesium.Color.fromCssColorString('#000000');
            entity.label.fillColor = new Cesium.Color.fromCssColorString(`rgba(${fillColor[0]},${fillColor[1]}, ${fillColor[2]}, 1)`);
            entity.label.outlineWidth = 1;
            if (this.isShowText) {
              entity.label.show = true;
            }
            if (!this.typeList.includes(type)) {
              entity.billboard.show = false;
              entity.label.show = false;
            }
            if (stateList.includes(2)) {
              if (this.visibleIds.includes(id)) {
                // 设置可见星高亮
                entity.label.outlineColor = new Cesium.Color.fromCssColorString('#fad900');
                entity.label.fillColor = new Cesium.Color.fromCssColorString('#ffffff');
                entity.label.outlineWidth = 2;
                entity.billboard.show = true;
                if (this.isShowText) {
                  entity.label.show = true;
                }
              }
            }
            if (stateList.includes(3)) {
              if (this.positionIds.includes(id)) {
                // 设置可用星高亮
                entity.label.outlineColor = new Cesium.Color.fromCssColorString('#05daa1');
                entity.label.fillColor = new Cesium.Color.fromCssColorString('#ffffff');
                entity.label.outlineWidth = 2;
                entity.billboard.show = true;
                if (this.isShowText) {
                  entity.label.show = true;
                }
              }
            }
          }
        });
      });
    },
    showTrackChange(isShow) {
      __czmlDataSourcePromise__.then(czmlData => {
        czmlData.entities.values.forEach(item => {
          if (this.isSatellite(item.id)) {
            if (item.path && showPathId.includes(item.id.split('/')[1])) {
              item.path.show = isShow;
            }
          }
        });
      });
    },
    showTextChange(isShow) {
      __czmlDataSourcePromise__.then(czmlData => {
        czmlData.entities.values.forEach(item => {
          if (this.isSatellite(item.id)) {
            if (item.label) {
              item.label.show = isShow;
            }
          }
        });
      });
    },
    isSatellite(id) {
      return id.includes('MEO') || id.includes('GEO') || id.includes('IGSO');
    },
    getInterval(leadTime) {
      const startInterval = leadTime[0].interval.split('/')[0];
      const endInterval = leadTime[leadTime.length - 1].interval.split('/')[1];
      return `${startInterval}/${endInterval}`;
    },
    createSatelliteList(data) {
      const {
        type,
        id,
        cartesian,
        leadTime
      } = data;
      const {
        fillColor,
        solidColor
      } = dataStyle[type];
      const interval = this.getInterval(data.leadTime);
      const epoch = interval.split('/')[0];
      return {
        id: `${type}/${id}`,
        name: `${type}/${id}`,
        availability: interval,
        description: '',
        billboard: {
          eyeOffset: {
            cartesian: [
              0,
              0,
              0
            ]
          },
          horizontalOrigin: 'CENTER',
          image: require(`@/assets/images/bd/${imgEnum[type]}`),
          pixelOffset: {
            cartesian2: [
              0,
              0
            ]
          },
          scale: 0.045,
          show: true,
          verticalOrigin: 'CENTER'
        },
        label: {
          fillColor: {
            rgba: fillColor
          },
          font: '10pt Microsoft YaHei',
          horizontalOrigin: 'LEFT',
          outlineColor: {
            rgba: [
              0,
              0,
              0,
              255
            ]
          },
          outlineWidth: 1,
          pixelOffset: {
            cartesian2: [
              14,
              0
            ]
          },
          show: true,
          style: 'FILL_AND_OUTLINE',
          text: ` ${type}-${id}`,
          verticalOrigin: 'CENTER'
        },
        path: {
          show: [
            {
              interval,
              boolean: showPathId.includes(id)
            }
          ],
          width: 2,
          material: {
            solidColor: {
              color: {
                rgba: solidColor
              }
            }
          },
          resolution: 120,
          // leadTime: type === 'MEO' ? 3600 * 12.3 : 3600 * 24.2,
          // trailTime: type === 'MEO' ? 3600 * 12.3 : 3600 * 24.2,
          leadTime: this.getLeadTime(leadTime, type),
          trailTime: this.getTrailTime(leadTime, type)
        },
        position: {
          interpolationAlgorithm: 'LAGRANGE',
          interpolationDegree: 5,
          referenceFrame: 'INERTIAL',
          epoch, // 开始时间
          cartesian: cartesian.flat().map((item, _index) => {
            return Number(item);
            // if (index % 4 !== 0 && index !== 0) {
            //   return -Number(item);
            // }
            // else {
            //   return Number(item);
            // }
          })
        }
      };
    },
    getLeadTime(leadTime, type) {
      const n = type === 'MEO' ? 3600 * 14 : 3600 * 24.2;
      const start = leadTime[0].interval.split('/')[0];
      const end = leadTime[leadTime.length - 1].interval.split('/')[1];
      return [
        {
          interval: `${start}/${end}`,
          epoch: `${start}`,
          number: [
            0,
            n,
            n,
            0
          ]
        }
      ];
    },
    getTrailTime(leadTime, type) {
      const n = type === 'MEO' ? 3600 * 14 : 3600 * 24.6;
      const start = leadTime[0].interval.split('/')[0];
      const end = leadTime[leadTime.length - 1].interval.split('/')[1];
      return [
        {
          interval: `${start}/${end}`,
          epoch: `${start}`,
          number: [
            0,
            0,
            n,
            n
          ]
        }
      ];
    },
    createCZMLObj(sourceData) {
      const interval = this.getInterval(sourceData[0].leadTime);
      // 减去2天 修正UTC时间8小时
      const date = (new Date().getTime() - 40 * 3600 * 1000);
      const headerParams = {
        interval,
        currentTime: new Date(date).toISOString()
      };
      const czmlArr = [
        defaultCZMLHeader(headerParams),
        defaultCZMLDes()
      ];
      sourceData.forEach(item => {
        if (item.cartesian?.length > 0 && item.leadTime?.length > 0) {
          czmlArr.push(this.createSatelliteList(item));
        }
      });
      console.log('-> czmlArr', czmlArr);
      return czmlArr;
    },
    async initBDSatelliteData() {
      const { data } = await getBDSatelliteData();
      const parseData = data.data;
      this.initCesium();
      this.czml = this.createCZMLObj(parseData);
      this.satellite();
    },
    initCesium() {
      Cesium.Camera.DEFAULT_VIEW_FACTOR = 6.5;
      __singleBDViewer__ = new Cesium.Viewer('canvas-frame', {
        shouldAnimate: true,
        baseLayerPicker: false,
        timeline: true,
        geocoder: false,
        infoBox: false,
        navigationHelpButton: false,
        animation: true,
        fullscreenButton: false,
        multiplier: 1,
        SkyAtmosphere: true,
        cesiumWidget: false,
        selectionIndicator: true,
        imageryProvider: new Cesium.UrlTemplateImageryProvider({
          maximumLevel: 18,//最大缩放级别
          url: 'https://webst02.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
          style: 'default',
          format: 'image/png',
          show: true
        }),
        contextOptions: {
          webgl: {
            alpha: true
          }
        }
      });
      __singleBDViewer__.timeline.makeLabel = (date) => {
        return dayjs(date).add(0, 'day').subtract(8, 'hour').format('MM-DD HH:mm');
      };
      __singleBDViewer__.animation.viewModel.dateFormatter = (date) => {
        return dayjs(date).add(0, 'day').format('YYYY-MM-DD');
      };
      __singleBDViewer__.animation.viewModel.timeFormatter = (date) => {
        return dayjs(date).add(0, 'day').subtract(8, 'hour').format('HH:mm:ss');
      };
      __singleBDViewer__.scene.skyBox.show = false;
      __singleBDViewer__.scene.backgroundColor = new Cesium.Color(0, 0, 0, 0);
      // __singleBDViewer__.scene.skyBox.destroy();
      // __singleBDViewer__.scene.skyBox = undefined;
      __singleBDViewer__.scene.sun.destroy();
      __singleBDViewer__.scene.sun = undefined;
      __singleBDViewer__.scene.moon.destroy();
      __singleBDViewer__.scene.moon = undefined;
      __singleBDViewer__.scene.skyAtmosphere.destroy();
      __singleBDViewer__.scene.skyAtmosphere = undefined;
      let handler = new Cesium.ScreenSpaceEventHandler(__singleBDViewer__.scene.canvas);//处理用户输入事件
      handler.setInputAction(this.selectPick, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      // 高德瓦片
      let tdtLayer = new Cesium.UrlTemplateImageryProvider({
        url: 'https://webst02.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
        minimumLevel: 3,
        maximumLevel: 25
      });
      __singleBDViewer__.camera.changed.addEventListener(() => {
        //视角改动监控
        const currentMagnitude = __singleBDViewer__.camera.getMagnitude();
        let layer = __singleBDViewer__.imageryLayers;
        if (currentMagnitude > 10000000) {
          if (this.layerType === 1) {
            return false;
          }
          this.layerType = 1;
          const newImageryProvider = new Cesium.UrlTemplateImageryProvider({
            url: 'https://webst02.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
            minimumLevel: 3,
            maximumLevel: 25
          });
          layer.addImageryProvider(newImageryProvider);
        }
        else {
          if (this.layerType === 2) {
            return false;
          }
          this.layerType = 2;
          const newImageryProvider = new Cesium.UrlTemplateImageryProvider({
            // url: 'http://wprd04.is.autonavi.com/appmaptile?lang=zh_cn&size=1&style=7&x={x}&y={y}&z={z}',
            url: 'https://webrd04.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=7&x={x}&y={y}&z={z}',
            minimumLevel: 3,
            maximumLevel: 25
          });
          layer.addImageryProvider(newImageryProvider);
        }
      });

      let layer = __singleBDViewer__.imageryLayers;
      layer.removeAll();
      layer.addImageryProvider(tdtLayer);
      // 测试
      // this.$nextTick(() => {
      //   this.setDeviceObj({
      //     longitude: 116.397428,
      //     latitude: 39.90923,
      //     deviceNum: '123456'
      //   });
      // });
    },
    selectPick(event) {
      let pick = __singleBDViewer__.scene.pick(event.position);
      this.selectPickFlag = true;
      if (Cesium.defined(pick)) {
        this.selectPickId = pick.id.id;
      }
      else {
        this.selectPickId = null;
      }
      this.$nextTick(() => {
        setTimeout(() => {
          this.selectPickFlag = false;
        }, 200);
      });
    },
    selectPickForId(id) {
      if (this.selectPickFlag) {
        return false;
      }
      const dataSource = __singleBDViewer__.dataSources.get(0);
      __singleBDViewer__._selectedEntity = dataSource.entities.getById(id);
    },
    selectSatellite(id) {
      if (id === null) {
        __singleBDViewer__._selectedEntity = undefined;
        this.selectPickId = null;
      }
      else {
        __czmlDataSourcePromise__.then(czmlData => {
          czmlData.entities.values.forEach(item => {
            if (this.isSatellite(item.id) && item.id.split('/')[1] === id) {
              this.selectPickId = item.id;
              this.selectPickForId(item.id);
            }
          });
        });
      }
    },
    highlightBar(selectPickId) {
      if (!this.chartBD) {
        return false;
      }
      const index = [];
      this.GSVList.forEach((val, idx) => {
        if (val.id === Number(selectPickId?.split('/')?.[1])) {
          index.push(idx);
        }
      });
      if (selectPickId === null || index.length === 0) {
        this.chartBD.setOption({
          xAxis: {
            axisLabel: {
              color: '#ffffff'
            }
          }
        });
      }
      else {
        this.chartBD.setOption({
          xAxis: {
            axisLabel: {
              color: function (_item, idx) {
                if (index.includes(idx)) {
                  return '#22ee22';
                }
                return '#ffffff';
              }
            }
          }
        });
      }
    },
    // 绘制轨道
    satellite() {
      __singleBDViewer__.dataSources.add(
        __czmlDataSourcePromise__ = Cesium.CzmlDataSource.load(this.czml)
      );
    },
    resize() {
      this?.chartBD?.resize();
    },
    fullscreenchange() {
      this.fullscreen = document.fullscreenElement;
    },
    // getDefaultDeviceNum() {
    //   getSystemParam('bdCheckInitDeviceNum').then(res => {
    //     this.deviceNum = res.data.data;
    //     this.initWebSocket(this.deviceNum);
    //   });
    // },
    /**
     * @name: 搜索赋码编号定位
     * @return {*}
     */
    searchData() {
      if (!this.deviceNum.trim()) {
        this.$message.error('请输入赋码编号');
        return false;
      }
      this.initWebSocket(this.deviceNum, true);
    },
    tableRowClassName({ row }) {
      if (row.checkRes === 1) {
        return 'green-row';
      }
      else {
        return 'red-row';
      }
    },
    /**
     * 单终端北斗识别ws
     * @param deviceNum
     * @param isShowMessage
     */
    initWebSocket(deviceNum, isShowMessage) {
      this.closeWebsocket();
      this.submitLoading = true;
      checkDeviceOnlineState(deviceNum).then(res => {
        if (res.code !== 200) {
          this.$message.closeAll();
          this.$message.error(res.msg);
          return false;
        }
        if (res.data?.onlineState === '1') {
          getAuthCodeForBD().then(res => {
            const socketCode = res.data;
            getSystemParam('websocketBdCheck').then(res => {
              const wsLocation = res.data.data;
              const protocol = window.location.origin.indexOf('https') !== -1 ? 'wss://' : 'ws://';
              const wsUrl = `${protocol}${wsLocation}/ws/bdCheck/pushSingle/${socketCode}/${deviceNum}`;
              this.websock = new ReconnectingWebSocket(wsUrl);
              this.websock.onopen = () => {
                this.resetData();
                if (deviceNum && isShowMessage) {
                  this.$message.success('连接成功');
                }
                this.submitLoading = false;
              };
              this.websock.onmessage = (e) => {
                const data = JSON.parse(e.data);
                console.log('-> 北斗识别单个终端接收数据', data);
                const newRow = {
                  deviceNum: data.deviceNum, //赋码编号
                  locTime: data.locTime.split(' ')[1], // 定位时间
                  terminalType: data.terminalType, //终端类型
                  deptName: data.deptName, //所属机构
                  checkRes: Number(data.checkRes), //1:北斗定位   0:疑似非北斗定位
                  checkResName: this.checkResObj[data.checkRes],
                  longitude: data.longitude, //经度
                  latitude: data.latitude, //纬度
                  checkResMessage: data.checkResMessage //结果描述
                };
                this.devicePosition = [data.longitude, data.latitude];
                const visibleIds = [];
                const positionIds = [];
                data.satellites.map(item => {
                  if (item.flag === 0) {
                    visibleIds.push(`${item.id}`.padStart(2, '0'));
                  }
                  else {
                    positionIds.push(`${item.id}`.padStart(2, '0'));
                  }
                });
                this.visibleIds = visibleIds;
                this.positionIds = positionIds;
                if (deviceNum) {
                  this.setTableData(newRow);
                  this.setDeviceObj(newRow);
                }
                this.setLocationInfo(data.longitude, data.latitude);
                const satellites = data.satellites.filter(item => {
                  return ![
                    31,
                    56,
                    57,
                    58,
                    61,
                    62,
                    48,
                    50,
                    47,
                    49
                  ].includes(item.id);
                });
                satellites.sort((a, b) => {
                  return a.id - b.id;
                });
                const uniqueArr = satellites.filter((item, index, self) => {
                  return index === self.findIndex(obj => obj.id === item.id);
                });
                const addTypeArr = uniqueArr.map(item => {
                  return {
                    ...item,
                    type: this.getType(item.id)
                  };
                });
                this.setSingleInfo(Number(data.checkRes), addTypeArr);
                this.updateGSV(satellites);
                this.GSVList = satellites;
              };
              this.websock.onclose = (e) => {
                console.log('->bd ws onclose', e);
                if (e.code === 1006) {
                  this.$message.closeAll();
                  this.$message.error('赋码号不存在或终端不在线');
                }
              };
              this.websock.onerror = (e) => {
                console.error('bd ws onerror', e);
                this.submitLoading = false;
              };
              this.$once('hook:deactivated', () => {
                this.closeWebsocket();
              });
            });
          });
        }
        else {
          this.$message.closeAll();
          this.$message.error('终端不在线');
          this.submitLoading = false;
        }
      }).catch(() => {
        this.submitLoading = false;
      });
    },
    getType(id) {
      if (GEO_IDS.includes(id)) {
        return 'GEO';
      }
      if (MEO_IDS.includes(id)) {
        return 'MEO';
      }
      if (IGSO_IDS.includes(id)) {
        return 'IGSO';
      }
    },
    /**
     * 设置当前位置
     * @param longitude
     * @param latitude
     */
    setLocationInfo(longitude, latitude) {
      // 每10条解析一次地址
      if (this.tableData.length % 10 === 0 || this.tableData.length === 1) {
        this.longitude = longitude;
        this.latitude = latitude;
        vehicleAddress({
          lon: Number(longitude),
          lat: Number(latitude)
        }).then(res => {
          if (res.code === 200) {
            this.locationAddr = res.data;
          }
        });
      }
    },
    /**
     * 设置卫星信息
     * @param checkRes 1:北斗定位   0:疑似非北斗定位
     * @param satellites
     */
    setSingleInfo(checkRes, satellites) {
      // 只显示北斗定位的数据的卫星信息和信噪比
      if (checkRes === 1) {
        this.satelliteList = satellites.map(item => {
          return {
            ...item,
            id: `${item.id}`.padStart(2, '0')
          };
        });
      }
      else {
        this.satelliteList = [];
      }
    },
    resetData() {
      this.tableData = [];
    },
    setTableData(newRow) {
      let tbody = null;
      this.tableData.unshift(newRow);
      tbody = this.$refs.table.$el.querySelector('.el-table__body-wrapper tbody');
      if (this.tableData.length > 500) {
        this.tableData = this.tableData.slice(0, 400);
      }
      this.$nextTick(() => {
        const rows = tbody.querySelectorAll('tr');
        const firstRow = rows[0];
        firstRow.style.animation = 'fadeInLeft 0.8s ease-in-out';
        firstRow.addEventListener('animationend', () => {
          firstRow.style.animation = '';
        });
      });
    },
    setDeviceObj(data) {
      const entityObj = __singleBDViewer__.entities.getById('deviceObjId');
      if (!entityObj) {
        const entity = new Cesium.Entity({
          id: 'deviceObjId',
          name: data.deviceNum,
          position: Cesium.Cartesian3.fromDegrees(data.longitude, data.latitude),
          billboard: {
            image: require(`@/assets/images/bd/location.png`),
            height: 91,
            width: 64,
            scale: .2,
            show: true,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
          },
          label: {
            font: '10pt Microsoft YaHei',
            show: true,
            text: `${data.deviceNum}`,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            pixelOffset: new Cesium.Cartesian2(0, 16),
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            fillColor: Cesium.Color.fromCssColorString('#d16cec'),
          }
        });
        __singleBDViewer__.entities.add(entity);
      }
      else {
        entityObj.position = Cesium.Cartesian3.fromDegrees(data.longitude, data.latitude);
        entityObj.name = data.deviceNum;
      }
    },
    closeWebsocket() {
      this.websock?.close();
    },
    /** 初始化图表数据 */
    initChart() {
      let gpsOption = {
        title: {
          textStyle: {
            fontSize: 12,
            color: '#ffffff'
          }
        },
        color: ['#99cc00'],
        grid: {
          top: 22,
          bottom: 20,
          left: 10,
          right: 10
        },
        xAxis: {
          type: 'category',
          show: true,
          data: [],
          axisLabel: {
            color: '#ffffff'
          }
        },
        yAxis: {
          type: 'value',
          show: false
        },
        series: [
          {
            cursor: 'unset',
            data: [],
            showBackground: true,
            type: 'bar',
            label: {
              show: true,
              position: 'inside',
              color: '#ffffff',
              fontWeight: 'bolder'
            },
            barMaxWidth: 30
          }
        ]
      };
      this.chartBD = this.$echarts.init(this.$refs.glnChart);
      this.chartBD.setOption(gpsOption);
    },
    /**
     * 更新信噪比
     * @param _data
     */
    updateGSV(_data) {
      let data = _data || {};
      this.chartBD.setOption({
        xAxis: {
          data: data.map(i => `${i.id}`.padStart(2, '0'))
        },
        series: [
          {
            data: data.map(i => i.ratio),
            itemStyle: {
              color: params => {
                let value = params.value;
                if (value < 36) {
                  return '#9b2c1d';
                }
                else if (value < 40) {
                  return '#9d891b';
                }
                else {
                  return '#5d8a39';
                }
              }
            }
          }
        ]
      });
    },
    full() {
      let element = document.getElementById('singleBdMonitor');
      if (this.fullscreen) {
        // 关闭全屏
        if (document.exitFullscreen) {
          document.exitFullscreen();
        }
        else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen();
        }
        else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        }
        else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      }
      else {
        // 全屏
        if (element.requestFullscreen) {
          element.requestFullscreen();
        }
        else if (element.webkitRequestFullScreen) {
          element.webkitRequestFullScreen();
        }
        else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen();
        }
        else if (element.msRequestFullscreen) {
          // IE11
          element.msRequestFullscreen();
        }
      }
      this.fullscreen = !this.fullscreen;
    }
  }
};
</script>

<style lang="less" scoped>
#singleBdMonitor {
  line-height: normal !important;
  display: flex;
  height: 100%;
  position: relative;
  background: url(../../../assets/images/beidouVerification/bg.jpg) no-repeat;
  background-size: cover;
}

#canvas-frame {
  border: none;
  flex: 1;
  height: 100%;
  background: none;
}


#canvas-frame-location {
  border: none;
  width: 30vw;
  margin: auto;
  height: 40vh;
}

.info-content {
  width: 45%;
  color: #f0f8ff;
  padding: 6px;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.info-text-title {
  display: flex;
  align-items: center;
}

.info-title-locality {
  width: 60%;
  font-size: 16px;
  height: 100%;
  padding: 4px 0;
  flex-shrink: 0;
}

.deviceNum, .input-wrap {
  margin-right: 5px;
}

.search-content {
  display: flex;
  align-items: center;
  height: 26px;
}

.input-wrap {
  border-radius: 3px;
  color: #ffffff;
  width: 50%;
  height: 100%;

  ::v-deep .el-input__inner {
    height: 100%;
    width: 100%;
    border: 1px solid #355598;
    background: rgba(53, 85, 152, 0.2);
    padding: 0 6px;
    color: #ffffff !important;
  }
}

.submit-btn {
  border-radius: 3px;
  border: 1px solid #355598 !important;
  background: rgba(53, 85, 152, 0.2) !important;
  padding: 0 20px;
  height: 26px;
  color: #64a9ce !important;

  &:hover {
    background: #75c5f0 !important;
    color: #355598 !important;
  }
}
.location-btn {
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  color: #64a9ce !important;
  cursor: pointer;

  .el-icon-location-information {
    font-size: 22px;
  }
}

.location-info {
  margin-top: 10px;
}

.location-addr {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
}

.location-info-con {
  display: flex;
}

.info-satellite-a {
  display: flex;
  flex-wrap: wrap;
  height: 200px;
  flex-shrink: 0;
}

.satellite-room {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.satellite-box {
  display: flex;
  align-items: center;
  flex-flow: row wrap;
  padding: 12px 0;
}

.satellite-title {
  margin-left: 2px;
  background: url(../../../assets/images/beidouVerification/title-bg.png) no-repeat;
  padding-left: 16px;
  font-size: 16px;
}

.info-title-satellite {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 16px;
  flex-shrink: 0;
}

.info-title-num {
  font-size: 52px;
  margin: 0 12px;
  font-weight: bolder;
  color: #e53d32;
  text-shadow: 0 0 8px #ffffff, 0 0 42px #ff7722, 0 0 72px #ff8844, 0 0 150px #ffaa55;
}

.info-chart-room {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.info-chart-earth {
  margin-top: 10px;
  flex: 1;
  display: flex;
  flex-direction: column;
  max-height: 450px;
}

.charts-body {
  width: 100%;
  height: calc(100% - 22px);
}

.info-chart-gsv {
  height: 180px;
  display: flex;
  flex-direction: column;
}

.explain {
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  width: 200px;
  height: 200px;
  bottom: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin: 10px;
  border-radius: 2px;
  color: rgba(255, 247, 247, 0.8);
  font-size: 16px;
}

.explain-point {
  font-size: 28px;
  font-weight: bolder;
  color: rgba(8, 255, 200, 1);
}

.yellow-text {
  color: #fed559;
}

/**
    表格样式
 */
.table-content {
  flex: 1;
  width: 100%;
  overflow: hidden;
}

::v-deep .el-table,
.el-table__expanded-cell {
  background-color: transparent;
}

::v-deep .el-table {
  .el-table__header-wrapper {
    tr {
      background-color: transparent !important;
      height: 30px;
    }
  }
}

::v-deep .el-table__body td, ::v-deep .el-table__header th,
.el-table .cell {
  background-color: transparent !important;
}

::v-deep .el-table::before {
  //去除底部白线
  left: 0;
  bottom: 0;
  width: 100%;
  height: 0;
}

//th的样式
::v-deep .el-table__header th {
  font-weight: bold;
  font-size: 14px;
  color: #ffffff;
}

::v-deep .el-table td.el-table__cell {
  border-bottom: none;
  height: 30px !important;
  font-size: 14px;
  color: #ffffff;
  margin-top: 0;
  margin-bottom: 0;
  padding: 0 !important;
}

::v-deep .green-row {
  background-color: rgba(221, 255, 0, 0.35) !important;
}

::v-deep .red-row {
  background-color: rgba(255, 0, 0, 0.35);
}

.page-title {
  font-size: 34px;
  height: 40px;
  line-height: 40px;
  padding-left: 8px;
  flex-shrink: 0;
}

.fullscreen-btn {
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 7px;
  right: 22px;
  color: #ffffff;
  z-index: 999;
  background: #303336;
  border: 1px solid #444444;
  border-radius: 14%;

  [class^="icon-"] {
    font-size: 24px !important;
  }

  &:hover {
    color: #ffffff;
    fill: #ffffff;
    background: #4488bb;
    border-color: #aaeeff;
    box-shadow: 0 0 8px #ffffff;
  }
}

.timeline-btn {
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #303336;
  border: 1px solid #444444;
  border-radius: 14%;
  color: #ffffff;

  [class^="el-icon-"] {
    font-size: 24px !important;
  }

  &:hover {
    border-color: #aaeeff;
  }
}

@keyframes fadeInLeft {
  0% {
    opacity: 0.5;
    transform: translate3d(-100%, 0, 0)
  }
  to {
    opacity: 1;
    transform: none
  }
}

@space: 8px;
@collapseBtnSize: 90px;
@sideSectionWidth: 350px;

.collapse-btn-base {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  cursor: pointer;
  background-color: #b0b3b8;
  color: #ffffff;

  i {
    font-weight: bold;
  }
}

.collapse-vertical {
  width: @space;
  height: 100%;
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.8);

  .collapse-btn {
    width: 100%;
    height: @collapseBtnSize;
    .collapse-btn-base
  }
}

/deep/ .cesium-viewer-toolbar {
  right: 50px;
}

.control-view {
  position: absolute;
  z-index: 999;
  //background-color: #ff000066;
  top: 8px;
  left: 8px;
  display: flex;
}

.control-item.check-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.control-item {
  margin: 4px;
  padding-right: 10px;

  /deep/ .el-checkbox__label {
    color: #ffffff !important;
    position: relative;
    top: 1px;
  }

  .el-checkbox {
    margin-bottom: 4px;
    margin-right: 12px;
  }

  .type-group {
    display: flex;
    flex-direction: column;
  }

  .state1 {
    color: #ffffff;
    text-shadow: -.5px -.5px 0 #fad900,
      .5px -.5px 0 #fad900,
    -.5px .5px 0 #fad900,
    .5px .5px 0 #fad900;;
  }

  .state2 {
    color: #ffffff;
    text-shadow: -.5px -.5px 0 #05daa1,
      .5px -.5px 0 #05daa1,
    -.5px .5px 0 #05daa1,
    .5px .5px 0 #05daa1;
  }
}

/deep/ .cesium-viewer-bottom {
  display: none;
}

.timeline-control {
  position: absolute;
  bottom: 34px;
  left: 176px;
  z-index: 999;
}
</style>
