<template>
  <div
    id="canvas-frame-location"
    class="canvas-frame-location"
  >
    <canvas
      ref="earthLocation"
      class="earth-location"
    />
  </div>
</template>

<script>
import * as THREE from 'three';
import IMG_EARTH from '../../../../assets/images/beidouVerification/earth4.jpg';
const BDSTYPE = [
  'NONE',
  'GEO', 'GEO', 'GEO', 'GEO', 'GEO', // 5 5
  'IGSO', 'IGSO', 'IGSO', 'IGSO', 'IGSO', // 5 10
  'MEO', 'MEO', 'IGSO', 'MEO', 'NONE', 'IGSO', // 6 16
  'NONE', 'NONE', 'MEO', 'MEO', 'MEO', 'MEO', // 6 22
  'MEO', 'MEO', 'MEO', 'MEO', 'MEO', 'MEO', // 6 28
  'MEO', 'MEO', 'IGS<PERSON>', 'MEO', 'MEO', 'MEO', // 6 34
  'MEO', 'MEO', 'MEO', 'IGSO', 'IGSO', 'IGSO', // 6 40
  'MEO', 'MEO', 'MEO', 'MEO', 'MEO', 'MEO', // 6 46
  'NONE', 'NONE', 'NONE', 'NONE', 'NONE', 'NONE', // 6  52
  'NONE', 'NONE', 'NONE', 'IGSO', 'MEO', 'MEO', // 6  58
  'GEO', 'GEO', 'GEO', 'NONE', 'NONE', 'NONE' // 6  64
];
export default {
  name: 'EarthLocation',
  data () {
    return {
      /* three location数据 */
      widthLoc: null,
      heightLoc: null,
      earthRadiusLoc: 100,
      radius: 0,
      size: [],
      star: null,
      context: null,
      lnglat: []

    };
  },
  watch: {
    lnglat: {
      handler (newVal, oldVal) {
        if (newVal[0] !== oldVal[0] || newVal[1] !== oldVal[1]) {
          this.rendererLoc = null;
          this.cameraLoc = null;
          this.sceneLoc = null;
          this.earthMeshLoc = null;
          this.controlsLoc = null;
          this.groupLoc = null;

          this.initThreeLoc();
          this.animateLoc();

          this.getPosition(newVal[0], newVal[1]);
        }
      }
    }
  },
  mounted () {
    this.rendererLoc = null;
    this.cameraLoc = null;
    this.sceneLoc = null;
    this.earthMeshLoc = null;
    this.controlsLoc = null;
    this.groupLoc = null;

    this.context = this.getctx();
  },
  methods: {
    /**
     * @name: 创建三维地球
     * @return {*}
     */
    initThreeLoc () {
      /* 创建canvas标签 */
      this.widthLoc = document.getElementById('canvas-frame-location').clientWidth;
      this.heightLoc = document.getElementById('canvas-frame-location').clientHeight;
      let _rendererLoc;
      // 创建渲染器对象
      _rendererLoc = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true,
        canvas: _rendererLoc
      });
      this.rendererLoc = _rendererLoc;
      this.rendererLoc.setSize(this.widthLoc, this.heightLoc);
      this.rendererLoc.domElement.id = 'three-loc';
      document.getElementById('three-loc') && document.getElementById('canvas-frame-location').removeChild(document.getElementById('three-loc'));

      document.getElementById('canvas-frame-location').appendChild(this.rendererLoc.domElement);

      /* 设置相机 */
      // 透视相机 视角越大，看到的场景越大，那么中间的物体相对于整个场景来说，就越小了
      this.cameraLoc = new THREE.PerspectiveCamera(60, this.widthLoc / this.heightLoc, 1, 10000);
      // 设置相机位置，可根据半径和视角计算
      this.cameraLoc.position.set(0, 0, 230);
      // 设置相机方向
      this.cameraLoc.lookAt(new THREE.Vector3(0, 0, 0));

      /* 创建场景对象scene */
      this.sceneLoc = new THREE.Scene();

      /* 设置环境光 */
      let hemisphereLightLoc = new THREE.HemisphereLight(0xffffff, 0x393939, 1.6);
      hemisphereLightLoc.position.set(-200, 200, -200);
      this.sceneLoc.add(hemisphereLightLoc);

      /* 设置贴画 */
      let loaderLoc = new THREE.TextureLoader();
      // 创建球体
      let geometryLoc = new THREE.SphereGeometry(this.earthRadiusLoc, 128, 128);
      // 添加贴图
      let textureLoc = loaderLoc.load(IMG_EARTH);
      let materialLoc = new THREE.MeshPhongMaterial();
      materialLoc.map = textureLoc;
      materialLoc.transparent = true;
      let meshLoc = new THREE.Mesh(geometryLoc, materialLoc);
      meshLoc.rotateY(Math.PI * -0.55);
      this.groupLoc = new THREE.Group();
      this.groupLoc.name = 'earthLoc';
      this.groupLoc.add(meshLoc);
      // 添加地球到场景
      this.sceneLoc.add(this.groupLoc);
      this.earthMeshLoc = this.groupLoc;
    },
    /**
     * @name: 渲染动画
     * @return {*}
     */
    animateLoc () {
      /* 渲染 */
      this.rendererLoc.render(this.sceneLoc, this.cameraLoc);
      requestAnimationFrame(this.animateLoc);
    },

    /**
     * @name: 获取canvas内容
     * @return {*}
     */
    getctx () {
      let ele = this.$refs.earthLocation;
      console.log({ele});
      let ctx = this.$refs.earthLocation.getContext('2d');
      const width = ele.clientWidth;
      const height = ele.clientHeight;
      ele.width = width;
      ele.height = height;
      this.size = [width, height];

      const centerW = width / 2;
      const centerH = height / 2;
      ctx.translate(centerW, centerH);
      /** 采取容器最短的一边为半径，然后空出 10px 边界，防止内容溢出 */
      this.radius = Math.min(centerW, centerH) - 31;

      return ctx;
    },
    /**
     * @name: 画俯仰角线
     * @param {object} _ctx canvas上下文
     * @return {*}
     */
    drawBorder (_ctx) {
      let ctx = _ctx;
      ctx.clearRect(-this.size[0] / 2, -this.size[1] / 2, this.size[0], this.size[1]);
      let RADIUS = this.radius;
      // 绘制背景
      ctx.beginPath();
      ctx.strokeStyle = 'rgb(255, 255, 54)';
      // 画弧线 圆心(0,0) 半径RADIUS 起始角0 结束角Math.PI * 2 顺时针绘画
      ctx.arc(0, 0, RADIUS, 0, Math.PI * 2, false);
      ctx.stroke();

      let len60 = Math.cos((60 * Math.PI) / 180) * RADIUS;
      ctx.beginPath();
      ctx.arc(0, 0, len60, 0, Math.PI * 2, false);
      ctx.stroke();

      let len30 = Math.cos((30 * Math.PI) / 180) * RADIUS;
      ctx.beginPath();
      ctx.arc(0, 0, len30, 0, Math.PI * 2, false);
      ctx.stroke();

      ctx.save(); // 保存状态

      // 通过旋转的方式画圆中的分割线
      ctx.beginPath();
      for (var i = 0; i < 8; i++) {
        ctx.rotate((Math.PI / 180) * 45);
        ctx.moveTo(0, 0);
        ctx.lineTo(0, RADIUS);
      }
      ctx.stroke();
      ctx.save(); // 保存状态

      /** 绘制定位文字 */
      const fontSize = 24;
      const fontSpace = 15;
      ctx.font = fontSize + 'px serif';
      ctx.fillStyle = 'rgb(255, 255, 54)';
      ctx.fillText('E', RADIUS + fontSpace, 0);
      ctx.fillText('W', -RADIUS - fontSpace, 0);
      ctx.fillText('S', 0, RADIUS + fontSpace + 5);
      ctx.fillText('N', 0, -RADIUS - fontSpace - 5);

      ctx.font = 'bold 15px sans-serif';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillStyle = 'rgb(255, 255, 54)';
      ctx.fillText('90', 10, 0);
      ctx.fillText('60', 10, -len60);
      ctx.fillText('30', 10, -len30);
      ctx.fillText('0', 10, -RADIUS);
    },
    /**
     * @name: 卫星定位
     * @param {array} drawData 卫星数组
     * @param {object} cxt 上下文
     * @return {*}
     */
    drawSkyPosition (drawData, cxt) {
      // 色盘
      let color = {
        BD: 'rgb(0,144,255)',
        GP: 'rgb(142,207,17)',
        GL: 'rgb(255,144,0)',
        GEO: 'rgb(233, 70, 93)',
        IGSO: 'rgb(241, 217, 126)',
        MEO: 'rgb(31, 136, 136)'
      };

      let radius = this.radius; // 半径
      let cosLen, x, y;

      cxt.save();

      cxt.font = 'bold 14px Arial';
      cxt.textAlign = 'center';
      cxt.textBaseline = 'middle';

      for (let i = 0, dataLen = drawData.length; i < dataLen; i++) {
        // 过滤掉编号超过61的北斗卫星
        if (drawData[i].type !== 'BD' || drawData[i].num > 61) {
          continue;
        }
        cxt.beginPath();
        cxt.fillStyle = color[BDSTYPE[drawData[i].num]];

        // 关键代码。求圆心坐标。coslen是求出来的该点到圆心的距离。
        cosLen = Math.cos((drawData[i].elevation * Math.PI) / 180) * radius;
        y = Math.cos((drawData[i].azimuth * Math.PI) / 180) * cosLen;
        x = Math.sin((drawData[i].azimuth * Math.PI) / 180) * cosLen;

        cxt.arc(x, -y, 14, 0, Math.PI * 2, false); // 在坐标点绘制圆
        cxt.fill();

        cxt.beginPath();
        cxt.fillStyle = 'white';
        cxt.fillText(drawData[i].num, x, -y); // 在坐标点写文字卫星号
      }
      cxt.restore();
    },
    /**
     * @name: 更新卫星数据
     * @param {object} _data 卫星数据
     * @return {*}
     */
    updateStar (_data) {
      this.star = _data.stars;
      this.drawBorder(this.context);
      this.drawSkyPosition(this.star, this.context);
      this.lnglat = [_data.longitude, _data.latitude];
    },
    /**
     * @name: 根据目标经纬度，将地球旋转至以该点为中心
     * @param {number} lng 经度
     * @param {number} lat 纬度
     * @return {*}
     */
    getPosition (lng = 87.62, lat = 43.83) {
      this.groupLoc.rotateX(THREE.MathUtils.degToRad(lat));
      // 由于贴图和真实经度相差9度，因此需-9
      this.groupLoc.rotateY(-THREE.MathUtils.degToRad(lng - 9));
    }
  }
};
</script>

<style scoped>
.canvas-frame-location{
  position: relative;

}
.earth-location{
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}

</style>
