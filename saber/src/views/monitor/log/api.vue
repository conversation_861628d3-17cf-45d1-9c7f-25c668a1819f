<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="100px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation :download="false" />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :max-height="tableMaxHeight"
          style="width: 100%; height: calc(100% - 47px);"
          :cell-style="{ 'text-align': 'center' }"
        >
          <el-table-column
            v-if="columns.visible('direction')"
            prop="direction"
            label="接口方向"
            show-overflow-tooltip
            min-width="120"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>
                {{ getEnumDictLabel("interfaceDirect", scope.row.direction) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('interCode')"
            prop="interCode"
            label="接口编码"
            show-overflow-tooltip
            min-width="120"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('interName')"
            prop="interName"
            label="接口名称"
            show-overflow-tooltip
            min-width="120"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('callAccount')"
            prop="callAccount"
            label="调用账号"
            show-overflow-tooltip
            min-width="120"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('systemId')"
            prop="systemId"
            label="所属平台"
            show-overflow-tooltip
            min-width="120"
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ platMap[scope.row.systemId] }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('serviceCode')"
            prop="serviceCode"
            label="业务编码"
            show-overflow-tooltip
            min-width="120"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('interType')"
            prop="interType"
            label="接口类型"
            show-overflow-tooltip
            min-width="120"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>
                {{ interTypeMap[scope.row.interType] }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('url')"
            prop="url"
            label="url"
            show-overflow-tooltip
            min-width="120"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('param')"
            prop="param"
            label="请求参数"
            min-width="180"
            :resizable="false"
          >
            <template slot-scope="scope">
              <el-tooltip
                :enterable="true"
                :content="scope.row.param"
              >
                <div class="ellipsis">{{ scope.row.param }}</div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('result')"
            prop="result"
            label="应答结果"
            show-overflow-tooltip
            min-width="100"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>
                {{
                  getEnumDictLabel("callInterfaceResult", scope.row.result)
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('resMessage')"
            prop="resMessage"
            label="返回参数"
            show-overflow-tooltip
            min-width="180"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('serverIp')"
            prop="serverIp"
            label="账号IP"
            min-width="120"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('callTime')"
            prop="callTime"
            show-overflow-tooltip
            min-width="180"
            label="调用时间"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.callTime }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('callCost')"
            prop="callCost"
            show-overflow-tooltip
            label="调用用时"
            min-width="140"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>
                {{ scope.row.callCost }}(毫秒)
              </span>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
    </div>
  </basic-container>
</template>

<script>
import api, { serviceListAll, listSystem } from "@/api/log/api.js";
import CRUD, { header, presenter } from "@/components/Crud/crud";
import crudOperation from "@/components/Crud/CRUD.operation";
import pagination from "@/components/Crud/Pagination";
import {getDictionary} from '@/api/system/dictNew';
import HeadCommon from '@/components/formHead/headCommon.vue';

const paginationApi = ({endTime, startTime, ...data}) => {
  if(startTime) data.startTime = parseInt(startTime/1000);
  if(endTime) data.endTime = parseInt(endTime/1000);
  return api.pagination({...data});
};
const crud = CRUD({
  title: "终端日志",
  optShow: {
    add: false,
    edit: false,
    download: false,
  },
  crudMethod: { ...api, pagination: paginationApi },
  queryOnPresenterCreated: false
});

export default {
  name: "TerminalLog",
  components: {
    crudOperation,
    pagination,
    HeadCommon
  },
  mixins: [presenter(crud), header()],
  dicts: ["interfaceDirect", "callInterfaceResult"],
  data() {
    return {
      serviceMap: {},
      platMap: {},
      interTypeMap: {},
      headConfig: {
        initQuery: true,
        item: {
          0: {
            name: '所属平台',
            type: 'select',
            value: 'systemId',
            options: []
          },
          1: {
            name: '开始时间',
            type: 'datetime',
            value: 'startTime',
            defaultFn: '7DS'
          },
          2: {
            name: '结束时间',
            type: 'datetime',
            value: 'endTime',
            defaultFn: 'toDE'
          },
          3: {
            name: '应答结果',
            type: 'select',
            value: 'result',
            dictsOptions: 'callInterfaceResult'
          },
          4: {
            name: '接口编码',
            type: 'input',
            value: 'interCode'
          }
        },
        button: {
        }
      }
    };
  },
  created() {
    this.getListSys();
  },
  methods: {
    getListSys() {
      getDictionary({code: 'api_scope_type'}).then( res => {
        const list = res.data || [];
        this.interTypeMap = list.reduce((obj, item) => {
          if(item.dictKey) {
            obj[item.dictKey] = item.dictValue;
          }
          return obj;
        }, {});
      });
      serviceListAll().then((res) => {
        const list = res.data.data || [];
        this.serviceMap = list.reduce((obj, item) => {
          if(item.id) {
            obj[item.id] = item.name;
          }
          return obj;
        }, {});
      });
      listSystem().then((res) => {
        const list = res.data.data || [];
        // 给initQuery赋值false, 避免重复调用查询接口
        this.headConfig.initQuery = false;
        this.headConfig.item[0].options = list.map(item => ({
          value: item.id,
          label: item.systemName
        }));
        this.platMap = list.reduce((obj, item) => {
          if(item.id) {
            obj[item.id] = item.systemName;
          }
          return obj;
        }, {});
      });
    },
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return "";
      }
    },
  },
};
</script>

<style lang="less" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

.ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>
