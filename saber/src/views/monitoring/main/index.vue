<template>
  <div class="container">
    <div class="statistic">
      <el-card
        v-for="(item, index) in statisticList"
        :key="item.title"
        class="category-card"
        @click.native="selectStatisticItem(index)"
      >
        <div class="card-content">
          <span
            class="category-title"
            :style="activeDeviceType === index ? {color: colorConfig[index].numColor, fontWeight: 'bold'}: {}"
          >{{ item.title }}</span>
          <span
            class="category-num"
            :style="{color: colorConfig[index].numColor}"
          >{{ item.number }}</span>
        </div>
      </el-card>
      <el-card
        class="category-card"
        style="cursor: default;"
      >
        <div class="card-content">
          <span
            class="category-title"
          >今日上线总数</span>
          <span
            class="category-num"
            :style="{color: colorConfig[activeDeviceType].numColor}"
          >{{ activeObj[activeDeviceType].terminalLinkCount }}</span>
        </div>
      </el-card>
      <el-card
        class="category-card"
        style="cursor: default;"
      >
        <div class="card-content">
          <span
            class="category-title"
          >在线总数</span>
          <span
            class="category-num"
            :style="{color: colorConfig[activeDeviceType].numColor}"
          >{{ activeObj[activeDeviceType].terminalOnlineCount }}</span>
        </div>
      </el-card>
    </div>
    <div
      v-show="!isShowStateSection"
      class="show-state-btn"
      @click="showStateSection"
    >
      <el-tooltip
        placement="top-end"
        content="查看终端运行状态"
      >
        <el-button
          type="primary"
          icon="el-icon-arrow-down"
          round
        />
      </el-tooltip>
    </div>
    <div
      class="terminal-state-section"
      :style="{height : isShowStateSection? 'calc(100% - 200px)': '0'}"
    >
      <div class="ts-title">
        <span>终端运行状态</span>
        <div
          class="pack-up-btn"
          @click="hideStateSection"
        >
          <i class="el-icon-arrow-up"/>
        </div>
      </div>
      <div
        ref="tsList"
        class="ts-list"
        @mouseenter="stopDeviceStateScroll = true"
        @mouseleave="stopDeviceStateScroll = false"
      >
        <div
          v-for="item in deviceStateList"
          :key="`${item.deviceType}-${item.deviceNum}-${item.time || item.startTime}`"
          class="ts-item"
        >
          <template v-if="item.onOffLine !== undefined">
            <span
              class="device-type-tag"
              :style="{'--border-color': colorConfig[item.deviceType].numColor}"
            >
              {{ getEnumDictLabel('bdmDeviceType', item.deviceType) }}
            </span>
            <b class="device-num-tag">【{{ item.targetName }}】</b>
            <b class="device-time-tag">于&ensp;{{ formatTime(item.time) }}&ensp;</b>
            <b :style="{color: item.onOffLine === 1? '#7c7a7a' : '#53c60c'}">{{ getOnOffLineStr(item.onOffLine) }}</b>
          </template>
          <template v-else>
            <span
              class="device-type-tag"
              :style="{'--border-color': colorConfig[item.deviceType].numColor}"
            >
              {{ getEnumDictLabel('bdmDeviceType', item.deviceType) }}
            </span>
            <b class="device-num-tag">【{{ item.targetName }}】</b>
            <b class="device-time-tag">于&ensp;{{ formatTime(item.startTime, item.level) }}&ensp;发生&ensp;</b>
            <b style="color: #dd5353;">{{ getEnumDictLabel('alarmType', item.type) }}</b>
          </template>
        </div>
      </div>
    </div>
    <div class="map-container">
      <div
        ref="mapContainer"
        class="map"
      />
    </div>
  </div>
</template>
<script>
import AMapUtil from '@/components/map/AMapUtil';
import StringUtil from '@/utils/helper/StringUtil';
import configMap from '@/config/configMap';
import { getTerminalLocation, terminalCount, lastStatusData } from '@/api/monitoring/main';
import crudBicycleMap from '@/api/monitoring/bicycleMap';
import { getWebsocketParam } from '@/api/user';
import ReconnectingWebSocket from '@/utils/rabbitmq/RealTimeProtocol/ReconnectingWebsocket';

const cacheStateArrLength = 200;
const splitStateArrLength = 150;

let __map = null;
let __AMap = null;
let __massMarks = null;
export default {
  components: {},
  dicts: [
    'bdmDeviceType',
    'alarmType'
  ],
  data() {
    return {
      toolConfig: {
        routeRegionEdit: false, // 跳转区域编辑
        routePolylineEdit: false, // 跳转路线编辑
        routePointEdit: false, // 跳转标注点编辑
        drawMarkerShow: false, // 标注点
        polylineEditorShow: false, // 绘制直线
        showZoneToolShow: false, // 绘制区域
        searchToolShow: false, // 搜索
        clearBtnShow: false, // 清除按钮
        returnBtnShow: false, // 回到中心
        setCenterShow: false, // 设置中心
        trafficLayerShow: false, // 路况
        layerSelectShow: false, // 卫星图
        drivingLineShow: false, // 路径规划
        mapToolsShow: false // 工具栏
      }, // 控制工具按钮
      isShowStateSection: true,
      statisticList: [
        {
          title: '终端总接入量',
          prop: 'totalCount',
          deviceType: '0',
          number: ''
        },
        {
          title: '北斗定位终端',
          prop: 'rnssCount',
          deviceType: '1',
          number: ''
        },
        {
          title: '北斗穿戴式终端',
          prop: 'wearCount',
          deviceType: '2',
          number: ''
        },
        {
          title: '北斗短报文终端',
          prop: 'rdssCount',
          deviceType: '3',
          number: ''
        },
        {
          title: '北斗监测终端',
          prop: 'monitCount',
          deviceType: '4',
          number: ''
        },
        {
          title: '北斗授时终端',
          prop: 'pntCount',
          deviceType: '5',
          number: ''
        }
      ],
      colorConfig: {
        '0': {
          titleColor: '#64d123',
          numColor: '#64d123'
        },
        '1': {
          titleColor: '#1a71bd',
          numColor: '#1a71bd'
        },
        '2': {
          titleColor: '#cf681f',
          numColor: '#cf681f'
        },
        '3': {
          titleColor: '#1ac1c1',
          numColor: '#1ac1c1'
        },
        '4': {
          titleColor: '#af4ef3',
          numColor: '#af4ef3'
        },
        '5': {
          titleColor: '#dd449b',
          numColor: '#dd449b'
        }
      },
      activeDeviceType: '0',
      activeObj: {
        '0': {
          terminalLinkCount: '',
          terminalOnlineCount: ''
        },
        '1': {
          terminalLinkCount: '',
          terminalOnlineCount: ''
        },
        '2': {
          terminalLinkCount: '',
          terminalOnlineCount: ''
        },
        '3': {
          terminalLinkCount: '',
          terminalOnlineCount: ''
        },
        '4': {
          terminalLinkCount: '',
          terminalOnlineCount: ''
        },
        '5': {
          terminalLinkCount: '',
          terminalOnlineCount: ''
        }
      },
      styles: null,
      massMarks: null,
      markerListObj: {},
      initialized: false,
      deviceStateList: [],
      deviceStateWebsock: null,
      alarmWebsock: null,
      map: null,
      stopDeviceStateScroll: false,
      timer: null
    };
  },
  computed: {
    // getChildrenCardLeft() {
    //   if (this.activeDeviceType === '0') {
    //     return 0;
    //   }
    //   else if (this.activeDeviceType === '5') {
    //     return `calc(${4 * 50}% + ${4 * 4}px)`;
    //   }
    //   else {
    //     return `calc(${this.activeDeviceType * 50}% + ${this.activeDeviceType * 4}px)`;
    //   }
    // }
  },
  watch: {
    $route: {
      handler(to) {
        if (!localStorage.getItem('cedi__Access-Token')) {
          if (to.path === '/wel/index') {
            this.$store.commit('SET_COLLAPSE', true);
          }
          else {
            this.$store.commit('SET_COLLAPSE', false);
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  async mounted() {
    this.$nextTick(() => {
      this.mapWidget = this.$refs.MapWidget;
    });
    this.initWebSocket();
    await this.init();
    this.getTerminalData();
    this.forEachGetAllTerminalInfo();
    this.monitorAlarmWs();
    this.getLastStatusData();
    document.addEventListener('visibilitychange', () => {
      if (document.hidden === true) {
        this.stopDeviceStateScroll = true;
      }
      else {
        this.onceScrollDeviceState();
      }
    });
  },
  beforeDestroy() {
    clearInterval(this.timer);
    if (__map) {
      //解绑地图的点击事件
      // __map.off("click", clickHandler);
      //销毁地图，并清空地图容器
      __map.destroy();
      //地图对象赋值为null
      __map = null;
      //清除地图容器的 DOM 元素
      document.getElementById(this.mapId)?.remove(); //"container" 为指定 DOM 元素的id
    }
  },
  activated() {
    // this.initInterval();
    this.onceScrollDeviceState();
    this.mapReload();
  },
  deactivated() {
    this.stopDeviceStateScroll = true;
    clearInterval(this.timer);
  },
  methods: {
    async mapReload () {
      let canvas = document.getElementsByClassName('amap-layer')[0];
      if (canvas) {
        console.log(canvas); // 打印绘制的canvas
        let canvasContent = canvas.getContext('webgl'); // 因为高德地图是通过webGl来绘制的，所以通过getContext(‘webgl’)才能获取到内容
        console.log(canvasContent);
        if (canvasContent?.drawingBufferHeight < 10 && canvasContent?.drawingBufferWidth < 10) {
          await this.init();
          this.forEachGetAllTerminalInfo();
        }
      }
    },
    onceScrollDeviceState() {
      const container = this.$refs.tsList;
      container.scrollTo({
        top: 0,
        behavior: 'instant'
      });
      setTimeout(() => {
        this.stopDeviceStateScroll = false;
      }, 1);
    },
    initInterval() {
      this.timer = setInterval(() => {
        this.getTerminalData();
        this.forEachGetAllTerminalInfo();
      }, 1000 * 30);
    },
    async init() {
      return new Promise(resolve => {
        AMapUtil.loadAMap(AMap => {
          __AMap = AMap;
          this.mapId = StringUtil.generateGuid();
          this.$refs.mapContainer.id = this.mapId;
          this.config = configMap.default;
          const massMarkerSize = 14;
          const massMarkerAnchor = massMarkerSize / 2;
          this.$nextTick(() => {
            let center = [
              108.552500,
              35.822700
            ];
            __map = new __AMap.Map(this.mapId, {
              resizeEnable: true, // 是否监控地图容器尺寸变化
              zoom: 4.1, // 初始化地图层级
              center: center, // 初始化地图中心点
              expandZoomRange: true,
              zooms: [
                4,
                18
              ],
              animateEnable: false,
              mapStyle: 'amap://styles/whitesmoke',
              viewMode: '3D',
              // features: [
              //   'bg',
              //   'road'
              // ]
            });
            this.styles = [
              0,
              1,
              2,
              3,
              4,
              5
            ].map(number => {
              return {
                // TODO 此处逻辑修改待终端全类型有数据后再修改 设计图片device-type99不记得了
                url: require(`@/assets/images/gn-home/device-type${number}.png`), //图标地址
                anchor: new AMap.Pixel(massMarkerAnchor, massMarkerAnchor), //图标显示位置偏移量，基准点为图标左上角
                size: new AMap.Size(massMarkerSize, massMarkerSize), //图标的尺寸
                zIndex: number + 1 //每种样式图标的叠加顺序，数字越大越靠前
              };
            });
            __AMap.plugin([
              'AMap.Scale',
              'AMap.MassMarks',
              'AMap.DistrictLayer',
              'AMap.GeoJSON' // 配置行政区查询服务
            ], () => {
              const disCountry = new AMap.DistrictLayer.Country({
                opacity: 0.8,
                // depth: 0,
                SOC: 'CHN', // 国家编码
                styles: {
                  'stroke-width': 1.1, // 描边线宽
                  "nation-stroke": "#84cdec", // 国界线颜色
                  "coastline-stroke": "#84cdec", // 海岸线颜色
                  "province-stroke": "#afd4f3", // 省线颜色
                  // "city-stroke": "#62b4ef",
                  fill: "rgba(214,229,255,0.2)", // 背景填充颜色
                },
              });
              disCountry.setMap(__map);
              __massMarks = new __AMap.MassMarks(
                [],
                {
                  zIndex: 999,
                  zooms: [
                    4,
                    18
                  ],
                  style: this.styles
                }
              );
              __massMarks.setMap(__map);
              const scale = new AMap.Scale({
                visible: true
              });
              __map.addControl(scale);
              resolve();
            });
          });
        });
      });
    },
    getTerminalData() {
      terminalCount().then(res => {
        if (res.code === 200) {
          this.statisticList.forEach(item => {
            item.number = res.data[item.prop];
          });
        }
      });
    },
    async getTerminalInfo(deviceType) {
      const { data } = await getTerminalLocation(deviceType);
      return {
        ...data,
        deviceType: deviceType || '0'
      };
    },
    forEachGetAllTerminalInfo() {
      const deviceTypeList = this.statisticList.map(item => item.deviceType);
      const promises = deviceTypeList.map(async deviceType => await this.getTerminalInfo(deviceType === '0' ? '' : deviceType));
      Promise.allSettled(promises).then((res) => {
        for (const item of res) {
          const tList = item.value?.terminalInfoList;
          const deviceType = item.value.deviceType;
          const {
            terminalOnlineCount,
            terminalLinkCount
          } = item.value;
          this.activeObj[deviceType].terminalOnlineCount = terminalOnlineCount;
          this.activeObj[deviceType].terminalLinkCount = terminalLinkCount;
          if (tList?.length) {
            const locationData = this.$utils.wgs84togcj02Batch(tList);
            this.markerListObj[deviceType] = locationData.map(item => {
              return {
                lnglat: [
                  item.longitude,
                  item.latitude
                ],
                style: item.teState === 0 ? 0 : Number(deviceType)
              };
            });
          }
        }
        this.createMarker();
      });
    },
    createMarker() {
      const activeDeviceType = this.activeDeviceType;
      const markerListObj = this.markerListObj;
      const allList = [];
      Object.keys(markerListObj).forEach(item => {
        allList.push(...markerListObj[item]);
      });
      __massMarks.clear();
      if (this.activeDeviceType === '0') {
        __massMarks.setData(allList);
      }
      else {
        console.log(markerListObj[activeDeviceType]);
        __massMarks.setData(markerListObj[activeDeviceType]);
      }
    },
    selectStatisticItem(index) {
      this.activeDeviceType = `${index}`;
      this.createMarker();
    },
    hideStateSection() {
      this.isShowStateSection = false;
    },
    showStateSection() {
      this.isShowStateSection = true;
    },
    // 获取最后推送的十条数据
    getLastStatusData() {
      const params = {
        size: 10,
        endTime: this.$moment().unix(),
        startTime: this.$moment().subtract(3, 'days').unix()
      };
      lastStatusData(params).then(res => {
        if (res.data) {
          this.deviceStateList = res.data.map(item => JSON.parse(item.content));
        }
      });
    },
    monitorAlarmWs() {
      this.$EventBus.$on('realTimeMap', (data) => {
        console.log('-> 首页推送终端告警数据', data);
        this.deviceStateList.unshift(data);
        this.scrollList();
        if (this.deviceStateList.length > cacheStateArrLength) {
          this.spliceDeviceStateArr();
        }
      });
    },
    initWebSocket() {
      if (typeof WebSocket === 'undefined') {
        console.log('您的浏览器不支持WebSocket');
        return false;
      }
      crudBicycleMap.getAuthCode().then(res => {
        const socketCode = res.data;
        getWebsocketParam().then(res => {
          const wsLocation = res.data.data;
          const protocol = window.location.origin.indexOf('https') !== -1 ? 'wss://' : 'ws://';
          this.initDeviceStateWebsock(protocol, wsLocation, socketCode);
        });
      });
      this.$once('hook:beforeDestroy', () => {
        this.closeWebsocket();
      });
    },
    initDeviceStateWebsock(protocol, wsLocation, socketCode) {
      const wsUrl = `${protocol}${wsLocation}/ws/deviceOnOff/push/${socketCode}`;
      this.deviceStateWebsock = new ReconnectingWebSocket(wsUrl);
      this.deviceStateWebsock.onopen = () => {
        console.log('终端状态ws连接成功');
      };
      this.deviceStateWebsock.onmessage = (e) => {
        const data = JSON.parse(e.data);
        console.log('-> 首页推送终端状态数据', JSON.parse(e.data));
        this.deviceStateList.unshift(data);
        this.scrollList();
        if (this.deviceStateList.length > cacheStateArrLength) {
          this.spliceDeviceStateArr();
        }
      };
      this.deviceStateWebsock.onerror = () => {
        console.log('终端状态数据传输已断开, 正在尝试重新连接');
      };
    },
    closeWebsocket() {
      this.deviceStateWebsock?.close();
      this.$EventBus.$off('realTimeMap');
    },
    spliceDeviceStateArr() {
      this.deviceStateList = this.deviceStateList.splice(0, splitStateArrLength);
    },
    scrollList() {
      if (!this.stopDeviceStateScroll) {
        this.$nextTick(function () {
          const container = this.$refs.tsList;
          container.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        });
      }
    },
    formatTime(time, level) {
      const isAlarm = level || level === 0;
      return this.$moment(isAlarm ? time * 1000 : time).format('HH:mm:ss');
    },
    getOnOffLineStr(onOffLine) {
      return onOffLine === 0 ? '上线' : '下线';
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    }
  }
};
</script>

<style lang="less" scoped>

.container {
  width: 100%;
  height: 100%;
  min-height: 600px;
  padding: 0 4px 4px;
  position: relative;
}

.map-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.statistic {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  padding: 8px;
  display: flex;
  overflow-x: auto;

  .category-card {
    //width: 12%;
    flex: 1;
    //min-width: 160px;
    width: 15%;
    margin-right: 8px;
    cursor: pointer;

    /deep/ .el-card__body {
      padding: 12px 12px !important;
    }
  }

  .card-content {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    user-select: none;

    .category-title {
      font-size: 1vw;
    }

    .category-num {
      margin-top: 2px;
      font-size: 1.4vw;
      font-weight: bold;
    }
  }
}

.show-state-btn {
  right: 12px;
  top: 110px;
  position: absolute;
  z-index: 3;
}

.terminal-state-section {
  width: 420px;
  height: calc(100% - 200px);
  background-color: #ffffffaf;
  position: absolute;
  right: 16px;
  top: 110px;
  z-index: 3;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;

  .ts-title {
    height: 50px;
    line-height: 50px;
    color: #303030;
    font-size: 16px;
    text-align: center;
    border-bottom: 1px solid #cccccc;
    position: relative;
    font-weight: bold;

    .pack-up-btn {
      position: absolute;
      right: 0;
      height: 80%;
      top: 10%;
      width: 30px;
      cursor: pointer;
      display: flex;
      align-items: center;
    }
  }

  .ts-list {
    height: calc(100% - 50px);
    padding: 4px 6px;
    box-sizing: border-box;
    overflow: scroll;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .ts-item {
    font-size: 14px;
    background-color: #ffffff;
    padding: 10px 12px;
    box-sizing: border-box;
    line-height: 24px;
    margin-bottom: 6px;
    border-radius: 4px;
    word-break: break-all;
  }
}

.map {
  width: 100%;
  height: 100%;
}
</style>
<style scoped>
.device-type-tag {
  height: 20px;
  padding: 0 8px;
  line-height: 20px;
  background-color: white;
  border-color: hsl(from var(--border-color) h s calc(l * 1.8));
  color: var(--border-color);
  display: inline-block;
  border-width: 1px;
  border-style: solid;
  border-radius: 4px;
  font-size: 12px;
  box-sizing: border-box;
}

.device-num-tag {
  color: #333333;
}

.device-time-tag {
  color: #333333;
}

b {
  font-weight: normal;
}
</style>
