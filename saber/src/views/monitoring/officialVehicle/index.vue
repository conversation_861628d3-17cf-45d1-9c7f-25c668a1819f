<template>
  <div class="container">
    <section
      v-show="showCarList"
      ref="leftContainer"
      class="left media-terminal-tree-container"
    >
      <TerminalMultiSelectTree
        ref="terminalMultiSelectTree"
        :monitor-car-obj="monitorCarObj"
        @checkedVehiclesChange="getVehicleMap"
        @CheckedNodesUpdate="CheckedNodesUpdate"
        @setTableWidth="setTableWidth"
        @questionHandle="questionHandle"
        @handleTrack="handleTrack"
        @clearAll="clearAll"
      />
    </section>
    <div
      v-treeDrag
      class="collapse-vertical"
    >
      <span
        class="collapse-btn"
        @click="showCarList = !showCarList"
      >
        <i :class="`el-icon-arrow-${showCarList ? 'left' : 'right'}`" />
      </span>
    </div>
    <section class="right">
      <div
        v-show="showMap"
        class="map-container chunk"
      >
        <MapWidget
          ref="MapWidget"
          class="map-widget"
          :tool-config="toolConfig"
          :is-clear-map="false"
          @setInfoData="setInfoData"
          @setTableData="setTableData"
          @toVehicleInfo="toVehicleInfo"
          @onTrackStop="onTrackStop"
          @setTrackElement="setTrackElement"
          @setTrackPath="setTrackPath"
          @handleClear="handleClear"
        />
      </div>
      <div
        v-tableDrag
        class="collapse"
      >
        <span
          v-show="showMap"
          class="collapse-btn"
          @click="showTable = !showTable"
        >
          <i :class="`el-icon-arrow-${showTable ? 'down' : 'up'}`" />
        </span>
        <span
          v-show="showTable"
          class="collapse-btn"
          @click="showMap = !showMap"
        >
          <i :class="`el-icon-arrow-${showMap ? 'up' : 'down'}`" />
        </span>
      </div>
      <!-- 数据很多的话使用v-show显示隐藏会出现明显卡顿, 不清楚什么原因, 所以将height设为0 -->
      <div
        ref="tableContainer"
        class="table-container chunk"
        :class="{ 'table-container-full': !showMap, 'table-container-none': !showTable }"
      >
        <el-tabs
          v-model="activeName"
          class="table-tabs"
          stretch
          type="card"
          @tab-click="handleClick"
        >
          <el-tab-pane
            label="定位信息"
            name="monitorTab"
          >
            <MonitorTab
              ref="monitorTab"
              :dict="dict"
            />
          </el-tab-pane>
          <el-tab-pane
            label="跟踪信息"
            name="followTab"
            :lazy="true"
          >
            <FollowTab
              ref="followTab"
            />
          </el-tab-pane>
          <el-tab-pane
            label="轨迹信息"
            name="trackTab"
            :lazy="true"
          >
            <TrackTab
              ref="trackTab"
              :dict="dict"
              @onExportDeviceData="onExportDeviceData"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </section>

    <VehicleInfo
      v-if="dialogVisible"
      ref="vehicleInfo"
      :dialog-visible.sync="dialogVisible"
      class="vehicle-info"
      :follow-car="followCar"
      :vehicle-data="vehicleData"
      :is-official="true"
      @followHandle="followHandle"
      @handleTrack="handleTrack"
    />
    <FollowDialog
      v-if="followCar.length > 0"
      class="follow-dialog"
      :follow-car="followCar"
      @followCloseAll="followCloseAll"
      @setFollowClose="setFollowClose"
    />
    <TerminalTipsDialog ref="terminalTipsDialog" />
    <div
      v-show="trackConfigDialog"
      class="config-container"
    >
      <i
        class="el-icon-circle-close config-close"
        @click="trackConfigDialog = !trackConfigDialog"
      />
      <TerminalTrackConfig
        ref="terminalTrackConfig"
        @clearTrackData="clearTrackData"
        @onTrackPlanelCancel="onTrackPlanelCancel"
        @onTrackSearch="onTrackSearch"
      />
    </div>
  </div>
</template>

<script>
import MapWidget from '@/components/map/MapWidgetAMap';
import TerminalMultiSelectTree from './tabs/terminalMultiSelectTree';
import VehicleInfo from '@/components/map/infoWindow/VehicleInfoNew';
import MonitorTab from './tabs/monitorTab.vue';
import FollowTab from './tabs/followTab.vue';
import TrackTab from './tabs/trackTab.vue';
import TerminalTrackConfig from './tabs/terminalTrackConfig.vue';
import TerminalTipsDialog from './tabs/terminalTipsDialog.vue';
import { vehicleAddress, terminalStates } from '@/api/monitoring/info.js';
import ReconnectingWebSocket from '@/utils/rabbitmq/RealTimeProtocol/ReconnectingWebsocket';
import { getAuthCode } from '@/api/monitoring/bicycleMap';
import FollowDialog from './tabs/followDialog.vue';
import { apiPostTrackcontrol } from '@/api/home';
import { getWebsocketParam } from '@/api/user';
import jsonToHump from '@/utils/helper/jsonToHump';
import { mapGetters } from "vuex";

export default {
  name: 'OfficialVehicle',
  components: {
    MapWidget: MapWidget,
    TerminalMultiSelectTree,
    VehicleInfo,
    MonitorTab,
    FollowTab,
    FollowDialog,
    TerminalTipsDialog,
    TrackTab,
    TerminalTrackConfig
  },
  directives: {
    tableDrag(el, bindings, vnode) { // 拖拽
      el.onmousedown = function (e) {
        let y = e.clientY;
        let yel = vnode.context.$refs.tableContainer;
        let yelDefault = yel.style.height || '324px';
        yelDefault = +yelDefault.substring(0, yelDefault.length - 2);
        yelDefault = yelDefault > 600 ? 600 : yelDefault;
        yelDefault = yelDefault < 240 ? 240 : yelDefault;
        document.onmousemove = function (e) {
          let ylong = yelDefault + y - e.clientY;
          ylong = ylong > 600 ? 600 : ylong;
          ylong = ylong < 240 ? 240 : ylong;
          yel.style.height = ylong + 'px';
        };
        document.onmouseup = function () {
          document.onmousemove = document.onmouseup = null;
        };
      };
    },
    treeDrag(el, bindings, vnode) { // 拖拽
      el.onmousedown = function (e) {
        let x = e.clientX;
        let xel = vnode.context.$refs.leftContainer;
        let xelDefault = xel.style.width || '300px';
        xelDefault = +xelDefault.substring(0, xelDefault.length - 2);
        xelDefault = xelDefault > 660 ? 660 : xelDefault;
        xelDefault = xelDefault < 230 ? 230 : xelDefault;
        document.onmousemove = function (e) {
          let xlong = xelDefault + e.clientX - x;
          xlong = xlong > 660 ? 660 : xlong;
          xlong = xlong < 230 ? 230 : xlong;
          xel.style.width = xlong + 'px';
        };
        document.onmouseup = function () {
          document.onmousemove = document.onmouseup = null;
        };
      };
    }
  },
  // 数据字典
  dicts: ['targetType', 'bdmDeviceType', 'batch'],
  data() {
    return {
      drawMarker: null,
      toolConfig: {
        routeRegionEdit: false, // 跳转区域编辑
        routePolylineEdit: false, // 跳转路线编辑
        routePointEdit: false, // 跳转标注点编辑
        drawMarkerShow: false, // 标注点
        polylineEditorShow: false, // 绘制直线
        showZoneToolShow: false, // 绘制区域
        searchToolShow: false, // 搜索
        clearBtnShow: false, // 清除按钮
        returnBtnShow: false, // 回到中心
        setCenterShow: false, // 设置中心
        trafficLayerShow: true, // 路况
        layerSelectShow: true, // 卫星图
        drivingLineShow: false, // 路径规划
        mapToolsShow: true, // 工具栏
        regionShow: false
      }, // 控制工具按钮
      activeName: 'monitorTab',
      monitorCarObj: {},
      autoUpDateTime: null, // 定时器
      pollTime: 30, // 30s更新一次车辆数据
      timeTrack: 0, // 防止重复点击
      followCar: [], // 跟踪车辆的集合
      followTrack: {
        data: undefined, // 跟踪的初始轨迹
        line: undefined,
        timer: null
      },
      dialogVisible: false,
      vehicleData: {},
      vehicleMarkerList: [], // 当前选中marker列表
      websock: null,
      showCarList: true,
      showTable: true,
      showMap: true,
      trackConfigDialog: false,
      isPlayTrack: false // 是否正在播放轨迹
    };
  },
  computed: {
    ...mapGetters([
      "userInfo"
    ])
  },
  mounted() {
    this.$nextTick(() => {
      const mapWidget = this.$refs.MapWidget;
      this.drawMarker = mapWidget.$refs.DrawMarker;
    });
    // if (!this.websock) {
    //   this.initLocationWebSocket();
    // }
  },
  activated() {
    if (!this.autoUpDateTime) {
      this.autoUpDateTime = setInterval(() => {
        this.autoUpdateVehicleTreeInCycke();
      }, this.pollTime * 1000);
    }
    let timer = null;
    let tableContainer = document.querySelector('.table-container');
    let observer = new ResizeObserver(() => {
      clearTimeout(timer); // 防抖
      timer = setTimeout(() => {
        this.$refs?.monitorTab?.editTableHeight();
        this.$refs?.trackTab?.editTableHeight();
      }, 500);
    });
    observer.observe(tableContainer); // 监听元素
    this.$once('hook:deactivated', () => {
      observer.disconnect();
      observer = null;
    });
  },
  deactivated() {
    clearInterval(this.autoUpDateTime);
    this.autoUpDateTime = null;
    this.followCloseAll();
  },
  beforeDestroy() {
    this.followCloseAll();
    this.$refs.monitorTab.setData([], []);
  },
  destroyed() {
    clearInterval(this.autoUpDateTime);
    this.autoUpDateTime = null;
  },
  methods: {
    // 地图右键"清除"后的事件
    handleClear() {
      this.isPlayTrack = false;
      this.clearTrackData();
      this.onTrackPlanelCancel();
    },
    // 导出设备轨迹
    onExportDeviceData() {
      this.$refs?.terminalTrackConfig?.onExportDeviceData();
    },
    setTrackPath(arr) {
      this.$refs.trackTab.setTrackPath(arr);
    },
    setTableData(data) {
      this.$refs.trackTab.setTableData(data);
    },
    setInfoData(historyInfo) {
      this.$refs.trackTab.setInfoData(historyInfo);
    },
    setTrackElement(mapInfo) {
      // 关闭轨迹查询弹窗
      this.trackConfigDialog = false;
      // 清空勾选状态
      this.$refs?.terminalMultiSelectTree?.clear();
      // 清除地图终端图标
      this.clearAll(false);
      // 当前有正在跟踪的终端，则停止跟踪
      if (this.followCar && this.followCar.length) {
        this.followCloseAll();
      }
      this.$refs.trackTab.setTrackElement(mapInfo);
      this.$nextTick(() => {
        this.isPlayTrack = true;
        this.activeName = 'trackTab';
      });
    },
    onTrackSearch(searchInfo) {
      this.$refs.MapWidget.terminalTrackSearch(searchInfo);
    },
    onTrackPlanelCancel() {
      this.$refs.MapWidget.onTrackPlanelCancel();
    },
    onTrackStop() {
      this.$refs.trackTab.onPlayerStop();
    },
    clearTrackData () {
      this.$refs.trackTab?.clearTrackData();
    },
    // 打开轨迹查询弹窗
    handleTrack(val) {
      this.dialogVisible = false;
      this.trackConfigDialog = true;
      this.activeName = 'trackTab';
      this.$refs?.terminalTrackConfig.setValue(val);
      this.$nextTick(() => {
        this.$refs?.trackTab?.editTableHeight();
      });
    },
    questionHandle() {
      this.$refs.terminalTipsDialog.questionHandle();
    },
    toVehicleInfo(val) {
      const data = this.vehicleMarkerList.find(item=>item.deviceIdStr === val);
      if (data) {
        this.vehicleData = data;
      }
      this.dialogVisible = true;
      this.trackConfigDialog = false;
    },
    setTableWidth(val) {
      let leftContainer = document.querySelector('.left');
      leftContainer.style.width = val + 'px';
    },
    initLocationWebSocket() {
      if (typeof WebSocket === 'undefined') {
        console.log('您的浏览器不支持WebSocket');
      }
      const JSONbig = require('json-bigint');
      getAuthCode().then(res => {
        const socketCode = res.data;
        getWebsocketParam().then(res => {
          const wsLocation = res.data.data;
          const protocol = window.location.origin.indexOf('https') !== -1 ? 'wss://' : 'ws://';
          const wsUrl = `${protocol}${wsLocation}/ws/locationTrack/push/${socketCode}`;
          this.websock = new ReconnectingWebSocket(wsUrl);
          this.websock.onopen = () => {
            console.log('成功');
            // 防止ws连接缓慢导致调用apiPostTrackcontrol接口后没有给ws推送当前要跟踪的车辆
            // 因此再次推送, 跟后端确认过多次推送是没关系的
            if (this.followCar && this.followCar.length > 0) {
              this.followCar.forEach(item => {
                const query = {
                  device_type: item.deviceType,
                  device_id: item.deviceId,
                  user_id: sessionStorage.getItem('saber-integration') ? this.userInfo?.user_id : undefined
                };
                this.websock.send(JSON.stringify(query));
              });
            }
          };
          this.websock.onmessage = (e) => {
            const data = jsonToHump(JSONbig({ storeAsString: true }).parse(e.data));
            const locationData = this.$utils.wgs84togcj02(data.longitude, data.latitude);
            data.longitudeGcj = locationData[0];
            data.latitudeGcj = locationData[1];
            const str = data.deviceType + '-' + data.deviceId;
            // 手动取消跟踪后, 如果ws碰巧这时候推了一条数据过来, 就会导致代码报错, 造成不可预料的后果
            // 例如造成控制台报错, 此时勾选终端节点不会往地图上渲染marker
            // 因此判断是否有在跟踪该终端
            if (!this.followTrack[str]) {
              return;
            }
            if (this.followTrack[str].isDraw) {
              this.drawMarker.removeMarkerFromCluster({
                labelTypeId: 1,
                vehicleId: [data.deviceIdStr || data.deviceId]
              });
              this.drawMarker.drawFollowMarker({
                ...this.followTrack[str].labelTypeData,
                ...data
              });
              this.followTrack[str].isDraw = false;
            }
            if (this.followTrack[str].timer) {
              clearTimeout(this.followTrack[str].timer);
              this.followTrack[str].timer = null;
            }
            this.followTrack[str].timer = setTimeout(() => {
              this.$message({
                showClose: true,
                message: '数据传输已断开, 请重新跟踪',
                type: 'warning',
                duration: 0
              });
              this.setFollowClose(this.followTrack[str].labelTypeData);
            }, 60 * 1000);
            const stateTerminalList = [{
              id: data.deviceId,
              fusionState: data.speed ? 2 : 1
            }];
            this.$refs.terminalMultiSelectTree.updateStateTerminal(stateTerminalList);
            this.setFollow(data);
          };
          this.websock.onerror = () => {
            console.log('数据传输已断开, 正在尝试重新连接');
          };
        });
      });
      this.$once('hook:beforeDestroy', () => {
        this.websock.close();
        this.websock = null;
      });
    },
    // 点击跟踪车辆事件
    followHandle(data) {
      if (!this.websock) {
        this.initLocationWebSocket();
      }
      this.followloop(data);
    },
    // 跟踪车辆循环
    followloop(data) {
      // 打开
      if (data.open) {
        this.activeName = 'followTab';
        this.followCar.push(data); // 记录开启车辆
        this.trackHandle(data); // 绘制轨迹
        this.getTrackcontrol(data);
      } else if (!data.open) {
        this.setFollowClose(data); // 关闭
      }
    },
    // 临时位置跟踪控制
    getTrackcontrol(val) {
      const str = val.deviceType + '-' + val.deviceId;
      this.followTrack[str].labelTypeData = {
        iconUrl: this.judgeTerminalIcon(val),
        bgUrl: this.judgeBackgroundIcon(val),
        iconWidth: 50,
        iconHeight: 50,
        ...val
      };
      const query = {
        device_type: val.deviceType,
        device_id: BigInt(val.deviceId),
        duration: 3,
        expire: 5 * 60 * 60 // 取消5分钟时间限制, 暂时定为5小时
      };
      apiPostTrackcontrol(query).then(result => {
        console.log('-> 已开启临时位置跟踪', result.data);
        if (this.websock && this.websock.readyState === 1) {
          const query = {
            device_type: val.deviceType,
            device_id: val.deviceId,
            user_id: sessionStorage.getItem('saber-integration') ? this.userInfo?.user_id : undefined
          };
          this.websock.send(JSON.stringify(query));
        }
      }).catch(() => {
        let index = this.followCar.findIndex(item => item.deviceType + '-' + item.deviceId === str);
        if (index >= 0) {
          this.followCar.splice(index, 1); // 删除车辆记录
        }
        if (this.followTrack[str]?.timer) {
          clearTimeout(this.followTrack[str].timer);
          this.followTrack[str].timer = null;
        }
        this.followTrack[str] = null;
        if (val.deviceIdStr === this.vehicleData.deviceIdStr) {
          this.$refs.vehicleInfo?.setFollowState();
        }
      });
    },
    // 跟踪车辆时适配合适视野范围
    setAllFitView() {
      this.drawMarker.setAllFitView(this.followCar);
    },
    setFollow(data) {
      const str = data.deviceType + '-' + data.deviceId;
      this.getAddress(data);
      let sData = [data];
      this.$refs.followTab.updateData(this.followCar, sData);
      if (this.followTrack[str] && this.followTrack[str].data && this.followTrack[str].line) { // 依次添加新的轨迹点
        this.followTrack[str].data.push({
          longitude: data.longitudeGcj,
          latitude: data.latitudeGcj,
          time: data.recvTime
        });
      }
      this.drawMarker.renewMarker(data, this.followTrack[str].line, this.followTrack[str].data);
      // this.setAllFitView(); // 适配合适视野范围
    },
    setFollowClose(data) {
      const str = data.deviceType + '-' + data.deviceId;
      // 关闭
      let i;
      this.followCar.forEach((v, index) => {
        if (v.deviceType + '-' + v.deviceId === str) {
          i = index;
        }
      });
      if (!i && i !== 0) {
        return;
      }
      const query = {
        device_type: data.deviceType,
        device_id: BigInt(data.deviceId),
        duration: 0,
        expire: 60
      };
      apiPostTrackcontrol(query).then(res => {
        console.log('停止跟踪');
      });
      this.followCar.splice(i, 1); // 删除车辆记录
      if (this.followCar.length === 0) {
        this.$refs.followTab.clearData();
      }
      if (this.followTrack[str] && this.followTrack[str].line) { // 清除跟踪的轨迹
        this.$refs.MapWidget.map.remove(this.followTrack[str].line);
      }
      // 可能出现下发指令成功，但是ws不推送数据的情况，则停止跟踪时先清空旧的图标，否则会导致地图上出现重复图标
      if (this.followTrack[str] && this.followTrack[str].data.length === 0) {
        this.drawMarker.removeMarkerFromCluster({
          labelTypeId: 1,
          vehicleId: [data.deviceIdStr || data.deviceId]
        });
      } else {
        // 清除位置跟踪的marker
        this.drawMarker.removeFollowMarker(str);
      }
      // 重新绘制点聚合marker
      let terminalData = this.vehicleMarkerList.find(item => item.deviceIdStr === data.deviceIdStr);
      if (terminalData) {
        // 跟踪结束后，不让终端回到旧的位置，直接拿ws推得最新的经纬度
        if (this.followTrack[str]?.data && this.followTrack[str].data?.length > 0) {
          let longitude = this.followTrack[str].data[this.followTrack[str].data.length - 1].longitude;
          let latitude = this.followTrack[str].data[this.followTrack[str].data.length - 1].latitude;
          const locationData = this.$utils.gcj02towgs84(longitude, latitude);
          terminalData.longitude = locationData[0];
          terminalData.latitude = locationData[1];
          terminalData.time = this.followTrack[str].data[this.followTrack[str].data.length - 1].time;
        }
        this.getVehicleMapMarker([terminalData]);
      }
      if (this.followTrack[str]?.timer) {
        clearTimeout(this.followTrack[str].timer);
        this.followTrack[str].timer = null;
      }
      this.followTrack[str] = null;
      if (data.deviceIdStr === this.vehicleData.deviceIdStr) {
        this.$nextTick(() => {
          this.$refs.vehicleInfo?.setFollowState();
        });
      }
    },
    followCloseAll() {
      console.log('关闭所有跟踪');
      // this.followCar.forEach(v => {
      //   this.setFollowClose(v);
      // });
      for (let i = this.followCar.length - 1; i >= 0; i--) {
        const element = this.followCar[i];
        this.setFollowClose(element);
      }
    },
    // 绘制轨迹
    trackHandle(data) {
      const str = data.deviceType + '-' + data.deviceId;
      this.followTrack[str] = {};
      this.followTrack[str].data = [];
      this.followTrack[str].isDraw = true;
      let coors = this.$refs.MapWidget.formatPathData(this.followTrack[str].data);
      this.followTrack[str].line = this.$refs.MapWidget.getPath(coors);
      this.$refs.MapWidget.map.add(this.followTrack[str].line);
    },
    /**
     * 删除地图元素与表格数据
     */
    clearAll(val = true) {
      this.dialogVisible = false;
      this.vehicleMarkerList = [];
      this.monitorCarObj = {};
      // 更新定位信息表
      this.$refs.monitorTab.setData(this.vehicleMarkerList, this.monitorCarObj);
      this.drawMarker.clearMarkerToMarkersCluster(1);
      if (val) {
        this.handleClear();
      }
    },
    /**
     * @param {Array} val
     * @param {String} val[].id
     * @param {String} val[].licencePlate
     */

    getVehicleMapMarker(carData, flag = false) {
      if (this.isPlayTrack) {
        this.handleClear();
      }
      console.time();
      const locationData = this.$utils.wgs84togcj02Batch(carData);
      let markerList = [];
      for (let index = 0; index < locationData.length; index++) {
        const element = locationData[index];
        let labelTypeData = {
          iconUrl: this.judgeTerminalIcon(element),
          bgUrl: this.judgeBackgroundIcon(element),
          iconWidth: 50,
          iconHeight: 50
        };
        let markerRenderParam = {
          iconUrl: labelTypeData.iconUrl,
          bgUrl: labelTypeData.bgUrl,
          iconWidth: labelTypeData.iconWidth,
          iconHeight: labelTypeData.iconHeight,
          labelTypeId: 1,
          vehicleId: element.deviceIdStr,
          // labelTypeName: '车辆',
          labelTypeName: element.targetName,
          longitude: element.longitude,
          latitude: element.latitude,
          speed: element.speed,
          bearing: element.bearing,
          time: element.time,
        };
        markerList.push(markerRenderParam);
      }
      console.timeEnd();
      // setFitView: 该参数用于marker刷新
      this.drawMarker.addMarkerToMarkersCluster(markerList, flag);
    },

    async getVehicleMap(val, checkedList) {
      console.time();
      let thisTime = new Date().getTime();
      this.timeTrack = thisTime;
      // 勾选
      if (val.checked) {
        this.activeName = 'monitorTab';
        let emptyLocation = []; // 没有定位的车辆
        let stateTerminalList = []; // 需要更新状态的终端
        const checkedObj = checkedList.reduce((acc, currentValue) => {
          acc[currentValue.id] = currentValue.fusionState;
          return acc;
        }, {});
        let query = {
          deviceIds: checkedList.map(item => BigInt(item.id))
        };
        await terminalStates(query).then(res => {
          if (thisTime !== this.timeTrack) {
            return;
          }
          let vehicleList = [];
          for (let index = 0; index < res.data.content.length; index++) {
            const element = res.data.content[index];
            element.treeCategory = String(element.deviceCategory);
            element.fusionState = element.status;
            if (checkedObj[element.deviceIdStr] !== element.status) {
              stateTerminalList.push({
                id: element.deviceIdStr,
                fusionState: element.status
              });
            }
            this.monitorCarObj[element.deviceIdStr] = true;
            const str = element.deviceType + '-' + element.deviceIdStr;
            // 有经度纬度且没在跟踪的终端
            if (element.longitude && element.latitude && !this.followTrack[str]?.timer) {
              vehicleList.push(element);
            } else if (!element.longitude || !element.latitude) {
              emptyLocation.push(`[${element.targetName}]`);
            }
          }
          if (vehicleList.length) {
            this.getVehicleMapMarker(vehicleList, true);
          }
          // 将当前选中的车辆都记录下来
          this.vehicleMarkerList = this.vehicleMarkerList.concat(res.data.content || []);
          this.$nextTick(() => {
            this.$refs.terminalMultiSelectTree.updateStateTerminal(stateTerminalList);
          });
        });
        if (emptyLocation.length === 1) {
          this.$notify({
            title: '提示',
            message: `设备没有定位点`,
            type: 'warning'
          });
          // 关闭车辆详情窗口(替换之前的closeInfoWindow)
          this.dialogVisible = false;
        } else if (emptyLocation.length > 1) {
          let str = '';
          emptyLocation.forEach(v => {
            str = `${str}<p>${v}</p>`;
          });
          this.$notify({
            title: '提示',
            message: `${str}没有定位点`,
            dangerouslyUseHTMLString: true,
            type: 'warning'
          });
          // 关闭车辆详情窗口(替换之前的closeInfoWindow)
          this.dialogVisible = false;
        }
        // 更新定位信息表
        this.$refs.monitorTab.setData(this.vehicleMarkerList, this.monitorCarObj);
      } else { // 取消勾选
        this.dialogVisible = false;
        let cancelCar = checkedList.map(item => item.id); // 取消选中的车辆
        for (let index = 0; index < cancelCar.length; index++) {
          const element = cancelCar[index];
          delete this.monitorCarObj[element];
        }
        this.drawMarker.removeMarkerFromCluster({ labelTypeId: 1, vehicleId: cancelCar }, true);
        // 当前有取消选中的车辆时, 将数组转为对象, 再从对象中删除取消选中的车辆, 再转为数组(比直接两个数组循环嵌套节省性能)
        if (cancelCar.length) {
          let obj = this.vehicleMarkerList.reduce((acc, curr) => {
            acc[curr.deviceIdStr] = curr;
            return acc;
          }, {});
          for (let index = 0; index < cancelCar.length; index++) {
            const element = cancelCar[index];
            delete obj[element];
          }
          this.vehicleMarkerList = Object.values(obj);
          // 更新定位信息表
          this.$refs.monitorTab.setData(this.vehicleMarkerList, this.monitorCarObj);
        }
      }
      console.timeEnd();
    },
    handleClick(tab, event) {
      if (this.activeName === 'monitorTab') {
        this.$nextTick(() => {
          this.$refs?.monitorTab?.editTableHeight();
        });
      } else if (this.activeName === 'trackTab') {
        this.$nextTick(() => {
          this.$refs?.trackTab?.editTableHeight();
        });
      }
    },
    /**
     * 根据终端类型判断图标
     */
    judgeTerminalIcon (val) {
      const vehicleModel = ['101', '102', '103', '104', '105'];
      const materialsModel = ['106', '108', '109', '111', '112', '114'];
      const personnelModel = ['201', '202', '301', '107', '110', '113'];
      const shortMessageModel = ['302', '303'];
      const timeServiceModel = ['501', '502', '503'];
      const monitorModel = ['401', '402'];
      let vehicleIcon = '';
      if (vehicleModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/vehicle.png`; // 车辆
      } else if (materialsModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/materials.png`; // 物资
      } else if (personnelModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/personnel.png`; // 人员
      } else if (shortMessageModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/shortMessage.png`; // 短报文终端
      } else if (timeServiceModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/timeService.png`; // 授时终端
      } else if (monitorModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/monitor.png`; // 监测终端
      } else if (val.treeCategory === '0') {
        vehicleIcon = `/bdsplatform/static/images/pic/other.png`; // 其他
      }
      return vehicleIcon;
    },
    judgeBackgroundIcon (val) {
      // 0-离线 1-静止 2-移动
      // 离线(Offline) 静止(Static) 移动(Move)
      let vehicleIcon = '';
      switch (val.fusionState) {
      case 0:
        vehicleIcon = `/bdsplatform/static/images/pic/offline.png`;
        break;
      case 1:
        vehicleIcon = `/bdsplatform/static/images/pic/static.png`;
        break;
      case 2:
        vehicleIcon = `/bdsplatform/static/images/pic/move.png`;
        break;
      }
      return vehicleIcon;
    },
    /**
     * 自动更新车辆列表
     */
    autoUpdateVehicleTreeInCycke() {
      this.$refs.terminalMultiSelectTree.onUpdate();
    },
    // 刷新车辆数据
    async CheckedNodesUpdate() {
      if (Object.keys(this.monitorCarObj).length === 0) {
        return;
      }
      this.vehicleMarkerList = [];
      let query = {
        deviceIds: Object.keys(this.monitorCarObj).map(item => BigInt(item))
      };
      // 批量请求车辆数据
      await terminalStates(query).then(res => {
        for (let index = 0; index < res.data.content.length; index++) {
          const element = res.data.content[index];
          element.treeCategory = String(element.deviceCategory);
          element.fusionState = element.status;
        }
        // 将当前选中的车辆都记录下来
        this.vehicleMarkerList = res.data.content;
      });
      let list = this.$utils.wgs84togcj02Batch(this.vehicleMarkerList);
      for (let i = list.length - 1; i >= 0; i--) {
        const element = list[i];
        let index = this.followCar.findIndex(item => item.deviceIdStr === element.deviceIdStr);
        if (index !== -1) {
          list.splice(i, 1);
        }
      }
      this.drawMarker.renewMarkerToMarkersCluster(list, this.judgeTerminalIcon, this.judgeBackgroundIcon, 1);
      // 更新定位信息表
      this.$refs.monitorTab.setData(this.vehicleMarkerList, this.monitorCarObj);
      // 更新车辆详情窗口内容(替换之前的信息窗体)
      const data = this.vehicleMarkerList.find(item=>item.deviceIdStr === this.vehicleData.deviceIdStr);
      if (data && data.time > this.vehicleData.time) {
        this.vehicleData = data;
      }
    },
    // 四维逆地理编码(后台接口)
    getAddress(data) {
      const query = {
        lon: Number(data.longitude),
        lat: Number(data.latitude)
      };
      vehicleAddress(query).then(res => {
        if (res.code === 200) {
          this.$set(data, 'address', res.data);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
  // padding: 0 4px 4px;
  display: flex;
  position: relative;
}

.map-widget {
  width: 100%;
  height: 100%;

  /deep/ .map-tool-bar {
     position: absolute;
     right: 10px;
  }
}

/deep/ .el-tabs__header {
    margin-bottom: 0;
    border-bottom: none;
        .el-tabs__nav {
            border: none;
        }
        .el-tabs__item {
            background: #f3f3f3;
            border-bottom: 1px solid #d8d8d8;
            border-left: 1px solid #d8d8d8;
            color: #333333;
        }
        .is-active {
            background: #114fb6;
            color: #ffffff;
        }
    }

/deep/.el-tabs__item {
  font-size: 16px;
}

.vehicle-info {
  width: 400px;
  position: absolute;
  top: 80px;
  right: 5px;
  background-color: white;
  z-index: 1000;
  border-radius: 5px;
  box-shadow: rgb(113, 144, 202) 1px 0px;
}

.follow-dialog {
  position: absolute;
  left: 313px;
  top: 20px;
  z-index: 9;
}

.collapse-btn-base {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  cursor: pointer;
  background-color: #b0b3b8;
  color: #ffffff;

  i {
    font-weight: bold;
  }
}
@space: 4px;
.collapse-vertical {
  width: @space;
  height: 100%;
  display: flex;
  align-items: center;
  cursor: ew-resize;
  .collapse-btn {
    width: 100%;
    height: 90px;
    .collapse-btn-base
  }
}

.collapse {
  height: @space;
  display: flex;
  justify-content: center;
  cursor: ns-resize;
  .collapse-btn {
    height: 100%;
    width: 90px;
    overflow: hidden;
    margin: 0 10px;
    .collapse-btn-base
  }
}

section {
  .chunk {
    border: 1px solid #e1e5e8;
    background-color: #ffffff;
  }
}

.left {
  width: 300px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  flex-shrink: 0;
}

.right {
  flex: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;

  .map-container {
    flex: 1;
    // height: calc(100% - 280px);
  }

  .table-container {
    height: 324px;
    overflow: auto;
    z-index: 10;
  }

  .table-container-full {
    height: 100% !important;
  }

  .table-container-none {
    height: 0 !important;
  }

  .table-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;

    /deep/ .el-tabs__content {
      flex: 1;

      .el-tab-pane {
        height: 100%;
      }
    }
    // ::v-deep .el-tabs__item{
    //   height: 36px;
    //   line-height: 36px;
    // }
  }
}
.config-container {
  position: absolute;
  top: 80px;
  right: 15px;
  padding: 27px 27px 20px 10px;
  background-color: #ffffff;
  border-radius: 5px;
  box-shadow: 1px 1px 6px #88888850;
  .config-close {
    position: absolute;
    right: 12px;
    top: 12px;
    font-size: 16px;
    cursor: pointer;
  }
}

// 不同分辨率媒体查询样式

@media screen and (max-width: 1500px) {
    // 底部表格
    .table-container {
      height: 240px !important;
    }
}
</style>
