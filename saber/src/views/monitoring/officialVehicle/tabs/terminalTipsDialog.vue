<template>
  <div>
    <transition name="el-fade-in-linear">
      <div
        v-show="vehicleTips"
        class="vehicle-dialog"
      >
        <div class="vehicle-table">
          <el-table
            ref="table"
            :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
            :data="staffData"
            :cell-style="{'text-align':'center'}"
          >
            <el-table-column
              type="index"
              width="55"
              label="序号"
            />
            <el-table-column
              prop="image"
              label="设备列表图标"
              width="150"
              :resizable="false"
            >
              <template slot-scope="scope">
                <svg-icon
                  :icon-class="svgStaffType(scope.row.image, 'vehicle')"
                />
              </template>
            </el-table-column>
            <el-table-column
              prop="image"
              label="设备地图图标"
              width="150"
              :resizable="false"
            >
              <template slot-scope="scope">
                <div class="map-icon">
                  <div
                    class="map-icon-bg"
                    :style="{ background: `url(${judgeBackgroundIcon(scope.row.image)})`, backgroundSize: '100%' }"
                  >
                    <img :src="judgeTerminalIcon('vehicle')">
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="text"
              width="100"
              label="状态"
              :resizable="false"
            />
            <el-table-column
              prop="explain"
              label="说明"
              width="300"
              :resizable="false"
            />
          </el-table>
          <span class="vehicle-tips">tips: 车辆状态图标。</span>
          <div
            class="close-btn"
            @click="questionHandle"
          >
            <i class="el-icon-circle-close" />
          </div>
        </div>
      </div>
    </transition>
    <div
      v-show="vehicleTips"
      class="vehicle-shade"
    />
  </div>
</template>
<script>
export default {
  data() {
    return {
      vehicleTips: false,
      staffData: [
        {image: 0, text: '离线', explain: '离线是终端断开连接，没有上传数据'},
        {image: 1, text: '静止', explain: '终端在静止'},
        {image: 2, text: '移动', explain: '终端在移动'}
      ]
    };
  },
  methods: {
    questionHandle () {
      this.vehicleTips = !this.vehicleTips;
    },
    svgStaffType (val, type) {
      // 0-离线 1-静止 2-移动
      // 离线(Offline) 静止(Static) 移动(Move)
      let vehicleIcon = '';
      switch (val) {
      case 0:
        vehicleIcon = `${type}Offline`;
        break;
      case 1:
        vehicleIcon = `${type}Static`;
        break;
      case 2:
        vehicleIcon = `${type}Move`;
        break;
      }
      return vehicleIcon;
    },
    /**
     * 根据终端类型判断图标
     */
    judgeTerminalIcon (type) {
      let vehicleIcon = `/bdsplatform/static/images/pic/${type}.png`;
      return vehicleIcon;
    },
    judgeBackgroundIcon (val) {
      // 0-离线 1-静止 2-移动
      // 离线(Offline) 静止(Static) 移动(Move)
      let vehicleIcon = '';
      switch (val) {
      case 0:
        vehicleIcon = `/bdsplatform/static/images/pic/offline.png`;
        break;
      case 1:
        vehicleIcon = `/bdsplatform/static/images/pic/static.png`;
        break;
      case 2:
        vehicleIcon = `/bdsplatform/static/images/pic/move.png`;
        break;
      }
      return vehicleIcon;
    }
  }
};
</script>

<style lang="less" scoped>
.vehicle-dialog{
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .vehicle-table{
      width: max-content;
      background-color: white;
      padding: 20px;
      position: relative;
      /deep/ .el-table{
        .svg-icon{
          width: 35px;
          height: 35px;
          margin-right: 5px;
        }
      }
      .close-btn{
        position: absolute;
        top: -45px;
        right: -35px;
        font-size: 35px;
        color: white;
        cursor: pointer;
      }
    }
    .staff-table {
      margin-top: 30px;
    }
  }
  .vehicle-shade{
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.4);
    z-index: 2999;
  }
  .vehicle-tips{
    font-size: 14px;
    color: rgb(153, 153, 153);
    display: inline-block;
    padding-top: 5px;
  }
  .map-icon {
    display: flex;
    .map-icon-bg {
      width: 50px;
      height: 50px;
    }
    img {
      width: 50px;
      height: 50px;
    }
  }
</style>
