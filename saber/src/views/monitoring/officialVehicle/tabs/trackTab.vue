<template>
  <div class="car-track-info">
    <div
      class="info-all"
    >
      <div
        class="row-bg"
      >
        <div class="config-btn">
          <!-- 降低播放速度 -->
          <span
            class="player-icon player-slow"
            title="降低播放速度"
            @click="onPlayerFastBack"
          />
          <!-- 暂停播放 -->
          <span
            v-if="IsPlaying"
            class="player-icon player-pause"
            title="暂停/继续播放"
            @click="onPlayerPause"
          />
          <!-- 播放 -->
          <span
            v-else
            class="player-icon player-play"
            title="播放"
            @click="onPlayerPlay"
          />
          <!-- 增加播放速度 -->
          <span
            class="player-icon player-fast"
            title="增加播放速度"
            @click="onPlayerFastFront"
          />
          <!-- 停止播放 -->
          <span
            class="player-icon player-stop"
            title="停止播放"
            @click="onPlayerStop"
          />
        </div>
        <div class="config-label">
          <span class="search_carname">播放速度：</span>
          <span class="search_form_centent_carid">{{ playSpeedString }}</span>
        </div>
        <div class="config-range">
          <span>播放进度：</span>
          <x-range
            class="history_range"
            :value="HistoryRange"
            :disabled="canCheck"
            @update:value="onRangeMove"
            @closeAnimation="closeAnimation"
          />
        </div>
        <div class="config-check">
          <el-checkbox
            v-model="showLocationPoint"
            :disabled="canCheck"
          >
            定位点
          </el-checkbox>
          <!-- <el-checkbox
            v-model="showTimeSpeed"
            class="setting-info"
            :disabled="canCheck"
          >
            时间/速度
          </el-checkbox>
          <el-checkbox
            v-model="showAddress"
            class="setting-info"
            :disabled="canCheck"
          >
            显示地址
          </el-checkbox> -->
        </div>
        <div class="config-export">
          <el-button
            size="mini"
            @click="onExportDeviceData"
          >
            导出
          </el-button>
        </div>
      </div>
      <TrackTable
        ref="trackTable"
        :dict="dict"
        @goToPoint="goToPoint"
      />
    </div>
  </div>
</template>

<script>
import xRange from './range';
import TrackTable from './trackTable.vue';
// 图资尺寸 : 用户偏移位置
const ICON_SIZE = 32;
export default {
  name: 'CarTrackInfo',
  components: {
    'x-range': xRange,
    TrackTable
  },
  props: {
    dict: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      HistoryRange: 0,
      IsPlaying: false,
      // 速度数组 ， 用于显示速度图表
      SpeedsArray: [],
      playSpeed: 1300, // 播放速度
      playSpeedString: '正常',
      CarMarker: null,
      PathMarker: null,
      Map: null,
      TrackData: null,
      Player: {
        index: 0,
        coors: []
      },
      // 定位点层
      trackPointGroups: null,
      // 新增起始点定位
      trackPointGroupsSub: null,
      // 时间速度层
      timeSpeedGroups: null,
      // 地址层
      addressGroups: null,
      // 单个点文本层
      signalGroups: null,
      truckData: [],
      showLocationPoint: false,
      showTimeSpeed: false,
      showAddress: false,
      canCheck: true,
      schedule: 0,
      presentLocation: [],
      timer: null,
      timerSpeed: 0,
      labelTypeData: null
    };
  },
  watch: {
    showLocationPoint (val) {
      if (val) {
        this.trackPointGroups ? this.trackPointGroups.show() : this.setTrackPoint(this.truckData);
      } else {
        if (this.trackPointGroups) {
          this.trackPointGroups.hide();
        }
      }
    },
    showTimeSpeed (val) {
      if (val) {
        this.timeSpeedGroups ? this.timeSpeedGroups.show() : this.setTimeSpeed(this.truckData);
        this.clearSignalPointText();
      } else {
        if (this.timeSpeedGroups) {
          this.timeSpeedGroups.hide();
        }
      }
    },
    showAddress (val) {
      if (val) {
        this.addressGroups ? this.addressGroups.show() : this.setAddress(this.truckData);
        this.clearSignalPointText();
      } else {
        if (this.addressGroups) {
          this.addressGroups.hide();
        }
      }
    }
  },
  methods: {
    editTableHeight() {
        this.$nextTick(() => {
            this.$refs?.trackTable?.editTableHeight();
        })
    },
    closeAnimation() {
      if (this.IsPlaying) {
        if (this.timer) {
          clearTimeout(this.timer);
          this.timer = null;
        }
        this.CarMarker.stopMove(); // 终止动画
      }
    },
    // 清空轨迹和表格数据(给CarTrackConfig组件使用)
    clearTrackData() {
      this.clearOverlays();
      this.$refs.trackTable.setTableData([]);
    },
    formatChartData (_data) {
      let data = _data || [];
      let chartData = [];
      for (let i = 0; i < data.length; i++) {
        const element = data[i];
        let item = {
          value: [element.time * 1000, element.speed]
        };
        if (i > 0 && data[i].time - data[i - 1].time > 1800) {
          item.value[1] = 0;
        }
        if (i < data.length - 1 && data[i + 1].time - data[i].time > 1800) {
          item.value[1] = 0;
        }
        chartData.push(item);
      }
      return chartData;
    },
    formatPathData (_data) {
      if (!_data) {
        return [];
      }
      let coors = [];
      for (let i = 0; i < _data.length; i++) {
        const element = _data[i];
        let coor = [Number(element.longitude), Number(element.latitude)];
        coors.push(coor);
      }
      return coors;
    },
    setInfoData (_data) {
      let data = _data || {};
      this.updateRange(data.time * 1000);
      if (data.index === data.total) {
        this.setPlayerStatus(false);
      }
    },
    setPlayerStatus (_bool) {
      this.IsPlaying = _bool || false;
    },
    /**
     * 开始时间，结束时间，当前时间
     */
    updateRange (_timeCurrent) {
      if (!this.SpeedsArray.length) {
        return;
      }
      let timeStart = this.SpeedsArray[0].value[0];
      let timeEnd = this.SpeedsArray[this.SpeedsArray.length - 1].value[0];
      let timeDistance = timeEnd - timeStart;
      let indexTime = _timeCurrent - timeStart;
      let range = indexTime / timeDistance;
      // range *= 100;
      // console.info('--> updateRange',range );
      this.HistoryRange = range;
    },
    /**
     * 设置车辆图标
     * 设置地图
     */
    setTrackElement (_opts) {
      let opts = _opts || {};
      this.Map = opts.map;
      this.AMap = opts.amap;
      this.CarMarker = opts.car;
      this.PathMarker = opts.path;
      this.TrackData = opts.track;
      this.Player.coors = this.formatPathData(opts.track);
      this.labelTypeData = opts.labelTypeData;
      // 点击地图空白区域，取消单个marker的文字标注
      this.Map.on('click', (e) => {
        this.clearSignalPointText();
      });
      this.Map.on('zoomchange', (e) => {
        let zoom = this.Map.getZoom();
        console.log('zoom: ', zoom);
      });
      this.CarMarker.on('moving', (e)=>{
        const markerDom = e.target.dom;
        let bgDom = markerDom.getElementsByClassName('follow-marker-bg')[0];
        bgDom.style.transform = `rotate(${e.target['_style'].rotate}deg)`;
        this.presentLocation = [e.passedPath[e.passedPath.length - 1].lng, e.passedPath[e.passedPath.length - 1].lat];
        // 设置地图位置
        if (!this.isInBound(this.presentLocation)) {
          this.Map.panTo(this.presentLocation);
        }
        if (this.Player.index !== e.passedPath.length - 2 + this.schedule ) {
          this.Player.index = e.passedPath.length - 2 + this.schedule;
          this.carMoving(this.Player.index);
        }
      });
      this.CarMarker.on('movealong', ()=>{
        if (this.Player.index !== this.Player.coors.length - 1) {
          this.carMoving(this.Player.coors.length - 1);
        }
        this.setPlayerStatus(false);
        this.schedule = 0;
        this.Player.index = 0;
        this.presentLocation = [];
        this.IsPlaying = false;
      });
    },
    /**
     * 设置已经过的路线
     */
    setPassPath (_coor, _index) {
      let path = Array.from(this.Player.coors);
      path.length = _index;
      path.push(_coor);
      this.PathMarker.setPath(path);
    },
    /**
     *  小车移动
     */
    carMoving (_index) {
      let historyInfo = this.TrackData[_index];
      if (_index && this.TrackData[_index - 1].speed !== historyInfo.speed) {
        const markerDom = this.CarMarker.dom;
        let bgDom = markerDom.getElementsByClassName('follow-marker-bg')[0];
        this.labelTypeData.bgUrl = historyInfo.speed ? '/bdsplatform/static/images/pic/move.png' : '/bdsplatform/static/images/pic/static.png';
        bgDom.style.backgroundImage = `url(${this.labelTypeData.bgUrl})`;
      }
      // 更新信息
      historyInfo.index = _index;
      historyInfo.total = this.TrackData.length - 1;
      this.setInfoData(historyInfo);
      this.$refs.trackTable.$refs.utable.setCurrentRow(this.TrackData[_index]);
      // 表格滚动条: top,left -> 距离顶部,左侧距离,不传值默认为0
      // 54为单位格的高度，-3为了将滚动条调整到视图下方位置
      this.$refs.trackTable.$refs.utable.pagingScrollTopLeft((this.TrackData[_index].customizeId - 3) * 54, 0);
    },
    /**
     * 判断是否在地图范围内
     * 在 返回 true
     */
    isInBound (_lnglat) {
      const bounds = this.Map.getBounds();
      const NorthEast = bounds.getNorthEast();
      const SouthWest = bounds.getSouthWest();
      const SouthEast = [NorthEast.lng, SouthWest.lat];
      const NorthWest = [SouthWest.lng, NorthEast.lat];
      const path = [[NorthEast.lng, NorthEast.lat], SouthEast, [SouthWest.lng, SouthWest.lat], NorthWest];
      const isPointInRing = this.AMap.GeometryUtil.isPointInRing(_lnglat, path);
      return isPointInRing;
    },
    switchTimerSpeedString () {
      switch (this.playSpeed) {
        case 100:
          this.playSpeedString = '特快';
          break;
        case 500:
          this.playSpeedString = '较快';
          break;
        case 900:
          this.playSpeedString = '快';
          break;
        case 1300:
          this.playSpeedString = '正常';
          break;
        case 1700:
          this.playSpeedString = '慢';
          break;
        case 2100:
          this.playSpeedString = '较慢';
          break;
        case 2500:
          this.playSpeedString = '特慢';
          break;
        default:
          break;
      }
    },
    // 暂停
    onPlayerPause () {
      this.setPlayerStatus(false);
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      this.CarMarker.stopMove(); // 终止动画
    },
    // 播放减速
    onPlayerFastBack () {
      if (this.canCheck) {
        return;
      }
      // 最低速度限制
      if (this.playSpeed === 2500) {
        return;
      }
      this.playSpeed += 400;
      // this.timerSpeed += 150;
      this.switchTimerSpeedString();
      if (this.IsPlaying) {
        if (this.timer) {
          clearTimeout(this.timer);
          this.timer = null;
        }
        this.CarMarker.stopMove(); // 终止动画
        this.$nextTick(() => {
          this.onPlayerPlay();
        });
      }
    },
    // 播放开始
    onPlayerPlay () {
      if (this.canCheck) {
        return;
      }
      this.setPlayerStatus(true);
      if ((this.Player.index && this.Player.index !== this.TrackData.length - 1) || this.presentLocation.length) {
        let list;
        this.schedule = this.Player.index;
        if (this.presentLocation.length) { // 手动点击暂停或控制速度时
          list = this.Player.coors.slice(this.Player.index + 1);
          list.unshift(this.presentLocation);
        } else { // 滑动进度条时
          list = this.Player.coors.slice(this.Player.index);
        }
        this.$nextTick(() => {
          this.CarMarker.moveAlong(list, {
            duration: this.playSpeed,
            autoRotation: true
          });
        });
      } else {
        this.schedule = 0;
        this.CarMarker.moveAlong(this.Player.coors, {
          duration: this.playSpeed,
          autoRotation: true
        });
        this.carMoving(0);
      }
    },
    // 播放加速
    onPlayerFastFront () {
      if (this.canCheck) {
        return;
      }
      // 最高速度限制
      if (this.playSpeed === 100) {
        return;
      }
      this.playSpeed -= 400;
      // this.timerSpeed -= 150;
      this.switchTimerSpeedString();
      if (this.IsPlaying) {
        if (this.timer) {
          clearTimeout(this.timer);
          this.timer = null;
        }
        this.CarMarker.stopMove(); // 终止动画
        this.$nextTick(() => {
          this.onPlayerPlay();
        });
      }
    },
    // 播放停止
    onPlayerStop () {
      /* if (this.canCheck) {
        return;
      } */
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      this.CarMarker.stopMove(); // 终止动画
      this.setPlayerStatus(false);
      this.IsPlaying = false;
      // 重置数据
      this.carMoving(0);
      this.Player.index = 0;
      this.HistoryRange = 0;
      this.schedule = 0;
      this.presentLocation = [];
      let trackIndex = this.TrackData[0];
      let lnglat = [trackIndex.longitude, trackIndex.latitude];
      this.CarMarker.setPosition(lnglat);
      this.Map.setCenter(lnglat);
    },
    trackRangeMove (e, type) {
      this.presentLocation = [];
      // 进度改变
      this.Player.index = e;
      this.carMoving(this.Player.index);
      let trackIndex = this.TrackData[e];
      let lnglat = [trackIndex.longitude, trackIndex.latitude];
      this.CarMarker.setPosition(lnglat);
      this.Map.setCenter(lnglat);
      // type = range不触发动画
      if (this.IsPlaying && type !== 'range') {
        this.onPlayerPlay();
      }
    },
    onRangeMove (_rangeVal, type) {
    //   console.info('--> onRangeMove',_rangeVal);
      if (!this.SpeedsArray.length) {
        return;
      }
      let range = _rangeVal;
      // 得到位置百分数 算出位置的时间
      let timeStart = this.SpeedsArray[0].value[0];
      let timeEnd = this.SpeedsArray[this.SpeedsArray.length - 1].value[0];
      let timeDistance = timeEnd - timeStart;
      let indexTime = range * timeDistance + timeStart;

      let date = new Date();
      date.setTime(indexTime);
      // 根据时间遍历出点
      for (let i = 0; i < this.SpeedsArray.length; i++) {
        const element = this.SpeedsArray[i];
        if (element.value[0] === indexTime) {
          let index = i;
          this.trackRangeMove(index, type);
          return i;
        } else if (element.value[0] > indexTime) {
          // this.$emit('onRangeMove',i-1);
          let index = i - 1 > 0 ? i - 1 : 0;
          this.trackRangeMove(index, type);
          return i;
        }
      }
    },
    // 导出设备轨迹
    onExportDeviceData() {
      this.$emit('onExportDeviceData');
    },
    /**
     * 清除轨迹相关
     */
    clearOverlays () {
      // this.showLocationPoint = false;
      this.canCheck = true;
      this.driftChecked = false;
      this.showTimeSpeed = false;
      this.showAddress = false;
      if (this.PathMarker) {
        // this.Map.remove(this.PathMarker);
        this.PathMarker.hide();
        // 清除轨迹
      }
      if (this.trackPointGroups) {
        this.trackPointGroups.clear();
        // this.trackPointGroups.clearOverlays();
        this.Map.remove(this.trackPointGroups);
        this.trackPointGroups = null;
      }
      if (this.trackPointGroupsSub) {
        this.trackPointGroupsSub.clear();
        this.Map.remove(this.trackPointGroupsSub);
        this.trackPointGroupsSub = null;
      }
      if (this.timeSpeedGroups) {
        this.timeSpeedGroups.clear();
        // this.timeSpeedGroups.clearOverlays();
        this.Map.remove(this.timeSpeedGroups);
        this.timeSpeedGroups = null;
      }
      if (this.addressGroups) {
        this.addressGroups.clear();
        // this.addressGroups.clearOverlays();
        this.Map.remove(this.addressGroups);
        this.addressGroups = null;
      }
      this.clearSignalPointText();
    },
    /**
     * 轨迹加载
     */
    setTrackPath (coor) {
      let path = Array.from(coor);
      this.PathMarker.setPath(path);
      this.PathMarker.show();
      setTimeout(() => {
        // this.setTrackPoint(this.truckData);
        this.setTrackPointSub(this.truckData);
      }, 100);
      this.canCheck = false;
    },
    /**
     * 开始结束加载
     */
    setTrackPointSub(currentData) {
      this.trackPointGroupsSub = new this.AMap.LabelsLayer({
        zooms: [2, 26],
        collision: false
      });
      [0, currentData.length - 1].forEach((i, index) => {
        let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
        let iconStyle = this.getIconStyleSub(index === 0 ? 's' : 'e');
        let pointMarker = new this.AMap.LabelMarker({
          position: position,
          icon: {
            image: iconStyle,
            anchor: 'bottom-center',
            size: [28, 36]
          }
        });
        pointMarker.on('click', (e) => {
          this.setSinglePointText([currentData[i]]);
        });
        this.trackPointGroupsSub.add(pointMarker);
      });
      this.Map.add(this.trackPointGroupsSub);
    },
    /**
     * 定位点加载
     */
    setTrackPoint (currentData) {
      if (this.trackPointGroups) {
        this.trackPointGroups.hide();
        return;
      }
      this.trackPointGroups = new this.AMap.LabelsLayer({
        zooms: [2, 26],
        collision: false
      });
      for (let i = 0; i < currentData.length; i++) {
        let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
        let iconStyle = this.getIconStyle(currentData[i]);
        let pointMarker = new this.AMap.LabelMarker({
          position: position,
          icon: {
            image: iconStyle,
            anchor: 'bottom-center',
            size: [11, 12]
          }
        });
        pointMarker.on('click', (e) => {
          this.$refs.trackTable.$refs.utable.setCurrentRow(currentData[i]);
          // 表格滚动条: top,left -> 距离顶部,左侧距离,不传值默认为0
          // 54为单位格的高度，-3为了将滚动条调整到视图中间位置
          this.$refs.trackTable.$refs.utable.pagingScrollTopLeft((currentData[i].customizeId - 3) * 54, 0);
          // 处理定位
          this.setSinglePointText([currentData[i]]);
        });
        this.trackPointGroups.add(pointMarker);
      }
      this.Map.add(this.trackPointGroups);
    },
    /**
     * 时间速度加载
     */
    setTimeSpeed (currentData) {
      if (this.timeSpeedGroups) {
        this.timeSpeedGroups.hide();
        return;
      }
      this.timeSpeedGroups = new this.AMap.LabelsLayer({
        zooms: [2, 26],
        collision: false
      });
      for (let i = 0; i < currentData.length; i++) {
        let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
        let colorStyle = this.getColor(currentData[i]);
        let timeSpeedText = new this.AMap.LabelMarker({
          position: position,
          text: {
            content: this.$moment(currentData[i].time * 1000).format('YYYY/MM/DD HH:mm:ss') + ', ' + currentData[i].speed + 'km/h',
            direction: 'right',
            offset: [6, -18],
            style: {
              'fontSize': 12,
              'backgroundColor': 'rgba(255, 255, 255, 0.1)',
              'fillColor': colorStyle,
              'strokeColor': 'white',
              'strokeWidth': 4
            }
          }
        });
        this.timeSpeedGroups.add(timeSpeedText);
      }
      this.Map.add(this.timeSpeedGroups);
    },
    /**
     * 地址加载
     */
    setAddress (currentData) {
      if (this.addressGroups) {
        this.addressGroups.hide();
        return;
      }
      this.addressGroups = new this.AMap.LabelsLayer({
        zooms: [2, 26],
        collision: false
      });
      for (let i = 0; i < currentData.length; i++) {
        let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
        let colorStyle = this.getColor(currentData[i]);
        let addressText = new this.AMap.LabelMarker({
          position: position,
          text: {
            content: currentData[i].locAddr,
            title: currentData[i].locAddr,
            direction: 'right',
            offset: [6, -4],
            style: {
              'fontSize': 12,
              'backgroundColor': 'rgba(255, 255, 255, 0.1)',
              'fillColor': colorStyle,
              'strokeColor': 'white',
              'strokeWidth': 4
            }
          }
        });
        this.addressGroups.add(addressText);
      }
      this.Map.add(this.addressGroups);
    },
    setSinglePointText (currentData) {
      if (this.showTimeSpeed) {
        this.showTimeSpeed = false;
      }
      if (this.showAddress) {
        this.showAddress = false;
      }
      this.clearSignalPointText();
      let markerCollection = [];
      let infoWindow = null;
      for (let i = 0; i < currentData.length; i++) {
        let position = new this.AMap.LngLat(currentData[i].longitude, currentData[i].latitude);
        var info = [];
        info.push("<div class='input-card content-window-card' style='width: 435px;>");
        info.push("<div style='padding:7px 0px 15px 0px;><h4>" + `${currentData[i].targetName}` + '</h4>');
        info.push("<p class='input-item' style='margin: 10px'>定位信息：" + `${currentData[i].speed}` + 'km/h,' + `${this.getDirection(currentData[i].bearing)}`);
        info.push("<p class='input-item' style='margin: 10px'>经纬度：" + `${this.handlePosition(currentData[i].longitudeTable)}` + ',' + `${this.handlePosition(currentData[i].latitudeTable)}`);
        info.push("<p class='input-item' style='margin: 10px'>总里程：" + `${currentData[i].mileage}` + 'km');
        // info.push("<p class='input-item' style='margin: 10px'>状态：" + `${currentData[i].stateAcc}`);
        info.push("<p class='input-item' style='margin: 10px'>定位时间：" + `${this.parseTimes(currentData[i].time)}`);
        info.push("<p class='input-item' style='margin: 10px'>地址：" + `${currentData[i].locAddr}`);

        infoWindow = new this.AMap.InfoWindow({
          position: position,
          anchor: 'bottom-center',
          content: info.join(''),
          offset: [0, -15]
        });
        markerCollection.push(infoWindow);
      }
      this.signalGroups = new this.AMap.OverlayGroup(markerCollection);
      this.Map.add(this.signalGroups);
    },
    goToPoint (row) {
      this.Map.setZoomAndCenter(18, [row.longitude, row.latitude]);
      this.setSinglePointText([row]);
    },
    setTableData (data) {
      this.$refs.trackTable.setTableData(data);
      this.truckData = data;
      let speedData = this.formatChartData(data);
      this.SpeedsArray = speedData;
    },
    getIconStyle (data) {
      let item = data;
      let icon = item.speed ? require('@/assets/images/car/track-run.png') : require('@/assets/images/car/track-stop.png');
      if (item.alarmType) {
        icon = require('@/assets/images/car/track-alarm.png');
      }
      return icon;
    },
    getIconStyleSub (i) {
      let icon;
      if (i === 's') {
        icon = require('@/assets/images/car/track-start.png');
      } else if (i === 'e') {
        icon = require('@/assets/images/car/track-end.png');
      }
      return icon;
    },
    getColor (item) {
      let color = item.speed ? '#036eb8' : '#f4a11e';
      if (item.alarmType) {
        color = '#f41e1e';
      }
      return color;
    },
    clearSignalPointText () {
      if (this.signalGroups) {
        this.signalGroups.clearOverlays();
        this.Map.remove(this.signalGroups);
        this.signalGroups = null;
      }
    },
    parseTimes (time) {
      return this.$moment(time * 1000).format('YYYY/MM/DD HH:mm:ss');
    },
    getDirection (bearing) {
      if (bearing === 0) {
        return '正北';
      }
      if (bearing) {
        switch (true) {
          case bearing === 90:
            return '正东';
          case bearing === 180:
            return '正南';
          case bearing === 270:
            return '正西';
          case bearing > 0 && bearing < 90:
            return '东北';
          case bearing > 90 && bearing < 180:
            return '东南';
          case bearing > 180 && bearing < 270:
            return '西南';
          case bearing > 270 && bearing < 360:
            return '西北';
          default:
            return '无';
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
.car-track-info{
  height: 100%;
}
.info-all{
  height: 100%;
  overflow-y: hidden;
  overflow-x: hidden;
  border-radius: @xhBorderRadiusBase;
  background-color: #fff;
  padding: 3px;
  z-index: 1000;
  font-size: 12px;
  display: flex;
  flex-direction: column;
}
.el-col {
  border-radius: 4px;
}
.row-bg {
  height: 40px;
  padding: 4px 5px;
  background-color: white;
  display: flex;
  align-items: center;
  font-size: 14px;
}
.config-btn{
  display: flex;
}
.config-label{
  padding: 0 20px;
}
.config-range{
  flex: 1;
  display: flex;
  align-items: center;
}
.history_range{
  flex: 1;
}
.config-check{
  padding: 0 25px;
  // /deep/ .el-checkbox__inner{
  //   border: 1px solid #176AC0;
  // }
  // /deep/ .el-checkbox__input.is-checked .el-checkbox__inner{
  //   background-color: #176AC0;
  //   border-color: #176AC0;
  // }
  // /deep/ .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after{
  //   border-color: white;
  // }
}
.search_carname{
  margin-right: 4px;
  margin-top: 5px;
}
.search_form_centent_carid{
  color: #118de3;
  font-weight: bolder;
  margin-top: 4px;
}
.tooltip-item{
  margin-right: 4px;
}
.custom-input-card{
  width: 18rem;
}
.custom-input-card .btn:last-child{
  margin-left: 1rem;
}
.content-window-card{
  position: relative;
  width: 23rem;
  padding: 0.75rem 0 0 1.25rem;
  box-shadow: none;
  bottom: 0;
  left: 0;
}
/deep/.content-window-card p{
  height: 2rem;
}
.player-icon{
  display: inline-block;
  width: 22px;
  height: 23px;
  padding: 1px;
  border: 1px solid #176AC0;
  margin-right: 4px;
  background-size: 100% 100%;
  cursor: pointer;
}
.player-play{
  background-image: url("../../../../assets/images/track/player-play.svg");
}
.player-fast{
  background-image: url("../../../../assets/images/track/player-fast.svg");
}
.player-pause{
  background-image: url("../../../../assets/images/track/player-pause.svg");
}
.player-slow{
  background-image: url("../../../../assets/images/track/player-slow.svg");
}
.player-stop{
  background-image: url("../../../../assets/images/track/player-stop.svg");
}
.follow-marker {
  transform: translate(-25px, -25px) scale(1) !important;
}
</style>
