<template>
  <div
    ref="work_eltable"
    class="work_eltable"
  >
    <template>
      <u-table
        ref="utable"
        use-virtual
        highlight-current-row
        inverse-current-row
        class="offline-car-table"
        :data="tableData"
        :header-cell-style="headerStyle"
        :beautify-table="true"
        :border="false"
        :row-height="54"
        :height="tableHeight"
        @row-click="rowClick"
      >
        <u-table-column
          prop="customizeId"
          :label="getLabel('customizeId')"
          align="center"
          width="60"
          show-overflow-tooltip
          :resizable="false"
        />
        <u-table-column
          prop="deviceType"
          :label="getLabel('deviceType')"
          align="center"
          width="120"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            <span>{{ EnumerationTypeHandling('bdmDeviceType',scope.row.deviceType) }}</span>
          </template>
        </u-table-column>
        <u-table-column
          prop="deviceCategory"
          :label="getLabel('deviceCategory')"
          align="center"
          width="210"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            <span>{{ EnumerationTypeHandling('bdmDeviceType',scope.row.deviceCategory) }}</span>
          </template>
        </u-table-column>
        <u-table-column
          prop="deviceUniqueId"
          :label="getLabel('deviceUniqueId')"
          align="center"
          width="160"
          show-overflow-tooltip
          :resizable="false"
        />
        <u-table-column
          prop="targetType"
          :label="getLabel('targetType')"
          align="center"
          width="80"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            <span>{{ EnumerationTypeHandling('targetType',scope.row.targetType) | nullValueStr }}</span>
          </template>
        </u-table-column>
        <u-table-column
          prop="targetName"
          :label="getLabel('targetName')"
          align="center"
          width="130"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            {{ scope.row.targetName || $utils.emptymap.targetName }}
          </template>
        </u-table-column>
        <u-table-column
          prop="time"
          :label="getLabel('time')"
          align="center"
          width="180"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            <span class="table-date-td">{{ parseTimes(scope.row.time) }}</span>
          </template>
        </u-table-column>
        <u-table-column
          prop="speed"
          :label="getLabel('speed')"
          align="center"
          width="120"
          show-overflow-tooltip
          :resizable="false"
        />
        <u-table-column
          prop="mileage"
          :label="getLabel('mileage')"
          align="center"
          width="110"
          show-overflow-tooltip
          :resizable="false"
        />
        <u-table-column
          prop="longitudeTable"
          :label="getLabel('longitude')"
          align="center"
          width="120"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            {{ handlePosition(scope.row.longitudeTable) }}
          </template>
        </u-table-column>
        <u-table-column
          prop="latitudeTable"
          :label="getLabel('latitude')"
          align="center"
          width="120"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            {{ handlePosition(scope.row.latitudeTable) }}
          </template>
        </u-table-column>
        <u-table-column
          prop="bearing"
          :label="getLabel('bearing')"
          align="center"
          width="80"
          show-overflow-tooltip
          :resizable="false"
        />
        <u-table-column
          prop="locAddr"
          :label="getLabel('locAddr')"
          show-overflow-tooltip
          align="center"
          width="450"
          :resizable="false"
        />
        <u-table-column
          prop="gnssNum"
          :label="getLabel('gnssNum')"
          align="center"
          width="90"
          show-overflow-tooltip
          :resizable="false"
        />
        <u-table-column
          prop="wireless"
          :label="getLabel('wireless')"
          align="center"
          width="90"
          show-overflow-tooltip
          :resizable="false"
        />
        <u-table-column
          prop="ioState"
          :label="getLabel('ioState')"
          align="center"
          width="90"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            {{ scope.row.ioState === 1 ? '深度休眠' : scope.row.ioState === 2 ? '休眠' : '无' }}
          </template>
        </u-table-column>
        <u-table-column
          prop="batch"
          label="上传类型"
          align="center"
          width="90"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            {{ getEnumDictLabel('batch', scope.row.batch) }}
          </template>
        </u-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/nodata.png')"
        />
      </u-table>
    </template>
  </div>
</template>

<script>
import getLabel from '@/utils/getLabel';
import { batchAddr } from '@/api/monitoring/track.js';
export default {
  name: 'TrackTable',
  components: {
  },
  props: {
    dict: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      headerStyle: {
        'background-color': '#F7F7F7',
        'font-size': '14px'
      },
      // tableHeight: window.outerHeight - 840,
      tableData: [],
      tableHeight: 0,
      // addrObj: {},
    };
  },
  // 枚举类型处理
  computed: {
    EnumerationTypeHandling () {
      return (dictName, val) => {
        if (this.dict.dict[dictName] && this.dict.dict[dictName][val]) {
          return this.dict.dict[dictName][val].label;
        }
      };
    }
  },
  methods: {
    // 动态改变表格高度
    editTableHeight() {
      this.$nextTick(()=>{
        this.tableHeight = this.$refs['work_eltable'].offsetHeight;
      });
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Track', value);
    },
    async setTableData (data) {
      this.tableData = data;
      let i = 1;
      if (this.tableData.length) {
        for (let index = 0; index < this.tableData.length; index++) {
          const element = this.tableData[index];
          element.customizeId = i++;
        }
        let coordinates = data.map(item => {
          return {
            latitude: Number(item.latitudeTable),
            longitude: Number(item.longitudeTable),
            id: item.customizeId
          };
        });
        // 先截取前50个经纬度使地址显示出来
        let hundredList = coordinates.splice(0, 100);
        batchAddr(hundredList).then(res => {
          for (let index = 0; index < res.data.length; index++) {
            const element = res.data[index];
            // this.addrObj[element.id] = {
            //   locAddr: element.locAddr
            // };
            this.tableData[element.id - 1].locAddr = element.locAddr;
          }
        });
        // 循环剩下的经纬度数组
        let trackList = this.chunkArray(coordinates, 1000);
        for (let index = 0; index < trackList.length; index++) {
          const element = trackList[index];
          await batchAddr(element).then(res => {
            for (let index = 0; index < res.data.length; index++) {
              const element = res.data[index];
              // this.addrObj[element.id] = {
              //   locAddr: element.locAddr
              // };
              this.tableData[element.id - 1].locAddr = element.locAddr;
            }
          });
        }
      }
    },
    rowClick (row) {
      this.$emit('goToPoint', row);
    },
    chunkArray(array, chunkSize) {
      let result = [];
      for (let i = 0; i < array.length; i += chunkSize) {
        let chunk = array.slice(i, i + chunkSize);
        result.push(chunk);
      }
      return result;
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    parseTimes (time) {
      return this.$moment(time * 1000).format('YYYY/MM/DD HH:mm:ss');
    }
  }
};
</script>

<style lang="less" scoped>
  .offline-car-tab{
    font-size: 13px;
    /deep/ .el-table{
      // height: 100%;
    }
  }
  /deep/.current-row{
    color: green;
  }
  /deep/.plTableBox .el-table th {
    padding: 5px 0;
  }
  .work_eltable{
    min-height: 150px;
    overflow-y: hidden !important;
  }
</style>
