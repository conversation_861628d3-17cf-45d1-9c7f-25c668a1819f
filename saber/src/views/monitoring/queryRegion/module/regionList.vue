<template>
  <div class="region-list">
    <el-form
      ref="form"
      label-width="80px"
    >
      <el-row :span="24">
        <div class="el-col el-col-24 el-col-xs-24 el-col-sm-24 el-col-md-24 form-item">
          <el-form-item :label="getLabel('startTime') + ':'">
            <el-date-picker
              v-model="form.startTime"
              size="mini"
              type="datetime"
              default-time="00:00:00"
              :placeholder="getPlaceholder('startTime')"
              @change="starttimeChange"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-xs-24 el-col-sm-24 el-col-md-24 form-item">
          <el-form-item :label="getLabel('endTime') + ':'">
            <el-date-picker
              v-model="form.endTime"
              size="mini"
              type="datetime"
              default-time="23:59:59"
              :placeholder="getPlaceholder('endTime')"
              @change="endtimeChange"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-xs-24 el-col-sm-24 el-col-md-24 form-item">
          <el-form-item :label="getLabel('type') + ':'">
            <xh-select
              v-model="form.type"
              size="mini"
              :placeholder="getPlaceholder('type')"
              @change="typeHandle"
            >
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-xs-24 el-col-sm-24 el-col-md-24 form-item">
          <el-form-item :label="getLabel('status') + ':'">
            <el-checkbox-group
              v-model="form.status"
            >
              <el-checkbox :label="1" >在线</el-checkbox>
              <el-checkbox :label="0">离线</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-xs-24 el-col-sm-24 el-col-md-24 form-item">
          <el-form-item :label="getLabel('cover') + ':'">
            <el-radio-group
              v-model="cover"
              :disabled="isRealTime"
            >
              <el-radio label="rectangle">矩形</el-radio>
              <el-radio label="circle">圆形</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div>
      <el-button
        class="filter-item"
        size="small"
        icon="el-icon-edit"
        style="margin-left: 10px;"
        @click="addNewRegion"
      >
        绘制
      </el-button>
      <!-- <el-button
        v-show="!showDraw"
        class="filter-item"
        size="small"
        type="danger"
        icon="el-icon-delete"
        @click="removeRegions"
      >
        清除
      </el-button> -->
      <el-button
        class="filter-item"
        size="small"
        type="primary"
        icon="el-icon-search"
        @click="queryCars"
      >查 询
      </el-button>
    </div>
    <!--表格渲染-->
    <u-table
      ref="table"
      class="table"
      use-virtual
      :data="resultData"
      :row-height="54"
      :highlight-current-row="true"
      :style="tableStyle"
      :border="false"
      @selection-change="selectionChangeHandler"
      @row-click="rowClick"
    >
      <!-- <u-table-column
              type="selection"
              width="50"
            /> -->
      <u-table-column
        width="20"
        :resizable="false"
      >
        <template slot-scope="scope">
          <div :class="scope.row.carStates" />
        </template>
      </u-table-column>
      <u-table-column
        prop="licencePlate"
        :label="getLabel('licencePlate')"
        min-width="100"
        :resizable="false"
      />
      <u-table-column
        prop="licenceColor"
        :label="getLabel('licenceColor')"
        min-width="100"
        :resizable="false"
      >
        <template slot-scope="scope">
          <span>{{ EnumerationTypeHandling('licenceColor',scope.row.licenceColor) }}</span>
        </template>
      </u-table-column>
      <u-table-column
        prop="locTime"
        :label="getLabel('locTime')"
        min-width="170"
        :resizable="false"
      >
        <template slot-scope="scope">
          <span class="table-date-td">
            {{ parseTime(scope.row.locTime) }}
          </span>
        </template>
      </u-table-column>
      <u-table-column
        prop="carStatus"
        :label="getLabel('carStatus')"
        min-width="100"
        :resizable="false"
      />
      <el-empty
        slot="empty"
        :image="require('@/assets/images/nodata.png')"
      />
    </u-table>
  </div>
</template>

<script>
import {queryRegion, queryrealTimeRegion} from '@/api/monitoring/region';
import {pagination} from '@/api/base/vehicle';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import variables from '@/assets/less/variables.js';
import { getCarStatus, getCarListStatus } from '@/utils/getCarStatus';
import { parseTime } from '@/api/utils/share';

export default {
  name: 'RegionList',
  // 数据字典
  dicts: ['terminalState', 'licenceColor'],
  props: {
    regionNameOption: {
      type: Array,
      default: () => []
    },
    showDraw: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      name: '',
      tableStyle: 'flex: 1; overflow-y: auto;',
      variables: {...variables},
      regionName: null,
      form: {
        startTime: null,
        endTime: null,
        type: 1,
        status: [1, 0],
      },
      resultData: [],
      licencePlate: '',
      licencePlateOptions: [],
      typeOptions: [
        {label: '实时查询', value: 1},
        {label: '历史查询', value: 0}
      ],
      cover: 'rectangle',
      isRealTime: false
    };
  },
  // 枚举类型处理
  computed: {
    EnumerationTypeHandling () {
      return (dictName, val) => {
        if (this.dict.dict[dictName] && this.dict.dict[dictName][val]) {
          return this.dict.dict[dictName][val].label;
        }
      };
    }
  },
  watch: {
    'resultData': function () {
      for (let i = 0; i < this.resultData.length; i++) {
        this.resultData[i].carStates = getCarListStatus()[this.resultData[i].status];
      }
    },
    'cover': {
      handler(newValue){
        if (!this.showDraw) {
          this.removeRegions();
        }
      }
    }
  },
  methods: {
    editRowChange(data) {
      let index = this.resultData.findIndex(item => item.vehicleId === data.vehicleId);
      if (index !== -1) {
        this.$nextTick(() => {
          this.$refs['table'].setCurrentRow(this.resultData[index]);
          this.$refs['table'].pagingScrollTopLeft(index * 36, 0);
        });
      }
    },
    rowClick (row) {
      if (!this.form.type) {
        const params = {
          licencePlate: row.licencePlate,
          vehicleId: row.vehicleId,
          licenceColor: row.licenceColor,
          startTime: this.form.startTime,
          endTime: this.form.endTime
        };
        this.$emit('trajectory', params);
      } else {
        this.$emit('toPoint', row);
      }
    },
    typeHandle () {
      if (this.form.type === 0) {
        this.cover = 'rectangle';
        this.isRealTime = true;
      }else {
        this.isRealTime = false;
      }
    },
    queryCars () {
      for (let i = 0; i < this.regionNameOption.length; i++) {
        this.regionName = this.regionNameOption[0].value;
      }
      if (!this.regionName || !this.form.startTime || !this.form.endTime) {
        this.$message({
          type: 'warning',
          message: '请先选择区域和时间'
        });
        return;
      }
      if (!this.form.status.length) {
        this.$message({
          type: 'warning',
          message: '请至少选择一个车辆状态'
        });
        return;
      }
      let parme = {}, pagination = null;
      // form.type因为当时理解错误, 所以将查询类型给当做form.type, 实际上form.type指的是绘制图形(1:矩形, 2:圆形)
      // 因为改动地方过多, 所以暂不做修改, 直接将parme的type请求参数给赋值成绘制图形对应的值(之前直接赋值的this.form.type),  其他地方不动
      if (this.form.type) {
        parme = {
          'startTime': this.$moment(this.form.startTime).valueOf() / 1000,
          'endTime': this.$moment(this.form.endTime).valueOf() / 1000,
          'status': this.form.status,
          'type': this.cover === 'rectangle' ? 1 : 2
        };
        // 图形为矩形时
        if (this.cover === 'rectangle') {
          let bounds = {
            'northeast': {
              'longitude': this.regionName[0].lng,
              'latitude': this.regionName[0].lat
            },
            'southwest': {
              'longitude': this.regionName[1].lng,
              'latitude': this.regionName[1].lat
            }
          };
          parme['rectBounds'] = bounds;
        }else{
          // 图形为圆形时
          parme['circleBounds'] = JSON.parse(JSON.stringify(this.regionName));
        }
        pagination = queryrealTimeRegion;
      }else{
        // 当前历史查询只支持矩形
        let bounds = {
          'northeast': {
            'longitude': this.regionName[0].lng,
            'latitude': this.regionName[0].lat
          },
          'southwest': {
            'longitude': this.regionName[1].lng,
            'latitude': this.regionName[1].lat
          }
        };
        parme = {
          'startTime': this.$moment(this.form.startTime).valueOf() / 1000,
          'endTime': this.$moment(this.form.endTime).valueOf() / 1000,
          'status': this.form.status,
          'bounds': bounds
        };
        pagination = queryRegion;
      }
      pagination(parme).then(res => {
        let carData = [];
        this.resultData = [];
        if (res.data.resData) {
          for (let i = 0; i < res.data.resData.length; i++) {
            carData.push({
              id: res.data.resData[i].licencePlateId,
              licencePlate: res.data.resData[i].licencePlate,
              carStatus: this.getEnumDictLabel('terminalState', res.data.resData[i].teState),
              status: res.data.resData[i].teState,
              data: res.data.resData[i],
              licenceColor: res.data.resData[i].licenceColor,
              locTime: res.data.resData[i].locTime,
              vehicleId: res.data.resData[i].vehicleId
            });
          }
          this.resultData = carData;
          if (this.form.type) {
            this.$emit('drawCarMarkers', carData);
          }else {
            this.$emit('clearMarkers');
          }
        } else {
          this.$message.warning('查询数据为空');
          this.$emit('clearMarkers');
        }
      });
    },
    getDepartOptions (val) {
      this.licencePlateOptions = [];
      let parme = {
        licencePlate: val
      };
      pagination(parme).then(req => {
        let result = [];
        req.data.content.forEach(res => {
          result.push(res.licencePlate);
        });
        this.licencePlateOptions = result;
      });
    },
    searchCar (val) {
      pagination(val).then(res => {
        console.log(res);
      });
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('QueryRegion', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('QueryRegion', value);
    },
    /**
     * 选择改变
     * @param {Object} val
     */
    selectionChangeHandler (val) {
      console.log(val);
    },
    /**
     * 绘制新的区域
     */
    addNewRegion () {
      if (!this.showDraw) {
        this.removeRegions();
      }
      this.$nextTick(() => {
        this.$emit('addNewRegion', this.cover);
      });
    },
    /**
     * 清除区域
     */
    removeRegions () {
      this.$emit('removeRegions');
      this.regionName = null;
      this.resultData = [];
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
    endtimeChange (e) {
      if (this.form.startTime !== null && this.form.endTime !== null) {
        if ((this.$moment(this.form.startTime).valueOf() / 1000) >= (this.$moment(this.form.endTime).valueOf() / 1000)) {
          this.$message({
            type: 'error',
            message: '结束时间必须大于开始时间'
          });
          this.form.endTime = null;
        }
        if (this.$moment(this.form.endTime).valueOf() > this.$moment().endOf('day').valueOf()) {
          this.$message({
            type: 'error',
            message: '结束时间不能超过今天'
          });
          this.form.endTime = null;
        }
      }
    },
    starttimeChange (e) {
      if (this.form.endTime !== null) {
        if ((this.$moment(this.form.startTime).valueOf() / 1000) >= (this.$moment(this.form.endTime).valueOf() / 1000)) {
          this.$message({
            type: 'error',
            message: '结束时间必须大于开始时间'
          });
          this.form.startTime = null;
        }
      }
      if (this.form.startTime !== null) {
        if (this.$moment(this.form.startTime).valueOf() > new Date().getTime()) {
          this.$message({
            type: 'error',
            message: '开始时间不能超过今天'
          });
          this.form.startTime = null;
        }
      }
    },
    parseTime
  }
};

</script>

<style lang="less" scoped>
  ::v-deep .el-form-item, ::v-deep .el-col{
    margin-bottom: 0px !important;
  }
  .region-list {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .form-item {
    padding-right: 10px;
  }
  ::v-deep ::-webkit-scrollbar-thumb {
    // background-color: rgba(125, 125, 125, 0.8);
  }
  ::v-deep .el-table {
    height: 100%;
  }
  .table {
    ::v-deep .current-row>td {
      background-color: #D4E8FF !important;
    }
  }
</style>
