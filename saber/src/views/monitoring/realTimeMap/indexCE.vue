<template>
  <div class="container">
    <section
      v-show="showCarList"
      ref="leftContainer"
      class="left media-terminal-tree-container"
    >
      <VehicleMultiSelectTree
        ref="VehicleMultiSelect"
        page-src="rm"
        :is-show="true"
        :monitor-car-obj="monitorCarObj"
        :from-ce="true"
        @checkedVehiclesChange="getVehicleMap"
        @CheckedNodesUpdate="CheckedNodesUpdate"
        @setTableWidth="setTableWidth"
        @questionHandle="questionHandle"
        @clearAll="clearAll"
      />
    </section>
    <div
      v-treeDrag
      class="collapse-vertical"
    >
      <span
        class="collapse-btn"
        @click="showCarList = !showCarList"
      >
        <i :class="`el-icon-arrow-${showCarList ? 'left' : 'right'}`"/>
      </span>
    </div>
    <section class="right">
      <div
        v-show="showMap"
        class="map-container chunk"
      >
        <MapWidget
          ref="MapWidget"
          class="liveMap"
          :tool-config="toolConfig"
          @toVehicleInfo="toVehicleInfo"
          @handleRegion="handleRegion"
          @mapLoaded="mapLoaded"
        />
      </div>
      <div
        v-tableDrag
        class="collapse"
      >
        <span
          v-show="showMap"
          class="collapse-btn"
          @click="showTable = !showTable"
        >
          <i :class="`el-icon-arrow-${showTable ? 'down' : 'up'}`"/>
        </span>
        <span
          v-show="showTable"
          class="collapse-btn"
          @click="showMap = !showMap"
        >
          <i :class="`el-icon-arrow-${showMap ? 'up' : 'down'}`"/>
        </span>
      </div>
      <!-- 数据很多的话使用v-show显示隐藏会出现明显卡顿, 不清楚什么原因, 所以将height设为0 -->
      <div
        ref="tableContainer"
        class="table-container chunk"
        :class="{ 'table-container-full': !showMap, 'table-container-none': !showTable }"
      >
        <el-tabs
          v-model="activeName"
          class="table-tabs"
          @tab-click="handleClick"
        >
          <el-tab-pane
            label="定位信息"
            name="monitorTab"
          >
            <MonitorTab
              ref="monitorTab"
              :dict="dict"
            />
          </el-tab-pane>
          <el-tab-pane
            label="跟踪信息"
            name="followTab"
            :lazy="true"
          >
            <FollowTab
              ref="followTab"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </section>

    <VehicleInfo
      v-if="dialogVisible"
      ref="vehicleInfo"
      :dialog-visible.sync="dialogVisible"
      class="vehicle-info"
      :follow-car="followCar"
      :vehicle-data="vehicleData"
      @followHandle="followHandle"
    />
    <!-- <TabStatistic
      ref="tabStatistic"
      class="tab-statistic"
      :dialog-visible.sync="dialogVisible"
    /> -->
    <FollowDialog
      v-if="followCar.length > 0"
      class="follow-dialog"
      :follow-car="followCar"
      @followCloseAll="followCloseAll"
      @setFollowClose="setFollowClose"
    />
    <VehicleTipsDialog ref="vehicleTipsDialog"/>
  </div>
</template>

<script>
import MapWidget from '@/components/map/MapWidgetAMapCE';
import VehicleMultiSelectTree from '@/components/select/VehicleMultiSelectTree/VehicleMultiSelectTreeNew';
import VehicleInfo from '@/components/map/infoWindow/VehicleInfoNewCE';
import MonitorTab from './tabs/MonitorTab.vue';
import FollowTab from './tabs/followTab.vue';
// import TabStatistic from './tabs/tabStatistic.vue';
import VehicleTipsDialog from '@/components/map/vehicleTipsDialog/index';
import { vehicleAddress, terminalStates } from '@/api/monitoring/info.js';
import ReconnectingWebSocket from '@/utils/rabbitmq/RealTimeProtocol/ReconnectingWebsocket';
import { getAuthCode } from '@/api/monitoring/bicycleMap';
import FollowDialog from './tabs/followDialog.vue';
import { apiPostTrackcontrol } from '@/api/home';
import { getWebsocketParam } from '@/api/user';
import { regionAll } from '@/api/base/regionManage';
import { parseTime } from '@/api/utils/share';
import jsonToHump from '@/utils/helper/jsonToHump';
export default {
  name: 'RealTimeMap',
  components: {
    MapWidget: MapWidget,
    VehicleMultiSelectTree,
    VehicleInfo,
    MonitorTab,
    FollowTab,
    // TabStatistic,
    FollowDialog,
    VehicleTipsDialog
  },
  directives: {
    tableDrag(el, bindings, vnode) { // 拖拽
      el.onmousedown = function (e) {
        let y = e.clientY;
        let yel = vnode.context.$refs.tableContainer;
        let yelDefault = yel.style.height || '324px';
        yelDefault = +yelDefault.substring(0, yelDefault.length - 2);
        yelDefault = yelDefault > 600 ? 600 : yelDefault;
        yelDefault = yelDefault < 240 ? 240 : yelDefault;
        document.onmousemove = function (e) {
          let ylong = yelDefault + y - e.clientY;
          ylong = ylong > 600 ? 600 : ylong;
          ylong = ylong < 240 ? 240 : ylong;
          yel.style.height = ylong + 'px';
        };
        document.onmouseup = function () {
          document.onmousemove = document.onmouseup = null;
        };
      };
    },
    treeDrag(el, bindings, vnode) { // 拖拽
      el.onmousedown = function (e) {
        let x = e.clientX;
        let xel = vnode.context.$refs.leftContainer;
        let xelDefault = xel.style.width || '300px';
        xelDefault = +xelDefault.substring(0, xelDefault.length - 2);
        xelDefault = xelDefault > 660 ? 660 : xelDefault;
        xelDefault = xelDefault < 230 ? 230 : xelDefault;
        document.onmousemove = function (e) {
          let xlong = xelDefault + e.clientX - x;
          xlong = xlong > 660 ? 660 : xlong;
          xlong = xlong < 230 ? 230 : xlong;
          xel.style.width = xlong + 'px';
        };
        document.onmouseup = function () {
          document.onmousemove = document.onmouseup = null;
        };
      };
    }
  },
  // 数据字典
  dicts: [
    'targetType',
    'bdmDeviceType'
  ],
  data() {
    return {
      drawMarker: null,
      toolConfig: {
        routeRegionEdit: false, // 跳转区域编辑
        routePolylineEdit: false, // 跳转路线编辑
        routePointEdit: false, // 跳转标注点编辑
        drawMarkerShow: false, // 标注点
        polylineEditorShow: false, // 绘制直线
        showZoneToolShow: false, // 绘制区域
        searchToolShow: false, // 搜索
        clearBtnShow: false, // 清除按钮
        returnBtnShow: false, // 回到中心
        setCenterShow: false, // 设置中心
        trafficLayerShow: true, // 路况
        layerSelectShow: true, // 卫星图
        drivingLineShow: false, // 路径规划
        mapToolsShow: true, // 工具栏
        regionShow: true
      }, // 控制工具按钮
      activeName: 'monitorTab',
      monitorCarObj: {}, // 当前选中的所有终端
      autoUpDateTime: null, // 定时器
      pollTime: 20, // 20s更新一次车辆数据
      timeTrack: 0, // 防止重复点击
      followCar: [], // 跟踪车辆的集合
      followTrack: {
        data: undefined, // 跟踪的初始轨迹
        line: undefined,
        timer: null
      },
      dialogVisible: false,
      vehicleData: {},
      vehicleMarkerList: [], // 当前选中marker列表
      websock: null,
      showCarList: true,
      showTable: true,
      showMap: true,
      stopDataNum: 0,
      steerDataNum: 0,
      regionList: [],
      overlayGroup: null, // 覆盖物集合
      isShowOverlayGroup: false,
      AMap: null,
      map: null,
      layer: null
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.mapWidget = this.$refs.MapWidget;
      this.drawMarker = this.mapWidget.$refs.DrawMarker;
    });
  },
  activated() {
    this.$EventBus.$on('realTimeMap', (data) => {
      this.createAlarmMarker(data);
    });
    if (!this.autoUpDateTime) {
      this.autoUpDateTime = setInterval(() => {
        this.autoUpdateVehicleTreeInCycke();
      }, this.pollTime * 1000);
    }
    let timer = null;
    let tableContainer = document.querySelector('.table-container');
    let observer = new ResizeObserver(() => {
      clearTimeout(timer); // 防抖
      timer = setTimeout(() => {
        this.$refs['monitorTab'].editTableHeight();
      }, 500);
    });
    observer.observe(tableContainer); // 监听元素
    this.$once('hook:deactivated', () => {
      observer.disconnect();
      observer = null;
    });
  },
  deactivated() {
    clearInterval(this.autoUpDateTime);
    this.autoUpDateTime = null;
    this.followCloseAll();
  },
  beforeDestroy() {
    this.$EventBus.$off('realTimeMap');
    this.followCloseAll();
    this.$refs.monitorTab.setData([], []);
  },
  destroyed() {
    clearInterval(this.autoUpDateTime);
    this.autoUpDateTime = null;
  },
  methods: {
    // 地图加载完成
    mapLoaded(v) {
      this.map = v.map;
      this.AMap = v.AMap;
    },
    /**
     * 显示电子围栏
     */
    handleRegion() {
      this.isShowOverlayGroup = !this.isShowOverlayGroup;
      if (this.regionList?.length) {
        this.initRegionOverlay();
      }
      else {
        regionAll().then(res => {
          this.regionList = res?.data;
          this.initRegionOverlay();
        }).catch(err => {
          this.regionList = [];
          console.log('接口请求失败', err);
        });
      }
    },
    // 生成电子围栏覆盖物
    initRegionOverlay() {
      if (!this.layer) {
        this.layer = MayMap.TileLayer.LabelsLayer();
        this.map.addLayer(this.layer);
        this.layer.setStyle((feature, resolution) => {
          return new MayMap.ol.style.Style({
            //边框色
            stroke: new MayMap.ol.style.Stroke({
              color: '#3ca2fa',
              width: 2
            }),
            //矢量圆点
            image: new MayMap.ol.style.Circle({
              radius: 6,
              fill: new MayMap.ol.style.Fill({
                color: 'rgb(23,145,252,0.4)'
              })
            }),
            //填充色
            fill: new MayMap.ol.style.Fill({
              color: 'rgb(23,145,252,0.4)'
            })
          });
        });
      }
      if (this.isShowOverlayGroup) {
        let list = [];
        let actions = {
          '1': () => {
            console.info('--> 绘制矩形');
            let leftTop = [list[0],list[3]];
            let leftBottom = [list[0], list[1]];
            let rightBottom = [list[2], list[1]];
            let rightTop = [list[2], list[3]];
            MayMap.TileLayer.add({
              layer: this.layer,
              type: 'Rectangle',
              coordinate: [
                [
                  leftTop,
                  leftBottom,
                  rightBottom,
                  rightTop,
                  leftTop
                ]
              ]
            });
          },
          '2': () => {
            MayMap.TileLayer.add({
              layer: this.layer,
              type: 'Circle',
              coordinate: [
                list[0],
                list[1]
              ],
              radius: list[2]
            });
          },
          '3': () => {
            let pointList = [];
            for (let index = 0; index < list.length; index += 2) {
              pointList.push([
                list[index],
                list[index + 1]
              ]);
            }
            MayMap.TileLayer.add({
              layer: this.layer,
              type: 'Polygon',
              coordinate: [pointList]
            });
          },
          '4': () => {
            let pointList = [];
            for (let index = 0; index < list.length; index += 2) {
              pointList.push([
                list[index],
                list[index + 1]
              ]);
            }
            MayMap.TileLayer.add({
              layer: this.layer,
              type: 'LineString',
              coordinate: pointList
            });
          }
        };
        for (let index = 0; index < this.regionList.length; index++) {
          const element = this.regionList[index];
          list = element.coordinate.split(' ').map(item => Number(item));
          actions[element.type].call(this);
        }
        // this.mapWidget.setFitView();
      }
      else {
        this.layer.getSource().clear();
      }
    },
    questionHandle() {
      this.$refs.vehicleTipsDialog.questionHandle();
    },
    createAlarmMarker(carData) {
      let labelTypeData = {
        iconUrl: require('@/assets/images/alarm.gif'),
        iconWidth: 80,
        iconHeight: 50
      };
      let markerRenderParam = {
        iconUrl: labelTypeData.iconUrl,
        iconWidth: labelTypeData.iconWidth,
        iconHeight: labelTypeData.iconHeight,
        // labelTypeName: '车辆',
        labelTypeName: carData.targetName,
        longitude: carData.startLon,
        latitude: carData.startLat,
        alarmType: carData.type,
        alarmLevel: Number(carData.level),
        startAddress: carData.startAddr,
        alarmTime: parseTime(carData.startTime),
        deviceType: carData.deviceType
      };
      this.drawMarker.createAlarmMaker(markerRenderParam);
    },
    toVehicleInfo(val) {
      // 关闭告警弹窗
      // this.$refs?.tabStatistic.closeDialog();
      const data = this.vehicleMarkerList.find(item => item.deviceIdStr === val);
      if (data) {
        this.vehicleData = data;
      }
      this.dialogVisible = true;
    },
    setTableWidth(val) {
      let leftContainer = document.querySelector('.left');
      leftContainer.style.width = val + 'px';
    },
    initLocationWebSocket() {
      if (typeof WebSocket === 'undefined') {
        console.log('您的浏览器不支持WebSocket');
      }
      const JSONbig = require('json-bigint');
      getAuthCode().then(res => {
        const socketCode = res.data;
        getWebsocketParam().then(res => {
          const wsLocation = res.data.data;
          const protocol = window.location.origin.indexOf('https') !== -1 ? 'wss://' : 'ws://';
          const wsUrl = `${protocol}${wsLocation}/ws/locationTrack/push/${socketCode}`;
          this.websock = new ReconnectingWebSocket(wsUrl);
          this.websock.onopen = () => {
            console.log('成功');
            // 防止ws连接缓慢导致调用apiPostTrackcontrol接口后没有给ws推送当前要跟踪的车辆
            // 因此再次推送, 跟后端确认过多次推送是没关系的
            if (this.followCar && this.followCar.length > 0) {
              this.followCar.forEach(item => {
                const query = {
                  device_type: item.deviceType,
                  device_id: item.deviceId
                };
                this.websock.send(JSON.stringify(query));
              });
            }
          };
          this.websock.onmessage = (e) => {
            const data = jsonToHump(JSONbig({ storeAsString: true }).parse(e.data));
            const str = data.deviceType + '-' + data.deviceId;
            // 手动取消跟踪后, 如果ws碰巧这时候推了一条数据过来, 就会导致代码报错, 造成不可预料的后果
            // 例如造成控制台报错, 此时勾选终端节点不会往地图上渲染marker
            // 因此判断是否有在跟踪该终端
            if (!this.followTrack[str]) {
              return;
            }
            if (this.followTrack[str].isDraw) {
              this.drawMarker.removeMarkerFromCluster({
                labelTypeId: 1,
                vehicleId: [data.deviceIdStr || data.deviceId]
              });
              this.drawMarker.drawFollowMarker({
                ...this.followTrack[str].labelTypeData,
                ...data
              });
              this.followTrack[str].isDraw = false;
            }
            if (this.followTrack[str].timer) {
              clearTimeout(this.followTrack[str].timer);
              this.followTrack[str].timer = null;
            }
            this.followTrack[str].timer = setTimeout(() => {
              this.$message({
                showClose: true,
                message: '数据传输已断开, 请重新跟踪',
                type: 'warning',
                duration: 0
              });
              this.setFollowClose(this.followTrack[str].labelTypeData);
            }, 60 * 1000);
            const stateTerminalList = [{
              id: data.deviceId,
              fusionState: data.speed ? 2 : 1
            }];
            this.$refs.VehicleMultiSelect.updateStateTerminal(stateTerminalList);
            this.setFollow(data);
          };
          this.websock.onerror = () => {
            console.log('数据传输已断开, 正在尝试重新连接');
          };
        });
      });
      this.$once('hook:beforeDestroy', () => {
        this.websock.close();
        this.websock = null;
      });
    },
    // 点击跟踪车辆事件
    followHandle(data) {
      if (!this.websock) {
        this.initLocationWebSocket();
      }
      this.followloop(data);
    },
    // 跟踪车辆循环
    followloop(data) {
      // 打开
      if (data.open) {
        this.map.setZoomAndCenter(data.position, 18);
        this.activeName = 'followTab';
        this.followCar.push(data); // 记录开启车辆
        this.trackHandle(data); // 绘制轨迹
        this.getTrackcontrol(data);
      }
      else if (!data.open) {
        this.setFollowClose(data); // 关闭
      }
    },
    // 临时位置跟踪控制
    getTrackcontrol(val) {
      const str = val.deviceType + '-' + val.deviceId;
      this.followTrack[str].labelTypeData = {
        iconUrl: this.judgeTerminalIcon(val),
        bgUrl: this.judgeBackgroundIcon(val),
        iconWidth: 50,
        iconHeight: 50,
        ...val
      };
      const query = {
        device_type: val.deviceType,
        device_id: BigInt(val.deviceId),
        duration: 3,
        expire: 5 * 60 * 60 // 取消5分钟时间限制, 暂时定为5小时
      };
      apiPostTrackcontrol(query).then(result => {
        console.log('-> 已开启临时位置跟踪', result.data);
        if (this.websock && this.websock.readyState === 1) {
          const query = {
            device_type: val.deviceType,
            device_id: val.deviceId
          };
          this.websock.send(JSON.stringify(query));
        }
      }).catch(() => {
        let index = this.followCar.findIndex(item => item.deviceType + '-' + item.deviceId === str);
        if (index >= 0) {
          this.followCar.splice(index, 1); // 删除车辆记录
        }
        if (this.followTrack[str]?.timer) {
          clearTimeout(this.followTrack[str].timer);
          this.followTrack[str].timer = null;
        }
        this.followTrack[str] = null;
        if (val.deviceIdStr === this.vehicleData.deviceIdStr) {
          this.$refs.vehicleInfo?.setFollowState();
        }
      });
    },
    // 跟踪车辆时适配合适视野范围
    setAllFitView() {
      this.drawMarker.setAllFitView(this.followCar);
    },
    setFollow(data) {
      const str = data.deviceType + '-' + data.deviceId;
      this.getAddress(data);
      let sData = [data];
      this.$refs.followTab.updateData(this.followCar, sData);
      if (this.followTrack[str] && this.followTrack[str].data && this.followTrack[str].line) { // 依次添加新的轨迹点
        this.followTrack[str].data.push({
          longitude: data.longitude,
          latitude: data.latitude,
          time: data.recvTime
        });
      }
      this.drawMarker.renewMarker(data, this.followTrack[str].line, this.followTrack[str].data);
      // this.setAllFitView(); // TODO 适配合适视野范围
    },
    setFollowClose(data) {
      const str = data.deviceType + '-' + data.deviceId;
      // 关闭
      let i;
      this.followCar.forEach((v, index) => {
        if (v.deviceType + '-' + v.deviceId === str) {
          i = index;
        }
      });
      if (!i && i !== 0) {
        return;
      }
      const query = {
        device_type: data.deviceType,
        device_id: BigInt(data.deviceId),
        duration: 0,
        expire: 60
      };
      apiPostTrackcontrol(query).then(res => {
        console.log('停止跟踪');
      });
      this.followCar.splice(i, 1); // 删除车辆记录
      if (this.followCar.length === 0) {
        this.$refs.followTab.clearData();
      }
      if (this.followTrack[str] && this.followTrack[str].line) { // 清除跟踪的轨迹
        this.$refs.MapWidget.removeTrack(this.followTrack[str].line);
      }
      // 可能出现下发指令成功，但是ws不推送数据的情况，则停止跟踪时先清空旧的图标，否则会导致地图上出现重复图标
      if (this.followTrack[str] && this.followTrack[str].data.length === 0) {
        this.drawMarker.removeMarkerFromCluster({
          labelTypeId: 1,
          vehicleId: [data.deviceIdStr || data.deviceId]
        });
      } else {
        // 清除位置跟踪的marker
        this.drawMarker.removeFollowMarker(str);
      }
      // 重新绘制点聚合marker
      let terminalData = this.vehicleMarkerList.find(item => item.deviceIdStr === data.deviceIdStr);
      if (terminalData) {
        // 跟踪结束后，不让终端回到旧的位置，直接拿ws推得最新的经纬度
        if (this.followTrack[str]?.data && this.followTrack[str].data?.length > 0) {
          terminalData.longitude = this.followTrack[str].data[this.followTrack[str].data.length - 1].longitude;
          terminalData.latitude = this.followTrack[str].data[this.followTrack[str].data.length - 1].latitude;
          terminalData.time = this.followTrack[str].data[this.followTrack[str].data.length - 1].time;
        }
        this.getVehicleMapMarker([terminalData]);
      }
      if (this.followTrack[str]?.timer) {
        clearTimeout(this.followTrack[str].timer);
        this.followTrack[str].timer = null;
      }
      this.followTrack[str] = null;
      if (data.deviceIdStr === this.vehicleData.deviceIdStr) {
        this.$nextTick(() => {
          this.$refs.vehicleInfo?.setFollowState();
        });
      }
    },
    followCloseAll() {
      console.log('关闭所有跟踪');
      for (let i = this.followCar.length - 1; i >= 0; i--) {
        const element = this.followCar[i];
        this.setFollowClose(element);
      }
    },
    // 绘制轨迹
    trackHandle(data) {
      const str = data.deviceType + '-' + data.deviceId;
      this.followTrack[str] = {};
      this.followTrack[str].data = [];
      this.followTrack[str].isDraw = true;
      let coors = this.$refs.MapWidget.formatPathData(this.followTrack[str].data);
      this.followTrack[str].line = this.$refs.MapWidget.getPath(coors);
    },
    /**
     * 删除地图元素与表格数据
     */
    clearAll() {
      this.dialogVisible = false;
      this.vehicleMarkerList = [];
      this.monitorCarObj = {};
      // 更新定位信息表
      this.$refs.monitorTab.setData(this.vehicleMarkerList, this.monitorCarObj);
      this.drawMarker.clearMarkerToMarkersCluster(1);
    },
    /**
     * @param locationData
     * @param flag
     */

    getVehicleMapMarker(locationData, flag = false) {
      console.time();
      let markerList = [];
      for (let index = 0; index < locationData.length; index++) {
        const element = locationData[index];
        let labelTypeData = {
          iconUrl: this.judgeTerminalIcon(element),
          bgUrl: this.judgeBackgroundIcon(element),
          iconWidth: 50,
          iconHeight: 50
        };
        let markerRenderParam = {
          iconUrl: labelTypeData.iconUrl,
          bgUrl: labelTypeData.bgUrl,
          iconWidth: labelTypeData.iconWidth,
          iconHeight: labelTypeData.iconHeight,
          labelTypeId: 1,
          vehicleId: element.deviceIdStr,
          // labelTypeName: '车辆',
          labelTypeName: element.targetName,
          longitude: element.longitude,
          latitude: element.latitude,
          speed: element.speed,
          bearing: element.bearing,
          time: element.time,
        };
        markerList.push(markerRenderParam);
      }
      console.timeEnd();
      this.drawMarker.addMarkerToMarkersCluster(markerList, flag);
    },

    async getVehicleMap(val, checkedList) {
      console.time();
      let thisTime = new Date().getTime();
      this.timeTrack = thisTime;
      // 勾选
      if (val.checked) {
        this.activeName = 'monitorTab';
        let emptyLocation = []; // 没有定位的车辆
        let stateTerminalList = []; // 需要更新状态的终端
        const checkedObj = checkedList.reduce((acc, currentValue) => {
          acc[currentValue.id] = currentValue.fusionState;
          return acc;
        }, {});
        let query = {
          deviceIds: checkedList.map(item => BigInt(item.id))
        };
        await terminalStates(query).then(res => {
          if (thisTime !== this.timeTrack) {
            return;
          }
          let vehicleList = [];
          for (let index = 0; index < res.data.content.length; index++) {
            const element = res.data.content[index];
            element.treeCategory = String(element.deviceCategory);
            element.fusionState = element.status;
            if (checkedObj[element.deviceIdStr] !== element.status) {
              stateTerminalList.push({
                id: element.deviceIdStr,
                fusionState: element.status
              });
            }
            this.monitorCarObj[element.deviceIdStr] = true;
            const str = element.deviceType + '-' + element.deviceIdStr;
            // 有经度纬度且没在跟踪的终端
            if (element.longitude && element.latitude && !this.followTrack[str]?.timer) {
              vehicleList.push(element);
            }
            else if (!element.longitude || !element.latitude) {
              emptyLocation.push(`[${element.targetName}]`);
            }
          }
          if (vehicleList.length) {
            this.getVehicleMapMarker(vehicleList, true);
          }
          // 将当前选中的车辆都记录下来
          this.vehicleMarkerList = this.vehicleMarkerList.concat(res.data.content || []);
          this.$nextTick(() => {
            this.$refs.VehicleMultiSelect.updateStateTerminal(stateTerminalList);
          });
        });
        if (emptyLocation.length === 1) {
          this.$notify({
            title: '提示',
            message: `设备没有定位点`,
            type: 'warning'
          });
          // 关闭车辆详情窗口(替换之前的closeInfoWindow)
          this.dialogVisible = false;
        }
        else if (emptyLocation.length > 1) {
          let str = '';
          emptyLocation.forEach(v => {
            str = `${str}<p>${v}</p>`;
          });
          this.$notify({
            title: '提示',
            message: `${str}没有定位点`,
            dangerouslyUseHTMLString: true,
            type: 'warning'
          });
          // 关闭车辆详情窗口(替换之前的closeInfoWindow)
          this.dialogVisible = false;
        }
        // 更新定位信息表
        this.$refs.monitorTab.setData(this.vehicleMarkerList, this.monitorCarObj);
      }
      else { // 取消勾选
        this.dialogVisible = false;
        let cancelCar = checkedList.map(item => item.id); // 取消选中的车辆
        for (let index = 0; index < cancelCar.length; index++) {
          const element = cancelCar[index];
          delete this.monitorCarObj[element];
        }
        this.drawMarker.removeMarkerFromCluster({
          labelTypeId: 1,
          vehicleId: cancelCar
        }, true);
        // 当前有取消选中的车辆时, 将数组转为对象, 再从对象中删除取消选中的车辆, 再转为数组(比直接两个数组循环嵌套节省性能)
        if (cancelCar.length) {
          let obj = this.vehicleMarkerList.reduce((acc, curr) => {
            acc[curr.deviceIdStr] = curr;
            return acc;
          }, {});
          for (let index = 0; index < cancelCar.length; index++) {
            const element = cancelCar[index];
            delete obj[element];
          }
          this.vehicleMarkerList = Object.values(obj);
          // 更新定位信息表
          this.$refs.monitorTab.setData(this.vehicleMarkerList, this.monitorCarObj);
        }
      }
      console.timeEnd();
    },
    handleClick(tab, event) {
      console.log(tab, event);
      if (this.activeName === 'monitorTab') {
        this.$refs['monitorTab'].editTableHeight();
      }
    },
    /**
     * 根据终端类型判断图标
     */
    judgeTerminalIcon(val) {
      const vehicleModel = [
        '101',
        '102',
        '103',
        '104',
        '105'
      ];
      const materialsModel = [
        '106',
        '108',
        '109',
        '111',
        '112',
        '114'
      ];
      const personnelModel = [
        '201',
        '202',
        '301',
        '107',
        '110',
        '113'
      ];
      const shortMessageModel = [
        '302',
        '303'
      ];
      const timeServiceModel = [
        '501',
        '502',
        '503'
      ];
      const monitorModel = [
        '401',
        '402'
      ];
      let vehicleIcon = '';
      if (vehicleModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/vehicle.png`; // 车辆
      }
      else if (materialsModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/materials.png`; // 物资
      }
      else if (personnelModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/personnel.png`; // 人员
      }
      else if (shortMessageModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/shortMessage.png`; // 短报文终端
      }
      else if (timeServiceModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/timeService.png`; // 授时终端
      }
      else if (monitorModel.includes(val.treeCategory)) {
        vehicleIcon = `/bdsplatform/static/images/pic/monitor.png`; // 监测终端
      }
      else if (val.treeCategory === '0') {
        vehicleIcon = `/bdsplatform/static/images/pic/other.png`; // 其他
      }
      return vehicleIcon;
    },
    judgeBackgroundIcon(val) {
      // 0-离线 1-静止 2-移动
      // 离线(Offline) 静止(Static) 移动(Move)
      let vehicleIcon = '';
      switch (val.fusionState) {
      case 0:
        vehicleIcon = `/bdsplatform/static/images/pic/offline.png`;
        break;
      case 1:
        vehicleIcon = `/bdsplatform/static/images/pic/static.png`;
        break;
      case 2:
        vehicleIcon = `/bdsplatform/static/images/pic/move.png`;
        break;
      }
      return vehicleIcon;
    },
    /**
     * 自动更新车辆列表
     */
    autoUpdateVehicleTreeInCycke() {
      this.$refs.VehicleMultiSelect.onUpdate();
      // setTimeout(() => {
      //   this.autoUpdateVehicleTreeInCycke();
      // }, this.pollTime * 1000);
    },
    // 刷新车辆数据
    async CheckedNodesUpdate() {
      if (Object.keys(this.monitorCarObj).length === 0) {
        return;
      }
      this.vehicleMarkerList = [];
      let query = {
        deviceIds: Object.keys(this.monitorCarObj).map(item => BigInt(item))
      };
      // 批量请求车辆数据
      await terminalStates(query).then(res => {
        for (let index = 0; index < res.data.content.length; index++) {
          const element = res.data.content[index];
          element.treeCategory = String(element.deviceCategory);
          element.fusionState = element.status;
        }
        // 将当前选中的车辆都记录下来
        this.vehicleMarkerList = res.data.content;
      });
      let list = JSON.parse(JSON.stringify(this.vehicleMarkerList));
      for (let i = list.length - 1; i >= 0; i--) {
        const element = list[i];
        let index = this.followCar.findIndex(item => item.deviceIdStr === element.deviceIdStr);
        if (index !== -1) {
          list.splice(i, 1);
        }
      }
      this.drawMarker.renewMarkerToMarkersCluster(list, this.judgeTerminalIcon, this.judgeBackgroundIcon, 1);
      // 更新定位信息表
      this.$refs.monitorTab.setData(this.vehicleMarkerList, this.monitorCarObj);
      // 更新车辆详情窗口内容(替换之前的信息窗体)
      const data = this.vehicleMarkerList.find(item => item.deviceIdStr === this.vehicleData.deviceIdStr);
      if (data && data.time > this.vehicleData.time) {
        this.vehicleData = data;
      }
    },
    // 四维逆地理编码(后台接口)
    getAddress(data) {
      const query = {
        lon: Number(data.longitude),
        lat: Number(data.latitude)
      };
      vehicleAddress(query).then(res => {
        if (res.code === 200) {
          this.$set(data, 'address', res.data);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
  // padding: 0 4px 4px;
  display: flex;
  position: relative;
}

.liveMap {
  width: 100%;
  height: 100%;
}

::v-deep .el-tabs__header {
  margin: 2px 0;
  padding-left: 20px;
  font-size: 14px;
  height: 32px;
  line-height: 32px;
}
::v-deep .el-tabs__nav {
  height: 32px;
  line-height: 32px;
}
::v-deep .el-tabs__item {
  font-size: 16px;
  top: -4px;
}

.vehicle-info {
  width: 440px;
  position: absolute;
  top: 80px;
  right: 5px;
  background-color: white;
  z-index: 1000;
  border-radius: 5px;
  box-shadow: rgb(113, 144, 202) 1px 0px;
}

.tab-statistic {
  position: absolute;
  top: 10px;
  right: 5px;
  z-index: 9;
}

.follow-dialog {
  position: absolute;
  left: 313px;
  top: 20px;
  z-index: 9;
}
@space: 5px;
.collapse-btn-base {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  cursor: pointer;
  background-color: #b0b3b8;
  color: #ffffff;

  i {
    font-weight: bold;
  }
}

.collapse-vertical {
  width: @space;
  height: 100%;
  display: flex;
  align-items: center;
  cursor: ew-resize;

  .collapse-btn {
    width: 100%;
    height: 90px;
    .collapse-btn-base
  }
}

.collapse {
  height: @space;
  display: flex;
  justify-content: center;
  cursor: ns-resize;

  .collapse-btn {
    height: 100%;
    width: 90px;
    overflow: hidden;
    margin: 0 10px;
    .collapse-btn-base
  }
}

section {
  .chunk {
    border: 1px solid #e1e5e8;
    background-color: #ffffff;
  }
}

.left {
  width: 300px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  flex-shrink: 0;
}

.right {
  flex: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;

  .map-container {
    flex: 1;
    // height: calc(100% - 280px);
  }

  .table-container {
    height: 324px;
    overflow: auto;
    z-index: 10;
  }

  .table-container-full {
    height: 100% !important;
  }

  .table-container-none {
    height: 0 !important;
  }

  .table-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;

    ::v-deep .el-tabs__content {
      flex: 1;

      .el-tab-pane {
        height: 100%;
      }
    }
  }
}


// 不同分辨率媒体查询样式

@media screen and (max-width: 1500px) {
  // 底部表格
  .table-container {
    height: 240px !important;
  }
}
</style>
