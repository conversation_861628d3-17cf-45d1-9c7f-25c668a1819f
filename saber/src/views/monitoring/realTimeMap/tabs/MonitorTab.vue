<template>
  <div class="alarm-tab">
    <template>
      <div
        v-show="false"
        class="head-btn"
      >
        <el-button
          size="mini"
          type="primary"
          @click="crud.toQuery"
        >
          刷新
        </el-button>
        <el-button
          size="mini"
          :loading="downloadLoading"
          :disabled="crud.data.length === 0"
          @click="downloadXlsx"
        >
          导出
        </el-button>
      </div>
      <div
        ref="work_eltable"
        class="work_eltable"
      >
        <u-table
          use-virtual
          highlight-current-row
          inverse-current-row
          :data="crud.data"
          :header-cell-style="headerStyle"
          row-height="54"
          :height="tableHeight"
          :border="false"
          class="monitor-tab"
        >
          <u-table-column
            type="index"
            width="50"
            label="序号"
          />
          <u-table-column
            v-if="columns.visible('deviceType')"
            prop="deviceType"
            :label="getLabel('deviceType')"
            width="120"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>{{ EnumerationTypeHandling('bdmDeviceType',scope.row.deviceType) }}</span>
            </template>
          </u-table-column>
          <u-table-column
            v-if="columns.visible('deviceCategory')"
            prop="deviceCategory"
            :label="getLabel('deviceCategory')"
            width="210"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>{{ EnumerationTypeHandling('bdmDeviceType',scope.row.deviceCategory) }}</span>
            </template>
          </u-table-column>
          <u-table-column
            v-if="columns.visible('uniqueId')"
            prop="uniqueId"
            width="160"
            :label="getLabel('uniqueId')"
            show-overflow-tooltip
            :resizable="false"
          />
          <u-table-column
            v-if="columns.visible('accStr')"
            prop="accStr"
            :label="'ACC状态'"
            align="center"
            width="80"
            show-overflow-tooltip
            :resizable="false"
          />
          <u-table-column
            v-if="columns.visible('targetType')"
            prop="targetType"
            width="80"
            :label="getLabel('targetType')"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span>{{ EnumerationTypeHandling('targetType',scope.row.targetType) | nullValueStr }}</span>
            </template>
          </u-table-column>
          <u-table-column
            v-if="columns.visible('targetName')"
            prop="targetName"
            width="130"
            :label="getLabel('targetName')"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.targetName || $utils.emptymap.targetName }}
            </template>
          </u-table-column>
          <u-table-column
            v-if="columns.visible('charge')"
            prop="charge"
            width="90"
            :label="getLabel('charge')"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.charge === 0 ? '-' : `${scope.row.charge}%` }}
            </template>
          </u-table-column>
          <u-table-column
            v-if="columns.visible('gnssNum')"
            prop="gnssNum"
            width="90"
            :label="getLabel('gnssNum')"
            show-overflow-tooltip
            :resizable="false"
          />
          <u-table-column
            v-if="columns.visible('wireless')"
            prop="wireless"
            width="90"
            :label="getLabel('wireless')"
            show-overflow-tooltip
            :resizable="false"
          />
          <u-table-column
            v-if="columns.visible('time')"
            prop="time"
            width="180"
            :label="getLabel('time')"
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">{{ scope.row.time ? parseTime(scope.row.time) : '-' }}</span>
            </template>
          </u-table-column>
          <u-table-column
            v-if="columns.visible('speed')"
            prop="speed"
            width="100"
            :label="getLabel('speed')"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ Math.floor(scope.row.speed * 10) / 10 }}
            </template>
          </u-table-column>

          <u-table-column
            v-if="columns.visible('position')"
            prop="position"
            width="450"
            :label="getLabel('position')"
            show-overflow-tooltip
            :resizable="false"
          />
          <u-table-column
            v-if="columns.visible('deptName')"
            prop="deptName"
            width="160"
            :label="getLabel('deptName')"
            show-overflow-tooltip
            :resizable="false"
          />
          <u-table-column
            v-if="columns.visible('longitude')"
            prop="longitude"
            width="120"
            :label="getLabel('longitude')"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ handlePosition(scope.row.longitude) }}
            </template>
          </u-table-column>
          <u-table-column
            v-if="columns.visible('latitude')"
            prop="latitude"
            width="120"
            :label="getLabel('latitude')"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ handlePosition(scope.row.latitude) }}
            </template>
          </u-table-column>
          <u-table-column
            v-if="columns.visible('mileage')"
            prop="mileage"
            width="150"
            :label="getLabel('mileage')"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ Math.floor(scope.row.mileage * 10) / 10 }}
            </template>
          </u-table-column>
          <u-table-column
            v-if="columns.visible('realSpeed')"
            prop="realSpeed"
            width="150"
            :label="getLabel('realSpeed')"
            show-overflow-tooltip
            :resizable="false"
          />
          <u-table-column
            v-if="columns.visible('oilMass')"
            prop="oilMass"
            width="130"
            :label="getLabel('oilMass')"
            show-overflow-tooltip
            :resizable="false"
          />
          <u-table-column
            v-if="columns.visible('ioStatus')"
            prop="ioStatus"
            width="150"
            :label="getLabel('ioStatus')"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ getIoStatusStr(scope.row.ioStatus) }}
            </template>
          </u-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </u-table>
      </div>
      <!--分页组件-->
      <!-- <pagination /> -->
    </template>
  </div>
</template>

<script>
import crudInfo from '@/api/monitoring/info.js';
import getLabel from '@/utils/getLabel';
import FileSaver from 'file-saver';
import CRUD, { presenter } from '@/components/Crud/crud';
// import pagination from '@/components/Crud/Pagination';
import { AutoNaviAddress, fourDimensionalAddress, gnAddress, vehicleAddress } from '@/api/monitoring/info.js';
import { batchAddr } from '@/api/monitoring/track.js';
const crud = CRUD({
  title: getLabel('Info', 'uniName'),
  sort: 'id,desc',
  crudMethod: { ...crudInfo },
  queryOnPresenterCreated: false
});
export default {
  name: 'MonitorTab',
  components: {
    // pagination
  },
  mixins: [presenter(crud)],
  props: {
    size: {
      type: Number,
      default: 10000
    },
    dict: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
      downloadLoading: false,
      headerStyle: {
        'background-color': '#87CEFA',
        'line-height': '10px',
        'height': '10px'
      },
      tableHeight: 0
    };
  },
  // 枚举类型处理
  computed: {
    EnumerationTypeHandling () {
      return (dictName, val) => {
        if (this.dict.dict[dictName] && this.dict.dict[dictName][val]) {
          return this.dict.dict[dictName][val].label;
        }
      };
    }
  },
  methods: {
    // /** 刷新 - 之后 */
    // [CRUD.HOOK.afterRefresh] () {
    //   // 四维逆地理编码
    //   for (let index = 0; index < Math.ceil(this.crud.data.length / 100); index++) {
    //     setTimeout(() => {
    //       for (let i = index * 100; i < (index + 1) * 100; i++) {
    //         const element = this.crud.data[i];
    //         if (element && element.longitude && element.latitude && !element.position) {
    //           this.getAddress(element);
    //         }
    //       }
    //     }, index * 1000);
    //   }
    // },
    // // 四维逆地理编码
    // async getAddress(data) {
    //   const query = {
    //     point: `${data.longitude}, ${data.latitude}`,
    //     st: 'Rgc2',
    //     uid: 'gzhgxh',
    //     cd: 'gcj02',
    //     output: 'json'
    //   };
    //   await fourDimensionalAddress(query).then(res => {
    //     if (res.status === 200) {
    //       let { district_text, address } = res.data.result;
    //       district_text = district_text.replace(/>/g, '');
    //       data.position = district_text + address;
    //     }
    //   });
    // },
    // 四维逆地理编码(后台接口)
    async getAddress(data) {
      const query = {
        lon: Number(data.longitude),
        lat: Number(data.latitude)
      };
      await vehicleAddress(query).then(res => {
        if (res.code === 200) {
          this.$set(data, 'position', res.data);
        }
      });
    },
    /** 刷新 - 之后 */
    [CRUD.HOOK.afterRefresh] () {
      // 国能逆地理编码
      for (let index = 0; index < Math.ceil(this.crud.data.length / 100); index++) {
        setTimeout(() => {
          for (let i = index * 100; i < (index + 1) * 100; i++) {
            const element = this.crud.data[i];
            if (element && element.longitude && element.latitude && !element.position) {
              this.getGNAddress(element);
            }
          }
        }, index * 1000);
      }
    },
    // 国能逆地理编码
    async getGNAddress(data) {
      const query = {
        postStr: {
          lon: data.longitude,
          lat: data.latitude,
          ver: 1
        },
        type: 'geocode'
      };
      await gnAddress(query).then(res => {
        if (res.status === 200) {
          let { status, formatted_address } = res.data.result;
          if(status === 0) {
            data.position = formatted_address;
          }
        }
      });
    },
    // 动态改变表格高度
    editTableHeight() {
      this.$nextTick(()=>{
        this.tableHeight = this.$refs['work_eltable'].offsetHeight;
      });
    },
    // 文件xlsx导出
    downloadXlsx () {
      this.downloadLoading = true;
      crudInfo.exportAll(crud.query).then(data => {
        console.log(data);
        try {
          FileSaver.saveAs(`${location.origin}${data.data}`, '定位信息数据', 'xlsx');
        } catch (error) {
          console.log(error);
          window.open(`${location.origin}${data.data}`, '_self');
        }
        this.downloadLoading = false;
      }).catch(() => {
        this.downloadLoading = false;
      });
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Info', value);
    },
    chunkArray(array, chunkSize) {
      let result = [];
      for (let i = 0; i < array.length; i += chunkSize) {
        let chunk = array.slice(i, i + chunkSize);
        result.push(chunk);
      }
      return result;
    },
    async setData (data, car) {
      crud.data = data;
      let tableData = [];
      for (let index = 0; index < data.length; index++) {
        const element = data[index];
        element.customizeId = index;
        if (element.latitude && element.longitude && !element.position) {
          tableData.push(element);
        }
      }
      let coordinates = tableData.map(item => {
        return {
          latitude: Number(item.latitude),
          longitude: Number(item.longitude),
          id: item.customizeId
        };
      });
      // 循环剩下的经纬度数组
      let trackList = this.chunkArray(coordinates, 1000);
      for (let index = 0; index < trackList.length; index++) {
        const element = trackList[index];
        batchAddr(element).then(res => {
          for (let index = 0; index < res.data.length; index++) {
            const item = res.data[index];
            // data[item.id].position = item.locAddr;
            this.$set(data[item.id], 'position', item.locAddr);
          }
        });
      }
      // 后台接口逆地理编码(四维)
      // for (let index = 0; index < Math.ceil(data.length / 100); index++) {
      //   setTimeout(() => {
      //     for (let i = index * 100; i < (index + 1) * 100; i++) {
      //       const element = data[i];
      //       if (element && element.longitude && element.latitude && !element.position) {
      //         this.getAddress(element);
      //       }
      //     }
      //   }, index * 1000);
      // }
      // 高德逆地理编码
      // for (let index = 0; index < Math.ceil(data.length / 20); index++) {
      //   let coordinate = '';
      //   for (let i = index * 20; i < (index + 1) * 20; i++) {
      //     if (data[i] && data[i].longitude && data[i].latitude) {
      //       coordinate += data[i].longitude + ',' + data[i].latitude + '|';
      //     }
      //   }
      //   // 公务车的key
      //   await AutoNaviAddress({key: '82fc439556d0047bf8096fb94a29da80', location: coordinate, batch: true}).then(res => {
      //     const { status, regeocodes } = res.data;
      //     // 请求成功时
      //     if (status === '1') {
      //       let num = 0;
      //       for (let i = index * 20; i < (index + 1) * 20; i++) {
      //         if (data[i] && data[i].longitude && data[i].latitude) {
      //           data[i].position = regeocodes[num].formatted_address;
      //           num += 1;
      //         }
      //       }
      //     }
      //   });
      // }
      crud.query.ids = Object.keys(car);
    },
    getIoStatusStr(ioStatus) {
      if (ioStatus === 1) {
        return '深度休眠';
      } else if (ioStatus === 2) {
        return '休眠';
      } else {
        return '无';
      }
    }
  }
};
</script>

<style lang="less" scoped>
  .el-table__fixed-body-wrapper{
    height:auto !important;
    position: absolute;
    top: 0;
    bottom: 17px;
  }
  .alarm-tab{
    width: 100%;
    height: 100%;
    margin-top: 0px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }
  .el-table__header tr,
    .el-table__header th {
      padding: 0;
      height: 30px;
  }
  .monitor-tab{
    font-size: 13px;
    // height: 100% !important;
    ::v-deep .el-table__body{
      table-layout: auto!important;
    }
  }
  .head-btn{
    display: flex;
    justify-content: right;
    margin-right: 5px;
  }
  .work_eltable{
    min-height: 250px;
    overflow-y: hidden !important;
  }
</style>
