<template>
  <div class="alarm-tab">
    <el-tabs
      v-model="activeName"
      tab-position="left"
      class="tabs"
      @tab-click="handleClick"
    >
      <el-tab-pane
        v-for="(item,index) in data"
        :key="index"
        :label="item.targetName"
        :name="item.name"
      >
        <template>
          <el-table
            :data="item.info"
            :header-cell-style="headerStyle"
            class="monitor-tab"
          >
            <el-table-column
              type="index"
              width="50"
              :index="(index) => { return handleIndex(index, item.info) }"
              label="序号"
            />
            <el-table-column
              prop="recvTime"
              width="180"
              :label="getLabel('locTime')"
            >
              <template slot-scope="scope">
                <span class="table-date-td">{{ scope.row.recvTime === 0 ? '' : parseTime(scope.row.recvTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="speed"
              width="100"
              :label="getLabel('speed')"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ Math.floor(scope.row.speed * 10) / 10 }}
              </template>
            </el-table-column>
            <el-table-column
              prop="longitude"
              width="120"
              :label="getLabel('longitude')"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ handlePosition(scope.row.longitude) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="latitude"
              width="120"
              :label="getLabel('latitude')"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ handlePosition(scope.row.latitude) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="address"
              min-width="250"
              :label="getLabel('address')"
              show-overflow-tooltip
            />
            <el-table-column
              prop="mileage"
              :label="getLabel('mileage')"
              width="150"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ Math.floor(scope.row.mileage * 10) / 10 }}
              </template>
            </el-table-column>
            <el-empty
              slot="empty"
              :image="require('@/assets/images/nodata.png')"
            />
          </el-table>
        </template>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import getLabel from '@/utils/getLabel';
import { parseTime } from '@/api/utils/share';
export default {
  name: 'FollowTab',
  props: {

  },
  data () {
    return {
      downloadLoading: false,
      headerStyle: {
        'background-color': '#87CEFA',
        'line-height': '10px',
        'height': '10px'
      },
      data: [{
        id: '2-5',
        name: '0',
        targetName: '终端名称',
        info: []
      }],
      activeName: '0'
    };
  },
  methods: {
    handleIndex(index, data) {
      return data.length - index;
    },
    parseTime (v) {
      return parseTime(v);
    },
    handleClick () {

    },
    // 清空跟踪车辆
    clearData() {
      this.data = [{
        id: '2-5',
        name: '0',
        targetName: '终端名称',
        info: []
      }];
      this.activeName = '0';
    },
    // 更新跟踪车辆信息
    updateData (car, data) {
      let oldName = car[0].deviceType + '-' + car[0].deviceId;
      let arr = car.map(item => {
        let str = item.deviceType + '-' + item.deviceId;
        if (this.activeName === str) {
          oldName = this.activeName;
        }
        let info = [];
        data.forEach(v => {
          if (v.deviceType + '-' + v.deviceId === str) {
            info.push(v);
          }
        });
        this.data.forEach(v => {
          if (v.id === str) {
            info = [...info, ...v.info];
          }
        });
        return {
          id: str,
          name: str,
          targetName: item.targetName,
          info: info
        };
      });
      this.activeName = oldName;
      this.data = arr;
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Info', value);
    }
  }
};
</script>

<style lang="less" scoped>
  .el-table__fixed-body-wrapper{
    height:auto !important;
    position: absolute;
    top: 0;
    bottom: 17px;
  }
  .alarm-tab{
    width: 100%;
    height: 100%;
    margin-top: 0px;
    overflow-y: auto;
  }
  .el-table__header tr,
    .el-table__header th {
      padding: 0;
      height: 30px;
  }
  .monitor-tab{
    // font-size: 13px;
    height: 100%;
  }
  .tabs{
      height: 100%;
      ::v-deep .el-tabs__content{
        height: 100%;
      }
    }
</style>
