<template>
  <div class="tab">
    <div
      v-show="!isAlarm"
      class="tab-container"
    >
      <div
        v-if="false"
        class="tab-item"
        @click="dialogMileageVisible = true"
      >
        <span class="tab-item-num">
          20
        </span>
        <span class="tab-item-label">
          里程统计
        </span>
      </div>
      <div
        v-if="false"
        class="tab-item"
        @click="dialogIssueVisible = true"
      >
        <span class="tab-item-num">
          {{ reminderTotal }}
        </span>
        <span class="tab-item-label">
          今日提醒
        </span>
      </div>
      <div
        class="tab-item tab-alarm"
        @click="handleAlarm"
      >
        <span class="tab-item-num">
          {{ alarmNum }}
        </span>
        <span class="tab-item-label">
          当前告警
        </span>
      </div>
    </div>
    <div
      v-show="isAlarm"
      class="alarm"
    >
      <div class="alarm-container">
        <div class="alarm-tabs">
          <div
            v-for="(item, index) in monitoringList"
            :key="index"
            class="alarm-tabs-item"
            :class="{'alarm-tabs-item-active': activeIndex === item.alarmType}"
            @click="handleClick(item)"
          >
            <div
              class="alarm-tabs-item-num"
              :class="{'alarm-tabs-color' : item.numPending && activeIndex !== item.alarmType}"
            >
              {{ item.numPending }}
            </div>
            <div class="alarm-tabs-item-label">{{ getEnumDictLabel('alarmTypeSpecial', item.alarmType) }}</div>
          </div>
        </div>
        <div
          class="alarm-title"
          :class="{'alarm-title-active': activeIndex === monitoring.alarmType}"
          @click="handleClick(monitoring)"
        >
          <div>{{ monitoring.numPending }}</div>
          <div class="alarm-title-label">
            {{ getEnumDictLabel('alarmTypeSpecial', monitoring.alarmType) }}
          </div>
        </div>
        <div
          class="close-btn"
          @click="isAlarm = false"
        >
          <i class="el-icon-error"/>
        </div>
      </div>
      <div class="alarm-container">
        <div class="alarm-tabs">
          <div
            v-for="(item, index) in safetyList"
            :key="index"
            class="alarm-tabs-item"
            :class="{'alarm-tabs-item-active': activeIndex === item.alarmType}"
            @click="handleClick(item)"
          >
            <div
              class="alarm-tabs-item-num"
              :class="{'alarm-tabs-color' : item.numPending && activeIndex !== item.alarmType}"
            >
              {{ item.numPending }}
            </div>
            <div class="alarm-tabs-item-label">{{ getEnumDictLabel('alarmTypeSpecial', item.alarmType) }}</div>
          </div>
        </div>
        <div
          class="alarm-title"
          :class="{'alarm-title-active': activeIndex === safety.alarmType}"
          @click="handleClick(safety)"
        >
          <div>{{ safety.numPending }}</div>
          <div class="alarm-title-label">
            {{ getEnumDictLabel('alarmTypeSpecial', safety.alarmType) }}
          </div>
        </div>
      </div>
      <div class="alarm-record">
        <div class="alarm-record-header">
          今日待处理 <span class="alarm-tabs-color">{{ activeLabel }}</span>
        </div>
        <el-scrollbar class="scroll-list">
          <div
            v-for="(item, index) in alarmList"
            :key="index"
            class="alarm-record-item"
            @click="toAlarmPage(item)"
          >
            <p class="alarm-record-time">{{ item.startTime }}</p>
            <div class="alarm-record-content">
              <span>{{ item.targetName }}</span>
              <span class="alarm-tabs-color">{{ item.alarmType }}</span>
            </div>
          </div>
          <el-empty
            v-if="alarmList.length === 0"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-scrollbar>
      </div>
    </div>
    <IssueRecord
      ref="issueRecord"
      :dialog-visible.sync="dialogIssueVisible"
      @editTotal="editTotal"
    />
    <MileageStatistic :dialog-visible.sync="dialogMileageVisible"/>
  </div>
</template>

<script>
import IssueRecord from './issueRecord';
import MileageStatistic from './mileageStatistic';
import { pendingCount, pagination } from '@/api/monitoring/realTimeMonitoring';
import { parseTime } from '@/utils';
export default {
  components:{
    IssueRecord,
    MileageStatistic
  },
  dicts: ['alarmTypeSpecial'],
  data(){
    return{
      dialogIssueVisible: false,
      dialogMileageVisible: false,
      isAlarm: false,
      monitoringList: [
        { numPending: 0, alarmType: "0"},
        { numPending: 0, alarmType: "103"},
        { numPending: 0, alarmType: "1003"},
        { numPending: 0, alarmType: "120"},
        { numPending: 0, alarmType: "82"},
        { numPending: 0, alarmType: "83"},
        { numPending: 0, alarmType: "84"},
        { numPending: 0, alarmType: "30"}
      ],
      safetyList: [
        { numPending: 0, alarmType: "211"},
        { numPending: 0, alarmType: "212"},
        { numPending: 0, alarmType: "213"},
        { numPending: 0, alarmType: "214"},
        { numPending: 0, alarmType: "161"},
        { numPending: 0, alarmType: "162"},
        { numPending: 0, alarmType: "163"},
        { numPending: 0, alarmType: "164"}
      ],
      monitoring: {
        numPending: 0,
        alarmType: "1001"
      },
      safety: {
        numPending: 0,
        alarmType: "1002"
      },
      alarmNum: 0,
      activeIndex: null,
      alarmList: [],
      query: {
        startTime: this.$moment().startOf('day'),
        endTime: this.$moment().endOf('day'),
      },
      activeLabel: null,
      reminderTotal: 0,
      alarmData: {}
    };
  },
  mounted () {
    // this.$nextTick(()=>{
    //   this.getAlarmNum();
    //   if (this.$refs['issueRecord']) {
    //     this.$refs['issueRecord'].toQuery();
    //   }
    // });
  },
  activated() {
    // this.$nextTick(() => {
    //   this.getAlarmNum();
    //   if (this.$refs['issueRecord']) {
    //     this.$refs['issueRecord'].toQuery();
    //   }
    //   if (this.isAlarm) {
    //     this.handleClick(this.alarmData);
    //   }
    // });
  },
  methods: {
    closeDialog () {
      this.isAlarm = false;
    },
    editTotal(val) {
      this.reminderTotal = val;
    },
    toAlarmPage (data) {
      const routerQuery = JSON.stringify(data);
      localStorage.setItem('ROUTER_QUERY', routerQuery);
      this.$router.push({ path: '/monitoring/realTimeMonitoring/index', query: { isRouter: this.$route.fullPath }});
      // let route = this.$router.resolve({ path: '/monitoring/realTimeMonitoring/index'});
      // window.open(route.href, '_blank');
    },
    // 获取近24小时的告警
    getAlarmNum () {
      pendingCount().then(res=>{
        // 1001 动态监控告警
        if (res.data && res.data[1001]) {
          this.monitoringList = res.data[1001].map((item)=>{
            const form = {
              alarmType: item.alarmType,
              numPending: item.numAlarm
            };
            return form;
          });
          const index = this.monitoringList.findIndex(item => item.alarmType === '1001');
          this.monitoring = this.monitoringList.splice(index, 1)[0];
        }
        // 1002 主动安全告警
        if (res.data && res.data[1002]) {
          this.safetyList = res.data[1002].map((item)=>{
            const form = {
              alarmType: item.alarmType,
              numPending: item.numAlarm
            };
            return form;
          });
          const num = this.safetyList.findIndex(item => item.alarmType === '1002');
          this.safety = this.safetyList.splice(num, 1)[0];
        }
        this.alarmNum = this.safety.numPending + this.monitoring.numPending || 0;
      });
    },
    // 点击当前告警
    handleAlarm () {
      this.isAlarm = true;
      this.$emit('update:dialogVisible', false);
      this.handleClick(this.monitoring);
    },
    // 点击告警类型
    async handleClick(val) {
      this.alarmData = val;
      this.activeIndex = val.alarmType;
      this.activeLabel = this.getEnumDictLabel('alarmTypeSpecial', val.alarmType);
      const query = {
        page: 0,
        size: val.numPending,
        alarmTypeList: [val.alarmType],
        specialAlarmType: 1,
        ...this.query
      };
      const { code, data} = await pagination(query);
      if (code === 200 && data) {
        this.alarmList = data.content;
      }
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    },
    parseTime
  }
};
</script>

<style lang="less" scoped>
.tab{
    text-align: center;
}
.tab-container{
        display: flex;
        flex-wrap: wrap;
        background: rgba(30,113,253,.1);
        padding: 0 0 5px 5px;
    }
    .tab-item{
        min-width: 55px;
        margin-top: 5px;
        margin-right: 5px;
        border-radius: 5px;
        padding: 0 5px;
        height: 55px;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        cursor: pointer;
        color: #fff;
        background: rgb(139, 195, 74);
    }
    .tab-alarm{
        background: rgb(244, 67, 54);
    }
.alarm{
    width: 302px;
    height: 100%;
    position: relative;
}
.alarm-tabs{
        display: flex;
        flex-wrap: wrap;
        margin-top: -5px;
    }
.alarm-tabs-item{
            width: 55px;
            background: #fff;
            margin-right: 5px;
            border-radius: 5px;
            height: 55px;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            margin-top: 5px;
            cursor: pointer;
        }
.alarm-title{
  background: #fff;
}
.alarm-title-active{
        background: #1e71fd;
        color: #fff;
    }
.alarm-title-label{
            width: 50px;
            writing-mode: tb-rl;
            line-height: 50px;
            text-align: center;
            font-size: 12px;
            border-radius: 5px;
            letter-spacing: 3px;
        }
.alarm-container{
        background: rgba(30,113,253,.1);
        padding: 5px;
        display: flex;
        margin-bottom: 6px;
        border-radius: 5px;
    }
.tab-item-num, .alarm-tabs-item-num{
    font-size: 18px;
}
.tab-item-label, .alarm-tabs-item-label{
    font-size: 12px;
    white-space: nowrap;
}
.alarm-tabs-color{
    color: red;
}
.alarm-tabs-item-active{
    background: red;
    color: #fff;
}
.alarm-record{
    height: 200px;
    border: 1px solid hsla(0,0%,87%,.867);
    background: #fff;
    border-radius: 5px;
    padding: 0 10px 5px 10px;
    height: calc(100% - 260px);
    box-shadow: 0 0 5px hsla(0,0%,87%,.867);
}
.alarm-record-header{
    height: 35px;
    line-height: 35px;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    border-bottom: 1px solid hsla(0,0%,87%,.867);
}
.scroll-list{
    height: 200px;
}
.alarm-record-item{
    padding-right: 15px;
    margin: 5px 0;
    cursor: pointer;
}
.alarm-record-time{
    text-align: right;
    color: #999;
    position: relative;
}
.alarm-record-content{
    background: #f5f5f5;
    border-radius: 5px;
    padding: 5px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.alarm-record-state{
    color: #fff;
    padding: 0 10px;
    font-size: 13px;
    border-radius: 5px;
}
.alarm-record-state-processed{
    background: rgb(139, 195, 74);
}
.alarm-record-state-untreated{
    background: rgb(244, 67, 54);
}
.close-btn{
    display: none;
    position: absolute;
    top: -5px;
    right: -2px;
    color: #fcbb1f;
    z-index: 99;
    font-size: 25px;
    cursor: pointer;
}
.alarm:hover{
    .close-btn{
        display: block;
    }
}
</style>
