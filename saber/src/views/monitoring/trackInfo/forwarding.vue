<template>
  <div
    class="back_box"
  >
    <div
      v-show="!trackConfigDialog"
      class="config-state-btn"
      @click="trackConfigDialog = true"
    >
      <el-tooltip
        placement="top-end"
        content="展开搜索条件"
      >
        <el-button
          type="primary"
          icon="el-icon-arrow-down"
          round
        />
      </el-tooltip>
    </div>
    <div
      v-show="trackConfigDialog"
      class="config-container"
    >
      <i
        class="el-icon-circle-close config-close"
        @click="trackConfigDialog = false"
      />
      <CarTrackConfig
        ref="carTrackConfig"
        :dict="dict"
        @onTrackSearch="queryTrackingByParagraph"
        @onTrackPlanelCancel="onTrackPlanelCancel"
        @clearTrackData="clearTrackData"
        @handleTime="handleTime"
      />
    </div>
    <section class="right">
      <div
        v-show="showMap"
        class="map-container chunk"
      >
        <MapWidget
          ref="MapWidget"
          class="map-info"
          :show-tools="false"
          :show-right-context="false"
          :tool-config="toolConfig"
          :operate-type="operateType"
          @setInfoData="setInfoData"
          @getIntegrityData="getIntegrityData"
          @setTrackElement="setTrackElement"
          @setTableData="setTableData"
          @setTrackPath="setTrackPath"
          @onTrackStop="onTrackStop"
        />
      </div>
      <div
        v-tableDrag
        class="collapse"
      >
        <span
          v-show="showMap"
          class="collapse-btn"
          @click="showTable = !showTable"
        >
          <i :class="`el-icon-arrow-${showTable ? 'down':'up'}`"/>
        </span>
        <span
          v-show="showTable"
          class="collapse-btn"
          @click="showMap = !showMap"
        >
          <i :class="`el-icon-arrow-${showMap ? 'up':'down'}`"/>
        </span>
      </div>
      <!-- 数据很多的话使用v-show显示隐藏会出现明显卡顿, 不清楚什么原因, 所以将height设为0 -->
      <div
        ref="tableContainer"
        class="table-container chunk"
        :class="{'table-container-full': !showMap, 'table-container-none': !showTable}"
      >
        <CarTrackInfo
          ref="carTrackInfo"
          :dict="dict"
          @setTrackCarMarker="setTrackCarMarker"
          @onTrackPlanelCancel="onTrackPlanelCancel"
          @onExportDeviceData="onExportDeviceData"
        />
      </div>
    </section>

    <TrackStatistics
      :track-info="trackInfo"
      :show-watch-card="showWatchCard"
    />
    <CarTrackTimeDialog
      ref="CarTrackTimeDialog"
      class="track-time-dialog"
      @onDeviceTrackSearch="onDeviceTrackSearch"
    />
  </div>
</template>
<script>
import MapWidget from '@/components/map/MapWidgetAMap';
import CarTrackInfo from '@/components/map/track/CarTrackInfo';
import CarTrackConfig from '@/components/map/track/CarTrackConfig';
import TrackStatistics from '@/components/map/track/TrackStatistics';
import CarTrackTimeDialog from '@/components/map/track/CarTrackTimeDialog';

export default {
  name: 'Forwarding',
  components: {
    MapWidget,
    CarTrackInfo,
    TrackStatistics,
    CarTrackConfig,
    CarTrackTimeDialog
  },
  directives: {
    tableDrag (el, bindings, vnode) { // 拖拽
      el.onmousedown = function (e) {
        let y = e.clientY;
        let yel = vnode.context.$refs.tableContainer;
        let yelDefault = yel.style.height || '280px';
        yelDefault = +yelDefault.substring(0, yelDefault.length - 2);
        yelDefault = yelDefault > 600 ? 600 : yelDefault;
        yelDefault = yelDefault < 230 ? 230 : yelDefault;
        document.onmousemove = function (e) {
          let ylong = yelDefault + y - e.clientY;
          ylong = ylong > 600 ? 600 : ylong;
          ylong = ylong < 230 ? 230 : ylong;
          yel.style.height = ylong + 'px';
        };
        document.onmouseup = function () {
          document.onmousemove = document.onmouseup = null;
        };
      };
    }
  },
  // 数据字典
  dicts: [
    'vehicleModel',
    'licenceColor',
    'alarmType',
    'bdmDeviceType',
    'targetType',
    'batch'
  ],
  data() {
    return {
      showWatchCard: false,
      trackInfo: null,
      trackConfigDialog: true,
      showTable: true,
      showMap: true,
      search: {
        isFilter: 0,
        licenceColor: null,
        s_time: null,
        e_time: null
      },
      toolConfig: {
        drawMarkerShow: false, // 标注点
        polylineEditorShow: false, // 绘制直线
        showZoneToolShow: false, // 绘制区域
        searchToolShow: false, // 搜索
        clearBtnShow: false, // 清除按钮
        returnBtnShow: false, // 回到中心
        setCenterShow: false, // 设置中心
        trafficLayerShow: true, // 路况
        layerSelectShow: true, // 卫星图
        drivingLineShow: false, // 路径规划
        mapToolsShow: true, // 工具栏
        showMapLabel: true // 地图标注
      }, // 控制工具按钮
      targetName: '',
      operateType: 0
    };
  },
  activated() {
    let timer = null;
    let tableContainer = document.querySelector('.table-container');
    let observer = new ResizeObserver(() => {
      clearTimeout(timer); // 防抖
      timer = setTimeout(()=>{
        // this.$refs['carTrackInfo'].$refs['trackTable'].editTableHeight();
      }, 500);
    });
    observer.observe(tableContainer); // 监听元素
    this.$once('hook:deactivated', () => {
      observer.disconnect();
      observer = null;
    });
  },
  methods: {
    // 赋值时间
    handleTime(query) {
      this.$refs.carTrackInfo.handleTime(query);
    },
    // 清除轨迹和表格数据
    clearTrackData() {
      this.operateType = 2;
      this.$refs.carTrackInfo.clearTrackData();
    },
    selectedRowSingle(data) {
      if (data) {
        const obj = {
          deviceId: data.deviceId,
          deviceType: data.deviceType,
          isFilter: data.treeCategory === '201' ? ['batch'] : []
        };
        this.operateType = 1;
        this.selectedRow(obj);
        this.$refs.CarTrackTimeDialog.closeDialog();
      }
    },
    selectedRow(val) {
      this.$refs.carTrackConfig.editValue(val);
      this.$refs.MapWidget.setDeviceMarker(val);
      this.$refs.carTrackConfig.resetTrackHandle();
      this.$refs.carTrackInfo.onTrackPlanelCancel(true);
    },
    queryTrackingByParagraph(searchInfo) {
      // 查询分段前清空旧轨迹
      this.$refs.carTrackConfig.resetTrackHandle();
      this.$refs.carTrackInfo.onTrackPlanelCancel(true);
      this.$refs.CarTrackTimeDialog.queryTrackingByParagraph(searchInfo);
    },
    onDeviceTrackSearch(data) {
      // 查询轨迹前清空旧轨迹
      this.$refs.carTrackConfig.resetTrackHandle();
      this.$refs.carTrackInfo.onTrackPlanelCancel(true);
      // 分页加载暂时用不到, 屏蔽掉
      // this.$refs.MapWidget.onDeviceTrackSearch(data);
      this.$refs.MapWidget.onDeviceAllTrackSearch(data);
    },
    // 导出设备轨迹
    onExportDeviceData() {
      this.$refs['carTrackConfig'].onExportDeviceData();
    },
    setInfoData(historyInfo) {
      this.$refs.carTrackInfo.setInfoData(historyInfo);
    },
    getIntegrityData(parme) {
      this.$refs.carTrackInfo.getIntegrityData(parme);
    },
    setTrackElement(mapInfo) {
      this.$refs.carTrackInfo.setTrackElement(mapInfo);
    },
    setTableData({data, pageFlag}) {
      this.$refs.carTrackInfo.setTableData(data, pageFlag);
    },
    setTrackCarMarker(obj) {
      this.$refs.MapWidget.setTrackCarMarker(obj);
    },
    setTrackPath({data, pageFlag}) {
      this.$refs.carTrackInfo.setTrackPath(data, pageFlag);
    },
    exitCard() {
      this.$refs.carTrackInfo.exitCard();
    },
    onTrackPlanelCancel() {
      this.$refs.MapWidget.onTrackPlanelCancel();
    },
    onTrackStop() {
      this.$refs.carTrackInfo.onPlayerStop();
    }
  }
};
</script>
<style lang="less" scoped>
.map-info {
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.back_box {
  width: 100%;
  height: 100%;
  // padding: 0 4px 4px;
  display: flex;
  position: relative;

  @space: 4px;
  @collapseBtnSize: 90px;
  @sideSectionWidth: 300px;

  .config-container {
    position: absolute;
    top: 15px;
    left: 15px;
    padding: 27px 27px 20px 10px;
    background-color: #ffffff;
    border-radius: 5px;
    box-shadow: 1px 1px 6px #88888850;
    z-index: 5000;
    .config-close {
        position: absolute;
        right: 12px;
        top: 12px;
        font-size: 16px;
        cursor: pointer;
    }
  }

  .config-state-btn {
    left: 15px;
    top: 15px;
    position: absolute;
    z-index: 5000;
  }

  .collapse-btn-base {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    cursor: pointer;
    background-color: #b0b3b8;
    color: #ffffff;

    i {
      font-weight: bold;
    }
  }

  .collapse {
    height: @space;
    display: flex;
    justify-content: center;
    cursor: ns-resize;
    .collapse-btn {
      height: 100%;
      width: @collapseBtnSize;
      overflow: hidden;
      margin: 0 10px;
      .collapse-btn-base
    }
  }

  section {
    .chunk {
      border: 1px solid #e1e5e8;
      background-color: #ffffff;
    }
  }

  .right {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .map-container {
      flex: 1;
      // height: calc(100% - 280px);
    }

    .table-container {
      height: 280px;
      overflow: auto;
    }
    .table-container-full{
      height: 100% !important;
    }
    .table-container-none{
      height: 0 !important;
    }
  }

}

.track-time-dialog {
  position: absolute;
  left: 313px;
  top: 20px;
  z-index: 1000;
}

// 不同分辨率媒体查询样式

@media screen and (max-width: 1500px) {
    // 底部表格
    .table-container {
        height: 230px !important;
    }
}

</style>
