<template>
  <div id="hiddenVideoPlayer">监听功能组件</div>
</template>
<script>

export default {
  name: 'HiddenVideoPlayer',
  props: {
    parentCtx: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      ctx: null
    };
  },
  watch: {
    parentCtx(val) {
      if (val) {
        this.$nextTick(() => {
          this.init();
        });
      }
    }
  },
  mounted() {
  },
  methods: {
    init() {
      const that = this;
      that.$nextTick(() => {
        that.ctx = CvNetVideo.Init(document.querySelector('#hiddenVideoPlayer'), 1,
          {
            MaxUcNum: 1,
            screenshot: false,
            yuntaiCtrl: false,
            videoWidth: 0,
            videoHeight: 0,
            events: {
              onStop(id, UCVideo) {
                console.log('-> 监听-停止播放', id, UCVideo);
              },
              onDevConnect(id, UCVideo) {
                console.log('-> 监听-开始传输', id, UCVideo);
                that.$emit('startMonitor');
              },
              onDevDisconnect(id, UCVideo) {
                console.log('-> 监听-设备断开', id, UCVideo);
                that.$message.error('设备已断开');
                that.closeParentIntercom();
              },
              onWsClose(id, UCVideo) {
                console.log('-> 监听-ws断开', id, UCVideo);
              },
              onServerNotice(type, id, UCVideo) {
                console.log('-> 监听-服务端通知', type, id, UCVideo);
              },
              onEndByServer(cause, id, UCVideo) {
                console.log('-> 监听-服务端结束', cause, id, UCVideo);
                that.$message.error(cause);
                that.closeParentIntercom();
              }
            },
            callback: function () {
              console.log('子组件完成初始化');
              // 重置一下解决父组件播放标签变形的问题
              that.ctx.Resize(100, 100);
            }
          }
        );

      });
    },
    play(options) {
      console.log('-> options', options, this.ctx);
      this.ctx.StartRealTimeVideo(
        options.simId,
        options.channel || 1,
        2, // 0 所有码流, 1 主码流, 2 子码流
        true, // 是否打开音频
        1,
        {
          clusterHost: options.clusterHost,
          clusterPort: options.clusterPort,
          protocol: options.protocol || 1 // 协议 0 JT/T 808-2013, 1 JT/T 808-2019, 2 GB/T 28181-2016
        },
        () => {
          console.log('文档说这个暂时没用到');
        },
        7 // 播放模式 0 默认, 3 软解(支持265), 4 fmp4, 5 webrtc, 6 hls, 7 codec需HTTPS
      );
    },
    stop() {
      this.ctx.Stop(1);
    },
    closeParentIntercom() {
      this.$emit('closeParentIntercom');
    }
  }
};
</script>
<style scoped lang="less">
#hiddenVideoPlayer {
  width: 0;
  height: 0;
  position: absolute;
  left: 0;
  top: 0;
  z-index: -9999;
  opacity: 0;
}
</style>
