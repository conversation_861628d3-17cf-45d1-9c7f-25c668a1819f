<template>
  <div class="video-live">
    <section
      v-show="showCarList"
      class="left"
    >
      <div class="car-container chunk">
        <TableTitleSlot title="视频终端监控中心"/>
        <div style="height: calc(100% - 40px);">
          <VideoMultiSelectTree
            ref="VideoMultiSelect"
            style="height: 100%;"
            page-src="vl"
            @changeCarList="changeCarList"
            @checkedChannelsChange="checkedChannelsChange"
            @selectedVehicle="handleSelectedVehicle"
          />
        </div>
        <el-card
          v-show="intercom"
          class="box-card"
        >
          <div
            slot="header"
            class="clearfix"
          >
            <span>{{ operate === 1 ? '双向对讲' : operate === 2 ? '监听' : '' }} — {{
                intercomOriginData.targetName
              }}</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text"
              @click="closeIntercom"
            >
              关闭
            </el-button>
          </div>
          <div :class="['inter-main', pendingInter? '': 'inter-online']">
            <div
              v-if="pendingInter"
              class="inter-loading"
            >
              <i class="el-icon-loading"/>
              <span>正在连接 <span class="dot"/></span>
            </div>
            <div
              v-else
              class="inter-online"
            >
              <i :class="[operate === 1 ? 'el-icon-microphone' : 'el-icon-headset']"/>
              <span>{{ operate === 1 ? '正在对讲中' : '正在监听中' }} <span class="dot"/></span>
            </div>
            <hidden-video-player
              ref="hiddenVideoPlayer"
              :parent-ctx="ctx"
              @closeParentIntercom="closeIntercom"
              @startMonitor="()=> pendingInter = false"
            />
          </div>
        </el-card>
      </div>
    </section>
    <div class="collapse-vertical">
      <span
        class="collapse-btn"
        @click="showCarList = !showCarList"
      >
        <i :class="`el-icon-arrow-${showCarList ? 'left':'right'}`"/>
      </span>
    </div>
    <section class="middle">
      <div class="video-container chunk">
        <div class="video-config">
          <div class="video-config-left">
            <span
              class="video-config-close common-default-btn"
              @click="closeAllHandle"
            >全部关闭</span>
            <span class="video-config-pass">
              <el-select
                v-model="passValue"
                placeholder="请选择"
                @change="changePassValue"
              >
                <el-option
                  v-for="item in passOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </span>
            <span>
              <el-select
                v-model="localUserConfig.videoLiveTime"
                placeholder="请选择"
                @change="handleTimeChange"
              >
                <el-option
                  v-for="item in timeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </span>
            <!--            <el-tooltip-->
            <!--              content="点击继续播放按钮可重置视频播放时长"-->
            <!--              placement="top"-->
            <!--            >-->
            <el-button
              size="small"
              type="primary"
              style="margin-left: 8px;border-radius: 0"
              @click="replayVideo"
            >继续播放
            </el-button>
            <!--            </el-tooltip>-->
            <el-tooltip
              style="margin-left: 8px;"
              content="是否在实时视频播放结束前10秒在右上方弹出提示"
              placement="top"
            >
              <el-switch
                v-model="tipsSwitch"
                @change="tipsSwitchChange"
              />
            </el-tooltip>
          </div>
          <div class="video-config-right">
            <div class="mode-list">
              <div
                v-for="item in modeIconOptions"
                class="mode-item"
              >
                <img
                  :src="require(`@/assets/images/video-live/mode-${item}.png`)"
                  alt=""
                  :class="videoModeValue === item? 'is-select-mode-icon': ''"
                  @click="videoModeValue = item"
                >
              </div>
            </div>
            <el-select
              v-model="videoModeValue"
              placeholder="请选择"
              @change="setVideoMode"
            >
              <el-option
                v-for="item in videoModeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div
              class="video-config-btn common-default-btn"
              @click="handleConfig"
            >
              <i
                class="icon-quanping"
              />
            </div>
          </div>
        </div>
        <div
          id="videoPlayer"
          ref="videoPlayer"
          v-resize="resizePlayers"
        />
      </div>
    </section>
    <!-- <div class="collapse-vertical">
      <span
        class="collapse-btn"
        @click="showMap = !showMap"
      >
        <i :class="`el-icon-arrow-${showMap ? 'right':'left'}`"/>
      </span>
    </div>
    <section
      v-show="showMap"
      class="right"
    >
      <div class="map-container chunk">
        <MapWidget
          ref="MapWidget"
          class="map-widget"
          :show-fold-button="true"
          :tool-config="toolConfig"
          @changeMap="changeMap"
          @customEvent="handleMarkerClick"
        />
      </div>
    </section> -->
  </div>
</template>

<script>
// import MapWidget from '@/components/map/MapWidgetAMap';
import VideoMultiSelectTree from '@/components/select/VideoMultiSelect/VideoMultiSelectTree';
import TableTitleSlot from '@/components/pageHead/tableTitleSlot.vue';
import _ from 'lodash';
import HiddenVideoPlayer from '@/views/monitoring/videoLiveRTVS/hiddenVideoPlayer.vue';
import { getterminalversion } from '@/api/system/terminalConfig';
import { getSystemParam } from '@/api/user';

export default {
  name: 'VideoLive',
  components: {
    HiddenVideoPlayer,
    TableTitleSlot,
    // MapWidget,
    VideoMultiSelectTree
  },
  directives: {
    resize: { // 指令的名称
      bind(el, binding) { // el为绑定的元素，binding为绑定给指令的对象
        let width = '', height = '';

        function isReSize() {
          const style = document.defaultView.getComputedStyle(el);
          if (width !== style.width || height !== style.height) {
            binding.value({
              width: style.width,
              height: style.height
            }); // 关键(这传入的是函数,所以执行此函数)
          }
          width = style.width;
          height = style.height;
        }

        el.__vueSetInterval__ = setInterval(isReSize, 300);
      },
      unbind(el) {
        clearInterval(el.__vueSetInterval__);
      }
    }
  },
  data() {
    return {
      showCarList: true,
      showMap: true,
      calcVideoWidth: false,
      carNumber: 0,
      selectedCar: [],
      lastCars: [],
      playFlag: false,
      toolConfig: {
        drawMarkerShow: false, // 标注点
        polylineEditorShow: false, // 绘制直线
        showZoneToolShow: false, // 绘制区域
        searchToolShow: false, // 搜索
        clearBtnShow: false, // 清除按钮
        returnBtnShow: false, // 回到中心
        setCenterShow: false, // 设置中心
        trafficLayerShow: false, // 路况
        layerSelectShow: false, // 卫星图
        drivingLineShow: false, // 路径规划
        mapToolsShow: false // 工具栏
      }, // 控制工具按钮
      intercom: false,
      rec: null,
      ws: null,
      modeIconOptions: [
        1,
        4,
        6,
        9,
        16
      ],
      videoModeList: [
        {
          label: '单主屏',
          value: 1
        },
        {
          label: '2分屏',
          value: 2
        },
        {
          label: '4分屏',
          value: 4
        },
        {
          label: '6分屏',
          value: 6
        },
        {
          label: '8分屏',
          value: 8
        },
        {
          label: '9分屏',
          value: 9
        },
        {
          label: '10分屏',
          value: 10
        },
        {
          label: '13分屏',
          value: 13
        },
        {
          label: '16分屏',
          value: 16
        }
      ],
      passOptions: [
        {
          value: 0,
          label: '清晰'
        },
        {
          value: 1,
          label: '流畅'
        }
      ],
      timeOptions: [
        {
          value: 0.5,
          label: '30秒'
        },
        {
          value: 1,
          label: '60秒'
        },
        {
          value: 2,
          label: '120秒'
        },
        {
          value: 3,
          label: '180秒'
        }
      ],
      videoModeValue: 9, // 分屏数量
      passValue: 1, // 默认码流
      localUserConfig: {
        videoLiveTime: 3
      }, // 设置播放时间
      operate: 1, // 监听和对讲切换
      clusterHost: '',
      clusterPort: '',
      ctx: null, // RTVS对象
      players: { // 记录视频播放对象

      },
      isInit: false,
      replayVideoFlag: false, // 选择时间重新刷新视频播放时 设置为true 防止onStop事件去掉已选的播放通道
      pendingInter: true, // 对接或监听loading状态
      intercomOriginData: {},
      timeoutMsg: null,
      tipsNotify: null,
      tipsSwitch: false,
      autoUpDateTimer: null,
      videoMapCar: [],
      dialogVisible: false,
      vehicleData: []
    };
  },
  watch: {
    videoModeValue: {
      handler(val) {
        this.setVideoMode(val);
      }
    }
  },
  async created() {
    const that = this;
    const { data } = await getSystemParam('rtvs.url');
    const clusterHost = data.data.split(':')[0];
    const clusterPort = data.data.split(':')[1];
    that.clusterHost = clusterHost;
    // that.clusterHost = 'way.haigexh.com';
    that.clusterPort = clusterPort;
    // that.clusterPort = '17000';
    that.$nextTick(() => {
      console.log('-> video初始化');
      that.ctx = CvNetVideo.Init(document.querySelector('#videoPlayer'), this.videoModeValue,
        {
          //超时警告时间 默认4.5分钟通知
          timeoutWarningMsec: 270000,
          //超时时间 默认5分钟
          timeoutCloseMsec: 300000,
          //0 自动 1 WASM软解(canvas+audioAPI) 2 js封装FMP4(h264Demuxer+audioAPI) 3 WASM封装FMP4(WASM2FMP4) 4 服务器推fmp4流 5 webrtc 6 hls 7 webrtc(srs)
          //模式1 2 3已经停止更新，新版本可能存在不兼容情况，不推荐使用
          playerMode: 7,
          //css地址 如需修改控件样式，请自行指定css地址
          cssUrl: '/bdsplatform/cdn/rtvs/CVNetVideo.css',
          //wasm库加载地址
          // libffmpegUrl: '/cdn/rtvs/libffmpeg.js',
          events: {
            timeoutWarning(leftMsec, time) {
              //参数1 leftMsec 剩余关闭毫秒
              //参数2 time 上次有动作时间
              //超时警告通知 仅通知
              console.log('-> 超时提醒', leftMsec, time);
              that.timeoutMsg = that.$message.warning(`用户长时间未进行操作, 视频将于${leftMsec / 1000}秒后关闭`);
            },
            timeoutCanceled() {
              //超时警告取消 表示警告通知后用户有了操作 重新开始计时
              console.log('-> 超时警告取消');
              if (that.timeoutMsg) {
                that.timeoutMsg.close();
              }
            },
            timeoutClose() {
              //超时通知 仅通知，如需关闭需用户在此事件响应中自行调用关闭接口
              that.closeAllHandle();
              console.log('-> 超时关闭');
              that.timeoutMsg.close();
            },
            onCapture(info) {
              console.log('-> 截图', info);
            },
            onStop(id, UCVideo) {
              console.log('-> 停止播放', id, UCVideo);
              console.log('-> that.replayVideoFlag', that.replayVideoFlag);
              if (!that.replayVideoFlag && id !== -1) {
                that.closeSingleChannel(UCVideo);
              }
            },
            onDevConnect(id, UCVideo) {
              console.log('-> 开始传输', id, UCVideo);
              if (id !== -1) {
                that.setPlayerTimer(id);
              }
            },
            onDevDisconnect(id, UCVideo) {
              console.log('-> 设备断开', id, UCVideo);
              // id -1 为对讲通道 断开后重置loading状态
              if (id === -1) {
                if (that.intercom) {
                  that.closeIntercom();
                  that.$message.error('双向对讲已断开连接');
                }
              }
            },
            onWsClose(id, UCVideo) {
              console.log('-> ws断开', id, UCVideo);
              if (id === -1) {
                if (that.intercom) {
                  that.closeIntercom();
                  that.$message.error('双向对讲已断开连接');
                }
              }
              else {
                if (!that.replayVideoFlag) {
                  that.closeSingleChannel(UCVideo, id);
                }
              }
            },
            onServerNotice(type, id, UCVideo) {
              console.log('-> 服务端通知', type, id, UCVideo);
            },
            onEndByServer(cause, id, UCVideo) {
              console.log('-> 服务端结束', cause, id, UCVideo);
              if (that.intercom) {
                that.intercom = false;
                that.pendingInter = false;
              }
              else {
                that.closeSingleChannel(UCVideo, id);
              }
              if (id === -1) {
                that.$message.error(`${cause}`);
                that.closeIntercom();
              }
              else {
                that.$message.error(`${cause}`);
              }
            },
            onStartSpeek() {
              console.log('-> 对讲开始');
              that.pendingInter = false;
            },
            onMicError(error) {
              console.log('-> 麦克风获取错误', error);
              that.$message.error('麦克风获取失败');
            }
          },
          callback: function () {
            console.log('完成初始化');
            that.isInit = true;
          }
        }
      );
      window.addEventListener('resize', _.debounce(() => {
        if (that.ctx) {
          that.resizePlayers();
        }
      }, 200));
    });
    let config = JSON.parse(localStorage.getItem(`localUserConfig`));
    if (config && config.videoLiveTime) {
      console.log(config.videoLiveTime, 'config.videoLiveTime');
      this.localUserConfig.videoLiveTime = config.videoLiveTime;
    }
  },
  activated() {
    this.$nextTick(() => {
      if (this.isInit) {
        this.setVideoMode(this.videoModeValue);
      }
    });
    this.tipsSwitch = JSON.parse(localStorage.getItem('tipsSwitch'));
    // if (!this.autoUpDateTimer) {
    //   this.autoUpDateTimer = setInterval(() => {
    //     if (this.videoMapCar.length) {
    //       this.$refs.MapWidget.drawRegionMarkers(this.videoMapCar, true);
    //     }
    //   }, 30 * 1000);
    // }
  },
  deactivated() {
    // clearInterval(this.autoUpDateTimer);
    // this.autoUpDateTimer = null;
  },
  methods: {
    tipsSwitchChange(val) {
      localStorage.setItem('tipsSwitch', JSON.stringify(val));
    },
    // 点击marker的自定义事件
    handleMarkerClick(data) {
      // 点击marker后 定时器刷新默认展示该marker
      if (this.videoMapCar.indexOf(data.vehicleId) !== -1) {
        this.videoMapCar.splice(this.videoMapCar.indexOf(data.vehicleId), 1);
        this.videoMapCar.push(data.vehicleId);
      }
    },
    resizePlayers() {
      const element = this.$refs['videoPlayer'];
      const {
        offsetWidth,
        offsetHeight
      } = element;
      if (offsetWidth && Object.keys(this.ctx.Videos).length) {
        this.ctx.Resize(offsetWidth, offsetHeight);
      }
    },
    // 全屏展示
    handleConfig() {
      console.log('handleConfig');
      const element = this.$refs['videoPlayer'];
      if (element.requestFullscreen) {
        element.requestFullscreen();
      }
      else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      }
      else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      }
      else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
    },
    /**
     * 获取车辆列表数据(测试多通道播放)
     */
    selectedRowTest(val) {
      console.log('selectedRowTest');
      // this.selectedCar = [];
      // 首先判断已选择列表中是否有需要删除的车辆
      for (let j = 0; j < this.selectedCar.length;) {
        if (!val.includes(this.selectedCar[j])) {
          if (this.selectedCar[j].play_start_mark) {
            // 停止该通道的推流
            // this.stopVideo(this.selectedCar[j], this.selectedCar[j].value)
          }
          // 从已选择的车辆列表中移除该车辆
          this.selectedCar.splice(j, 1);
          j = 0;
        }
        else {
          j++;
        }
      }
      // 其次判断已选择列表中是否有需要增加的车辆
      for (let i = 0; i < val.length; i++) {
        if (!this.selectedCar.includes(val[i])) {
          // 设置可推流的状态
          val[i].play_start_mark = false;
          // 在已选择的车辆列表中添加该车辆
          this.selectedCar.push(val[i]);
        }
      }
      // 绘制车辆Marker
      let firstData = [];
      let drawMarkerCars = [];
      for (let k = 0; k < this.selectedCar.length; k++) {
        firstData = this.selectedCar[0];
        if (!drawMarkerCars.includes(firstData.data)) {
          drawMarkerCars.push(firstData.data);
        }
        if (this.selectedCar[0].car !== this.selectedCar[k].car) {
          drawMarkerCars.push(this.selectedCar[k].data);
        }
      }
      // this.$refs.MapWidget.drawRegionMarkers(drawMarkerCars);
    },
    /**
     * 勾选树结构
     */
    checkedChannelsChange(data) {
      console.log('checkedChannelsChange', data);
      // 没有勾选数据时清空所有视频播放
      if (!data.checkedChannels.length) {
        this.stopVideo();
        return;
      }
      const players = this.getVideoIsPlayList();
      // 是否使用当前选中的容器播放视频
      let isChecked = false;
      // 取消勾选的视频通道停止播放
      if (data.cancelChannels.length) {
        for (let index = 0; index < data.cancelChannels.length; index++) {
          const element = data.cancelChannels[index];
          for (const video of players) {
            if (video.Channel === element.channel && video.Sim.includes(element.simId) && video.IsPlay) {
              this.stopVideo(video.video_id);
            }
          }
        }
      }
      this.treeChecking = true;
      // 判断当前是否需要增加分屏
      this.checkAndSetScreen(data.checkedChannels.length);
      for (let index = 0; index < data.addChannels.length; index++) {
        const element = data.addChannels[index];
        const {
          simId,
          channel,
          treeNodeKeys,
          deviceType,
          deviceId
        } = element;
        const {
          videos,
          NowSelectVideo
        } = this.ctx;
        // 判断当前选中窗口是否在播放视频 没播放时设置选中视频播放
        if (videos[NowSelectVideo].IsPlay === false) {
          this.setCustomData(videos[NowSelectVideo], 'simId', simId);
          this.setCustomData(videos[NowSelectVideo], 'deviceType', deviceType);
          this.setCustomData(videos[NowSelectVideo], 'deviceId', deviceId);
          videos[NowSelectVideo].customData.treeNodeKey = treeNodeKeys;
          this.playVideo(
            simId,
            channel,
            0,// 0 代表当前选中窗口
            deviceId,
            deviceType,
            treeNodeKeys
          );
          // IsPlay值更新慢, 多选时手动将IsPlay设置为true, 防止加载第二个视频时依旧执行当前if语句
          videos[NowSelectVideo].IsPlay = true;
          isChecked = true;
        }
        else {
          let firstNotPlayVideo;
          // 选中容器播放后依次往后排列播放
          if (isChecked && NowSelectVideo + index <= this.videoModeValue) {
            firstNotPlayVideo = NowSelectVideo + index;
          }
          else {
            // 获取第一个没有播放的窗口
            firstNotPlayVideo = this.getFirstVideoPlayer();
          }
          this.setCustomData(videos[firstNotPlayVideo], 'simId', simId);
          this.setCustomData(videos[firstNotPlayVideo], 'deviceType', deviceType);
          this.setCustomData(videos[firstNotPlayVideo], 'deviceId', deviceId);
          videos[firstNotPlayVideo].customData.treeNodeKey = treeNodeKeys;
          this.playVideo(
            simId,
            channel,
            firstNotPlayVideo,
            deviceId,
            deviceType,
            treeNodeKeys
          );
          // IsPlay值更新慢, 多选时手动将IsPlay设置为true, 防止加载第二个视频时依旧执行当前if语句
          videos[firstNotPlayVideo].IsPlay = true;
        }
      }
    },
    setCustomData(target, key, value) {
      if (!target.customData) {
        target.customData = {};
        target.customData[key] = value;
      }
      else {
        target.customData[key] = value;
      }
    },
    checkAndSetScreen(currentListNum) {
      console.log('checkAndSetScreen');
      if (currentListNum > this.videoModeValue) {
        for (let i = 0; i < this.videoModeList.length; i++) {
          const item = this.videoModeList[i];
          if (item.value >= currentListNum) {
            this.ctx.LayoutByScreens(item.value);
            this.videoModeValue = item.value;
            break;
          }
        }
      }
      else {
        this.ctx.LayoutByScreens(this.videoModeValue);
      }
    },
    /**
     * 开始视频推流
     */
    playVideo(simId, channelNum, videoId, deviceId, deviceType, treeNodeKey) {
      getterminalversion({
        deviceId,
        deviceType
      }).then(res => {
        if (res.code === 200) {
          const protocol = res.data.versionCode;
          console.log('-> protocol', protocol);
          this.ctx.StartRealTimeVideo(
            simId,
            channelNum,
            this.passValue, // 0 所有码流, 1 主码流, 2 子码流
            true, // 是否打开音频
            videoId,
            {
              clusterHost: this.clusterHost,
              clusterPort: this.clusterPort,
              protocol // 协议 0 JT/T 808-2013, 1 JT/T 808-2019, 2 GB/T 28181-2016
            },
            () => {
              console.log('文档说这个暂时没用到');
            },
            0 // 播放模式 0 默认, 3 软解(支持265), 4 fmp4, 5 webrtc, 6 hls, 7 codec需HTTPS
          );
        }
        else {
          this.$refs.VideoMultiSelect.$refs.tree.setChecked(treeNodeKey, false);
          this.stopVideo(videoId);
        }
      }).catch(() => {
        this.$refs.VideoMultiSelect.$refs.tree.setChecked(treeNodeKey, false);
        this.stopVideo(videoId);
      });

    },
    /**
     * 设置视频倒计时播放的计时器
     * @param videoId
     */
    setPlayerTimer(videoId) {
      console.log('setPlayerTimer');
      if (!this.players[videoId]) {
        this.players[videoId] = {};
      }
      this.players[videoId].closeTimer = setTimeout(() => {
        this.stopVideo(videoId);
        this.tipsNotify = null;
      }, this.localUserConfig.videoLiveTime * 60 * 1000);
      this.players[videoId].tipsTimer = setTimeout(() => {
        console.log(this.tipsNotify, 'this.tipsNotify3');
        if (this.tipsSwitch) {
          this.$notify({
            title: '提示',
            message: '实时视频播放将于10秒后结束, 需继续播放请点击 继续播放按钮',
            type: 'warning',
            duration: 10000
          });
        }
      }, this.localUserConfig.videoLiveTime * 60 * 1000 - 10000);
    },
    /**
     * 停止视频推流播放
     */
    stopVideo(videoId = -1) {
      console.log('stopVideo');
      if (videoId) {
        this.ctx.Stop(videoId);
        // 重置定时器
        if (videoId === -1) {
          Object.keys(this.players).forEach(item => {
            clearTimeout(item.closeTimer);
            clearTimeout(item.tipsTimer);
          });
          this.players = {};
        }
        else {
          clearTimeout(this.players[videoId].closeTimer);
          clearTimeout(this.players[videoId].tipsTimer);
          if (!this.replayVideoFlag) {
            this.players[videoId] = undefined;
          }
        }
      }
    },
    // 设置视频分屏
    setVideoMode(value) {
      console.log('setVideoMode');
      this.ctx.LayoutByScreens(value);
    },
    // 获取播放窗口
    getVideoPlayer(index) {
      console.log('getVideoPlayer');
      const videos = this.ctx.videos;
      return Object.keys(videos).filter(key => videos[key].IsPlay === false)[index];
    },
    // 获取第一个没有播放的窗口
    getFirstVideoPlayer() {
      console.log('getFirstVideoPlayer');
      const videos = this.ctx.videos;
      return Object.keys(videos).filter(key => videos[key].IsPlay === false)[0];
    },
    getVideoIsPlayList() {
      console.log('getVideoIsPlayList');
      const videos = this.ctx.videos;
      const list = [];
      Object.keys(videos).forEach(key => {
        if (videos[key].IsPlay === true) {
          list.push(videos[key]);
        }
      });
      return list;
    },
    // 关闭所有通道 关闭视频播放
    closeAllHandle() {
      console.log('closeAllHandle');
      this.stopVideo();
      this.$refs.VideoMultiSelect.clearAll();
    },
    // 关闭单个通道（通过treeNodeKey取消选中的通道节点）
    closeSingleChannel(UCVideo, id) {
      console.log('closeSingleChannel', UCVideo, id);
      if (UCVideo.customData.treeNodeKey) {
        this.$refs.VideoMultiSelect.$refs.tree.setChecked(UCVideo.customData.treeNodeKey, false);
        UCVideo.customData.treeNodeKey = null;
      }
      if (this.$refs.VideoMultiSelect.$refs.tree.getCheckedKeys().length === 0) {
        // todo 后续用到rtvs时需要重写此方法
      }

      this.clearPlayers(id);
    },
    clearPlayers(videoId) {
      if (this.players[videoId]) {
        clearTimeout(this.players[videoId].closeTimer);
        clearTimeout(this.players[videoId].tipsTimer);
        this.players[videoId] = undefined;
      }
    },
    // 设置码流
    changePassValue(val) {
      console.log('changePassValue', this.passValue);
      // const videos = this.ctx.videos
      this.replayVideoCtx();
    },
    /**
     * 修改时间重新播放所有视频并重置所有视频的关闭定时器
     */
    replayVideoCtx() {
      console.log('replayVideoCtx');
      this.replayVideoFlag = true;
      this.getVideoIsPlayList().forEach(video => {
        const simId = video.customData.simId;
        const channel = video.Channel;
        const videoId = video.video_id;
        const deviceId = video.customData.deviceId;
        const deviceType = video.customData.deviceType;
        this.stopVideo(videoId);
        this.playVideo(simId, channel, videoId, deviceId, deviceType);
      });
      // 重置刷新播放的标记
      setTimeout(() => {
        this.replayVideoFlag = false;
      }, 1000);
    },
    /**
     * 车辆列表
     */
    changeCarList() {
      console.log('changeCarList');
      this.showCarList = !this.showCarList;
      this.carNumber = this.$refs.VideoMultiSelect.getCarNumber();
    },
    changeMap() {
      console.log('changeMap');
      this.showMap = !this.showMap;
    },
    // 监听
    handleMonitorSelect(intercomOriginData) {
      console.log('-> intercomOriginData', intercomOriginData);
      getterminalversion({
        deviceId: intercomOriginData.deviceId,
        deviceType: intercomOriginData.deviceType
      }).then(res => {
        if (res.code === 200) {
          this.operate = 2;
          this.intercom = true;
          this.intercomOriginData = intercomOriginData;
          this.$refs.hiddenVideoPlayer.play({
            simId: intercomOriginData.phone,
            clusterHost: this.clusterHost,
            clusterPort: this.clusterPort,
            protocol: res.data.versionCode
          });
        }
      });

    },
    // 关闭监听
    handleMonitorStop() {
      console.log('handleMonitorStop');
      this.$refs.hiddenVideoPlayer.stop();
      this.intercom = false;
      this.pendingInter = true;
    },
    /**
     * 获取麦克风
     * @returns {Promise<MediaStream>}
     */
    async getMicAuth() {
      console.log('getMicAuth');
      const options = {
        audio: true
      };
      try {
        return await navigator.mediaDevices
          .getUserMedia(options);
      }
      catch (e) {
        this.$message.error('获取麦克风失败');
      }
    },
    /**
     * 对讲
     * @param intercomOriginData
     */
    async handleSelect(intercomOriginData) {
      console.log('-> handleSelect');
      this.stream = await this.getMicAuth();
      if (this.stream) {
        this.operate = 1;
        this.intercomOriginData = intercomOriginData;
        this.intercom = true;
        this.startSpeak(intercomOriginData);
      }
    },
    startSpeak(intercomOriginData) {
      console.log('startSpeak');
      navigator.mediaDevices.getUserMedia({ audio: true })
        .then(() => {
          getterminalversion({
            deviceId: intercomOriginData.deviceId,
            deviceType: intercomOriginData.deviceType
          }).then(res => {
            const {
              phone: simId
            } = intercomOriginData;
            if (res.code === 200) {
              const ret = this.ctx.StartSpeek(
                simId,
                1,
                {
                  clusterHost: this.clusterHost,
                  clusterPort: this.clusterPort,
                  // protocol: res.data.versionCode
                  // 9C终端只支持2013协议 -7终端2个都可以 固定传0兼容
                  protocol: 0
                }
              );
              if (!ret) {
                this.$message.error('未能获取到麦克风');
                this.intercom = false;
                this.pendingInter = true;
              }
            }
          });
        }).catch(() => {
          this.$message.error('未能获取到麦克风');
          this.intercom = false;
          this.pendingInter = true;
      });
    },
    handleStop(intercomOriginData) {
      // 停止双向对讲
      this.ctx.StopSpeak();
      this.intercom = false;
      this.pendingInter = true;
    },
    closeIntercom() {
      console.log('closeIntercom');
      // 更新树组件的样式适配恢复原样
      this.$refs.VideoMultiSelect.updateStyle();
      if (this.operate === 1) {
        this.handleStop();
      }
      else if (this.operate === 2) {
        this.handleMonitorStop();
      }
    },
    // 延时时长
    handleTimeChange() {
      console.log('handleTimeChange');
      localStorage.setItem('localUserConfig', JSON.stringify(this.localUserConfig));
      // this.replayVideoCtx()
    },
    // 点击按钮重新延续播放视频
    replayVideo() {
      this.$notify.close();
      this.replayVideoCtx();
    },
    handleSelectedVehicle(val) {
      this.$refs.MapWidget.setDeviceMarker(val);
    }
  }
};
</script>

<style lang="less" scoped>
.video-live {
  width: 100%;
  height: 100%;
  // padding: 0 4px 4px;
  display: flex;
  min-height: 800px;
  min-width: 1000px;
  overflow-x: auto;

  @space: 4px;
  @collapseBtnSize: 90px;

  .collapse-btn-base {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    cursor: pointer;
    background-color: #b0b3b8;
    color: #ffffff;

    i {
      font-weight: bold;
    }
  }

  section {
    .chunk {
      border: 1px solid #e1e5e8;
      background-color: #ffffff;
    }
  }

  .left {
    width: 380px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
    flex-shrink: 0;

    .car-container {
      flex: 1;
      overflow: hidden;
    }
  }

  .middle {
    flex: 1;
    min-width: 700px;
    display: flex;
    flex-direction: column;

    .video-container {
      flex: 1;
      position: relative;
      flex-shrink: 0;
      min-width: 700px;
    }
  }

  .right {
    width: 420px;
    flex-shrink: 0;

    .map-container {
      height: 100%;
    }
  }

  .collapse-vertical {
    width: @space;
    height: 100%;
    display: flex;
    align-items: center;

    .collapse-btn {
      width: 100%;
      height: @collapseBtnSize;
      .collapse-btn-base
    }
  }

}

.video-up {
  display: flex;
  height: @videoSelectHeight;
  width: 100%;
  background: #f0f2f5;
  justify-content: center;
  align-items: center;
  box-shadow: 0px 0px 5px #888888;
}

.carList {
  position: absolute;
  left: 20px;
  cursor: pointer;
}

.mapList {
  position: absolute;
  right: 40px;
  cursor: pointer;
}

.video-down {
  display: flex;
  width: 100%;
  height: calc(100% - @videoSelectHeight);
}

.video-left {
  width: @selectComponentContainerWidth;
  padding: 5px;
}

.video-right {
  width: @selectComponentContainerWidth;
  padding: 5px;
}

.video-middle {
  flex: auto;
}

.videoPlayer-container-allopen {
  position: absolute;
  height: calc(100% - @videoSelectHeight);
  width: 100%;
  bottom: 1px;
}

.videoPlayer-container-half {
  position: absolute;
  height: calc(100% - @videoSelectHeight);
  // width: calc(100% - 1 * @selectComponentContainerWidth - 2 * @xhSpacingBase);
  width: 100%;
  bottom: 1px;
}

.videoPlayer-container-allclose {
  position: absolute;
  height: calc(100% - @videoSelectHeight);
  width: calc(100% - 0 * @selectComponentContainerWidth - 2 * @xhSpacingBase);
  bottom: 1px;
}

.video-mode-switch-button {
  margin-right: @xhSpacingBase;
  background-color: @xhUIColorMain;
  border-radius: @xhBorderRadiusBase;
  color: @xhTextColor4;
  padding: @xhSpacingBase;
  cursor: pointer;
  text-align: center;
  line-height: @xhLineHigh1;
  height: @xhLineHigh1;
  width: @xhLineHigh1;
}

.video-mode-switch-button-active {
  background-color: @xhUIColorSuccess;
}

.box-card {
  position: absolute;
  z-index: 100000;
  height: 300px;
  width: 270px;
  left: 15px;
  bottom: 135px;
}

.intercom-container {
  z-index: 100000;
  height: 300px;
  width: 270px;
}

.el-card ::v-deep .el-card__header {
  padding: 1px;
}

.el-card ::v-deep .el-card__body {
  padding: 1px;
}

.video-option-switch-close, .video-option-switch-pass {
  position: absolute;
  left: 110px;
  top: 0;
  line-height: calc(1 * @videoSelectHeight - 10px);
  height: calc(1 * @videoSelectHeight - 10px);
  width: 100px;
  margin: 5px;
}

.video-option-set-time {
  position: absolute;
  left: 220px;
  top: 0;
  line-height: calc(1 * @videoSelectHeight - 10px);
  height: calc(1 * @videoSelectHeight - 10px);
  width: 100px;
  margin: 5px;
}

.video-option-switch-close {
  left: 0;
  border: 1px solid #dcdfe6;
  text-align: center;
  cursor: pointer;
  background-color: #ffffff;
  border-radius: 4px;
}

.video-option-switch-close:hover {
  border: 1px solid #c0c4cc;
}

.video-config {
  height: 40px;
  display: flex;
  justify-content: space-between;
  padding: 0 5px;
}

.video-config-left {
  color: #000000;
  display: flex;
  align-items: center;

  ::v-deep .el-select {
    width: 90px;

    .el-input__inner {
      height: 32px;
      line-height: 32px;
      border: 1px solid #d3d4d6;
      border-radius: 0;
    }

    .el-input__icon {
      line-height: 32px;
    }
  }
}

.video-config-close {
  display: inline-block;
  width: 90px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  // font-weight: 600;
  border: 1px solid #000000;
  cursor: pointer;
}

.video-config-pass {
  margin: 0 7px;
}

.video-config-right {
  color: #000000;
  display: flex;
  align-items: center;

  ::v-deep .el-select {
    width: 90px;

    .el-input__inner {
      height: 32px;
      line-height: 32px;
      border: 1px solid #d3d4d6;
      border-radius: 0;
    }

    .el-input__icon {
      line-height: 32px;
    }
  }
}

.video-config-btn {
  text-align: center;
  width: 32px;
  height: 32px;
  line-height: 32px;
  border: 1px solid #1d6dcf;
  margin-left: 5px;
  cursor: pointer;
}

#videoPlayer {
  width: 100%;
  height: calc(100% - 40px);
  min-height: 500px;
  min-width: 600px;
}

.inter-main {
  width: 100%;
  height: 260px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #888888;

  i {
    font-size: 50px;
    color: #ffffff;
  }

  .el-icon-microphone,
  .el-icon-headset {
    color: #409eff;
  }

  .inter-loading,
  .inter-online {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 14px;
    color: #e8e8e8;

    .el-icon-loading,
    .el-icon-microphone {
      margin-bottom: 8px;
    }
  }
}

.inter-online.inter-main {
  color: #686868;
  background-color: #ffffff;

  .inter-online {
    color: #409eff;

  }
}

.dot {
  display: inline-block;
  height: 1em;
  line-height: 1;
  text-align: left;
  vertical-align: -.25em;
  overflow: hidden;
}

.dot::before {
  display: block;
  content: '...\A..\A.';
  white-space: pre-wrap;
  animation: dot 3s infinite step-start both;
}

@keyframes dot {
  33% {
    transform: translateY(-2em);
  }
  66% {
    transform: translateY(-1em);
  }
}

.mode-list {
  display: flex;
  align-items: center;

  .mode-item {
    margin-right: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    img {
      width: 30px;
      height: 30px;
    }
  }
}

.is-select-mode-icon {
  filter: sepia(1) hue-rotate(194deg) saturate(20);
}

.vehicle-info {
  width: 400px;
  position: absolute;
  top: 80px;
  right: 5px;
  background-color: white;
  z-index: 1000;
  border-radius: 5px;
  box-shadow: rgb(113, 144, 202) 1px 0px;
}

</style>
