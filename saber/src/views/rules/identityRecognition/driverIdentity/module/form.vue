<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="form.isEdit ? '驾驶员身份识别-修改前' : crud.status.title"
    :close-on-press-escape="close-on-press-escape"
    :class="{'form-dialog': form.isEdit}"
    append-to-body
    width="60%"
    top="5vh"
    @close="close"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="140px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('ruleName')"
            prop="ruleName"
          >
            <el-input
              v-model="form.ruleName"
              :placeholder="getPlaceholder('ruleName')"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('shootingChannel')"
            prop="shootingChannel"
          >
            <xh-select
              v-model="form.shootingChannel"
              :placeholder="getPlaceholder('shootingChannel')"
              @input="handleRemark"
            >
              <el-option
                v-for="item in channelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('identifyPassValue')"
            prop="identifyPassValue"
          >
            <el-input
              v-model.number="form.identifyPassValue"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('identifyPassValue')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('retryNumber')"
            prop="retryNumber"
          >
            <el-input
              v-model.number="form.retryNumber"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('retryNumber')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('produceType')"
            prop="produceType"
          >
            <xh-select
              v-model="form.produceType"
              :placeholder="getPlaceholder('produceType')"
              @input="handleRemark"
            >
              <el-option
                v-for="item in produceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div
          v-if="form.produceType === '20'"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('accConditions')"
            prop="accConditions"
          >
            <el-radio-group
              v-model="form.accConditions"
            >
              <el-radio :label="1">
                忽略ACC
              </el-radio>
              <el-radio :label="2">
                ACC开
              </el-radio>
              <el-radio :label="3">
                ACC关
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div
          v-if="form.produceType === '22'"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('accTriggerInterval')"
            prop="accTriggerInterval"
          >
            <el-input
              v-model.number="form.accTriggerInterval"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('accTriggerInterval')"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.produceType !== '22'"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('speedThreshold')"
            prop="speedThreshold"
          >
            <el-input
              v-model.number="form.speedThreshold"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('speedThreshold')"
            >
              <template slot="append">km/h</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.produceType !== '22'"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('duration')"
            prop="duration"
          >
            <el-time-picker
              v-model="form.duration"
              :placeholder="getPlaceholder('duration')"
              value-format="HH:mm:ss"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('startTime')"
            prop="startTime"
          >
            <el-time-picker
              v-model="form.startTime"
              :placeholder="getPlaceholder('startTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('endTime')"
            prop="endTime"
          >
            <el-time-picker
              v-model="form.endTime"
              :placeholder="getPlaceholder('endTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isPhoto')"
          >
            <el-radio-group
              v-model="form.isPhoto"
            >
              <el-radio :label="1">
                拍照
              </el-radio>
              <el-radio :label="0">
                不拍照
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div
          v-if="form.isPhoto"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('photoNumber')"
          >
            <el-input
              v-model.number="form.photoNumber"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('photoNumber')"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.isPhoto"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('photoInterval')"
            prop="photoInterval"
          >
            <el-input
              v-model.number="form.photoInterval"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('photoInterval')"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.isPhoto"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('camera')"
            prop="camera"
          >
            <xh-select
              v-model="form.camera"
              :placeholder="getPlaceholder('camera')"
              clearable
            >
              <el-option
                v-for="item in channelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isAlarm')"
          >
            <el-radio-group
              v-model="form.isAlarm"
              @change="alarmHandle"
            >
              <el-radio :label="0">
                不告警
              </el-radio>
              <el-radio :label="1">
                告警
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('tips')"
            prop="tips"
            :rules="form.isAlarm !== 0 ? validate.validateTips : ''"
          >
            <el-checkbox-group v-model="form.tips">
              <el-checkbox
                :label="1"
              >紧急</el-checkbox>
              <el-checkbox
                :label="2"
              >显示器显示</el-checkbox>
              <el-checkbox
                :label="3"
              >TTS播读</el-checkbox>
              <el-checkbox
                :label="4"
              >广告屏显示</el-checkbox>
            </el-checkbox-group>
            <el-input
              v-model="form.tipsText"
              :autosize="{ minRows: 2, maxRows: 2 }"
              :placeholder="getPlaceholder('tipsText')"
              type="textarea"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.tipsText"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('tipsTimes')"
            prop="tipsTimes"
          >
            <el-input
              v-model.number="form.tipsTimes"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('tipsTimes')"
            >
              <template slot="append">次</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.tipsText"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('tipsInterval')"
            prop="tipsInterval"
          >
            <el-input
              v-model.number="form.tipsInterval"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('tipsInterval')"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('rightTips')"
            prop="rightTips"
            :rules="form.isAlarm !== 0 ? validate.validateRightTips : ''"
          >
            <el-checkbox-group v-model="form.rightTips">
              <el-checkbox
                :label="1"
              >紧急</el-checkbox>
              <el-checkbox
                :label="2"
              >显示器显示</el-checkbox>
              <el-checkbox
                :label="3"
              >TTS播读</el-checkbox>
              <el-checkbox
                :label="4"
              >广告屏显示</el-checkbox>
            </el-checkbox-group>
            <el-input
              v-model="form.rightTipsText"
              :autosize="{ minRows: 2, maxRows: 2 }"
              :placeholder="getPlaceholder('rightTipsText')"
              type="textarea"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model="form.remark"
              :autosize="{ minRows: 4, maxRows: 6 }"
              :placeholder="getPlaceholder('remark')"
              type="textarea"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="form.isEdit !== 1"
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

const defaultForm = {
  id: null,
  ruleTypeId: null,
  ruleName: '',
  shootingChannel: null,
  identifyPassValue: 70,
  retryNumber: 1,
  produceType: '20',
  speedThreshold: null,
  accConditions: 1,
  accTriggerInterval: null,
  duration: '',
  isPhoto: 0,
  startTime: '00:00:00',
  endTime: '23:59:59',
  photoNumber: null,
  photoInterval: null,
  camera: null,
  isAlarm: 1,
  tipsText: '',
  tipsTimes: 1,
  tipsInterval: 5,
  rightTipsText: '',
  remark: '',
  tips: [2, 3],
  rightTips: [2, 3]
};
export default {
  components: { },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    ruleTypeId: {
      type: Number,
      default: null
    }
  },
  data () {
    const validateTips = (rule, value, callback) => {
      // 自定义验证逻辑
      if (value.length === 0 || this.form.tipsText === '') {
        callback(new Error('请选择语音提示并输入语音提示内容'));
      } else {
        callback();
      }
    };
    const validateRightTips = (rule, value, callback) => {
      // 自定义验证逻辑
      if (value.length === 0 || this.form.rightTipsText === '') {
        callback(new Error('请选择正确语音提示并输入正确语音提示内容'));
      } else {
        callback();
      }
    };
    return {
      rules: {
        ruleName: { required: true, message: '请输入规则名称', trigger: 'blur' },
        shootingChannel: { required: true, message: '请输入拍摄通道', trigger: 'blur' },
        identifyPassValue: { required: true, message: '请输入识别通过值', trigger: 'blur' },
        retryNumber: { required: true, message: '请输入重试次数', trigger: 'blur' },
        speedThreshold: { required: true, message: '请输入速度阈值', trigger: 'blur' },
        duration: { required: true, message: '请输入持续时间', trigger: 'blur' },
        photoInterval: { required: true, message: '请输入拍照间隔', trigger: 'blur' },
        camera: { required: true, message: '请选择摄像头', trigger: 'change' },
        tipsTimes: { required: true, message: '请输入语音提示总次数', trigger: 'blur' },
        tipsInterval: { required: true, message: '请输入语音提示间隔', trigger: 'blur' },
      },
      produceOptions: [],
      channelOptions: [],
      validate: {
        validateTips: {required: true, validator: validateTips, trigger: 'blur'},
        validateRightTips: {required: true, validator: validateRightTips, trigger: 'blur'}
      }
    };
  },
  watch: {
    dict: {
      handler(){
        const produceData = this.dict.ruleManage.find((item)=>item.value === 'rule_driver');
        this.produceOptions = produceData.children;
        const channelData = this.dict.ruleManage.find((item)=>item.value === 'rule_camera');
        this.channelOptions = channelData?.children;
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    alarmHandle(){
      this.$refs.form.clearValidate('tips');
      this.$refs.form.clearValidate('rightTips');
    },
    handleRemark(){
      let data = this.produceOptions.find((item)=> item.value === this.form.produceType);
      this.$set(this.form, 'remark', `名称：${this.form.ruleName}，产生类型：${data.label}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}`);
    },
    // 添加前
    [CRUD.HOOK.beforeToAdd] () {
      this.$refs.form && this.$refs.form.clearValidate();
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      let data = this.produceOptions.find((item)=> item.value === this.form.produceType);
      this.form.remark = `名称：${this.form.ruleName}，产生类型：${data.label}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}`;
    },
    /** 编辑 - 之前 */
    [CRUD.HOOK.beforeToEdit] () {
    },
    /** 提交 - 之前 */
    [CRUD.HOOK.beforeSubmit] () {
      this.form.ruleTypeId = this.ruleTypeId;
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('DriverIdentity', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('DriverIdentity', value);
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  .form-dialog{
    z-index: 3001 !important;
    pointer-events: none;
    ::v-deep .el-dialog{
      pointer-events: auto;
      width: 49% !important;
      float: left;
      // margin-left: 15px;
      left: 15px;
      transform: translate(0, -50%);
      z-index: 3001;
      .el-dialog__headerbtn{
        display: none;
      }
    }
  }
</style>
