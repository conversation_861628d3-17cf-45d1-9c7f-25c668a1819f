<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible="dialogVisible"
    :title="detailForm.dialogModal ? '电子围栏规则' : '电子围栏规则-修改后'"
    :close-on-press-escape="false"
    append-to-body
    :modal="detailForm.dialogModal"
    width="60%"
    :class="{'form-dialog': !detailForm.dialogModal}"
    @close="close"
  >
    <el-form
      ref="form"
      :model="form"
      size="small"
      label-width="125px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('ruleName')"
            prop="ruleName"
          >
            <el-input
              v-model="form.ruleName"
              :placeholder="getPlaceholder('ruleName')"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('ruleType')"
            prop="ruleType"
          >
            <xh-select
              v-model="form.ruleType"
              :placeholder="getPlaceholder('ruleType')"
              clearable
              @input="handleRemark"
            >
              <el-option
                v-for="item in ruleOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('deptIds')"
            prop="deptIds"
          >
            <el-input
              v-model="form.deptName"
              disabled
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="速度阈值"
            prop="limitSpeed"
          >
            <el-input-number
              v-model="form.limitSpeed"
              placeholder="请输入速度阈值(Km/h)"
              maxlength="3"
              :controls="false"
              :min="0"
              :max="160"
              @input="handleRemark"
              style="width: 100%;"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.ruleType !== 110"
          class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24"
        >
          <el-form-item
            :label=" (form.ruleType === 108 || form.ruleType === 109) ? getLabel('durationType') : getLabel('duration')"
            prop="duration"
          >
            <div class="form-item">
              <xh-select
                v-if="form.ruleType === 108 || form.ruleType === 109"
                v-model="form.durationType"
                :placeholder="getPlaceholder('durationType')"
              >
                <el-option
                  v-for="item in durationOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </xh-select>
              <el-time-picker
                v-model="form.duration"
                :placeholder="getPlaceholder('duration')"
                value-format="HH:mm:ss"
                @input="handleRemark"
              />
            </div>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('startTime')"
            prop="startTime"
          >
            <el-time-picker
              v-model="form.startTime"
              :placeholder="getPlaceholder('startTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('endTime')"
            prop="endTime"
          >
            <el-time-picker
              v-model="form.endTime"
              :placeholder="getPlaceholder('endTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('regionId')"
            prop="regionId"
          >
            <el-input
              v-model="form.regionName"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isPhoto')"
          >
            <el-radio-group
              v-model="form.isPhoto"
            >
              <el-radio :label="1">
                拍照
              </el-radio>
              <el-radio :label="0">
                不拍照
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div
          v-if="form.isPhoto"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('photoNumber')"
          >
            <el-input
              v-model.number="form.photoNumber"
              :placeholder="getPlaceholder('photoNumber')"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.isPhoto"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('photoInterval')"
            prop="photoInterval"
          >
            <el-input
              v-model.number="form.photoInterval"
              :placeholder="getPlaceholder('photoInterval')"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.isPhoto"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('camera')"
            prop="camera"
          >
            <el-input
              v-model.number="form.camera"
              :placeholder="getPlaceholder('camera')"
            >
              <template slot="append">号摄像头</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isAlarm')"
          >
            <el-radio-group
              v-model="form.isAlarm"
            >
              <el-radio :label="0">
                不告警
              </el-radio>
              <el-radio :label="1">
                告警
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('tips')"
            prop="tips"
          >
            <el-checkbox-group v-model="form.tips">
              <el-checkbox
                :label="1"
              >紧急</el-checkbox>
              <el-checkbox
                :label="2"
              >显示器显示</el-checkbox>
              <el-checkbox
                :label="3"
              >TTS播读</el-checkbox>
              <el-checkbox
                :label="4"
              >广告屏显示</el-checkbox>
            </el-checkbox-group>
            <el-input
              v-model="form.tipsText"
              :autosize="{ minRows: 2, maxRows: 2 }"
              :placeholder="getPlaceholder('tipsText')"
              type="textarea"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.tipsText"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('tipsTimes')"
            prop="tipsTimes"
          >
            <el-input
              v-model.number="form.tipsTimes"
              :placeholder="getPlaceholder('tipsTimes')"
            >
              <template slot="append">次</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.tipsText"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('tipsInterval')"
            prop="tipsInterval"
          >
            <el-input
              v-model.number="form.tipsInterval"
              :placeholder="getPlaceholder('tipsInterval')"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model="form.remark"
              :autosize="{ minRows: 4, maxRows: 6 }"
              :placeholder="getPlaceholder('remark')"
              type="textarea"
              maxlength="150"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="!detailForm.dialogModal"
      class="close-btn"
    >
      <el-button
        type="danger"
        @click="closeHandle"
      >
        关 闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

export default {
  components: { },
  props: {
    detailForm: {
      type: Object,
      default: ()=>{return {};}
    },
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      form: {
        id: null,
        ruleTypeId: null,
        ruleName: '',
        ruleType: 106,
        durationType: 0,
        duration: '00:10:00',
        regionId: null,
        regionName: '',
        limitSpeed: 0,
        isPhoto: 0,
        startTime: '00:00:00',
        endTime: '23:59:59',
        photoNumber: null,
        photoInterval: null,
        camera: null,
        isAlarm: 1,
        tipsText: '',
        tipsTimes: 1,
        tipsInterval: 5,
        remark: '',
        tips: [2, 3],
        deptIds: []
      },
      durationOptions: [
        {label: '关闭', value: 0},
        {label: '大于', value: 1},
        {label: '小于', value: 2}
      ],
      ruleOptions: [
        {label: '禁入告警', value: 106},
        {label: '禁出告警', value: 107},
        {label: '跨入告警', value: 108},
        {label: '跨出告警', value: 109},
        {label: '跨入跨出告警', value: 110}
      ]
    };
  },
  watch:{
    detailForm: {
      handler(newVal){
        if (newVal.ruleTypeName === '电子围栏规则') {
          this.form = Object.assign(this.form, newVal);
          let data = this.ruleOptions.find((item)=>item.value === this.form.ruleType);
          this.form.remark = `名称：${this.form.ruleName}，规则类型：${data?.label}，速度阈值：${this.form.limitSpeed || 0}(Km/h)，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}`;
        }
      },
      deep: true
    }
  },
  methods: {
    closeHandle(){
      this.$emit('closeHandle');
      this.close();
    },
    handleRemark(){
      let data = this.ruleOptions.find((item)=>item.value === this.form.ruleType);
      this.$set(this.form, 'remark', `名称：${this.form.ruleName}，规则类型：${data?.label}，速度阈值：${this.form.limitSpeed || 0}(Km/h)，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}`);
    },
    close(){
      this.$emit('update:dialogVisible', false);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('ElectronicFenceRule', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('ElectronicFenceRule', value);
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  .form-item{
    display: flex;
    ::v-deep .el-select{
      width: 30% !important;
    }
  }
  .close-btn{
    position: absolute;
    bottom: -60px;
    left: -50px;
  }
  .form-dialog{
    z-index: 3001 !important;
    pointer-events: none;
    ::v-deep .el-dialog{
      pointer-events: auto;
      width: 49% !important;
      float: right;
      // margin-right: 15px;
      left: -15px !important;
      transform: translate(0, -50%);
      .el-dialog__headerbtn{
        display: none;
      }
    }
  }
</style>
