<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible="dialogVisible"
    :title="detailForm.dialogModal ? '夜间异动规则' : '夜间异动规则-修改后'"
    :close-on-press-escape="false"
    append-to-body
    :modal="detailForm.dialogModal"
    width="60%"
    :class="{'form-dialog': !detailForm.dialogModal}"
    @close="close"
  >
    <el-form
      ref="form"
      :model="form"
      size="small"
      label-width="110px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('ruleName')"
            prop="ruleName"
          >
            <el-input
              v-model="form.ruleName"
              :placeholder="getPlaceholder('ruleName')"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isAlarm')"
          >
            <el-radio-group
              v-model="form.isAlarm"
            >
              <el-radio :label="0">
                不告警
              </el-radio>
              <el-radio :label="1">
                告警
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('startDate')"
            prop="startDate"
          >
            <el-date-picker
              v-model="form.startDate"
              :placeholder="getPlaceholder('startDate')"
              type="date"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('endDate')"
            prop="endDate"
          >
            <el-date-picker
              v-model="form.endDate"
              :placeholder="getPlaceholder('endDate')"
              type="date"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('startTime')"
            prop="startTime"
          >
            <el-time-picker
              v-model="form.startTime"
              :placeholder="getPlaceholder('startTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('endTime')"
            prop="endTime"
          >
            <el-time-picker
              v-model="form.endTime"
              :placeholder="getPlaceholder('endTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('speedLimit')"
            prop="speedLimit"
          >
            <el-input
              v-model.number="form.speedLimit"
              :placeholder="getPlaceholder('speedLimit')"
              @input="handleRemark"
            >
              <template slot="append">km/h</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('duration')"
            prop="duration"
          >
            <el-time-picker
              v-model="form.duration"
              :placeholder="getPlaceholder('duration')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('deptIds')"
            prop="deptIds"
          >
            <el-input
              v-model="form.deptName"
              disabled
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            label="告警等级"
            prop="levels"
          >
            <div class="main">
              <div class="item_label main_parameter">
                时长
              </div>
              <div class="item_label main_parameter">
                告警等级
              </div>
              <div class="item_label">
                <span
                  class="add_item"
                  @click="addHandle"
                >添加</span>
              </div>
            </div>
            <div
              v-for="(item,index) in form.levels"
              :key="index"
              class="main"
            >
              <div class="main_item main_size">
                <div class="main_item_select">
                  <el-time-picker
                    v-model="form.levels[index].threshold"
                    :placeholder="getPlaceholder('threshold')"
                    value-format="HH:mm:ss"
                    :default-value="new Date(0, 0, 0, 0, 5, 0)"
                    style="width: 90% !important;"
                  />
                </div>
              </div>
              <div class="main_item">
                <el-select
                  v-model="form.levels[index].level"
                  clearable
                  placeholder="请选择告警等级"
                  style="width: 90% !important;"
                >
                  <el-option
                    v-for="item in dict.dict.alarmLevel"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
              <div class="main_item">
                <div
                  class="reduce_item"
                >
                  <i
                    class="el-icon-remove-outline reduce_icon"
                    @click="reduceHandle(index)"
                  />
                </div>
              </div>
            </div>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model="form.remark"
              :autosize="{ minRows: 4, maxRows: 6 }"
              :placeholder="getPlaceholder('remark')"
              type="textarea"
              maxlength="150"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="!detailForm.dialogModal"
      class="close-btn"
    >
      <el-button
        type="danger"
        @click="closeHandle"
      >
        关 闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

export default {
  components: { },
  props: {
    detailForm: {
      type: Object,
      default: ()=>{return {};}
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    dict: {
      type: Object,
      default: ()=>{return {};}
    }
  },
  data () {
    return {
      form: {
        id: null,
        ruleName: '',
        startTime: '22:00:00',
        endTime: '06:00:00',
        isAlarm: 1,
        duration: '00:10:00',
        speedLimit: '60',
        startDate: '',
        endDate: '',
        remark: '',
        deptIds: []
      },
      formItem: {
        threshold: '',
        level: ''
      },
    };
  },
  watch:{
    detailForm: {
      handler(newVal){
        if (newVal.ruleTypeName === '夜间异动规则') {
          this.form = Object.assign(this.form, newVal);
          this.form.remark = `名称：${this.form.ruleName}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}，限速阈值：${this.form.speedLimit}km/h，持续时间：${this.form.duration}`;
        }
      },
      deep: true
    },
    dialogVisible: {
      handler(newVal) {
        if (newVal) {
          this.editLevelsData();
        }
      },
    }
  },
  methods: {
    closeHandle(){
      this.$emit('closeHandle');
      this.close();
    },
    addHandle() {
      let obj = JSON.parse(JSON.stringify(this.formItem));
      this.form.levels.push(obj);
    },
    // 修改levels的值
    editLevelsData() {
      if (this.form.levels && this.form.levels.length) {
        this.form.levels.forEach(element => {
          element.level = element.level.toString();
          element.threshold = this.secondsToTime(element.threshold);
        });
      }
    },
    // 秒数转为时分秒
    secondsToTime(seconds) {
      var hours = Math.floor(seconds / 3600);
      var minutes = Math.floor((seconds % 3600) / 60);
      var secs = seconds % 60;
      hours = hours < 10 ? "0" + hours : hours;
      minutes = minutes < 10 ? "0" + minutes : minutes;
      secs = secs < 10 ? "0" + secs : secs;
      return hours + ":" + minutes + ":" + secs;
    },
    handleRemark(){
      this.$set(this.form, 'remark', `名称：${this.form.ruleName}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}，限速阈值：${this.form.speedLimit}km/h，持续时间：${this.form.duration}`);
    },
    close(){
      this.$emit('update:dialogVisible', false);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('NightSteerRule', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('NightSteerRule', value);
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  .close-btn{
    position: absolute;
    bottom: -60px;
    left: -50px;
  }
  .form-dialog{
    z-index: 3001 !important;
    pointer-events: none;
    ::v-deep .el-dialog{
      pointer-events: auto;
      width: 49% !important;
      float: right;
      // margin-right: 15px;
      left: -15px !important;
      transform: translate(0, -50%);
      .el-dialog__headerbtn{
        display: none;
      }
    }
  }
.main {
  display: flex;
  width: 85%;
  text-align: center;
}

.main_item {
  flex: 1;
  height: 45px;
  line-height: 45px;
  border: 1px solid #c1c9da;
}

.main_size {
  flex: 1 !important;
}

.item_label {
  flex: 1;
  height: 40px;
  line-height: 40px;
  background-color: #e1e5ee;
  border: 1px solid #c1c9da;
}

.main_item ::v-deep .el-select, .main_item ::v-deep .el-input {
  width: 100%;
}

.add_item {
  border: 1px solid #aebac5;
  padding: 3px 5px;
  background-color: #ffffff;
  margin: 5px;
  cursor: pointer;
}
</style>
