<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible="dialogVisible"
    :title="detailForm.dialogModal ? '离线位移规则' : '离线位移规则-修改后'"
    :close-on-press-escape="false"
    append-to-body
    :modal="detailForm.dialogModal"
    width="60%"
    :class="{'form-dialog': !detailForm.dialogModal}"
    @close="close"
  >
    <el-form
      ref="form"
      :model="form"
      size="small"
      label-width="110px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('ruleName')"
            prop="ruleName"
          >
            <el-input
              v-model="form.ruleName"
              :placeholder="getPlaceholder('ruleName')"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('offlineTime')"
            prop="offlineTime"
          >
            <el-input
              v-model.number="form.offlineTime"
              :placeholder="getPlaceholder('offlineTime')"
              @input="handleRemark"
            >
              <template slot="append">秒</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('distance')"
            prop="distance"
          >
            <el-input
              v-model.number="form.distance"
              :placeholder="getPlaceholder('distance')"
              @input="handleRemark"
            >
              <template slot="append">米</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('dayStartTime')"
            prop="dayStartTime"
          >
            <el-time-picker
              v-model="form.dayStartTime"
              :placeholder="getPlaceholder('dayStartTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('dayEndTime')"
            prop="dayEndTime"
          >
            <el-time-picker
              v-model="form.dayEndTime"
              :placeholder="getPlaceholder('dayEndTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('deptIds')"
            prop="deptIds"
          >
            <el-input
              v-model="form.deptName"
              disabled
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            label="告警等级"
            prop="levels"
          >
            <div class="main">
              <div class="item_label main_parameter">
                离线位移距离阈值(米)
              </div>
              <div class="item_label main_parameter">
                告警等级
              </div>
              <div class="item_label">
                <span
                  class="add_item"
                  @click="addHandle"
                >添加</span>
              </div>
            </div>
            <div
              v-for="(item,index) in form.levels"
              :key="index"
              class="main"
            >
              <div class="main_item main_size">
                <div class="main_item_select">
                  <el-input-number
                    v-model="form.levels[index].threshold"
                    placeholder="请输入离线位移距离阈值"
                    :controls="false"
                    style="width: 90%;"
                  />
                </div>
              </div>
              <div class="main_item">
                <el-select
                  v-model="form.levels[index].level"
                  clearable
                  placeholder="请选择告警等级"
                  style="width: 90%;"
                >
                  <el-option
                    v-for="item in dict.dict.alarmLevel"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
              <div class="main_item">
                <div
                  class="reduce_item"
                >
                  <i
                    class="el-icon-remove-outline reduce_icon"
                    @click="reduceHandle(index)"
                  />
                </div>
              </div>
            </div>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model="form.remark"
              :autosize="{ minRows: 4, maxRows: 6 }"
              :placeholder="getPlaceholder('remark')"
              type="textarea"
              maxlength="150"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="!detailForm.dialogModal"
      class="close-btn"
    >
      <el-button
        type="danger"
        @click="closeHandle"
      >
        关 闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

export default {
  components: { },
  props: {
    dict: {
      type: Object,
      required: true
    },
    detailForm: {
      type: Object,
      default: ()=>{return {};}
    },
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      form: {
        id: null,
        ruleName: '',
        dayStartTime: '00:00:00',
        dayEndTime: '23:59:59',
        offlineTime: 120,
        distance: 500,
        remark: '',
        levels: [],
        deptIds: []
      },
      formItem: {
        threshold: '',
        level: ''
      },
    };
  },
  watch:{
    detailForm: {
      handler(newVal){
        if (newVal.ruleTypeName === '离线位移规则') {
          this.form = Object.assign(this.form, newVal);
          this.form.remark = `名称：${this.form.ruleName}，离线时长：${this.form.offlineTime}秒，离线位移：${this.form.distance}米，开始时间：${this.form.dayStartTime}，结束时间：${this.form.dayEndTime}`;
          this.handleEditNumber();
        }
      },
      deep: true
    }
  },
  methods: {
    addHandle() {
      let obj = JSON.parse(JSON.stringify(this.formItem));
      this.form.levels.push(obj);
    },
    handleEditNumber() {
      if (this.form.levels && this.form.levels.length) {
        this.form.levels.forEach(element => {
          element.level = element.level.toString();
        });
      }
    },
    reduceHandle (index) {
      this.form.levels.splice(index, 1);
    },
    closeHandle(){
      this.$emit('closeHandle');
      this.close();
    },
    handleRemark(){
      this.$set(this.form, 'remark', `名称：${this.form.ruleName}，离线时长：${this.form.offlineTime}秒，离线位移：${this.form.distance}米，开始时间：${this.form.dayStartTime}，结束时间：${this.form.dayEndTime}`);
    },
    close(){
      this.$emit('update:dialogVisible', false);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('OfflineDisplacement', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('OfflineDisplacement', value);
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  .close-btn{
    position: absolute;
    bottom: -60px;
    left: -50px;
  }
  .form-dialog{
    z-index: 3001 !important;
    pointer-events: none;
    ::v-deep .el-dialog{
      pointer-events: auto;
      width: 49% !important;
      float: right;
      // margin-right: 15px;
      left: -15px !important;
      transform: translate(0, -50%);
      .el-dialog__headerbtn{
        display: none;
      }
    }
  }
.main {
  display: flex;
  width: 85%;
  text-align: center;
}

.main_item {
  flex: 1;
  height: 45px;
  line-height: 45px;
  border: 1px solid #c1c9da;
}

.main_size {
  flex: 1 !important;
}

.item_label {
  flex: 1;
  height: 40px;
  line-height: 40px;
  background-color: #e1e5ee;
  border: 1px solid #c1c9da;
}

.main_item ::v-deep .el-select, .main_item ::v-deep .el-input {
  width: 100%;
}

.add_item {
  border: 1px solid #aebac5;
  padding: 3px 5px;
  background-color: #ffffff;
  margin: 5px;
  cursor: pointer;
}
</style>
