<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible="dialogVisible"
    :title="detailForm.dialogModal ? '离线通知规则' : '离线通知规则-修改后'"
    :close-on-press-escape="false"
    append-to-body
    :modal="detailForm.dialogModal"
    width="60%"
    :class="{'form-dialog': !detailForm.dialogModal}"
    @close="close"
  >
    <el-form
      ref="form"
      :model="form"
      size="small"
      label-width="120px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('ruleName')"
            prop="ruleName"
          >
            <el-input
              v-model="form.ruleName"
              :placeholder="getPlaceholder('ruleName')"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('regionId')"
            prop="regionId"
          >
            <xh-select
              v-model="form.regionName"
              :placeholder="getPlaceholder('regionId')"
              clearable
              @clear="handleClear"
            >
              <el-option>
                <el-tree
                  ref="tree"
                  show-checkbox
                  :data="interRegionList"
                  :props="defaultProps"
                  node-key="id"
                  :expand-on-click-node="false"
                  :check-on-click-node="true"
                  @check="handleNodeCheck"
                />
              </el-option>
            </xh-select>
          </el-form-item>
        </div>
        <div
          v-if="form.regionName"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('limitMode')"
            prop="limitMode"
          >
            <el-radio-group
              v-model="form.limitMode"
            >
              <el-radio :label="1">
                电子围栏内
              </el-radio>
              <el-radio :label="2">
                电子围栏外
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('startTime')"
            prop="startTime"
          >
            <el-time-picker
              v-model="form.startTime"
              :placeholder="getPlaceholder('startTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('endTime')"
            prop="endTime"
          >
            <el-time-picker
              v-model="form.endTime"
              :placeholder="getPlaceholder('endTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('acc')"
            prop="acc"
          >
            <el-radio-group
              v-model="form.acc"
            >
              <el-radio :label="1">
                不限制
              </el-radio>
              <el-radio :label="2">
                限制开
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isSpeed')"
            prop="isSpeed"
          >
            <el-radio-group
              v-model="form.isSpeed"
              @input="handleRemark"
            >
              <el-radio :label="0">
                否
              </el-radio>
              <el-radio :label="1">
                是
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div
          v-if="form.isSpeed"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('limitSpeed')"
            prop="limitSpeed"
          >
            <el-input
              v-model.number="form.limitSpeed"
              :placeholder="getPlaceholder('limitSpeed')"
            >
              <template slot="append">km/h</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('ruleType')"
            prop="ruleType"
          >
            <el-radio-group
              v-model="form.ruleType"
              @input="handleRemark"
            >
              <el-radio :label="1">
                通知
              </el-radio>
              <el-radio :label="2">
                告警
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div v-if="form.ruleType === 1">
          <div
            class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24 flex-item"
          >
            <el-form-item
              :label="getLabel('offlineTime')"
              prop="offlineTime"
            >
              <el-input
                v-model.number="day"
              >
                <template slot="append">天</template>
              </el-input>
              <el-input
                v-model.number="hour"
              >
                <template slot="append">时</template>
              </el-input>
              <el-input
                v-model.number="minute"
              >
                <template slot="append">分</template>
              </el-input>
              <el-input
                v-model.number="second"
              >
                <template slot="append">秒</template>
              </el-input>
            </el-form-item>
          </div>
          <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
            <el-form-item
              :label="getLabel('notifyOnce')"
              prop="notifyOnce"
            >
              <el-radio-group
                v-model="form.notifyOnce"
              >
                <el-radio :label="0">
                  否
                </el-radio>
                <el-radio :label="1">
                  是
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
            <el-form-item
              :label="getLabel('notifyObj')"
              prop="notifyObj"
            >
              <el-checkbox-group
                v-model="notifyObj"
                @change="handleChange"
              >
                <el-checkbox
                  :label="1"
                >自定义</el-checkbox>
                <el-checkbox
                  :label="2"
                >车队联系人</el-checkbox>
                <el-checkbox
                  :label="3"
                >车主</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>
          <div
            v-if="isCustom"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('phone')"
              prop="phone"
            >
              <el-input
                v-model.number="form.phone"
                :placeholder="getPlaceholder('phone')"
              />
            </el-form-item>
          </div>
          <div
            v-if="isCustom"
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('email')"
              prop="email"
            >
              <el-input
                v-model.number="form.email"
                :placeholder="getPlaceholder('email')"
              />
            </el-form-item>
          </div>
        </div>
        <div v-if="form.ruleType === 2">
          <div
            class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24"
          >
            <el-form-item
              :label="getLabel('alarmLevel')"
              prop="alarmLevel"
            >
              <AlarmLevel
                v-model="form.alarmLevel"
                @clearRule="clearRule"
              />
            </el-form-item>
          </div>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model="form.remark"
              :autosize="{ minRows: 4, maxRows: 6 }"
              :placeholder="getPlaceholder('remark')"
              type="textarea"
              maxlength="150"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="!detailForm.dialogModal"
      class="close-btn"
    >
      <el-button
        type="danger"
        @click="closeHandle"
      >
        关 闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import AlarmLevel from '../notification/offlineNotification/module/alarmLevel.vue';

export default {
  components: { AlarmLevel },
  props: {
    interRegionList: {
      type: Array,
      default: ()=>{return [];}
    },
    detailForm: {
      type: Object,
      default: ()=>{return {};}
    },
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      defaultProps: {
        children: 'children',
        label: 'name',
      },
      isCustom: false,
      day: 0,
      hour: 0,
      minute: 10,
      second: 0,
      notifyObj: [2],
      form: {
        id: null,
        ruleName: '',
        limitSpeed: 1,
        acc: 2,
        regionId: null,
        regionName: '',
        limitMode: 1,
        isSpeed: 0,
        ruleType: 1,
        alarmLevel: null,
        notifyOnce: 0,
        offlineTime: null,
        notifyObj: '2',
        startTime: '00:00:00',
        endTime: '23:59:59',
        phone: null,
        email: null,
        remark: ''
      }
    };
  },
  watch:{
    detailForm: {
      handler(newVal){
        if (newVal.ruleTypeName === '离线通知规则') {
          this.form = Object.assign(this.form, newVal);
          this.form.remark = `名称：${this.form.ruleName}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}，是否判断速度：${this.form.isSpeed ? '是' : '否'}，规则类型：${this.form.ruleType === 1 ? '通知' : '告警'}`;
          if (this.form.notifyObj) {
            this.notifyObj = this.form.notifyObj.split(',');
          }else{
            this.notifyObj = [];
          }
          if (this.form.offlineTime) {
            let list = this.form.offlineTime.split(':');
            this.day = list[0];
            this.hour = list[1];
            this.minute = list[2];
            this.second = list[3];
          }
          if (this.form.regionId) {
            let list = this.form.regionId.split(',');
            this.$nextTick(()=>{
              this.$refs.tree.setCheckedKeys(list);
            });
          }
        }
      },
      deep: true
    }
  },
  methods: {
    closeHandle(){
      this.$emit('closeHandle');
      this.close();
    },
    handleClear(){
      this.form.regionId = '';
      this.$refs.tree.setCheckedKeys([]);
    },
    clearRule(val){
      this.form.alarmLevel = val;
      this.$refs.form.clearValidate('alarmLevel');
    },
    handleChange(){
      this.isCustom = this.notifyObj.includes(1);
      this.form.notifyObj = this.notifyObj.toString();
    },
    handleNodeCheck(node, options){
      let ids = [];
      let list = [];
      let checkedNodes = options.checkedNodes;
      checkedNodes.forEach((item)=>{
        if (item.type === 2) {
          ids.push(item.id);
          list.push(item.name);
        }
      });
      this.form.regionName = list.toString();
      this.form.regionId = ids.toString();
    },
    handleRemark(){
      this.$set(this.form, 'remark', `名称：${this.form.ruleName}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}，是否判断速度：${this.form.isSpeed ? '是' : '否'}，规则类型：${this.form.ruleType === 1 ? '通知' : '告警'}`);
    },
    close(){
      this.$emit('update:dialogVisible', false);
      this.notifyObj = [2];
      this.$refs.tree.setCheckedKeys([]);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('OfflineNotification', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('OfflineNotification', value);
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  .flex-item{
    ::v-deep .el-form-item__content{
      display: flex;
    }
  }
  .close-btn{
    position: absolute;
    bottom: -60px;
    left: -50px;
  }
  .form-dialog{
    z-index: 3001 !important;
    pointer-events: none;
    ::v-deep .el-dialog{
      pointer-events: auto;
      width: 49% !important;
      float: right;
      // margin-right: 15px;
      left: -15px !important;
      transform: translate(0, -50%);
      .el-dialog__headerbtn{
        display: none;
      }
    }
  }
</style>
