<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible="dialogVisible"
    :title="detailForm.dialogModal ? '分段限速规则' : '分段限速规则-修改后'"
    :close-on-press-escape="false"
    append-to-body
    :modal="detailForm.dialogModal"
    width="60%"
    :class="{'form-dialog': !detailForm.dialogModal}"
    @close="close"
  >
    <el-form
      ref="form"
      :model="form"
      size="small"
      label-width="140px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('ruleName')"
            prop="ruleName"
          >
            <el-input
              v-model="form.ruleName"
              :placeholder="getPlaceholder('ruleName')"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('minSpeed')"
            prop="minSpeed"
          >
            <el-input
              v-model="form.minSpeed"
              :placeholder="getPlaceholder('minSpeed')"
              @input="handleRemark"
            >
              <template slot="append">km/h</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('positionMinSpeed')"
            prop="positionMinSpeed"
          >
            <el-input
              v-model.number="form.positionMinSpeed"
              :placeholder="getPlaceholder('positionMinSpeed')"
              @input="handleRemark"
            >
              <template slot="append">km/h</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isAlarm')"
          >
            <el-radio-group
              v-model="form.isAlarm"
            >
              <el-radio :label="0">
                不告警
              </el-radio>
              <el-radio :label="1">
                告警
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('duration')"
            prop="duration"
          >
            <el-time-picker
              v-model="form.duration"
              :placeholder="getPlaceholder('duration')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('startDate')"
            prop="startDate"
          >
            <el-date-picker
              v-model="form.startDate"
              :placeholder="getPlaceholder('startDate')"
              value-format="yyyy-MM-dd"
              type="date"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('endDate')"
            prop="endDate"
          >
            <el-date-picker
              v-model="form.endDate"
              type="date"
              :placeholder="getPlaceholder('endDate')"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('startTime')"
            prop="startTime"
          >
            <el-time-picker
              v-model="form.startTime"
              :placeholder="getPlaceholder('startTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('endTime')"
            prop="endTime"
          >
            <el-time-picker
              v-model="form.endTime"
              :placeholder="getPlaceholder('endTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('deptIds')"
            prop="deptIds"
          >
            <el-input
              v-model="form.deptName"
              disabled
            />
          </el-form-item>
        </div>
        <!-- <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="超速比"
            prop="overSpeedRatio"
          >
            <el-input
              v-model.number="form.overSpeedRatio"
              placeholder="请输入超速比"
              @input="handleRemark"
            >
            </el-input>
          </el-form-item>
        </div> -->
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            label="告警等级"
            prop="levelConfig"
          >
            <div class="main">
              <div class="item_label main_size">
                超速比
              </div>
              <div class="item_label main_parameter">
                超速时间(分钟)
              </div>
              <div class="item_label main_parameter">
                告警等级
              </div>
              <div class="item_label">
                  <span
                    class="add_item"
                  >添加</span>
              </div>
            </div>
            <div
              v-for="(item,index) in form.levelConfig"
              :key="index"
              class="main"
            >
              <div class="main_item main_size">
                <div class="main_item_select">
                  <el-input-number
                    v-model="form.levelConfig[index].percentage"
                    placeholder="请输入超速比"
                    :controls="false"
                    style="width: 90%;"
                  ></el-input-number>
                </div>
              </div>
              <div class="main_item main_parameter">
                <el-input-number
                  v-model="form.levelConfig[index].overSpeedTime"
                  clearable
                  placeholder="请输入超速时间"
                  :controls="false"
                  style="width: 90% !important;"
                ></el-input-number>
              </div>
              <div class="main_item">
                <el-select
                  v-model="form.levelConfig[index].alarmLevel"
                  clearable
                  placeholder="请选择告警等级"
                  style="width: 90% !important;"
                >
                  <el-option
                    v-for="item in dict.dict.alarmLevel"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
              <div class="main_item">
                <div
                  class="reduce_item"
                >
                  <i
                    class="el-icon-remove-outline reduce_icon"
                  />
                </div>
              </div>
            </div>
          </el-form-item>
        </div>
        <!-- <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('limitRate')"
            prop="limitRate"
          >
            <el-slider
              v-model="form.limitRate"
              :format-tooltip="formatTooltip"
              @input="handleRemark"
            />
          </el-form-item>
        </div> -->
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('tips')"
            prop="tips"
          >
            <el-checkbox-group v-model="form.tips">
              <el-checkbox
                :label="1"
              >紧急</el-checkbox>
              <el-checkbox
                :label="2"
              >显示器显示</el-checkbox>
              <el-checkbox
                :label="3"
              >TTS播读</el-checkbox>
              <el-checkbox
                :label="4"
              >广告屏显示</el-checkbox>
            </el-checkbox-group>
            <el-input
              v-model="form.tipsText"
              :autosize="{ minRows: 2, maxRows: 2 }"
              :placeholder="getPlaceholder('tipsText')"
              type="textarea"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.tipsText"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('tipsTimes')"
            prop="tipsTimes"
          >
            <el-input
              v-model.number="form.tipsTimes"
              :placeholder="getPlaceholder('tipsTimes')"
            >
              <template slot="append">次</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.tipsText"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('tipsInterval')"
            prop="tipsInterval"
          >
            <el-input
              v-model.number="form.tipsInterval"
              :placeholder="getPlaceholder('tipsInterval')"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model="form.remark"
              :autosize="{ minRows: 4, maxRows: 6 }"
              :placeholder="getPlaceholder('remark')"
              type="textarea"
              maxlength="150"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="!detailForm.dialogModal"
      class="close-btn"
    >
      <el-button
        type="danger"
        @click="closeHandle"
      >
        关 闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

export default {
  components: { },
  props: {
    detailForm: {
      type: Object,
      default: ()=>{return {};}
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    dict: {
      type: Object,
      default: ()=>{return {};}
    }
  },
  data () {
    return {
      form: {
        id: null,
        ruleName: '',
        isAlarm: 1,
        minSpeed: 60,
        positionMinSpeed: 60,
        startDate: '',
        endDate: '',
        duration: '00:02:00',
        startTime: '00:00:00',
        endTime: '23:59:59',
        deptIds: [],
        // limitRate: 80,
        remark: '',
        tips: [2, 3],
        tipsText: '',
        tipsTimes: 1,
        tipsInterval: 5,
        // overSpeedRatio: 0,
        levelConfig: [
          {
            percentage: '',
            overSpeedTime: '',
            alarm_level: ''
          }
        ]
      }
    };
  },
  watch:{
    detailForm: {
      handler(newVal){
        if (newVal.ruleTypeName === '分段限速规则') {
          this.form = Object.assign(this.form, newVal);
          this.form.remark = `名称：${this.form.ruleName}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}，持续时间：${this.form.duration}， 分段限速最低速度：${this.form.minSpeed}km/h，定位最低速度：${this.form.positionMinSpeed}km/h`;
        }
      },
      deep: true
    }
  },
  methods: {
    closeHandle(){
      this.$emit('closeHandle');
      this.close();
    },
    // 格式化滑块的显示值
    formatTooltip (val) {
      return val + '%';
    },
    handleRemark(){
      this.$set(this.form, 'remark', `名称：${this.form.ruleName}，开始时间：${this.form.startTime}，结束时间：${this.form.endTime}，持续时间：${this.form.duration}，分段限速最低速度：${this.form.minSpeed}km/h，定位最低速度：${this.form.positionMinSpeed}km/h`);
    },
    close(){
      this.$emit('update:dialogVisible', false);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('SubsectionRestrictRule', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('SubsectionRestrictRule', value);
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  .close-btn{
    position: absolute;
    bottom: -60px;
    left: -50px;
  }
  .form-dialog{
    z-index: 3001 !important;
    pointer-events: none;
    ::v-deep .el-dialog{
      pointer-events: auto;
      width: 49% !important;
      float: right;
      // margin-right: 15px;
      left: -15px !important;
      transform: translate(0, -50%);
      .el-dialog__headerbtn{
        display: none;
      }
    }
  }

  .main {
    display: flex;
    width: 85%;
    text-align: center;
  }

  .main_item {
    flex: 1;
    height: 45px;
    line-height: 45px;
    border: 1px solid #c1c9da;
  }

  .main_size {
    flex: 1 !important;
  }

  .item_label {
    flex: 1;
    height: 40px;
    line-height: 40px;
    background-color: #e1e5ee;
    border: 1px solid #c1c9da;
  }

  .main_item ::v-deep .el-select, .main_item ::v-deep .el-input {
    width: 100%;
  }

  .add_item {
    border: 1px solid #aebac5;
    padding: 3px 5px;
    background-color: #ffffff;
    margin: 5px;
    cursor: pointer;
  }

  .main_item_input {
    ::v-deep .el-input__inner {
      background-color: #ecf5ff;
      border-color: #d9ecff;
      display: inline-block;
      height: 32px;
      line-height: 30px;
      color: #409eff;
      border-width: 1px;
      border-style: solid;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      white-space: nowrap;
    }
  }
</style>
