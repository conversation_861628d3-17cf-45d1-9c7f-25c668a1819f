<template>
  <div class="alarm-level">
    <div @click="dialog = !dialog">
      <el-input
        v-model="alarmName"
        placeholder="请选择告警级别"
        readonly
      />
    </div>
    <div
      v-show="dialog"
      class="alarm-level-content"
    >
      <div class="main">
        <div class="item_label main_parameter">
          离线时长（秒）
        </div>
        <div class="item_label">
          告警级别
        </div>
        <div class="item_label">
          <el-button
            class="add_item"
            :disabled="commandList.length > 2"
            @click="addHandle"
          >添加</el-button>
        </div>
      </div>
      <div
        v-for="(item,index) in commandList"
        :key="index"
        class="main"
      >
        <div class="main_item main_parameter">
          <span>
            <el-input-number
              v-model.number="item.day"
              controls-position="right"
            />天
          </span>
          <span>
            <el-input-number
              v-model.number="item.hour"
              controls-position="right"
              :min="0"
              :max="23"
            />时
          </span>
          <span>
            <el-input-number
              v-model.number="item.minute"
              controls-position="right"
              :min="0"
              :max="59"
            />分
          </span>
          <span>
            <el-input-number
              v-model.number="item.second"
              controls-position="right"
              :min="0"
              :max="59"
            />秒
          </span>
        </div>
        <div class="main_item">
          <div class="main_item_select">
            <el-select
              v-model="commandList[index].level"
            >
              <el-option
                v-for="subItem in alarmLevelOptions"
                :key="subItem.value"
                :label="subItem.label"
                :value="subItem.value"
              />
            </el-select>
          </div>
        </div>
        <div class="main_item">
          <div
            class="reduce_item"
          >
            <i
              class="el-icon-remove-outline reduce_icon"
              @click="reduceHandle(index)"
            />
          </div>
        </div>
      </div>
      <div class="main main_btn">
        <el-button @click="dialog = false">关闭</el-button>
        <el-button @click="submit">确认</el-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props:{
    value: {
      type: String,
      default: null
    }
  },
  data(){
    return{
      commandList: [
        { level: 1, day: 0, hour: 0, minute: 5, second: 0 }
      ],
      alarmName: '',
      alarmLevelOptions: [
        {label: '一级告警', value: 1},
        {label: '二级告警', value: 2},
        {label: '三级告警', value: 3}
      ],
      formItem: {
        level: 1,
        day: 0,
        hour: 0,
        minute: 0,
        second: 0
      },
      dialog: false,
      alarmValue: ''
    };
  },
  watch:{
    value: {
      handler(newVal){
        if (newVal) {
          this.editValue(newVal);
        }
      }
    }
  },
  methods:{
    editValue(val){
      this.alarmName = '';
      this.commandList = [];
      let list = val.split(',');
      list.forEach((item)=>{
        let data = item.split(':');
        let form = this.formattingTime(data[0]);
        let obj = Object.assign(JSON.parse(JSON.stringify(this.formItem)), form);
        obj.level = Number(data[1]);
        let alarmData = this.alarmLevelOptions.find((element=> element.value === obj.level));
        let val = alarmData.label + '：' + data[0] + '秒；';
        this.commandList.push(obj);
        this.alarmName += val;
      });
    },
    formattingTime(count) {
      let second = count % 60;
      let minute = parseInt(parseInt(count) / 60) % 60;
      let hour = parseInt( parseInt(count / 60) / 60 ) % 24;
      let day = parseInt( parseInt( parseInt(count / 60) / 60 ) / 24 );
      let from = {
        second: second,
        minute: minute,
        hour: hour,
        day: day
      };
      return from;
    },
    submit(){
      this.alarmName = '';
      this.alarmValue = '';
      this.commandList.forEach((item)=>{
        let data = this.alarmLevelOptions.find((element=> element.value === item.level));
        let time = item.day * 86400 + item.hour * 3600 + item.minute * 60 + item.second;
        let val = data.label + '：' + time + '秒；';
        this.alarmName += val;
        this.alarmValue += time + ':' + item.level + ',';
      });
      this.dialog = false;
      this.$emit('clearRule', this.alarmValue.slice(0,this.alarmValue.length-1));

    },
    reduceHandle (index) {
      this.commandList.splice(index, 1);
    },
    addHandle () {
      let obj = JSON.parse(JSON.stringify(this.formItem));
      switch (this.commandList.length) {
      case 0:
        obj.minute = 5;
        break;
      case 1:
        obj.minute = 10;
        obj.level = 2;
        break;
      case 2:
        obj.minute = 30;
        obj.level = 3;
        break;
      }
      this.commandList.push(obj);
    },
  }
};
</script>
<style lang="less" scoped>
.main{
  display: flex;
  width: 100%;
  text-align: center;
}
.main_item{
  flex: 1;
  height: 45px;
  line-height: 45px;
  border: 1px solid #c1c9da;
}
.item_label{
  flex: 1;
  height: 40px;
  line-height: 40px;
  background-color: #e1e5ee;
  border: 1px solid #c1c9da;
}
.main_item ::v-deep.el-select, .main_item ::v-deep.el-input{
  width: 100%;
}
.add_item{
  border: 1px solid #aebac5;
  padding: 5px 8px;
  background-color: #fff;
  margin: 5px;
  cursor: pointer;
}
.main_item_input{
  width: 140px !important;
  ::v-deep .el-input__inner{
    background-color: #ecf5ff;
    border-color: #d9ecff;
    display: inline-block;
    height: 32px;
    line-height: 30px;
    color: #409EFF;
    border-width: 1px;
    border-style: solid;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    white-space: nowrap;
  }
}
.main_item_tag{
  margin-left: 3px;
}
.main_parameter{
  flex: 3 !important;
  overflow: auto;
  display: flex;
  justify-content: center;
  box-sizing: content-box;
  ::v-deep .el-input-number{
    width: 100px;
  }
  span{
    margin-right: 10px;
  }
}
.main_item_select{
  ::v-deep .el-input__inner{
    height: 45px;
  }
}
.alarm-level{
    position: relative;
    &-content{
        position: absolute;
        width: 100%;
        z-index: 99;
        background-color: #e1e5ee;
    }
}
.main_btn{
    display: flex;
    justify-content: center;
    padding: 10px 0;
}
</style>
