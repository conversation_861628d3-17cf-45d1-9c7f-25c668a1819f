<template>
  <div class="xh-container">
    <!--工具栏-->
    <div class="head-container">
      <HeadCommon
        :dict="dict"
        :permission="permission"
        :head-config="headConfig"
      />
    </div>
    <div class="xh-crud-table-container">
      <crudOperation
        :download="false"
        :permission="permission"
      >
        <el-button
          slot="right"
          v-permission="['admin', 'overspeedRule:del']"
          :loading="crud.delAllLoading"
          class="filter-item"
          icon="el-icon-delete"
          size="small"
          @click="toDeleteAll(crud.selections)"
        >
          删 除
        </el-button>
        <el-button
          slot="right"
          v-permission="['admin', 'rule:allot']"
          class="filter-item"
          icon="el-icon-s-tools"
          size="small"
          @click="setRuleAllot"
        >
          规则分配
        </el-button>
        <el-button
          slot="right"
          v-permission="['admin', 'rule:logs']"
          class="filter-item"
          icon="el-icon-search"
          size="small"
          @click="setRuleLogs"
        >
          规则日志
        </el-button>
        <el-button
          slot="right"
          class="filter-item"
          icon="el-icon-warning-outline"
          size="small"
          @click="getRuleTips"
        >
          规则说明
        </el-button>
      </crudOperation>
      <!--表格渲染-->
      <el-table
        ref="table"
        v-loading="crud.loading"
        :data="crud.data"
        :cell-style="{'text-align':'center'}"
        :max-height="tableMaxHeight"
        style="width: 100%;height: calc(100% - 47px);"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column
          type="selection"
          width="50"
        />
        <!--      <el-table-column-->
        <!--        type="index"-->
        <!--        label="#"-->
        <!--      />-->
        <el-table-column
          v-permission="['admin', 'overspeedRule:edit', 'overspeedRule:del']"
          label="操作"
          width="160"
          fixed="right"
          :resizable="false"
        >
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            >
              <template slot="right">
                <el-button
                  v-permission="['admin', 'overspeedRule:del']"
                  :loading="crud.dataStatus[scope.row.id].delete === 2"
                  type="text"
                  size="small"
                  class="table-button-del"
                  @click="toDelete(scope.row)"
                >
                  删除
                </el-button>
                <el-button
                  v-permission="['admin', 'rule:logs']"
                  type="text"
                  size="small"
                  class="table-button-view"
                  @click="setRuleLogs(scope.row)"
                >
                  操作日志
                </el-button>
              </template>
            </udOperation>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns.visible('ruleName')"
          :label="getLabel('ruleName')"
          prop="ruleName"
          show-overflow-tooltip
          min-width="140"
          :resizable="false"
        />
        <el-table-column
          v-if="columns.visible('regionName')"
          :label="getLabel('regionName')"
          prop="regionName"
          show-overflow-tooltip
          min-width="120"
          :resizable="false"
        />
        <el-table-column
          v-if="columns.visible('limitSpeed')"
          :label="getLabel('limitSpeedUnit')"
          prop="limitSpeed"
          show-overflow-tooltip
          min-width="120"
          :resizable="false"
        />
        <el-table-column
          v-if="columns.visible('duration')"
          :label="getLabel('duration')"
          prop="duration"
          min-width="100"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            {{ parseDuration(scope.row.duration) }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns.visible('startTime')"
          :label="getLabel('startTime')"
          prop="startTime"
          min-width="120"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            <span class="table-date-td">
              {{ scope.row.startTime }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns.visible('endTime')"
          :label="getLabel('endTime')"
          prop="endTime"
          min-width="120"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            <span class="table-date-td">
              {{ scope.row.endTime }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns.visible('isPhoto')"
          :label="getLabel('isPhoto')"
          prop="isPhoto"
          min-width="100"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            {{ scope.row.isWarn === 1 ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns.visible('isWarn')"
          :label="getLabel('isWarn')"
          prop="isWarn"
          min-width="100"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            {{ scope.row.isWarn === 1 ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns.visible('isAlarm')"
          :label="getLabel('isAlarm')"
          prop="isAlarm"
          min-width="100"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            {{ scope.row.isAlarm === 0 ? '否' : scope.row.isAlarm === 1 ? '仅告警一次' : '间隔告警' }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns.visible('tipsInterval')"
          :label="getLabel('tipsIntervalUnit')"
          prop="tipsInterval"
          min-width="140"
          show-overflow-tooltip
          :resizable="false"
        />
        <el-table-column
          v-if="columns.visible('tipsTimes')"
          :label="getLabel('tipsTimesUnit')"
          prop="tipsTimes"
          min-width="150"
          show-overflow-tooltip
          :resizable="false"
        />
        <el-table-column
          v-if="columns.visible('createUserName')"
          :label="getLabel('createUserName')"
          prop="createUserName"
          min-width="120"
          show-overflow-tooltip
          :resizable="false"
        />
        <el-table-column
          v-if="columns.visible('createTime')"
          :label="getLabel('createTime')"
          prop="createTime"
          min-width="180"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            <span class="table-date-td">{{ parseTimes(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns.visible('targetNum')"
          :label="getLabel('targetNum')"
          prop="targetNum"
          min-width="100"
          show-overflow-tooltip
          :resizable="false"
        >
          <template slot-scope="scope">
            <span
              class="vehicle"
              @click="setRuleVehicle(scope.row)"
            >
              {{ scope.row.targetNum }}
            </span>
          </template>
        </el-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/nodata.png')"
        />
      </el-table>
    </div>
    <!--表单渲染-->
    <eForm
      :dict="dict"
      :ruleTypeId="ruleTypeId"
      v-bind="$attrs"
    />
  </div>
</template>

<script>
import crudRule from '@/api/rule';
import eForm from './module/form';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import getLabel from '@/utils/getLabel';
import HeadCommon from '@/components/formHead/headCommon.vue';

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('OverspeedRule', 'uniName'), // 超速规则
  crudMethod: { ...crudRule }
});

export default {
  name: 'OverspeedRule',
  components: { eForm, crudOperation, udOperation, HeadCommon },
  mixins: [presenter(crud)],
  props:{
    nodeData: {
      type: Object,
      default: ()=>{ return {};}
    },
    dict: {
      type: Object,
      default: ()=>{ return {};}
    }
  },
  data () {
    return {
      permission: {
        add: ['admin', 'overspeedRule:add'],
        edit: ['admin', 'overspeedRule:edit'],
        del: ['admin', ''],
        export: ['admin', 'overspeedRule:export']
      },
      headConfig: {
        item: {
          1: {
            name: '规则名称',
            type: 'input',
            value: 'name'
          }
        },
        button: {
        }
      },
      ruleTypeId: null
    };
  },
  watch:{
    nodeData:{
      handler(newVal){
        if (newVal.name === '超速规则') {
          this.ruleTypeId = newVal.id;
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    parseDuration(val){
      const [hours, minutes] = val.split(":").map(Number);
      let formattedTime = "";
      if (hours > 0) {
        formattedTime += `${hours}时`;
      }
      if (minutes > 0) {
        formattedTime += `${minutes}分`;
      }
      return formattedTime;
    },
    closeHandle(){
      this.crud.form.isEdit = undefined;
      this.crud.status.edit = CRUD.STATUS.NORMAL;
    },
    compareDetails(data) {
      this.crud.toEdit(data);
    },
    toDetails(id){
      this.$nextTick(() => {
        if (this.crud.data.length) {
          let data = this.crud.data.find(item => item.id === id);
          if (data) {
            this.crud.toEdit(data);
          }
        } else if(this.crud.loading){
          setTimeout(() => {
            this.toDetails(id);
          }, 500);
        }
      });
    },
    setRuleVehicle(data){
      if (!data.targetNum) {
        this.$message.warning('该规则没有关联车辆');
        return;
      }
      this.$emit('setRuleVehicle', data);
    },
    setRuleAllot(){
      this.$emit('setRuleAllot');
    },
    setRuleLogs(data){
      this.$emit('setRuleLogs', data);
    },
    // 删除拎出来单独处理
    toDelete (data) {
      this.$confirm(`确定将选择数据删除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = [];
        let dataStatus = this.crud.getDataStatus(data.id);
        ids.push(data.id);
        dataStatus.delete = CRUD.STATUS.PROCESSING;
        crudRule.del({ruleId: ids, ruleTypeId: this.ruleTypeId}).then(() => {
          dataStatus.delete = CRUD.STATUS.PREPARED;
          this.crud.dleChangePage(ids.length);
          this.crud.delSuccessNotify();
          this.crud.refresh();
        }).catch(() => {
          dataStatus.delete = CRUD.STATUS.PREPARED;
        });
      }).catch(() => {
      });
    },
    // 多选删除
    toDeleteAll (datas) {
      this.$confirm(`确定将选择数据删除?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (datas.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.crud.delAllLoading = true;
        const ids = [];
        datas.forEach(val => {
          ids.push(val.id);
        });
        crudRule.del({ruleId: ids, ruleTypeId: this.ruleTypeId}).then(() => {
          this.crud.delAllLoading = false;
          this.crud.dleChangePage(ids.length);
          this.crud.delSuccessNotify();
          this.crud.refresh();
        }).catch(() => {
          this.crud.delAllLoading = false;
        });
      }).catch(() => {
      });
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('OverspeedRule', value);
    },
    /** 刷新 - 之前 */
    [CRUD.HOOK.beforeRefresh] () {
      this.crud.query.ruleTypeId = this.ruleTypeId;
    },
    parseTimes (time) {
      if (time) {
        return this.$moment(time).format('YYYY-MM-DD HH:mm:ss');
      }
    },
    getRuleTips(){
      this.$emit('getRuleTips', `${this.ruleTypeId}`);
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}
::v-deep .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
  background-color: #fcf0c1
}
.vehicle{
  color: #027DB4;
  text-decoration:underline;
  cursor:pointer;
}
</style>
