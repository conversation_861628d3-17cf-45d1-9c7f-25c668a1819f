<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="form.isEdit ? '道路限速规则-修改前' : crud.status.title"
    :close-on-press-escape="close-on-press-escape"
    :class="{'form-dialog': form.isEdit}"
    append-to-body
    width="85%"
    top="5vh"
    @close="close"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="150px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('ruleName')"
            prop="ruleName"
          >
            <el-input
              v-model="form.ruleName"
              :placeholder="getPlaceholder('ruleName')"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('regionId')"
            prop="regionId"
          >
            <xh-select
              v-model="form.regionName"
              :placeholder="getPlaceholder('regionId')"
              clearable
              @clear="handleClear"
            >
              <el-option>
                <el-tree
                  ref="tree"
                  show-checkbox
                  :data="interRegionList"
                  :props="defaultProps"
                  node-key="id"
                  :expand-on-click-node="false"
                  :check-on-click-node="true"
                  @check="handleNodeCheck"
                />
              </el-option>
            </xh-select>
          </el-form-item>
        </div>
        <div
          v-if="form.regionName"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('limitMode')"
          >
            <el-radio-group
              v-model="form.limitMode"
            >
              <el-radio :label="1">
                电子围栏内
              </el-radio>
              <el-radio :label="2">
                电子围栏外
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('interRegionId')"
            prop="interRegionId"
          >
            <xh-select
              v-model="form.interRegionName"
              :placeholder="getPlaceholder('interRegionId')"
              clearable
              @clear="handleInterClear"
            >
              <el-option>
                <el-tree
                  ref="interTree"
                  show-checkbox
                  :data="interRegionList"
                  :props="defaultProps"
                  node-key="id"
                  :expand-on-click-node="false"
                  :check-on-click-node="true"
                  @check="handleInterNodeCheck"
                />
              </el-option>
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24 flex-item">
          <el-form-item
            :label="getLabel('highLimitSpeed')"
            prop="highLimitSpeed"
          >
            <el-input
              v-model.number="form.highLimitSpeed"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('highLimitSpeed')"
              :disabled="!highLimitSpeedCheck"
            >
              <template slot="suffix">km/h</template>
              <template slot="append">
                <el-checkbox v-model="highLimitSpeedCheck"/>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item
            :label="getLabel('highLimitText')"
            prop="highLimitText"
          >
            <el-input
              v-model="form.highLimitText"
              :placeholder="getPlaceholder('highLimitText')"
            />
          </el-form-item>
          <el-form-item
            :label="getLabel('highWarnDiffSpeed')"
            prop="highWarnDiffSpeed"
          >
            <el-input
              v-model.number="form.highWarnDiffSpeed"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('highWarnDiffSpeed')"
              :disabled="!highWarnDiffSpeedCheck"
            >
              <template slot="suffix">km/h</template>
              <template slot="append">
                <el-checkbox v-model="highWarnDiffSpeedCheck"/>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item
            :label="getLabel('highWarnText')"
            prop="highWarnText"
          >
            <el-input
              v-model="form.highWarnText"
              :placeholder="getPlaceholder('highWarnText')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24 flex-item">
          <el-form-item
            :label="getLabel('countryLimitSpeed')"
            prop="countryLimitSpeed"
          >
            <el-input
              v-model.number="form.countryLimitSpeed"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('countryLimitSpeed')"
              :disabled="!countryLimitSpeedCheck"
            >
              <template slot="suffix">km/h</template>
              <template slot="append">
                <el-checkbox v-model="countryLimitSpeedCheck"/>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item
            :label="getLabel('countryLimitText')"
            prop="countryLimitText"
          >
            <el-input
              v-model="form.countryLimitText"
              :placeholder="getPlaceholder('countryLimitText')"
            />
          </el-form-item>
          <el-form-item
            :label="getLabel('countryWarnDiffSpeed')"
            prop="countryWarnDiffSpeed"
          >
            <el-input
              v-model.number="form.countryWarnDiffSpeed"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('countryWarnDiffSpeed')"
              :disabled="!countryWarnDiffSpeedCheck"
            >
              <template slot="suffix">km/h</template>
              <template slot="append">
                <el-checkbox v-model="countryWarnDiffSpeedCheck"/>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item
            :label="getLabel('countryWarnText')"
            prop="countryWarnText"
          >
            <el-input
              v-model="form.countryWarnText"
              :placeholder="getPlaceholder('countryWarnText')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24 flex-item">
          <el-form-item
            :label="getLabel('otherLimitSpeed')"
            prop="otherLimitSpeed"
          >
            <el-input
              v-model.number="form.otherLimitSpeed"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('otherLimitSpeed')"
              :disabled="!otherLimitSpeedCheck"
            >
              <template slot="suffix">km/h</template>
              <template slot="append">
                <el-checkbox v-model="otherLimitSpeedCheck"/>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item
            :label="getLabel('otherLimitText')"
            prop="otherLimitText"
          >
            <el-input
              v-model="form.otherLimitText"
              :placeholder="getPlaceholder('otherLimitText')"
            />
          </el-form-item>
          <el-form-item
            :label="getLabel('otherWarnDiffSpeed')"
            prop="otherWarnDiffSpeed"
          >
            <el-input
              v-model.number="form.otherWarnDiffSpeed"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('otherWarnDiffSpeed')"
              :disabled="!otherWarnDiffSpeedCheck"
            >
              <template slot="suffix">km/h</template>
              <template slot="append">
                <el-checkbox v-model="otherWarnDiffSpeedCheck"/>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item
            :label="getLabel('otherWarnText')"
            prop="otherWarnText"
          >
            <el-input
              v-model="form.otherWarnText"
              :placeholder="getPlaceholder('otherWarnText')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24 flex-item">
          <el-form-item
            :label="getLabel('provinceLimitSpeed')"
            prop="provinceLimitSpeed"
          >
            <el-input
              v-model.number="form.provinceLimitSpeed"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('provinceLimitSpeed')"
              :disabled="!provinceLimitSpeedCheck"
            >
              <template slot="suffix">km/h</template>
              <template slot="append">
                <el-checkbox v-model="provinceLimitSpeedCheck"/>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item
            :label="getLabel('provinceLimitText')"
            prop="provinceLimitText"
          >
            <el-input
              v-model="form.provinceLimitText"
              :placeholder="getPlaceholder('provinceLimitText')"
            />
          </el-form-item>
          <el-form-item
            :label="getLabel('provinceWarnDiffSpeed')"
            prop="provinceWarnDiffSpeed"
          >
            <el-input
              v-model.number="form.provinceWarnDiffSpeed"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('provinceWarnDiffSpeed')"
              :disabled="!provinceWarnDiffSpeedCheck"
            >
              <template slot="suffix">km/h</template>
              <template slot="append">
                <el-checkbox v-model="provinceWarnDiffSpeedCheck"/>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item
            :label="getLabel('provinceWarnText')"
            prop="provinceWarnText"
          >
            <el-input
              v-model="form.provinceWarnText"
              :placeholder="getPlaceholder('provinceWarnText')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('duration')"
            prop="duration"
          >
            <el-time-picker
              v-model="form.duration"
              :placeholder="getPlaceholder('duration')"
              value-format="HH:mm:ss"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isPhoto')"
          >
            <el-radio-group
              v-model="form.isPhoto"
            >
              <el-radio :label="1">
                拍照
              </el-radio>
              <el-radio :label="0">
                不拍照
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div
          v-if="form.isPhoto"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('photoNumber')"
          >
            <el-input
              v-model.number="form.photoNumber"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('photoNumber')"
            />
          </el-form-item>
        </div>
        <div
          v-if="form.isPhoto"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('photoInterval')"
            prop="photoInterval"
          >
            <el-input
              v-model.number="form.photoInterval"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('photoInterval')"
            >
              <template slot="append">分</template>
            </el-input>
          </el-form-item>
        </div>
        <div
          v-if="form.isPhoto"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('camera')"
            prop="camera"
          >
            <xh-select
              v-model="form.camera"
              :placeholder="getPlaceholder('camera')"
              clearable
            >
              <el-option
                v-for="item in channelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </xh-select>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('isAlarm')"
          >
            <el-radio-group
              v-model="form.isAlarm"
              @change="$refs.form.clearValidate('tips')"
            >
              <el-radio :label="0">
                不告警
              </el-radio>
              <el-radio :label="1">
                告警
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div v-if="form.highLimitText || form.highWarnText || form.countryLimitText || form.countryWarnText || form.provinceLimitText || form.provinceWarnText || form.otherLimitText || form.otherWarnText">
          <div
            class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24"
          >
            <el-form-item
              :label="getLabel('tips')"
              prop="tips"
              :rules="form.isAlarm !== 0 ? validate.validateTips : ''"
            >
              <el-checkbox-group v-model="form.tips">
                <el-checkbox
                  :label="1"
                >紧急</el-checkbox>
                <el-checkbox
                  :label="2"
                >显示器显示</el-checkbox>
                <el-checkbox
                  :label="3"
                >TTS播读</el-checkbox>
                <el-checkbox
                  :label="4"
                >广告屏显示</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </div>
          <div
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('tipsTimes')"
              prop="tipsTimes"
            >
              <el-input
                v-model.number="form.tipsTimes"
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="getPlaceholder('tipsTimes')"
              >
                <template slot="append">次</template>
              </el-input>
            </el-form-item>
          </div>
          <div
            class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
          >
            <el-form-item
              :label="getLabel('tipsInterval')"
              prop="tipsInterval"
            >
              <el-input
                v-model.number="form.tipsInterval"
                oninput="value=value.replace(/[^\d]/g,'')"
                :placeholder="getPlaceholder('tipsInterval')"
              >
                <template slot="append">分</template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model="form.remark"
              :autosize="{ minRows: 4, maxRows: 6 }"
              :placeholder="getPlaceholder('remark')"
              type="textarea"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="form.isEdit !== 1"
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

const defaultForm = {
  id: null,
  ruleTypeId: null,
  ruleName: '',
  duration: '00:10:00',
  regionId: null,
  regionName: null,
  limitMode: null,
  interRegionId: null,
  interRegionName: '',
  highLimitSpeed: null,
  highLimitText: null,
  highWarnDiffSpeed: null,
  highWarnText: null,
  countryLimitSpeed: null,
  countryLimitText: null,
  countryWarnDiffSpeed: null,
  countryWarnText: null,
  provinceLimitSpeed: null,
  provinceLimitText: null,
  provinceWarnDiffSpeed: null,
  provinceWarnText: null,
  otherLimitSpeed: null,
  otherLimitText: null,
  otherWarnDiffSpeed: null,
  otherWarnText: null,
  isPhoto: 0,
  photoNumber: null,
  photoInterval: null,
  camera: null,
  isAlarm: 1,
  tipsTimes: 1,
  tipsInterval: 5,
  remark: '',
  tips: [2, 3]
};
export default {
  components: { },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    ruleTypeId: {
      type: Number,
      default: null
    },
    interRegionList: {
      type: Array,
      default: ()=>{return [];}
    }
  },
  data () {
    const validateTips = (rule, value, callback) => {
      // 自定义验证逻辑
      if (value.length === 0) {
        callback(new Error('请选择语音提示'));
      } else {
        callback();
      }
    };
    return {
      rules: {
        ruleName: { required: true, message: '请输入规则名称', trigger: 'blur' },
        photoInterval: { required: true, message: '请输入拍照间隔', trigger: 'blur' },
        camera: { required: true, message: '请选择摄像头', trigger: 'change' },
        tipsTimes: { required: true, message: '请输入语音提示总次数', trigger: 'blur' },
        tipsInterval: { required: true, message: '请输入语音提示间隔', trigger: 'blur' }
      },
      defaultProps: {
        children: 'children',
        label: 'name',
        disabled: node => node.type === 1 && !node.children
      },
      highLimitSpeedCheck: true,
      highWarnDiffSpeedCheck: false,
      countryLimitSpeedCheck: true,
      countryWarnDiffSpeedCheck: false,
      provinceLimitSpeedCheck: true,
      provinceWarnDiffSpeedCheck: false,
      otherLimitSpeedCheck: true,
      otherWarnDiffSpeedCheck: false,
      channelOptions: [],
      validate: {
        validateTips: {required: true, validator: validateTips, trigger: 'change'}
      }
    };
  },
  watch: {
    dict: {
      handler(){
        const channelData = this.dict.ruleManage.find((item)=>item.value === 'rule_camera');
        this.channelOptions = channelData?.children;
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleInterClear(){
      this.form.interRegionId = '';
      this.$refs.interTree.setCheckedKeys([]);
    },
    handleClear(){
      this.form.regionId = '';
      this.$refs.tree.setCheckedKeys([]);
    },
    handleNodeCheck(node, options){
      let ids = [];
      let list = [];
      let checkedNodes = options.checkedNodes;
      checkedNodes.forEach((item)=>{
        if (item.type === 2) {
          ids.push(item.id);
          list.push(item.name);
        }
      });
      this.form.regionName = list.toString();
      this.form.regionId = ids.toString();
      if (this.form.regionName) {
        this.form.limitMode = 1;
      }else{
        this.form.limitMode = null;
      }
    },
    handleInterNodeCheck(node, options){
      let ids = [];
      let list = [];
      let checkedNodes = options.checkedNodes;
      checkedNodes.forEach((item)=>{
        if (item.type === 2) {
          ids.push(item.id);
          list.push(item.name);
        }
      });
      this.form.interRegionName = list.toString();
      this.form.interRegionId = ids.toString();
    },
    handleRemark(){
      this.$set(this.form, 'remark', `名称：${this.form.ruleName}`);
    },
    close(){
      this.$refs.tree.setCheckedKeys([]);
      this.$refs.interTree.setCheckedKeys([]);
    },
    // 添加前
    [CRUD.HOOK.beforeToAdd] () {
      this.$refs.form && this.$refs.form.clearValidate();
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.form.remark = `名称：${this.form.ruleName}`;
    },
    /** 编辑 - 之前 */
    [CRUD.HOOK.beforeToEdit] () {
      if (this.form.interRegionId) {
        let list = this.form.interRegionId.split(',');
        this.$nextTick(()=>{
          this.$refs.interTree.setCheckedKeys(list);
        });
      }
      if (this.form.regionId) {
        let list = this.form.regionId.split(',');
        this.$nextTick(()=>{
          this.$refs.tree.setCheckedKeys(list);
        });
      }
    },
    /** 提交 - 之前 */
    [CRUD.HOOK.beforeSubmit] () {
      this.form.ruleTypeId = this.ruleTypeId;
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('RoadwayRestrict', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('RoadwayRestrict', value);
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  ::v-deep .el-input__suffix{
    line-height: 32px;
  }
  .flex-item{
    display: flex;
    ::v-deep .el-checkbox{
      margin-right: 0;
    }
    ::v-deep .el-form-item{
      flex: 1;
    }
  }
  .form-dialog{
    z-index: 3001 !important;
    pointer-events: none;
    ::v-deep .el-dialog{
      pointer-events: auto;
      width: 49% !important;
      float: left;
      // margin-left: 15px;
      left: 15px;
      transform: translate(0, -50%);
      z-index: 3001;
      .el-dialog__headerbtn{
        display: none;
      }
    }
  }
</style>
