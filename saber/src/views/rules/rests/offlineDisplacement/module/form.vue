<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="form.isEdit ? '离线位移规则-修改前' : crud.status.title"
    :close-on-press-escape="close-on-press-escape"
    :class="{'form-dialog': form.isEdit}"
    append-to-body
    width="60%"
    top="5vh"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="110px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('ruleName')"
            prop="ruleName"
          >
            <el-input
              v-model="form.ruleName"
              :placeholder="getPlaceholder('ruleName')"
              maxlength="15"
              show-word-limit
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('offlineTime')"
            prop="offlineTime"
          >
            <el-input
              v-model.number="form.offlineTime"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('offlineTime')"
              @input="handleRemark"
            >
              <template slot="append">秒</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('distance')"
            prop="distance"
          >
            <el-input
              v-model.number="form.distance"
              oninput="value=value.replace(/[^\d]/g,'')"
              :placeholder="getPlaceholder('distance')"
              @input="handleRemark"
            >
              <template slot="append">米</template>
            </el-input>
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('dayStartTime')"
            prop="dayStartTime"
          >
            <el-time-picker
              v-model="form.dayStartTime"
              :placeholder="getPlaceholder('dayStartTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            :label="getLabel('dayEndTime')"
            prop="dayEndTime"
          >
            <el-time-picker
              v-model="form.dayEndTime"
              :placeholder="getPlaceholder('dayEndTime')"
              value-format="HH:mm:ss"
              @input="handleRemark"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            :label="getLabel('deptIds')"
            prop="deptIds"
          >
            <DeptFormMultiSelect
              v-if="crud.status.add > 0"
              v-model="form.deptIds"
              :is-show="crud.status.cu > 0"
              placeholder="请选择所属机构"
              size="small"
              checkStrictly
            />
            <el-input
              v-else
              v-model="form.deptName"
              disabled
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            label="告警等级"
            prop="levels"
          >
            <div class="main">
              <div class="item_label main_parameter">
                离线位移距离阈值(米)
              </div>
              <div class="item_label main_parameter">
                告警等级
              </div>
              <div class="item_label">
                <span
                  class="add_item"
                  @click="addHandle"
                >添加</span>
              </div>
            </div>
            <div
              v-for="(item,index) in form.levels"
              :key="index"
              class="main"
            >
              <div class="main_item main_size">
                <div class="main_item_select">
                  <el-input-number
                    v-model="form.levels[index].threshold"
                    placeholder="请输入离线位移距离阈值"
                    :controls="false"
                    style="width: 90%;"
                  />
                </div>
              </div>
              <div class="main_item">
                <el-select
                  v-model="form.levels[index].level"
                  clearable
                  placeholder="请选择告警等级"
                  style="width: 90%;"
                >
                  <el-option
                    v-for="item in dict.dict.alarmLevel"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
              <div class="main_item">
                <div
                  class="reduce_item"
                >
                  <i
                    class="el-icon-remove-outline reduce_icon"
                    @click="reduceHandle(index)"
                  />
                </div>
              </div>
            </div>
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-24">
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model="form.remark"
              :autosize="{ minRows: 4, maxRows: 6 }"
              :placeholder="getPlaceholder('remark')"
              type="textarea"
              maxlength="150"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="form.isEdit !== 1"
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import DeptFormMultiSelect from '@/components/select/DeptFormMultiSelect/DeptFormMultiSelect.vue';
import { ruledetail } from '@/api/rule';

const defaultForm = {
  id: null,
  ruleTypeId: null,
  ruleName: '',
  dayStartTime: '00:00:00',
  dayEndTime: '23:59:59',
  offlineTime: 120,
  distance: 500,
  remark: '',
  levels: [],
  deptIds: [],
};
export default {
  components: { DeptFormMultiSelect },
  mixins: [form(defaultForm)],
  props: {
    dict: {
      type: Object,
      required: true
    },
    ruleTypeId: {
      type: Number,
      default: null
    }
  },
  data () {
    return {
      rules: {
        ruleName: { required: true, message: '请输入规则名称', trigger: 'blur' },
        offlineTime: { required: true, message: '请输入离线时长', trigger: 'blur' },
        distance: { required: true, message: '请输入离线位移', trigger: 'blur' },
        deptIds: { required: true, message: '请选择所属机构', trigger: 'change' },
      },
      formItem: {
        threshold: '',
        level: ''
      },
    };
  },
  methods: {
    handleRemark(){
      this.$set(this.form, 'remark', `名称：${this.form.ruleName}，离线时长：${this.form.offlineTime}秒，离线位移：${this.form.distance}米，开始时间：${this.form.dayStartTime}，结束时间：${this.form.dayEndTime}`);
    },
    addHandle() {
      let obj = JSON.parse(JSON.stringify(this.formItem));
      this.form.levels.push(obj);
    },
    reduceHandle (index) {
      this.form.levels.splice(index, 1);
    },
    /** 编辑 - 之前 */
    [CRUD.HOOK.beforeToEdit] () {
      if (!this.form.isEdit) {
        ruledetail({ruleType: this.form.ruleTypeId, ruleId: this.form.id}).then(res => {
          Object.assign( this.form, res.data);
          this.handleEditNumber();
        }).catch(err => {
          console.log('获取详情失败', err);
        });
      } else {
        this.handleEditNumber();
      }
    },
    // 添加前
    [CRUD.HOOK.beforeToAdd] () {
      this.$refs.form && this.$refs.form.clearValidate();
    },
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.form.remark = `名称：${this.form.ruleName}，离线时长：${this.form.offlineTime}秒，离线位移：${this.form.distance}米，开始时间：${this.form.dayStartTime}，结束时间：${this.form.dayEndTime}`;
      this.$nextTick(() => {
        this.rules.deptIds = this.crud.status.add > 0 ? { required: true, message: '请选择所属机构', trigger: 'change' } : null;
        this.$refs['form'].clearValidate('deptIds');
      });
    },
    /** 提交 - 之前 */
    [CRUD.HOOK.beforeSubmit] () {
      this.form.ruleTypeId = this.ruleTypeId;
      if (this.form.levels && this.form.levels.length) {
        this.form.levels.forEach(element => {
          element.level = Number(element.level);
        });
      }
    },
    /** 新增错误 - 之后 */
    [CRUD.HOOK.afterAddError] () {
      this.handleEditNumber();
    },
    /** 编辑错误 - 之后 */
    [CRUD.HOOK.afterEditError] () {
      this.handleEditNumber();
    },
    handleEditNumber() {
      if (this.form.levels && this.form.levels.length) {
        this.form.levels.forEach(element => {
          element.level = element.level.toString();
        });
      }
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('OfflineDisplacement', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('OfflineDisplacement', value);
    }
  }
};
</script>

<style lang="less" scoped>
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
  }
  .form-dialog{
    z-index: 3001 !important;
    pointer-events: none;
    ::v-deep .el-dialog{
      pointer-events: auto;
      width: 49% !important;
      float: left;
      // margin-left: 15px;
      left: 15px;
      transform: translate(0, -50%);
      z-index: 3001;
      .el-dialog__headerbtn{
        display: none;
      }
    }
  }
.main {
  display: flex;
  width: 85%;
  text-align: center;
}

.main_item {
  flex: 1;
  height: 45px;
  line-height: 45px;
  border: 1px solid #c1c9da;
}

.main_size {
  flex: 1 !important;
}

.item_label {
  flex: 1;
  height: 40px;
  line-height: 40px;
  background-color: #e1e5ee;
  border: 1px solid #c1c9da;
}

.main_item ::v-deep .el-select, .main_item ::v-deep .el-input {
  width: 100%;
}

.add_item {
  border: 1px solid #aebac5;
  padding: 3px 5px;
  background-color: #ffffff;
  margin: 5px;
  cursor: pointer;
}
</style>
