<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="beforeClose"
    :visible="dialogVisible"
    title="规则日志"
    append-to-body
    width="60%"
  >
    <el-row v-if="!ruleQuery.ruleId">
      <el-form
        ref="form"
        :label-width="labelWidth"
      >
        <div class="xh-header-content">
          <div class="el-col el-col-6 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-6 xh-header-content-item">
            <el-form-item label="规则名称">
              <el-input
                v-model="query.ruleName"
                clearable
                size="small"
                placeholder="请输入规则名称"
              />
            </el-form-item>
          </div>
          <div class="el-col el-col-6 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-6 xh-header-content-item">
            <el-form-item label="开始时间">
              <el-date-picker
                v-model="query.startTime"
                size="small"
                class="timeQuery"
                type="datetime"
                default-time="00:00:00"
                placeholder="选择开始日期时间"
                :clearable="false"
                value-format="timestamp"
                @blur="judge()"
              />
            </el-form-item>
          </div>
          <div class="el-col el-col-6 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-6 xh-header-content-item">
            <el-form-item label="结束时间">
              <el-date-picker
                v-model="query.endTime"
                size="small"
                class="timeQuery"
                type="datetime"
                default-time="23:59:59"
                :clearable="false"
                placeholder="选择结束日期时间"
                value-format="timestamp"
                @blur="judge()"
              />
            </el-form-item>
          </div>
          <div class="el-col el-col-6 el-col-md-6 no-print xh-crud-search">
            <el-form-item>
              <el-button
                class="filter-item"
                size="small"
                type="primary"
                icon="el-icon-search"
                @click="searchClick"
              >查 询
              </el-button>
              <el-button
                class="filter-item clear-item"
                size="small"
                icon="el-icon-refresh-left"
                @click="clearClick"
              >重 置
              </el-button>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-row>
    <el-button
      v-if="!ruleQuery.ruleId"
      class="filter-item clear-item"
      size="small"
      type="success"
      icon="el-icon-download"
      :loading="btnLoading"
      style="margin-bottom: 4px;"
      @click="toExport"
    >导 出
    </el-button>
    <!--表格渲染-->
    <el-table
      ref="table"
      v-loading="loading"
      :data="tableData"
      class="dialog-table"
      :cell-style="{'text-align':'center'}"
      height="60vh"
    >
      <el-table-column
        type="index"
        label="#"
        width="80"
      />
      <el-table-column
        label="规则名称"
        prop="name"
        show-overflow-tooltip
        min-width="100"
        :resizable="false"
      />
      <el-table-column
        label="所属机构"
        prop="deptName"
        show-overflow-tooltip
        min-width="100"
        :resizable="false"
      />
      <el-table-column
        label="规则类型"
        prop="ruleType"
        show-overflow-tooltip
        min-width="100"
        :resizable="false"
      />
      <el-table-column
        label="操作用户"
        prop="author"
        min-width="100"
        show-overflow-tooltip
        :resizable="false"
      />
      <el-table-column
        label="操作时间"
        prop="operateTime"
        min-width="180"
        show-overflow-tooltip
        :resizable="false"
      >
        <template slot-scope="scope">
          <span class="table-date-td">{{ parseTimes1000(scope.row.operateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作动作"
        prop="operateType"
        min-width="100"
        show-overflow-tooltip
        :resizable="false"
      >
        <template slot-scope="scope">
          <span>{{ parseType(scope.row.operateType) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="详情"
        min-width="100"
        :resizable="false"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            icon="el-icon-view"
            @click="toDetails(scope.row)"
          />
        </template>
      </el-table-column>
      <el-empty
        slot="empty"
        :image="require('@/assets/images/nodata.png')"
      />
    </el-table>
    <div class="xh-pagination">
      <el-pagination
        background
        :page-size.sync="page.size"
        :total="page.total"
        :current-page.sync="page.current"
        :page-sizes="[10, 20, 30, 40, 50, 100]"
        :pager-count="5"
        layout="total, prev, pager, next, sizes"
        @size-change="sizeChangeHandler"
        @current-change="pageChangeHandler"
      />
    </div>
    <FatigueRuleForm
      :dialogVisible.sync="fatigueVisible"
      :detail-form="detailForm"
      :interRegionList="interRegionList"
      :dict="dict"
      @closeHandle="closeHandle"
    />
    <OverspeedForm
      :dialogVisible.sync="overspeedVisible"
      :detail-form="detailForm"
      :interRegionList="interRegionList"
      @closeHandle="closeHandle"
    />
    <ProhibitForm
      :dialogVisible.sync="prohibitVisible"
      :detail-form="detailForm"
      :interRegionList="interRegionList"
      :dict="dict"
      @closeHandle="closeHandle"
    />
    <NightRestrictForm
      :dialogVisible.sync="nightRestrictVisible"
      :detail-form="detailForm"
      @closeHandle="closeHandle"
    />
    <SubsectionRestrictForm
      :dialogVisible.sync="subsectionRestrictVisible"
      :detail-form="detailForm"
      :dict="dict"
      @closeHandle="closeHandle"
    />
    <AnomalousRuleForm
      :dialogVisible.sync="anomalousRuleFormVisible"
      :detail-form="detailForm"
      :dict="dict"
      @closeHandle="closeHandle"
    />
    <NightSteerForm
      :dialogVisible.sync="nightSteerVisible"
      :detail-form="detailForm"
      :dict="dict"
      @closeHandle="closeHandle"
    />
    <ElectronicFenceForm
      :dialogVisible.sync="electronicFenceVisible"
      :detail-form="detailForm"
      :interRegionList="interRegionList"
      @closeHandle="closeHandle"
    />
    <PathExcursionForm
      :dialogVisible.sync="pathExcursionVisible"
      :detail-form="detailForm"
      :interRegionList="interRegionList"
      @closeHandle="closeHandle"
    />
    <ApproachRegionForm
      :dialogVisible.sync="approachRegionVisible"
      :detail-form="detailForm"
      :interRegionList="interRegionList"
      @closeHandle="closeHandle"
    />
    <RoadwayRestrictForm
      :dialogVisible.sync="roadwayRestrictVisible"
      :detail-form="detailForm"
      :interRegionList="interRegionList"
      @closeHandle="closeHandle"
    />
    <OfflineNotificationForm
      :dialogVisible.sync="offlineNotificationVisible"
      :detail-form="detailForm"
      :interRegionList="interRegionList"
      @closeHandle="closeHandle"
    />
    <DriverIdentityForm
      :dialogVisible.sync="driverIdentityVisible"
      :detail-form="detailForm"
      @closeHandle="closeHandle"
    />
    <OfflineDisplacementForm
      :dict="dict"
      :dialogVisible.sync="offlineDisplacementVisible"
      :detail-form="detailForm"
      @closeHandle="closeHandle"
    />
    <VehicleTable
      ref="vehicleTable"
      :dialogVisible.sync="vehicleTableVisible"
    />
  </el-dialog>
</template>
<script>
import { getLogs } from '@/api/rule';
import FatigueRuleForm from './module/fatigueRuleForm.vue';
import OverspeedForm from './module/overspeedForm.vue';
import ProhibitForm from './module/prohibitForm.vue';
import NightRestrictForm from './module/nightRestrictForm.vue';
import SubsectionRestrictForm from './module/subsectionRestrictForm.vue';
import NightSteerForm from './module/nightSteerForm.vue';
import ElectronicFenceForm from './module/electronicFenceForm.vue';
import PathExcursionForm from './module/pathExcursionForm.vue';
import ApproachRegionForm from './module/approachRegionForm.vue';
import RoadwayRestrictForm from './module/roadwayRestrictForm.vue';
import AnomalousRuleForm from './module/anomalousRuleForm.vue';
import OfflineNotificationForm from './module/offlineNotificationForm.vue';
import DriverIdentityForm from './module/driverIdentityForm.vue';
import OfflineDisplacementForm from './module/offlineDisplacementForm.vue';
import VehicleTable from './module/vehicleTable.vue';
import jsonToHump from '@/utils/helper/jsonToHump';
import { downLoadFile } from '@/utils';
const visibleList = [
  {label: '疲劳规则', value: 'fatigueVisible'},
  {label: '超速规则', value: 'overspeedVisible'},
  {label: '禁行规则', value: 'prohibitVisible'},
  {label: '夜间限速规则', value: 'nightRestrictVisible'},
  {label: '分段限速规则', value: 'subsectionRestrictVisible'},
  {label: '报停异动规则', value: 'anomalousRuleFormVisible'},
  {label: '夜间异动规则', value: 'nightSteerVisible'},
  {label: '电子围栏规则', value: 'electronicFenceVisible'},
  {label: '路线偏航规则', value: 'pathExcursionVisible' },
  {label: '接近区域规则', value: 'approachRegionVisible' },
  {label: '道路限速规则', value: 'roadwayRestrictVisible'},
  {label: '离线通知规则', value: 'offlineNotificationVisible'},
  {label: '驾驶员身份识别', value: 'driverIdentityVisible'},
  {label: '离线位移规则', value: 'offlineDisplacementVisible'},
];
export default {
  dicts: ['alarmLevel'],
  components:{
    FatigueRuleForm, OverspeedForm, ProhibitForm, NightRestrictForm, SubsectionRestrictForm, NightSteerForm, ElectronicFenceForm, PathExcursionForm, ApproachRegionForm, RoadwayRestrictForm, OfflineNotificationForm, DriverIdentityForm, OfflineDisplacementForm, VehicleTable, AnomalousRuleForm
  },
  props:{
    dialogVisible: {
      type: Boolean,
      default: false
    },
    ruleQuery: {
      type: Object,
      default: ()=>{return {};}
    },
    interRegionList: {
      type: Array,
      default: ()=>{return [];}
    }
  },
  data(){
    return{
      query:{
        ruleName: undefined,
        startTime: undefined,
        endTime: undefined,
        export: 0
      },
      labelWidth: '80px',
      loading: false,
      tableData: [],
      detailForm: {},
      fatigueVisible: false,
      overspeedVisible: false,
      prohibitVisible: false,
      nightRestrictVisible: false,
      subsectionRestrictVisible: false,
      anomalousRuleFormVisible: false,
      nightSteerVisible: false,
      electronicFenceVisible: false,
      pathExcursionVisible: false,
      approachRegionVisible: false,
      roadwayRestrictVisible: false,
      offlineNotificationVisible: false,
      driverIdentityVisible: false,
      offlineDisplacementVisible: false,
      vehicleTableVisible: false,
      btnLoading: false,
      page: {
        current: 1,
        size: 10,
        total: 0
      }
    };
  },
  mounted(){
    this.query.startTime = new Date(new Date().toLocaleDateString()).getTime();
    this.query.endTime = new Date(new Date().toLocaleDateString()).getTime() + 86400000 - 1000;
  },
  methods:{
    pageChangeHandler(e) {
      this.page.current = e;
      this.getData();
    },
    sizeChangeHandler(e) {
      this.page.size = e;
      this.page.current = 1;
      this.getData();
    },
    closeHandle(){
      this.$emit('closeHandle', this.detailForm.ruleTypeName);
    },
    toDetails(data){
      if (data.operateType === 4) {
        let tableData = [];
        if (data.detail) {
          let list = data.detail.split(',');
          list.forEach(element => {
            let form = {
              targetName: element
            };
            tableData.push(form);
          });
        }
        this.$refs.vehicleTable.getData(tableData);
        this.vehicleTableVisible = true;
      }else{
        let obj = visibleList.find((item)=>item.label === data.ruleType);
        this[obj.value] = true;
        this.detailForm = jsonToHump(JSON.parse(data.detail));
        this.detailForm.ruleTypeName = data.ruleType;
        this.detailForm.deptName = data.deptName;
        this.detailForm.dialogModal = true;
        if (data.operateType === 2 && this.ruleQuery.ruleId) {
          this.detailForm.dialogModal = false;
          let beforeDetailForm = jsonToHump(JSON.parse(data.beforeFixing));
          beforeDetailForm.ruleTypeName = data.ruleType;
          beforeDetailForm.deptName = data.deptName;
          beforeDetailForm.isEdit = 1;
          this.$emit('compareDetails', beforeDetailForm);
        }
      }

    },
    getData(){
      const ruleQuery = {
        startTime: 1000, // 开始时间为1查询所有操作日志
        endTime: new Date(new Date().toLocaleDateString()).getTime() + 86400000 - 1000,
        ...JSON.parse(JSON.stringify(this.ruleQuery))
      };
      let query = this.ruleQuery.ruleId ? ruleQuery : JSON.parse(JSON.stringify(this.query));
      query.startTime = query.startTime ? query.startTime / 1000 : undefined;
      query.endTime = query.endTime ? query.endTime / 1000 : undefined;
      query.current = this.page.current;
      query.size = this.page.size;
      this.loading = true;
      getLogs(query).then((res)=>{
        this.loading = false;
        this.tableData = res.data.content;
        this.page.total = res.data.total;
      }).catch((err)=>{
        this.loading = false;
      });
    },
    // 判断开始时间小于结束时间
    judge () {
      if (this.query.startTime) {
        if (
          this.$moment(this.query.startTime).valueOf() >
          this.$moment(this.query.endTime).valueOf()
        ) {
          this.$message({
            type: 'warning',
            message: '开始日期时间要小于结束日期时间'
          });
        }
      }
    },
    searchClick(){
      this.getData();
    },
    toExport(){
      if (!this.tableData.length) {
        this.$message.warning('当前无数据可导出');
        return;
      }
      let query = JSON.parse(JSON.stringify(this.query));
      query.startTime = query.startTime ? query.startTime / 1000 : undefined;
      query.endTime = query.endTime ? query.endTime / 1000 : undefined;
      query.export = 1;
      this.btnLoading = true;
      getLogs(query).then((res)=>{
        this.btnLoading = false;
        this.$message.success('导出成功');
        downLoadFile(`${location.origin}${res.data.content}`);
      }).catch((err)=>{
        this.btnLoading = false;
      });
    },
    clearClick(){
      this.query = {
        ruleName:'',
        startTime: null,
        endTime: null,
        export: 0
      };
    },
    beforeClose(){
      this.$emit('update:dialogVisible', false);
      this.query = {
        ruleName:'',
        startTime: new Date(new Date().toLocaleDateString()).getTime(),
        endTime: new Date(new Date().toLocaleDateString()).getTime() + 86400000 - 1000,
        export: 0
      };
      this.page = {
        current: 1,
        size: 10,
        total: 0
      };
      this.tableData = [];
    },
    parseType(type){
      return type === 1 ? '添加' : type === 2 ? '编辑' : type === 3 ? '删除' : type === 4 ? '分配' : '';
    },
    parseTimes1000 (time) {
      if (time) {
        return this.$moment(time * 1000).format('YYYY-MM-DD HH:mm:ss');
      }
    }
  }
};
</script>
<style lang="less" scoped>
.xh-crud-search{
    ::v-deep .el-form-item__content{
      margin-left: 0 !important;
    }
}
.dialog-table {
  margin-bottom: 10px;
}
.xh-pagination{
  text-align: right;
  ::v-deep .el-pagination__sizes {
    margin-right: 0;
  }
}
</style>
