<template>
  <div class="big-screen-base">
    <div class="top-center-title">北斗基础数据</div>
    <div class="content">
      <div class="left">
        <div class="echart-box">
          <div class="short-report-item echart-item">
            <div class="item-title">
              <div class="img-left"></div>
              <div class="text">北斗短报文应用方向分析</div>
              <div class="img-right"></div>
            </div>
            <ShortReport class="content-echart" :mapData="bdmTerminalApp" :list="domainList" />
            <div class="item-bottom"></div>
          </div>
        </div>
        <div class="echart-box">
          <div class="annual-increase-item echart-item">
            <div class="item-title">
              <div class="img-left"></div>
              <div class="text">北斗应用年度增长分析</div>
              <div class="img-right"></div>
            </div>
            <AnnualIncrease class="content-echart" :mapData="terminalYearList" />
            <div class="item-bottom"></div>
          </div>
        </div>
        <div class="echart-box">
          <div class="position-mode-item echart-item">
            <div class="item-title">
              <div class="img-left"></div>
              <div class="text">终端定位模式</div>
              <div class="img-right"></div>
            </div>
            <PositioningMode class="content-echart" :mapData="bdmGnssMode" :list="rnssGnssModeList" />
            <div class="item-bottom"></div>
          </div>
        </div>
      </div>
      <div class="center">
        <div class="nums">
          <div
            :class="['nums-item', curIndex === item.deviceType ? 'active' : '']"
            v-for="item in statisticList"
            :key="item.deviceType"
            @click="selectStatisticItem(item.deviceType)"
          >
            <div class="value">
              {{ item.value ? item.value.toLocaleString("en-US") : item.value }}
            </div>
            <div class="label">{{ item.label }}</div>
          </div>
        </div>
        <div
          class="map-content"
          id="containerBase"
          v-loading="mapLoading"
          element-loading-background="rgba(0, 0, 0, 0.8)"
        />
      </div>
      <div class="right">
        <div class="echart-box">
          <div class="high-precision-item echart-item">
            <div class="item-title">
              <div class="img-left"></div>
              <div class="text">北斗高精度应用方向分析</div>
              <div class="img-right"></div>
            </div>
            <highPrecision class="content-echart" :mapData="bdmTerminalApp" :list="highPrecisionList" />
            <div class="item-bottom"></div>
          </div>
        </div>
        <div class="echart-box line">
          <div class="rank-line-item echart-line">
            <div class="item-title">
              <div class="img-left"></div>
              <div class="text">总接入量排名 TOP10</div>
              <div class="img-right"></div>
            </div>
            <rankingLine class="content-line" :mapData="bdmTerminalApp" :list="terminalDeptList" />
            <div class="item-bottom"></div>
          </div>
      </div>
    </div>
    </div>
  </div>
</template>

<script>
import ShortReport from "./shortReport.vue";
import AnnualIncrease from "./annualIncrease.vue";
import HighPrecision from "./highPrecision.vue";
import RankingLine from "./rankingLine.vue";
import PositioningMode from "./positioningMode.vue";
import AMapUtil from "@/components/map/AMapUtil";
import { getTerminalLocation, terminalCount } from "@/api/monitoring/main";
import {
  domain,
  terminalYear,
  rnssGnssMode,
  terminalDept,
  highPrecision,
} from "@/api/show/base.js";
import { getDictDetail } from "@/api/security/alarmHistory";

let __AMap = null;
let __map = null;
let __massMarks = null;
let __styles = null;
let __markerListObj = {};

export default {
  name: "bigScreenBase",
  components: {
    ShortReport,
    AnnualIncrease,
    HighPrecision,
    RankingLine,
    PositioningMode,
  },
  data() {
    return {
      AMap: null, // 高德地图对象
      map: null, // 本页面地图实例
      mapLoading: false,
      curIndex: "0",
      statisticList: [
        {
          label: "北斗终端",
          value: 0,
          prop: "totalCount",
          deviceType: "0",
        },
        {
          label: "定位终端",
          value: 0,
          prop: "rnssCount",
          deviceType: "1",
        },
        {
          label: "穿戴终端",
          value: 0,
          prop: "wearCount",
          deviceType: "2",
        },
        {
          label: "授时终端",
          value: 0,
          prop: "pntCount",
          deviceType: "5",
        },
        {
          label: "监测终端",
          value: 0,
          prop: "monitCount",
          deviceType: "4",
        },
        {
          label: "短报文终端",
          value: 0,
          prop: "rdssCount",
          deviceType: "3",
        },
      ],
      styles: null,
      massMarks: null,
      markerListObj: {},
      bdmGnssMode: [],
      bdmTerminalApp: [],
      domainList: [],
      bdmDeviceType: [],
      terminalYearList: [],
      rnssGnssModeList: [],
      terminalDeptList: [],
      highPrecisionList: [],
    };
  },
  created() {
    this.getDictData();
  },
  async mounted() {
    this.getInitData();
    this.getTerminalData();
    await this.initMap();
    this.forEachGetAllTerminalInfo();
  },
  beforeDestroy() {
    clearInterval(this.timer);
    this.timer = null;
    if(__massMarks) {
      __massMarks = null
    }
    if(__map) {
      //解绑地图的点击事件
      // __map.off("click", clickHandler);
      //销毁地图，并清空地图容器
      __map.destroy();
      //地图对象赋值为null
      __map = null
      //清除地图容器的 DOM 元素
      document.getElementById("containerBase").remove(); //"container" 为指定 DOM 元素的id
    }
  },
  activated() {
    this.initInterval();
    this.mapReload();
  },
  methods: {
    async mapReload () {
      let canvas = document.getElementsByClassName('amap-layer')[0];
      if (canvas) {
        console.log(canvas); // 打印绘制的canvas
        let canvasContent = canvas.getContext('webgl'); // 因为高德地图是通过webGl来绘制的，所以通过getContext(‘webgl’)才能获取到内容
        console.log(canvasContent);
        if (canvasContent?.drawingBufferHeight < 10 && canvasContent?.drawingBufferWidth < 10) {
          await this.initMap();
          this.forEachGetAllTerminalInfo();
        }
      }
    },
    initInterval() {
      this.timer = setInterval(() => {
        let index = this.statisticList.findIndex( item => item.deviceType === this.curIndex)
        if(index + 2 > this.statisticList.length) {
          index = 0
        } else {
          index += 1
        }
        this.selectStatisticItem(this.statisticList[index].deviceType)
        this.getInitData();
        this.getTerminalData();
        this.forEachGetAllTerminalInfo();
      }, 1000 * 30);
      this.$once('hook:deactivated', () => {
        clearInterval(this.timer);
        this.timer = null;
      });
    },
    getDictData() {
      getDictDetail("bdm_gnss_mode").then((res) => {
        const list = res.data || [];
        this.bdmGnssMode = list
      });
      getDictDetail("bdm_terminal_app").then((res) => {
        const list = res.data || [];
        this.bdmTerminalApp = list
      });
      getDictDetail("bdm_device_type").then((res) => {
        const list = res.data || [];
        this.bdmDeviceType = list
      });
    },
    getInitData() {
      domain().then((res) => {
        this.domainList = res.data.data || [];
      });
      terminalYear().then((res) => {
        this.terminalYearList = res.data.data || [];
      });
      rnssGnssMode().then((res) => {
        this.rnssGnssModeList = res.data.data || [];
      });
      terminalDept().then((res) => {
        this.terminalDeptList = res.data.data || [];
      });
      highPrecision().then((res) => {
        this.highPrecisionList = res.data.data || [];
      });
    },
    selectStatisticItem(curIndex) {
      this.curIndex = curIndex;
      this.createMarker();
    },
    getTerminalData() {
      terminalCount().then((res) => {
        if (res.code === 200) {
          this.statisticList.forEach((item) => {
            item.value = res.data[item.prop];
          });
        }
      });
    },
    async getTerminalInfo(deviceType) {
      const { data } = await getTerminalLocation(deviceType);
      return {
        ...data,
        deviceType: deviceType || "0",
      };
    },
    forEachGetAllTerminalInfo() {
      const deviceTypeList = this.statisticList.map((item) => item.deviceType);
      const promises = deviceTypeList.map(
        async (deviceType) =>
          await this.getTerminalInfo(deviceType === "0" ? "" : deviceType)
      );
      Promise.allSettled(promises).then((res) => {
        for (const item of res) {
          const tList = item.value?.terminalInfoList;
          const deviceType = item.value.deviceType;
          if (tList?.length) {
            __markerListObj[deviceType] = tList.reduce((arr, item) => {
              arr.push({
                lnglat: this.$utils.wgs84togcj02(item.longitude, item.latitude),
                style: Number(deviceType),
              })
              return arr;
            }, []);
          }
        }
        this.createMarker();
      });
    },
    createMarker() {
      const curIndex = this.curIndex;
      __massMarks.clear();
      if (curIndex === "0") {
        const allList = [];
        Object.keys(__markerListObj).forEach((item) => {
          allList.push(...__markerListObj[item]);
        });
        __massMarks.setData(allList);
      } else {
        __massMarks.setData(__markerListObj[curIndex]);
      }
    },
    initMap() {
      return new Promise((resolve) => {
        AMapUtil.loadAMap((AMap) => {
          console.log("-> 初始化地图成功");
          __AMap = AMap;
          this.$nextTick(() => {
            const massMarkerSize = 16;
            const massMarkerAnchor = massMarkerSize / 2;
            let center = [108.5525, 34.3227];
            __map = new AMap.Map("containerBase", {
              //设置地图容器id
              viewMode: "3D", //是否为3D地图模式
              zoom: 4.8, //初始化地图级别
              pitch: 40,
              center, //初始化地图中心点位置
              expandZoomRange: true,
              zooms: [4, 18],
              animateEnable: false,
              // zoomEnable: true, // 地图是否可缩放
              // doubleClickZoom: true, // 地图是否可通过双击鼠标放大地图
              // keyboardEnable: true, // 地图是否可通过键盘控制,默认为true
              // dragEnable: true, // 地图是否可通过鼠标拖拽平移
              // features: ["bg", "point", "road"],
              mapStyle: "amap://styles/darkblue",
              features: ['bg','road'],
              showBuildingBlock: false,
              isHotspot: false,
              terrain: false,
              jogEnable: false
            });
            __styles = [0, 1, 2, 3, 4, 5].map((number) => {
              return {
                url: require(`@/assets/images/map/iconMap.png`), //图标地址
                anchor: new AMap.Pixel(massMarkerAnchor, massMarkerAnchor), //图标显示位置偏移量，基准点为图标左上角
                size: new AMap.Size(massMarkerSize, massMarkerSize), //图标的尺寸
                zIndex: number + 1, //每种样式图标的叠加顺序，数字越大越靠前
              };
            });
            __AMap.plugin(
              ["AMap.DistrictSearch", "AMap.Scale", "AMap.MassMarks"],
              () => {
                const disCountry = new AMap.DistrictLayer.Country({
                  opacity: 0.8,
                  // depth: 0,
                  SOC: 'CHN', // 国家编码
                  styles: {
                    'stroke-width': 1.1, // 描边线宽
                    "nation-stroke": "#3ab7d7", // 国界线颜色
                    "coastline-stroke": "#3ab7d7", // 海岸线颜色
                    "province-stroke": "#89b7eb", // 省线颜色
                    // "city-stroke": "#62b4ef",
                    fill: "rgba(177,204,250,0.2)", // 背景填充颜色
                  },
                })
                disCountry.setMap(__map);
                __massMarks = new __AMap.MassMarks([], {
                  zIndex: 999,
                  zooms: [4, 18],
                  style: __styles,
                });
                __massMarks.setMap(__map);
                // const scale = new AMap.Scale({
                //   visible: true,
                // });
                // __map.addControl(scale);
                resolve();
              }
            );
          });
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.big-screen-base {
  -webkit-user-select: none; /* Safari */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE10+ */
  user-select: none; /* Standard syntax */
  flex:1;
  height: 100%;
  background-color: #050912;
  color: #fff;
  display: flex;
  flex-direction: column;
  position: relative;
  .top-center-title {
    // color: #fff;
    height: 100px;
    line-height: 60px;
    font-weight: 500;
    font-size: 26px;
    text-align: center;
    background: url(../images/top.png) center top/contain no-repeat;
  }
  .content {
    flex: 1;
    text-align: center;
    .echart-box{
      height: 33.33%;
      padding-bottom: 20px;
      &.line{
        height: 66.66%;
      }
      .echart-item,
      .echart-line {
        background-color: rgba(5,9,18, 0.8);
        .item-title {
          padding: 8px 10px;
          background-color: #051e34;
          display: flex;
          .img-left {
            flex: 1;
            background: url(../images/frame01_1.png) left top/contain no-repeat;
          }
          .text {
            text-align: left;
            flex: 6;
          }
          .img-right {
            flex: 1;
            background: url(../images/frame01_2.png) right top/contain no-repeat;
          }
        }
        .item-bottom {
          height: 20px;
          background: url(../images/frame02.png) center top/contain no-repeat;
        }
      }
    }
    .left {
      z-index: 999;
      position: absolute;
      left: 0;
      top: 80px;
      width: 21vw;
      height:calc(100% - 100px);
      overflow-y: auto;
      padding: 0px 10px;
      .echart-item {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        .content-echart {
          flex: 1;
        }
      }
    }
    .center {
      height: 100%;
      position: relative;
      .nums {
        position: absolute;
        display: flex;
        justify-content: center;
        z-index: 99;
        width: 100%;
        padding: 10px;
        .nums-item {
          padding: 8px 16px;
          cursor: pointer;
          background-color: #09131d;
          border: 1px solid transparent;
          margin-right: 8px;
          &.active {
            background-color: #2b6f94;
            border: 1px solid #3083af;
          }
          .value {
            font-size: 30px;
            font-weight: bold;
            color: #f5bd34;
          }
          .label {
            font-size: 16px;
            color: #fff;
          }
        }
      }
      .map-content {
        width: 100%;
        height: 100%;
        background-color: #050912;
      }
    }
    .right {
      z-index: 999;
      position: absolute;
      right: 0;
      top: 80px;
      width: 21vw;
      height:calc(100% - 100px);
      overflow-y: auto;
      padding: 0px 10px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .echart-item,
      .echart-line {
        // background-color: #050912;
        height: 100%;
        display: flex;
        flex-direction: column;
        .content-echart {
          flex: 1;
        }
      }
    }
  }
}
</style>
