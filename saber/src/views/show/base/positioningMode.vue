<template>
  <div ref="positioningModeRef"></div>
</template>

<script>
export default {
  name: "positioningMode",
  data() {
    return {
      options: {
        backgroundColor: "rgba(5,9,18, 0.8)",
        grid: {
          top: "15%",
          left: "10%",
          right: "10%",
          bottom: "15%",
          containLabel: true,
        },
        tooltip: {
          trigger: "item",
        },
        series: [
          {
            name: "",
            type: "pie",
            radius: ["40%", "55%"],
            avoidLabelOverlap: false,
            colors: ["#4b6fbd", "#91cd77", "#fac857"],
            padAngle: 5,
            itemStyle: {
              borderRadius: 4,
            },
            label: {
              color: "rgba(255, 255, 255, 1)",
              formatter: "{name|{b}}\n{time|{c}}",
              rich: {
                time: {},
              },
            },
            data: [
              { value: 0, name: "单GPS" },
              { value: 0, name: "北斗双模" },
              { value: 0, name: "单北斗" },
            ],
          },
        ],
      },
    };
  },
  mounted() {
    this.init();
    window.addEventListener("resize", this.resize);
  },
  beforeDestroy() {
    if(this.chartElement) {
      this.chartElement.dispose()
      this.chartElement = null
    }
    window.removeEventListener("resize", this.resize);
  },
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    mapData: {
      type: Object,
      default: () => [],
    },
  },
  watch: {
    list() {
      this.setData();
    },
    mapData() {
      this.setData();
    },
  },
  methods: {
    setData() {
      if (this.list.length && this.mapData.length) {
        const data = [];
        this.mapData.forEach((item) => {
          const cur = { value: 0, name: item.label };
          const obj = this.list.find((each) => each.gnssMode == item.value);
          if (obj) {
            cur.value = obj.count;
          }
          data.push(cur)
        });
        this.options.series[0].data = data;
        this.init()
      }
    },
    resize() {
      this.$nextTick(() => {
        this.chartElement.resize();
      });
    },
    init() {
      // const colors = [
      //   {label: '单GPS', color: '#4b6fbd',},
      //   {label: '北斗双模', color: '#91cd77',},
      //   {label: '单北斗', color: '#fac857',},
      // ]
      this.chartElement = this.$echarts.init(this.$refs.positioningModeRef);
      this.chartElement.setOption(this.options);
    },
  },
};
</script>

<style lang="less" scoped></style>
