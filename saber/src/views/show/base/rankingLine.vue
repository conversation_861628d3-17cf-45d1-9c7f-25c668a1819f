<template>
  <div class="rank-line">
    <div class="list-item" v-for="(item, index) in list" :key="index">
      <div class="line-box">
        <div class="line-text">
          <img :src="imgList[index]" alt="" v-if="index < 3" />
          <div v-else>{{ index + 1 }}</div>
        </div>
        <div class="real-item">
          <div class="line-title">
            <div :title="item.deptName" class="deptName">{{ item.deptName }}</div>
            <div :title="item.count">（{{ item.count }}）</div>
          </div>
          <div
            class="line-bar"
            :style="{ width: `${getWidth(item)}%`, height: '10px' }"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "rankingLine",
  data() {
    this.imgList = [
      require("../images/rank01.png"),
      require("../images/rank02.png"),
      require("../images/rank03.png"),
    ];
    return {
      listData: [],
      srcList: [],
    };
  },
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    getWidth(item) {
      const maxLen = this.list[0]?.count || 1
      return (item.count / maxLen) * 100
    }
  },
};
</script>

<style lang="less" scoped>
.rank-line {
  height: 100%;
  overflow-y: auto;
  background-color: rgba(5,9,18, 0.8);
  color: #fff;
  padding-top: 5%;
  .list-item {
    padding: 10px 20px;
    .line-box {
      display: flex;
      height: 36px;
      .line-text {
        width: 36px;
        height: 36px;
        img {
          height: 100%;
        }
        div {
          font-size: 20px;
        }
      }
      .real-item {
        flex: 1;
        overflow: hidden;
        padding-left: 10px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .line-title {
          display: flex;
          // justify-content: space-between;
          font-size: 14px;
          height: 22px;
          .deptName{
            max-width: 65%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .line-bar {
          background: linear-gradient(to right, #1e68e5, #1f8cd1, #27a9c1);
          border-radius: 10px;
        }
      }
    }
  }
}
</style>
