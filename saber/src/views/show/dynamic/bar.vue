<template>
  <div ref="barRef"></div>
</template>
  
<script>
export default {
  name: "Bar",
  data() {
    return {
      options: {
        backgroundColor: "rgba(5,9,18, 0.8)",
        label: {
          color: "rgba(255, 255, 255, 1)",
        },
        legend: {
          data: ["报警数量"],
          bottom: 0,
          itemWidth: 8,
          itemHeight: 8,
          borderRadius: 8,
          textStyle: {
            color: "#fff", // 设置图例文字颜色为红色
          },
        },
        grid: {
          top: "8%",
          left: "3%",
          right: "4%",
          bottom: "15%",
          containLabel: true,
        },
        tooltip: {
          trigger: "item",
        },
        xAxis: {
          type: "category",
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "rgba(255, 255, 255, 1)",
          },
          data: [],
        },
        yAxis: {
          type: "value",
          axisLabel: {
            color: "rgba(255, 255, 255, 1)",
          },
          axisLine: {
            show: true, // 是否显示y轴轴线
            lineStyle: {
              type: "solid", // 设置y轴轴线的类型
            },
          },
          splitLine: {
            show: false,
          },
        },
        series: [
          {
            data: [],
            name: "报警数量",
            type: "bar",
            barWidth: "20px",
            itemStyle: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "#5480d9" },
                { offset: 0.5, color: "#62acd9" },
                { offset: 1, color: "#62c7d9" },
              ]),
            },
            emphasis: {
              itemStyle: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "#2378f7" },
                  { offset: 0.7, color: "#2378f7" },
                  { offset: 1, color: "#83bff6" },
                ]),
              },
            },
          },
        ],
      }
    };
  },
  props: {
    mapData: {
      type: Array,
      default: () => {},
    },
  },
  watch: {
    mapData() {
      this.setData();
    },
  },
  created() {
    let today = this.$moment()
    let sevenDays = []
    for (let i = 0; i < 7; i++) {
      let date = this.$moment(today).subtract(i, 'days').format('MM/DD')
      sevenDays.unshift(date);
    }
    this.options.xAxis.data = sevenDays
  },
  mounted() {
    this.init();
    window.addEventListener("resize", this.resize);
  },
  beforeDestroy() {
    if(this.chartElement) {
      this.chartElement.dispose()
      this.chartElement = null
    }
    window.removeEventListener("resize", this.resize);
  },
  methods: {
    setData() {
      const data = [];
      this.options.xAxis.data.forEach((key) => {
        data.push(this.mapData[key] || 0);
      });
      this.options.series[0].data = data;
      this.init();
    },
    resize() {
      this.$nextTick(() => {
        this.chartElement.resize();
      })
    },
    init() {
      this.chartElement = this.$echarts.init(this.$refs.barRef);
      this.chartElement.setOption(this.options);
    },
  },
};
</script>
  
<style lang="less" scoped>
</style>
  