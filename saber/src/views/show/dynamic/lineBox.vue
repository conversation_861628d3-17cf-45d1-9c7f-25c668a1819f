<template>
  <div ref="annualIncreaseRef"></div>
</template>

<script>
export default {
  name: "annualIncrease",
  data() {
    return {
      options: {
        backgroundColor: "rgba(5,9,18, 0.8)",
        label: {
          color: "rgba(255, 255, 255, 1)",
        },
        legend: {
          data: [{name: "上线量", itemStyle: {color: '#5bc4fd'}}, {name: "在线量", itemStyle: {color: '#ddde5f'}}],
          bottom: 0,
          itemWidth: 8,
          itemHeight: 8,
          borderRadius: 8,
          textStyle: {
            color: "#fff", // 设置图例文字颜色为红色
          },
        },
        grid: {
          top: "8%",
          left: "3%",
          right: "4%",
          bottom: "15%",
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
          }
        },
        xAxis: {
          type: "category",
          axisTick: {
            show: false,
          },
          boundaryGap: false,
          axisLabel: {
            color: "rgba(255, 255, 255, 1)",
          },
          data: [],
        },
        yAxis: {
          type: "value",
          axisLabel: {
            color: "rgba(255, 255, 255, 1)",
          },
          axisLine: {
            show: true, // 是否显示y轴轴线
            lineStyle: {
              type: "solid", // 设置y轴轴线的类型
            },
          },
          splitLine: {
            show: false,
          },
        },
        series: [
          {
            type: "line",
            smooth: true,
            name: "上线量",
            areaStyle: {
              normal: {
                color: "#314d7d",
              },
            },
            lineStyle: {
              color: "#5bc4fd",
            },
            emphasis: {
              focus: "series",
            },
            symbol: "none",
            data: [],
          },
          {
            type: "line",
            name: "在线量",
            smooth: true,
            areaStyle: {
              normal: {
                color: "#8e763a",
              },
            },
            lineStyle: {
              color: "#ddde5f",
            },
            emphasis: {
              focus: "series",
            },
            symbol: "none",
            data: [],
          },
        ],
      },
    };
  },
  props: {
    mapData: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    mapData() {
      this.setData();
    },
  },
  created() {
    const h = new Date().getHours();
    const list = [];
    const xAxis = []
    const dua = 24;
    for (let i = 0; i < dua; i++) {
      let cur = h - (24 / dua) * i;
      if (cur < 0) {
        cur = cur + 24;
      }
      xAxis.unshift(`${cur}:00`);
      list.unshift(cur);
    }
    this.list = list
    this.options.xAxis.data = xAxis;
  },
  mounted() {
    this.init();
    window.addEventListener("resize", this.resize);
  },
  beforeDestroy() {
    if (this.chartElement) {
      this.chartElement.dispose();
      this.chartElement = null;
    }
    window.removeEventListener("resize", this.resize);
  },
  methods: {
    setData() {
      const data1 = [];
      const data2 = [];
      const {ups, ons} = this.mapData
      this.list.forEach((key) => {
        data1.push(ups[key] || 0);
        data2.push(ons[key] || 0);
      });
      this.options.series[0].data = data1;
      this.options.series[1].data = data2
      this.init();
    },
    resize() {
      this.$nextTick(() => {
        this.chartElement.resize();
      });
    },
    init() {
      this.chartElement = this.$echarts.init(this.$refs.annualIncreaseRef);
      this.chartElement.setOption(this.options);
    },
  },
};
</script>

<style lang="less" scoped></style>
