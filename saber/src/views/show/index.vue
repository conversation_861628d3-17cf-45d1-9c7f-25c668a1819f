<template>
  <div class="big-screen" id="bigScreen">
    <div class="fullscreen-btn" @click="full">
      <i v-if="!fullscreen" class="icon-quanping" :style="{color: '#fff'}" />
    </div>
    <div class="base-dynamic">
      <BaseScreen />
      <div class="line"></div>
      <DynamicScreen />
    </div>
  </div>
</template>

<script>
import BaseScreen from "./base/index.vue";
import DynamicScreen from "./dynamic/index.vue";
export default {
  name: "bigScreen",
  components: {
    BaseScreen,
    DynamicScreen
  },
  data() {
    return {
      fullscreen: false,
    };
  },
  mounted() {
    window.addEventListener('fullscreenchange', this.fullscreenchange);
  },
  methods: {
    fullscreenchange(){
      this.fullscreen = document.fullscreenElement;
    },
    full() {
      let element = document.getElementById("bigScreen");
      if (this.fullscreen) {
        // 关闭全屏
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      } else {
        // 全屏
        if (element.requestFullscreen) {
          element.requestFullscreen();
        } else if (element.webkitRequestFullScreen) {
          element.webkitRequestFullScreen();
        } else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen();
        } else if (element.msRequestFullscreen) {
          // IE11
          element.msRequestFullscreen();
        }
      }
      this.fullscreen = !this.fullscreen;
    },
  },
};
</script>

<style lang="less" scoped>
.big-screen {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  position: relative;
  background-color: #091020;
  .fullscreen-btn {
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    right: 20px;
    font-size: 20px;
    z-index: 9999;
  }
  .base-dynamic {
    width: 200%;
    min-height: 860px;
    min-width: 3440px;
    display: flex;
    height: 100%;
    letter-spacing:1px;
    .line{
      width: 2px;
      background-color: antiquewhite;
      height: 100%;
    }
  }
}
</style>
