<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          labelWidth="104px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
          width="190"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{
            background: '#e1e5ee',
            'text-align': 'center',
          }"
          :data="crud.data"
          :max-height="tableMaxHeight"
          style="width: 100%; height: calc(100% - 47px);"
          :cell-style="{ 'text-align': 'center' }"
        >
          <el-table-column
            v-if="columns.visible('deviceType')"
            prop="deviceType"
            label="终端类别"
            min-width="150"
            :resizable="false"
          >
            <template #default="{ row }">
              <span>{{
                getEnumDictLabel("bdmDeviceType", row.deviceType)
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('deviceNum')"
            prop="deviceNum"
            label="赋码编号"
            min-width="180"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('uniqueId')"
            prop="uniqueId"
            label="序列号"
            min-width="180"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('targetName')"
            prop="targetName"
            label="监控对象"
            min-width="160"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.targetName || $utils.emptymap.targetName }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('action')"
            prop="action"
            min-width="100"
            label="在线状态"
            :resizable="false"
          >
            <template #default="{ row }">
              <span>{{ getEnumDictLabel("onlineStatus", row.action) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('actionTime')"
            prop="actionTime"
            label="最近一次上线/下线时间"
            width="180"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ scope.row.actionTime }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('faultCount')"
            prop="faultCount"
            min-width="160"
            label="设备在线异常告警数"
            :resizable="false"
          >
            <template #header>
              <div>
                <span>设备在线异常告警数</span>
                <el-tooltip>
                  <template #content>
                    <div style="font-size: 14px;">
                      <div>设备在线异常告警包括: </div>
                      <div>1. GNSS模块发生故障告警</div>
                      <div>2. GNSS天线未接或被剪断告警</div>
                      <div>3. GNSS天线短路告警</div>
                      <div>4. 终端主电源欠压告警</div>
                      <div>5. 终端主电源掉电告警</div>
                      <div>6. 终端LCD或显示器故障告警</div>
                      <div>7. TTS模块故障告警</div>
                      <div>8. 道路运输证IC卡模块故障告警</div>
                      <div>9. 摄像头故障告警</div>
                    </div>
                  </template>
                  <i
                    class="el-icon-info"
                    style="font-size: 16px;margin-left: 2px;position: relative;top: 1px"
                  />
                </el-tooltip>
              </div>
            </template>
            <template #default="{ row }">
              <span
                class="active-label"
                @click="toMore(row)"
              >
                {{ row.faultCount }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('runningStatus')"
            prop="runningStatus"
            min-width="100"
            label="运行状态"
            :resizable="false"
          >
            <template #default="{ row }">
              <span>{{ row.faultCount === 0 ? "正常" : "异常" }}</span>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
    </div>
    <el-dialog
      v-dialog-drag
      :close-on-click-modal="false"
      :visible="detailListVisible"
      title="终端告警"
      append-to-body
      width="800px"
      @close="handleClose"
    >
      <el-table
        v-loading="modalLoad"
        :data="detailAllList"
      >
        <el-table-column
          type="index"
          width="60"
          label="序号"
        />
        <el-table-column
          :resizable="false"
          prop="alarm_type"
          label="告警类型"
        >
          <template #default="{ row }">
            <span>{{ getEnumDictLabel("alarmType", row.alarm_type) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :resizable="false"
          prop="start_time"
          label="告警时间"
        >
          <template slot-scope="scope">
            {{ parseTimes(scope.row.start_time) }}
          </template>
        </el-table-column>
        <el-empty
          slot="empty"
          :image="require('@/assets/images/nodata.png')"
        />
      </el-table>
      <el-pagination
        layout="prev, pager, next"
        :total="modalTotal"
        @current-change="modalChange"
      />
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="handleClose"
        >
          关闭
        </el-button>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
import CRUD, { presenter } from "@/components/Crud/crud";
import crudOperation from "@/components/Crud/CRUD.operation";
import pagination from "@/components/Crud/Pagination";
import HeadCommon from "@/components/formHead/headCommon.vue";
import api, { getList } from "@/api/statistics/deviceStatus.js";

const crud = CRUD({
  title: "设备状态查询",
  optShow: {
    add: false,
    edit: false,
    del: false,
    download: false,
  },
  crudMethod: { ...api },
  queryOnPresenterCreated: false
});

export default {
  name: "DeviceStatus",
  components: {
    HeadCommon,
    crudOperation,
    pagination,
  },
  mixins: [presenter(crud)],
  // 数据字典
  dicts: [
    "statusType",
    "runningStatus",
    "onlineStatus",
    "bdmDeviceType",
    "alarmType",
  ],
  data() {
    return {
      permission: {
        add: ["admin", "deviceStatus:add"],
        edit: ["admin", "deviceStatus:edit"],
        del: ["admin", "deviceStatus:del"],
      },
      headConfig: {
        initQuery: true,
        item: {
          1: {
            name: "赋码编号",
            type: "input",
            value: "deviceNum",
          },
          2: {
            name: "开始时间",
            type: "datetime",
            value: "startTime",
            defaultFn: "7DS",
          },
          3: {
            name: "结束日期",
            type: "datetime",
            value: "endTime",
            defaultFn: "toDE",
          },
          4: {
            name: "监控对象",
            type: "input",
            value: "targetName",
          },
          5: {
            name: "运行状态",
            type: "select",
            value: "runningStatus",
            dictOptions: "runningStatus",
          },
        },
        button: {},
      },
      detailListVisible: false,
      detailAllList: [],
      modalTotal: 0,
      modalPage: 1,
      modalSize: 10,
      modalLoad: false,
    };
  },
  // 枚举类型处理
  computed: {
    EnumerationTypeHandling() {
      return (dictName, val) => {
        if (this.dict.dict[dictName] && this.dict.dict[dictName][val]) {
          return this.dict.dict[dictName][val].label;
        }
      };
    },
  },
  methods: {
    parseTimes(time) {
      if (time) {
        return this.$moment(time * 1000).format("YYYY-MM-DD HH:mm:ss");
      }
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return "";
      }
    },
    toMore({ deviceId, deviceType, action, actionTime }) {
      this.detailListVisible = true;
      this.deviceObj = {
        deviceType,
        deviceId,
        action,
        time: Math.floor(+new Date(actionTime) / 1000),
      };
      this.getModalData();
    },
    handleClose() {
      this.modalTotal = 0;
      this.modalPage = 1;
      this.detailAllList = [];
      this.detailListVisible = false;
    },
    getModalData() {
      this.modalLoad = true;
      getList({
        ...this.deviceObj,
        current: this.modalPage,
        size: this.modalSize,
      })
        .then((res) => {
          this.detailAllList = res.data.records;
          this.modalTotal = res.data.total;
        })
        .finally(() => {
          this.modalLoad = false;
        });
    },
    modalChange(val) {
      this.modalPage = val;
      this.getModalData();
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-input-number .el-input__inner {
  text-align: left;
}
/deep/.el-table th {
  text-align: center;
}
/deep/.el-table td {
  text-align: center;
}
</style>
