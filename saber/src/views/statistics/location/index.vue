<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="100px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :download="false"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          :cell-style="{'text-align':'center'}"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            v-if="columns.visible('deviceNum')"
            prop="deviceNum"
            min-width="180"
            label="赋码编号"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns.visible('deviceType')"
            prop="deviceType"
            min-width="120"
            label="终端类别"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <div>
                {{ getEnumDictLabel('bdmDeviceType', scope.row.deviceType) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('targetName')"
            prop="targetName"
            min-width="160"
            label="监控对象"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ scope.row.targetName || $utils.emptymap.targetName }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('charge')"
            prop="wireless"
            label="电量"
            align="center"
            width="90"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ scope.row.charge === 0 ? '-' : `${scope.row.charge}%` }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('gnssNum')"
            prop="gnssNum"
            label="定位卫星"
            align="center"
            width="90"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns.visible('wireless')"
            prop="wireless"
            label="通信信号"
            align="center"
            width="90"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns.visible('longitude')"
            prop="longitude"
            label="经度"
            min-width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ handlePosition(scope.row.longitude) }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('latitude')"
            prop="latitude"
            label="纬度"
            min-width="120"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ handlePosition(scope.row.latitude) }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('address')"
            prop="address"
            label="地址"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns.visible('altitude')"
            prop="altitude"
            min-width="100"
            label="高程(米)"
          >
            <template slot-scope="scope">
              <div>
                {{ $utils.emptymap.altitude(scope.row.altitude) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('speed')"
            prop="speed"
            min-width="100"
            label="速度(km)"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns.visible('bearing')"
            prop="bearing"
            min-width="100"
            label="方向(度)"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns.visible('time')"
            prop="time"
            min-width="180"
            label="定位时间"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ parseTime(scope.row.time) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('recvTime')"
            prop="recvTime"
            min-width="180"
            label="接收时间"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span class="table-date-td">
                {{ parseTime(scope.row.recvTime) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('batch')"
            prop="batch"
            min-width="120"
            label="上传类型"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <div>
                {{ getEnumDictLabel('batch', scope.row.batch) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('valid')"
            prop="valid"
            min-width="100"
            label="定位有效性"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <div>
                {{ scope.row.valid === 1 ? '有效' : '无效' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('mileage')"
            prop="mileage"
            width="150"
            label="里程km(OBD数据)"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns.visible('realSpeed')"
            prop="realSpeed"
            width="150"
            label="行驶速度(OBD数据)"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns.visible('oilMass')"
            prop="oilMass"
            width="130"
            label="油量(OBD数据)"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="columns.visible('ioStatus')"
            prop="ioStatus"
            width="150"
            label="休眠状态(OBD数据)"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ getIoStatusStr(scope.row.ioStatus) }}
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination/>
    </div>
  </basic-container>
</template>

<script>
import crudLocation from '@/api/statistics/location.js';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import pagination from '@/components/Crud/Pagination';
import { batchAddr } from '@/api/monitoring/track';
import HeadCommon from '@/components/formHead/headCommon.vue';
// crud交由presenter持有
const crud = CRUD({
  title: '定位信息查询',
  crudMethod: { ...crudLocation },
  optShow: {
    add: false,
    edit: false,
    del: false,
    download: false,
  },
  queryOnPresenterCreated: false
});

export default {
  name: 'LocationStatistics',
  components: {
    crudOperation,
    pagination,
    HeadCommon
  },
  mixins: [presenter(crud)],
  dicts: [
    'bdmDeviceType',
    'batch'
  ],
  data() {
    return {
      visibleForm: [
        'deviceType',
        'uniqueId',
        'deviceNum',
        'targetName',
        'charge',
        'gnssNum',
        'wireless',
        'latitude',
        'longitude',
        'address',
        'batch',
        'altitude',
        'valid',
        'time',
        'bearing',
        'recvTime',
        'mileage',
        'realSpeed',
        'oilMass',
        'ioStatus'
      ],
      headConfig: {
        initQuery: false,
        item: {
          1: {
            name: '赋码编号',
            type: 'input',
            value: 'deviceNum',
          },
          2: {
            name: '开始时间',
            type: 'datetime',
            value: 'startTime',
            defaultFn: '7DS'
          },
          3: {
            name: '结束时间',
            type: 'datetime',
            value: 'endTime',
            defaultFn: 'toDE'
          },
          4: {
            name: '监控对象',
            type: 'input',
            value: 'targetName'
          }
        },
        button: {
        }
      }
    };
  },
  created () {
    this.getDefaultDeviceNum();
  },
  methods: {
    getDefaultDeviceNum () {
      crudLocation.defaultDeviceNum().then(res => {
        if (res.data) {
          this.$set(this.crud.query, 'deviceNum', res.data);
          this.crud.defaultQuery.deviceNum = res.data;
          this.crud.toQuery();
        } else {
          this.crud.loading = false;
        }
      }).catch(() => {
        this.crud.loading = false;
      });
    },
    /** 刷新 - 之前 */
    [CRUD.HOOK.beforeRefresh] () {
      if (!this.crud.query.targetName && !this.crud.query.deviceNum) {
        this.$message.warning('请先输入监控对象或赋码编号进行查询');
        return false;
      }
    },
    /** 刷新 - 之后 */
    [CRUD.HOOK.afterRefresh]() {
      const hundredList = crud.data.map((item, index) => {
        return {
          latitude: Number(item.latitude),
          longitude: Number(item.longitude),
          id: index + 1
        };
      });
      if (hundredList.length > 0) {
        batchAddr(hundredList).then(res => {
          const tableData = JSON.parse(JSON.stringify(crud.data));
          for (let index = 0; index < res.data.length; index++) {
            const element = res.data[index];
            tableData[element.id - 1].address = element.locAddr;
          }
          crud.data = tableData;
        });
      }
    },
    getIoStatusStr(ioStatus) {
      if (ioStatus === 1) {
        return '深度休眠';
      } else if (ioStatus === 2) {
        return '休眠';
      } else {
        return '无';
      }
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel(dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      }
      else {
        return '';
      }
    },
  }
};
</script>

<style lang="less" scoped>
::v-deep .el-input-number .el-input__inner {
  text-align: left;
}

.app-container ::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background-color: #fcf0c1 !important
}
</style>
