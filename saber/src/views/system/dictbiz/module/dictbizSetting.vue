<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    :title="`[${dialogTitle}] 字典配置`"
    append-to-body
    width="80%"
    @close="close"
  >
    <div class="xh-container">
      <div class="head-container">
        <HeadCommon
          :head-config="headConfig"
          label-width="80px"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        />
        <u-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee'}"
          :data="crud.data"
          :treeConfig="{
            children: 'children',
            expandAll: false}"
          use-virtual
          row-id="id"
          :height="tableMaxHeight"
          row-height="54"
          :border="false"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <!--u-table大数据表格 你需要在列上指定某个列显示展开收起 treeNode属性-->
          <u-table-column
            type="selection"
            width="50"
          />
          <u-table-column
            v-permission="['admin','dictbizSetting:edit','dictbizSetting:del']"
            width="190"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetail(scope.row)"
                  >
                    详情
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    @click="handleChildClick(scope.row)"
                  >
                    新增子项
                  </el-button>
                </template>
              </udOperation>
            </template>
          </u-table-column>
          <!-- 字典编号 -->
          <u-table-column
            v-if="columns.visible('code')"
            :tree-node="true"
            :label="getLabel('code')"
            prop="code"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 字典名称 -->
          <u-table-column
            v-if="columns.visible('dictValue')"
            :label="getLabel('dictValue')"
            prop="dictValue"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 字典键值 -->
          <u-table-column
            v-if="columns.visible('dictKey')"
            :label="getLabel('dictKey')"
            prop="dictKey"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 封存 -->
          <u-table-column
            v-if="columns.visible('isSealed')"
            prop="isSealed"
            :label="getLabel('isSealed')"
            width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <el-tag>{{ scope.row.isSealed === 0 ? '否' : '是' }}</el-tag>
            </template>
          </u-table-column>
          <!-- 字典备注 -->
          <u-table-column
            v-if="columns.visible('remark')"
            :label="getLabel('remark')"
            prop="remark"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </u-table>
      </div>
    </div>
    <!--表单渲染-->
    <DictbizSettingForm
      :is-detail.sync="isDetail"
      :dict-code="dictCode"
      :parent-id="parentId"
    />
  </el-dialog>
</template>

<script>
import crudDictbizSetting from '@/api/system/dictbizNew';
import DictbizSettingForm from './dictbizSettingForm';
import HeadCommon from '@/components/formHead/headCommon.vue';
import CRUD, { presenter } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import getLabel from '@/utils/getLabel';
// crud交由presenter持有
const crud = CRUD({
  title: '',
  crudMethod: { ...crudDictbizSetting, pagination: crudDictbizSetting.getChildList },
  queryOnPresenterCreated: false
});

export default {
  name: 'DictbizSetting',
  components: {
    HeadCommon, crudOperation, udOperation, DictbizSettingForm
  },
  mixins: [presenter(crud)],
  props:{
    dialogVisible: {
      type: Boolean,
      default: false
    },
    parentId: {
      type: String,
      default: ''
    },
    dialogTitle: {
      type: String,
      default: ''
    },
    dictCode: {
      type: String,
      default: ''
    }
  },
  data(){
    return{
      permission: {
        add: ['admin', 'dictbizSetting:add'],
        edit: ['admin', 'dictbizSetting:edit'],
        del: ['admin', 'dictbizSetting:del'],
        view: ['admin', 'dictbizSetting:view'],
      },
      headConfig: {
        item: {
          1: {
            name: '字典编号',
            type: 'input',
            value: 'code',
          },
          2: {
            name: '字典名称',
            type: 'input',
            value: 'dictValue',
          }
        },
        button: {
        }
      },
      isDetail: false,
      isFirstOpen: true
    };
  },
  watch: {
    dialogVisible: {
      handler (newValue) {
        if (newValue) {
          this.handleQuery();
          if (this.isFirstOpen) {
            this.isFirstOpen = false;
            this.initTable();
          }
        }
      }
    }
  },
  methods: {
    initTable () {
      this.$nextTick(() => {
        let columns = {};
        // 兼容u-table获取表格列
        const tableColumns = this.$refs.table.columns || this.$refs.table.getTableColumn();
        tableColumns.forEach(e => {
          if (!e.property || e.type !== 'default') {
            return;
          }
          columns[e.property] = {
            label: e.label,
            visible: true
          };
        });
        this.columns = this.obColumns(columns);
        this.crud.updateProp('tableColumns', columns);
        const element = this.$refs['table'].$el;
        this.tableMaxHeight = element.offsetHeight;
      });
    },
    obColumns(columns) {
      return {
        visible(col) {
          return !columns || !columns[col] ? true : columns[col].visible;
        }
      };
    },
    handleChildClick (data) {
      crud.form.parentId = data.id;
      crud.form.parentName = data.dictValue;
      crud.toAdd();
    },
    /** 刷新 - 之前 */
    [CRUD.HOOK.beforeRefresh] () {
      // 避免重置后将parentId清空, 因此请求前赋值
      this.crud.query.parentId = this.parentId;
    },
    // 详情
    toDetail (data) {
      this.isDetail = true;
      this.crud.toEdit(data);
    },
    // 查询
    handleQuery () {
      this.crud.toQuery();
    },
    close () {
      this.$emit('update:dialogVisible', false);
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Dictbiz', value);
    }
  }
};
</script>

<style lang="less" scoped>
.xh-container{
    height: calc(80vh - 60px);
}
</style>
