<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="isDetail ? '查看' : crud.status.title"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      :hide-required-asterisk="isDetail"
      label-width="120px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 字典编号 -->
          <el-form-item
            :label="getLabel('code')"
            prop="code"
          >
            <el-input
              v-model.trim="form.code"
              disabled
              :placeholder="getPlaceholder('code')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 字典名称 -->
          <el-form-item
            :label="getLabel('dictValue')"
            prop="dictValue"
          >
            <el-input
              v-model.trim="form.dictValue"
              :disabled="isDetail"
              :placeholder="getPlaceholder('dictValue')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 上级字典 -->
          <el-form-item
            :label="getLabel('parentName')"
            prop="parentName"
          >
            <el-input
              v-model.trim="form.parentName"
              disabled
              :placeholder="getPlaceholder('parentName')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 字典键值 -->
          <el-form-item
            :label="getLabel('dictKey')"
            prop="dictKey"
          >
            <el-input
              v-model.trim="form.dictKey"
              :disabled="isDetail"
              :placeholder="getPlaceholder('dictKey')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 字典排序 -->
          <el-form-item
            :label="getLabel('sort')"
            prop="sort"
          >
            <el-input
              v-model.trim.number="form.sort"
              :disabled="isDetail"
              :placeholder="getPlaceholder('sort')"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <!-- 封存 -->
          <el-form-item
            :label="getLabel('isSealed')"
            prop="isSealed"
          >
            <el-switch
              v-model="form.isSealed"
              :disabled="isDetail"
              active-text="是"
              inactive-text="否"
              :active-value="1"
              :inactive-value="0"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <!-- 字典备注 -->
          <el-form-item
            :label="getLabel('remark')"
            prop="remark"
          >
            <el-input
              v-model.trim="form.remark"
              :disabled="isDetail"
              :placeholder="getPlaceholder('remark')"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="!isDetail"
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import { getDictTree } from '@/api/system/dictbizNew';

const defaultForm = {
  id: null,
  code: null,
  dictValue: null,
  dictKey: null,
  sort: null,
  remark: null,
  isSealed: 0,
  parentId: null,
  parentName: null
};
export default {
  components: { },
  mixins: [form(defaultForm)],
  props: {
    isDetail: {
      type: Boolean,
      default: false
    },
    parentId: {
      type: String,
      default: ''
    },
    dictCode: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      rules: {
        dictValue: { required: true, message: '请输入字典名称', trigger: 'blur' }, // 字典名称
        dictKey: { required: true, message: '请输入字典键值', trigger: 'blur' }, // 字典键值
        sort: { required: true, message: '请输入字典排序', trigger: 'blur' } // 字典排序
      }
    };
  },
  methods: {
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.$refs?.form?.clearValidate();
      this.form.code = this.dictCode;
      if (!this.form.parentName) {
        getDictTree().then(res =>{
          const obj = this.flattenArrayToObj(res.data);
          this.$set(this.form, 'parentName', obj[this.form.parentId]);
        });
      }
    },
    /** 新建 - 之前 */
    [CRUD.HOOK.beforeToAdd] () {
      if (!this.form.parentId) {
        this.form.parentId = this.parentId;
      }
    },
    flattenArrayToObj(array) {
      return array.reduce((accumulator, current) => {
        // 将当前对象的键值对添加到累加器中
        accumulator[current.id] = current.title;
        // 如果当前对象有子数组，则递归处理子数组
        if (current.children && current.children.length) {
          const childObj = this.flattenArrayToObj(current.children);
          // 合并子对象到累加器中
          Object.assign(accumulator, childObj);
        }

        return accumulator;
      }, {});
    },
    // 监听关闭事件
    closed () {
      this.$emit('update:isDetail', false);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Dictbiz', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('Dictbiz', value);
    }
  }
};
</script>

<style lang="less" scoped>

</style>
