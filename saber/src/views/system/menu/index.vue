<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :head-config="headConfig"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        />
        <!--表格渲染-->
        <u-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee'}"
          :data="crud.data"
          :treeConfig="{
            children: 'children',
            expandAll: false}"
          use-virtual
          row-id="id"
          :height="tableMaxHeight"
          row-height="54"
          :border="false"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <!--u-table大数据表格 你需要在列上指定某个列显示展开收起 treeNode属性-->
          <u-table-column
            type="selection"
            width="50"
          />
          <u-table-column
            v-permission="['admin','role:edit','role:del']"
            width="190"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetail(scope.row)"
                  >
                    详情
                  </el-button>
                  <el-button
                    v-if="scope.row.category === 1"
                    type="text"
                    size="small"
                    @click="handleAddChild(scope.row)"
                  >
                    新增子项
                  </el-button>
                </template>
              </udOperation>
            </template>
          </u-table-column>
          <!-- 菜单名称 -->
          <u-table-column
            v-if="columns.visible('name')"
            :tree-node="true"
            :label="getLabel('name')"
            prop="name"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 路由地址 -->
          <u-table-column
            v-if="columns.visible('path')"
            :label="getLabel('path')"
            prop="path"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 菜单图标 -->
          <u-table-column
            v-if="columns.visible('source')"
            :label="getLabel('source')"
            prop="source"
            width="100"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              <i :class="scope.row.source"/>
            </template>
          </u-table-column>
          <!-- 菜单编号 -->
          <u-table-column
            v-if="columns.visible('code')"
            :label="getLabel('code')"
            prop="code"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 菜单别名 -->
          <u-table-column
            v-if="columns.visible('alias')"
            :label="getLabel('alias')"
            prop="alias"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 新窗口 -->
          <u-table-column
            v-if="columns.visible('isOpen')"
            :label="getLabel('isOpen')"
            prop="isOpen"
            width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 菜单排序 -->
          <u-table-column
            v-if="columns.visible('sort')"
            :label="getLabel('sort')"
            prop="sort"
            width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </u-table>
      </div>
      <!--表单渲染-->
      <eForm
        :is-detail.sync="isDetail"
        :is-tree-disabled.sync="isTreeDisabled"
      />
    </div>
  </basic-container>
</template>

<script>
import crudMenu from '@/api/system/menuNew';
import eForm from './module/form';
import CRUD, { presenter, header } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import HeadCommon from '@/components/formHead/headCommon.vue';

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('Menu', 'uniName'), // 菜单
  crudMethod: { ...crudMenu }
});

export default {
  name: 'Menu',
  components: { eForm, crudOperation, udOperation, HeadCommon },
  mixins: [presenter(crud), header()],
  data () {
    return {
      permission: {
        add: ['admin', 'menu:add'],
        edit: ['admin', 'menu:edit'],
        del: ['admin', 'menu:del'],
        view: ['admin', 'menu:view']
      },
      headConfig: {
        item: {
          1: {
            name: '菜单名称',
            type: 'input',
            value: 'name',
          },
          2: {
            name: '菜单编号',
            type: 'input',
            value: 'code',
          },
          3: {
            name: '菜单别名',
            type: 'input',
            value: 'alias',
          }
        },
        button: {
        }
      },
      isDetail: false,
      isTreeDisabled: false
    };
  },
  methods: {
    handleAddChild (data) {
      crud.form.parentId = data.id;
      this.isTreeDisabled = true;
      crud.toAdd();
    },
    // 详情
    toDetail (data) {
      this.isDetail = true;
      this.crud.toEdit(data);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Menu', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('Menu', value);
    }
  }
};
</script>

<style lang="less" scoped>

</style>
