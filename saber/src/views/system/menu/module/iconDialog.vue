<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    title="请选择 菜单图标"
    append-to-body
    width="80%"
    @closed="closed"
  >
    <el-tabs
      v-model="activeName"
    >
      <el-tab-pane
        :label="iconList[0].label"
        name="first"
      >
        <div class="avue-input-icon__list">
          <div
            v-for="(item, index) in iconList[0].list"
            :key="index"
            class="avue-input-icon__item"
            @click="handleIconClick(item)"
          >
            <span class="avue-icon">
              <i
                :class="item"
                class="iconfont"
              />
            </span>
            <p>{{ item }}</p>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane
        :label="iconList[1].label"
        name="second"
      >
        <div class="avue-input-icon__list">
          <div
            v-for="(item, index) in iconList[1].list"
            :key="index"
            class="avue-input-icon__item"
            @click="handleIconClick(item)"
          >
            <span class="avue-icon">
              <i
                :class="item"
                class="iconfont"
              />
            </span>
            <p>{{ item }}</p>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script>
import iconList from "@/config/iconList";
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      activeName: 'first',
      iconList: iconList
    };
  },
  methods: {
    handleIconClick (data) {
      this.$emit('handleIconClick', data);
    },
    closed () {
      this.$emit('update:dialogVisible', false);
    },
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-tabs__content {
  overflow: visible;
}
</style>
