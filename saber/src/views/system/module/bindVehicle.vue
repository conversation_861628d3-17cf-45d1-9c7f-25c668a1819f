<template>
  <el-dialog
    title="账号实际使用车辆关系"
    class="vehicle-relation"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    append-to-body
    width="90%"
    @close="closeDialog"
  >
    <!-- 表头用户信息 -->
    <el-form
      :model="bindUser"
      :inline="true"
    >
      <div class="current-user">
        当前账号信息
      </div>
      <el-row>
        <el-col :span="8">
          <el-form-item label="用户名">
            <el-input
              v-model="bindUser.account"
              placeholder="用户名"
              disabled
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="用户车组">
            <el-input
              v-model="bindUser.dept.name"
              placeholder="用户车组"
              disabled
            />
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-form>
    <!-- 穿梭框头部搜索 -->
    <div class="vehicle-transfer-header">
      <div class="transfer-header">
        <div class="transfer-header-title">
          未绑定车辆列表
        </div>
        <el-form
          label-width="40px"
        >
          <el-row>
            <el-col :span="11">
              <el-form-item label="搜索">
                <el-input
                  v-model="search.unbound"
                  placeholder="请输入车牌号搜索"
                  prefix-icon="el-icon-search"
                  clearable
                  @input="(val)=>{
                    searchHandle('left', 1, {licencePlate: val, deptId: search.deptLeft}, true);
                  }"
                />
              </el-form-item>
            </el-col>
            <el-col :span="2">
              &nbsp;
            </el-col>
            <el-col :span="11">
              <el-form-item
                label="搜索"
                prop="deptLeft"
              >
                <treeselect
                  v-model="search.deptLeft"
                  :options="depts"
                  :normalizer="normalizerDeptId"
                  no-results-text="暂无数据"
                  clear-value-text="清空"
                  placeholder="请输入车组搜索"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="transfer-header-middle" />
      <div class="transfer-header">
        <div class="transfer-header-title">
          已绑定车辆列表
        </div>
        <!-- <el-form
          label-width="40px"
        >
          <el-row>
            <el-col :span="11">
              <el-form-item label="搜索">
                <el-input
                  v-model="search.bind"
                  placeholder="请输入车牌号搜索"
                  prefix-icon="el-icon-search"
                  clearable
                  @input="(val)=>{
                    searchHandle('right', 1, {licencePlate: val, deptId: search.deptRight}, true);
                  }"
                />
              </el-form-item>
            </el-col>
            <el-col :span="2">
              &nbsp;
            </el-col>
            <el-col :span="11">
              <el-form-item
                label="搜索"
                prop="deptRight"
              >
                <treeselect
                  v-model="search.deptRight"
                  :options="depts"
                  no-results-text="暂无数据"
                  clear-value-text="清空"
                  :normalizer="normalizerDeptId"
                  placeholder="请输入车组搜索"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form> -->
        <el-form
          label-width="40px"
        >
          <el-row>
            <el-col :span="11">
              <el-form-item label="搜索">
                <el-input
                  v-model="search.bind"
                  placeholder="请输入车牌号搜索"
                  prefix-icon="el-icon-search"
                  clearable
                  @input="searchFront"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <!-- 双table穿梭框 -->
    <div class="vehicle-transfer">
      <el-table
        :data="table.left"
        style="width: 100%"
        size="mini"
        height="80%"
        :header-cell-style="{background:'#EBF5FF',color:'#606266'}"
        @selection-change="(val)=>{
          currentTable.left = val;
        }"
      >
        <el-table-column
          type="selection"
          label-class-name="selection-label"
          width="50px"
          :resizable="false"
        />
        <el-table-column
          prop="licencePlate"
          label="车牌号码"
          :resizable="false"
        />
        <el-table-column
          prop="deptName"
          label="车组"
          :resizable="false"
        />
        <el-table-column
          prop="terminalId"
          label="序列号"
          :resizable="false"
        />
      </el-table>

      <div class="btns">
        <el-button
          type="primary"
          :disabled="currentTable.left.length == 0"
          @click="toWhere('right')"
        >
          添加
        </el-button>
        <el-button
          type="primary"
          :disabled="currentTable.right.length == 0"
          @click="toWhere('left')"
        >
          移除
        </el-button>
      </div>
      <el-table
        :data="table.right"
        style="width:100%"
        size="mini"
        height="80%"
        :header-cell-style="{background:'#EBF5FF',color:'#606266'}"
        @selection-change="(val)=>{
          currentTable.right = val;
        }"
      >
        <el-table-column
          type="selection"
          label-class-name="selection-label"
          width="50"
        />
        <el-table-column
          prop="licencePlate"
          label="车牌号码"
        />
        <el-table-column
          prop="deptName"
          label="车组"
        />
        <el-table-column
          prop="terminalId"
          label="序列号"
        />
      </el-table>
    </div>
    <!-- 穿梭框底部分页 -->
    <div class="vehicle-transfer-footer">
      <div class="transfer-footer">
        <el-pagination
          layout="total,prev, pager, next"
          :total="page.leftTotal"
          :current-page.sync="page.leftCurrent"
          @current-change="(val)=>{
            searchHandle('left', val, {licencePlate: search.unbound, deptId: search.deptLeft});
          }"
        />
      </div>
      <div class="transfer-footer-middle" />
      <div class="transfer-footer">
        <!-- <el-pagination
          layout="total,prev, pager, next"
          :total="page.rightTotal"
          :current-page.sync="page.rightCurrent"
          @current-change="(val)=>{
            searchHandle('right', val, {licencePlate: search.bind, deptId: search.deptRight});
          }"
        /> -->
      </div>
    </div>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="closeDialog"
      >
        取消
      </el-button>
      <el-button
        type="primary"
        size="small"
        @click="handleConfirm"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { Transfer } from 'element-ui';
import {userBindVehicleDetail, userBindVehicleEdit, userUnbindVehicleDetail} from '@/api/system/user.js';
// import {pagination} from '@/api/base/vehicle.js';
import { getEnabledDepts } from '@/api/base/dept';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
export default {
  name: 'BindVehicle',
  components: {
    [Transfer.name]: Transfer,
    Treeselect
  },
  props: {
    // 外部控制显隐
    bindDialogVisible: {
      type: Boolean
    },
    // props当前用户信息
    bindUser: {
      type: Object,
      required: true,
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
      dialogVisible: true,
      // table内容(展示data池)
      table: {
        left: [],
        right: []
      },
      // 选中table内容
      currentTable: {
        left: [],
        right: []
      },
      // 搜索
      search: {
        unbound: '',
        bind: '',
        deptLeft: null,
        deptRight: null
      },
      // 分页
      page: {
        leftTotal: 0,
        rightTotal: 0,
        leftCurrent: 1,
        rightCurrent: 1
      },
      // treeSelect自定义键名
      normalizerDeptId (node) {
        return {
          id: node.id,
          label: node.title,
          children: (node.children && node.children.length > 0) ? node.children : undefined
        };
      },
      depts: [],
      // 拷贝的提交数据
      submitData: [],
      rightDataSearch: [] // 为搜索data池
    };
  },
  watch: {
    bindDialogVisible (newValue, oldValue) {
      this.dialogVisible = newValue;
    },
    'search.deptLeft' (newValue, oldValue) {
      this.searchHandle('left', 1, {deptId: newValue, licencePlate: this.search.unbound}, true);
    },
    'search.deptRight' (newValue, oldValue) {
      this.searchHandle('right', 1, {deptId: newValue, licencePlate: this.search.bind}, true);
    },
    'table.left' (newValue, oldValue) {
      // if (newValue.length === 0) {
      //   this.page.leftCurrent += 1;
      //   this.searchHandle('left', (this.page.leftCurrent - 1) * 10, {licencePlate: this.search.unbound, deptId: this.search.deptLeft});
      // }
    }
  },
  created () {
    this.searchHandle('left');
    this.searchHandle('right', 1, null, null, (data) => {
      [...this.submitData] = data;
      [...this.rightDataSearch] = data;
    });
    getEnabledDepts().then(res => {
      this.depts = res.data;
    });
  },
  methods: {
    // 关闭dialog
    closeDialog () {
      this.$emit('closeBind');
    },

    // 绑定项增减
    toWhere (to) {
      let from = to === 'right' ? 'left' : 'right';
      from === 'left' ? this.addCar(this.currentTable[from], this.submitData) : this.cutCar(this.currentTable[from], this.submitData);
      this.toWhereSearch(from);
      this.addCar(this.currentTable[from], this.table[to]);
      this.cutCar(this.table[to], this.table[from]);
      this.currentTable[from] = [];
      this.$nextTick(() => {
        if (this.table['left'].length === 0) {
          this.page.leftCurrent += 1;
          this.searchHandle('left', this.page.leftCurrent, {licencePlate: this.search.unbound, deptId: this.search.deptLeft});
        }
      });
    },
    // 搜索池也增减
    toWhereSearch (from) {
      from === 'left' ? this.addCar(this.currentTable[from], this.rightDataSearch) : this.cutCar(this.currentTable[from], this.rightDataSearch);
    },

    // 分页查询[绑定right]or[未绑定left]车辆
    searchHandle (way, start = 1, val = {}, back, callback) {
      let fn = way === 'right' ? userBindVehicleDetail : userUnbindVehicleDetail;
      let params = {
        current: start,
        size: 10,
        userId: this.bindUser.id,
        licencePlate: ''
      };
      fn({...params, ...val}).then((res) => {
        this.page[way + 'Total'] = res.data.total;
        this.table[way] = res.data.resData || [];
        back && (this.page[way + 'Current'] = 1);
        callback && callback(res.data.resData || []);
      });
    },

    // 提交
    handleConfirm () {
      let params = {
        userId: this.bindUser.id,
        vehicleIds: []
      };
      params.vehicleIds = this.submitData.map(item => {
        return item.id;
      });
      userBindVehicleEdit(params).then((res) => {
        // if (res.code === 0) {
        //   this.$notify({
        //     title: res.msg,
        //     type: 'success',
        //     duration: 2500
        //   });
        //   this.closeDialog();
        // } else {
        //   this.$notify({
        //     title: res.msg,
        //     type: 'error'
        //   });
        // }
        this.$notify({
          title: res.msg,
          type: 'success',
          duration: 2500
        });
        this.closeDialog();
      });
    },
    // 增项
    addCar (add, total) {
      add.length > 0 && add.forEach(item => {
        let flag = true;
        total.length > 0 && total.forEach(v => {
          if (v.id === item.id) {
            flag = false;
          }
        });
        flag && total.push(item);
      });
    },
    // 减项
    cutCar (cut, total) {
      cut.length > 0 && cut.forEach(item => {
        total.length > 0 && total.forEach((v, index) => {
          if (item.id === v.id) {
            total.splice(index, 1);
          }
        });
      });
    },
    select (val) {
      this.currentTable.left = val;
      console.log(this.table.left);
      console.log(this.page.leftCurrent);
    },
    // 总data池搜索已绑
    searchFront () {
      let arr = [];
      this.rightDataSearch.forEach(item => {
        if (item.licencePlate.indexOf(this.search.bind) !== -1) {
          arr.push(item);
        }
      });
      this.table.right = arr;
    }
  }
};
</script>

<style lang="less" scoped>
.vehicle-relation{
  ::v-deep .el-dialog{
    .el-dialog__header{
      padding: 10px;
      .el-dialog__title{
        display: block;
        line-height: 32px;
        font-size: 20px;
        padding-left: 6px;
        border-left: 2px solid #BEE1FF;
      }
    }
  }
  ::v-deep .el-dialog__body{
    padding: 0 20px;
  }
  .current-user{
    font-size: 16px;
    padding: 10px 0;
  }
  ::v-deep .el-input.is-disabled .el-input__inner{
    background-color: #fff;
    color: #606266;
    cursor: default;
  }
  .vehicle-transfer{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .el-table{
      padding:0 10px;
      border-right: 1px solid #E4E7ED;
      border-left: 1px solid #E4E7ED;
      min-height: 450px;
      ::v-deep .el-table__cell{
        border: none;
      }
    }
    .btns{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 120px;
    .el-button{
        margin:10px 0;
      }
    }
    // ::v-deep.selection-label .cell .el-checkbox__inner{
    //   margin-left: -5px;
    //   position:relative;
    // }
    // ::v-deep.selection-label .cell:before{
    //   content:"全选";
    //   position:absolute;
    //   left:22px ;
    // }
  }
  .vehicle-transfer-header{
    display: flex;
    .transfer-header{
      padding: 0 10px;
      flex: 1;
      border: 1px solid #E4E7ED;
      border-bottom:none ;
      border-radius: 4px 4px 0 0;
      .transfer-header-title{
        padding: 10px 0;
      }
      ::v-deep .el-form-item{
        margin-bottom: 5px;
      }
    }
    .transfer-header-middle{
      width: 120px;
    }
  }
  .vehicle-transfer-footer{
    display: flex;
    .transfer-footer{
      padding: 0 10px;
      flex: 1;
      border: 1px solid #E4E7ED;
      border-top:none ;
      border-radius: 0 0 4px 4px;
      .transfer-footer-title{
        padding: 10px;
      }
    }
    .transfer-footer-middle{
      width: 120px;
    }
  }
}

</style>
