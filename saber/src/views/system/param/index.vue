<template>
  <basic-container>
    <div class="xh-container">
      <!--工具栏-->
      <div class="head-container">
        <HeadCommon
          :head-config="headConfig"
        />
      </div>
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="permission"
          :download="false"
        />
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
          :data="crud.data"
          :cell-style="{'text-align':'center'}"
          :max-height="tableMaxHeight"
          style="width: 100%;height: calc(100% - 47px);"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            type="selection"
            width="50"
          />
          <el-table-column
            v-permission="['admin','param:edit','param:del']"
            width="190"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="scope">
              <udOperation
                :data="scope.row"
                :permission="permission"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetail(scope.row)"
                  >
                    详情
                  </el-button>
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <!-- 参数名称 -->
          <el-table-column
            v-if="columns.visible('paramName')"
            :label="getLabel('paramName')"
            prop="paramName"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 参数键名 -->
          <el-table-column
            v-if="columns.visible('paramKey')"
            :label="getLabel('paramKey')"
            prop="paramKey"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <!-- 参数键值 -->
          <el-table-column
            v-if="columns.visible('paramValue')"
            :label="getLabel('paramValue')"
            prop="paramValue"
            min-width="100"
            show-overflow-tooltip
            :resizable="false"
          />
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
      <!--表单渲染-->
      <eForm
        :is-detail.sync="isDetail"
      />
    </div>
  </basic-container>
</template>

<script>
import crudParam from '@/api/system/param';
import eForm from './module/form';
import CRUD, { presenter, header } from '@/components/Crud/crud';
import crudOperation from '@/components/Crud/CRUD.operation';
import udOperation from '@/components/Crud/UD.operation';
import pagination from '@/components/Crud/Pagination';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';
import HeadCommon from '@/components/formHead/headCommon.vue';

// crud交由presenter持有
const crud = CRUD({
  title: getLabel('Param', 'uniName'), // 参数
  crudMethod: { ...crudParam }
});

export default {
  name: 'Param',
  components: { eForm, crudOperation, udOperation, pagination, HeadCommon },
  mixins: [presenter(crud), header()],
  data () {
    return {
      permission: {
        add: ['admin', 'param:add'],
        edit: ['admin', 'param:edit'],
        del: ['admin', 'param:del'],
        view: ['admin', 'param:view']
      },
      headConfig: {
        item: {
          1: {
            name: '参数名称',
            type: 'input',
            value: 'paramName',
          },
          2: {
            name: '参数键名',
            type: 'input',
            value: 'paramKey',
          }
        },
        button: {
        }
      },
      isDetail: false
    };
  },
  methods: {
    // 详情
    toDetail (data) {
      this.isDetail = true;
      this.crud.toEdit(data);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Param', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('Param', value);
    }
  }
};
</script>

<style lang="less" scoped>

</style>
