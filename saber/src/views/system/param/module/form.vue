<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="isDetail ? '查看参数' : crud.status.title"
    append-to-body
    width="60%"
    @closed="closed"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      :hide-required-asterisk="isDetail"
      label-width="120px"
    >
      <el-row
        type="flex"
        span="24"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 参数名称 -->
          <el-form-item
            :label="getLabel('paramName')"
            prop="paramName"
          >
            <el-input
              v-model.trim="form.paramName"
              :disabled="isDetail"
              :placeholder="getPlaceholder('paramName')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <!-- 参数键名 -->
          <el-form-item
            :label="getLabel('paramKey')"
            prop="paramKey"
          >
            <el-input
              v-model.trim="form.paramKey"
              :disabled="isDetail"
              :placeholder="getPlaceholder('paramKey')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-24 el-col-offset-0 el-col-xs-24 el-col-sm-24 el-col-md-24">
          <!-- 参数键值 -->
          <el-form-item
            :label="getLabel('paramValue')"
            prop="paramValue"
          >
            <el-input
              v-model.trim="form.paramValue"
              :disabled="isDetail"
              type="textarea"
              :rows="5"
              :placeholder="getPlaceholder('paramValue')"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      v-if="!isDetail"
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="crud.submitCU"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from '@/components/Crud/crud';
import getLabel from '@/utils/getLabel';
import getPlaceholder from '@/utils/getPlaceholder';

const defaultForm = {
  id: null,
  paramName: null,
  paramKey: null,
  paramValue: null
};
export default {
  components: { },
  mixins: [form(defaultForm)],
  props: {
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      rules: {
        paramName: { required: true, message: '请输入参数名称', trigger: 'blur' }, // 参数名称
        paramKey: { required: true, message: '请输入参数键名', trigger: 'blur' }, // 参数键名
        paramValue: { required: true, message: '请输入参数键值', trigger: 'blur' }, // 参数键值
      }
    };
  },
  methods: {
    /** 开始 "新建/编辑" - 之前 */
    [CRUD.HOOK.beforeToCU] () {
      this.$refs?.form?.clearValidate();
    },
    // 监听关闭事件
    closed () {
      this.$emit('update:isDetail', false);
    },
    /**
     * 获取字段对应的标签
     * @param value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('Param', value);
    },
    /**
     * 获取字段对应的占位符
     * @param {String} value
     * @return {String}
     */
    getPlaceholder (value) {
      return getPlaceholder('Param', value);
    }
  }
};
</script>

<style lang="less" scoped>

</style>
