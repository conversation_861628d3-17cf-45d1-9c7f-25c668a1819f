<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="btnShow ? crud.status.title : '查看'"
    append-to-body
    width="60%"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      class="rewriting-form-disable"
      :hide-required-asterisk="!btnShow"
      label-width="155px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="账号"
            prop="account"
          >
            <el-input
              v-model.trim="form.account"
              placeholder="请输入账号"
              maxlength="20"
              style="width: 100%"
              :disabled="!crud.status.title.includes('新增')"
            />
          </el-form-item>
        </div>
        <div
          v-if="crud.status.title.includes('新增')"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="密码"
            prop="password"
          >
            <el-input
              v-model.trim="form.password"
              placeholder="请输入密码"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="联系人"
            prop="contactName"
          >
            <el-input
              v-model.trim="form.contactName"
              placeholder="请输入联系人"
              maxlength="20"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="联系邮箱"
            prop="contactEmail"
          >
            <el-input
              v-model.trim="form.contactEmail"
              placeholder="请输入联系邮箱"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="联系电话"
            prop="contactPhone"
          >
            <el-input
              v-model.trim="form.contactPhone"
              placeholder="请输入联系电话"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="账号状态"
            prop="status"
          >
            <el-select
              v-model="form.status"
              filterable
              placeholder="请选择账号状态"
              :disabled="!btnShow"
            >
              <el-option
                v-for="item in dict.accountStatus"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="账号用户描述"
            prop="userDesc"
          >
            <el-input
              v-model.trim="form.userDesc"
              placeholder="请输入账号用户描述"
              maxlength="200"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="所属机构"
            prop="deptId"
          >
            <DeptFormMultiSelect
              ref="deptIdsRef"
              v-model="deptIds"
              :disabled="!btnShow"
              :is-show="crud.status.cu > 0"
              :deptOptionsP="deptOptions"
              placeholder="请选择所属机构"
              size="small"
              checkStrictly
              @input="validateTreeSelect('deptIds')"
            />
          </el-form-item>
        </div>
        <div
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="所属平台"
            prop="systemId"
          >
            <el-select
              v-model="form.systemId"
              filterable
              placeholder="请选择所属平台"
              :disabled="!btnShow"
            >
              <el-option
                v-for="item in sysList"
                :key="item.id"
                :label="item.systemName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        v-if="btnShow"
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="btnShow"
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="handleSubmit"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from "@/components/Crud/crud";
import { passwordRuleUpdate } from "@/utils/validate";
import { email, phoneNum } from "@/utils/validate";
import DeptFormMultiSelect from '@/components/select/DeptFormMultiSelect/DeptFormMultiSelect.vue';

const defaultForm = {
  account: "",
  password: "",
  contactName: "",
  contactEmail: "",
  contactPhone: "",
  status: "",
  userDesc: "",
  deptId: "",
  deptName: "",
  systemId: "",
  id: null,
};
export default {
  components: {
    DeptFormMultiSelect,
  },
  mixins: [form(defaultForm)],
  props: {
    btnShow: {
      type: Boolean,
      default: true,
    },
    sysList: {
      type: Array,
      required: true,
    },
    dict: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      rules: {
        account: { required: true, trigger: "blur" },
        password: { required: true, trigger: "blur" },
        contactName: { required: true, trigger: "blur" },
        contactEmail: { required: true, trigger: "blur" },
        contactPhone: { required: true, trigger: "blur" },
        status: { required: true, trigger: "blur" },
        userDesc: { required: true, trigger: "blur" },
        deptId: { required: true, trigger: "blur" },
        systemId: { required: true, trigger: "blur" },
      },
      depts: [],
      deptIds: [],
      loading: false,
      deptOptions: [],
    };
  },
  methods: {
    handleSubmit() {
      this.form.deptId = this.deptIds.join(',');
      this.crud.submitCU();
    },
    /**
     * 提交前的验证
     */
    [CRUD.HOOK.afterValidateCU]() {
      if (!this.form.id && !passwordRuleUpdate.test(this.form.password)) {
        this.$message.error("密码必须8到20位, 且包含数字、大小写字母、特殊符号!");
        return false;
      } else if (!email.test(this.form.contactEmail)) {
        this.$message.error("邮箱不正确!");
        return false;
      } else if (!phoneNum.test(this.form.contactPhone)) {
        this.$message.error("联系电话不正确!");
        return false;
      }
      return true;
    },
    /** 点击取消之前*/
    [CRUD.HOOK.afterEditCancel]() {
      this.$emit("cancelCU");
    },
    // 新增/编辑前
    [CRUD.HOOK.beforeToCU]() {
      if(this.form.deptId) {
        this.deptIds = this.form.deptId?.split(',')
      }
      this.$nextTick(() => {
        if(!this.btnShow && this.form.deptList?.length) {
          this.deptOptions = this.form.deptList.map( item => ({title: item.deptName, id: item.deptId}))
        }
      })
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
          this.$refs[`deptIdsRef`].$refs[`deptIdsStrRef`].$children[0].$el.style.borderColor = '#BFBFBF';
        }
      });
    },
    /**
     * 验证treeSelect项
     * @param {String} item
     */
    validateTreeSelect (item) {
      this.$refs.form.validateField(item, (valid) => {
        this.$refs[`${item}Ref`].$refs[`${item}StrRef`].$children[0].$el.style.borderColor = valid !== '' ? '#F56C6C' : '#BFBFBF';
      });
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .el-input__inner {
  text-align: left;
}

/deep/ .el-form-item__label {
  font-weight: 400;
}

/deep/ .el-icon-delete {
  font-size: 30px;
}
</style>
