<template>
  <basic-container>
    <div class="xh-container">
      <div class="head-container">
        <HeadCommon
          :dict="dict"
          :head-config="headConfig"
          label-width="80px"
        />
      </div>
      <!--工具栏-->
      <div class="xh-crud-table-container">
        <crudOperation
          :permission="batchPer"
          :download="false"
        >
          <template slot="right">
            <el-button
              v-if="batchPer.resetPassw"
              size="small"
              icon="el-icon-refresh"
              @click="handleReset"
            >修改密码
            </el-button>
            <el-button
              v-if="batchPer.lock"
              size="small"
              icon="el-icon-coordinate"
              @click="handleLock(true)"
            >账号禁用
            </el-button>
            <el-button
              v-if="batchPer.unlock"
              size="small"
              icon="el-icon-coordinate"
              @click="handleLock(false)"
            >账号解封
            </el-button>
            <el-button
              v-if="batchPer.resetPassw"
              size="small"
              icon="el-icon-refresh"
              @click.native="resetPwd"
            >位置平台重置密钥
            </el-button>
          </template>
        </crudOperation>
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          :data="crud.data"
          :cell-style="{ 'text-align': 'center' }"
          :max-height="tableMaxHeight"
          style="width: 100%; height: calc(100% - 47px)"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column
            :resizable="false"
            type="selection"
            width="50"
          />
          <el-table-column
            v-permission="[
              'admin',
              'platformAccount:edit',
              'platformAccount:del',
            ]"
            width="260"
            label="操作"
            align="center"
            fixed="right"
            :resizable="false"
          >
            <template slot-scope="{ row }">
              <udOperation
                :data="row"
                :permission="batchPer"
              >
                <template slot="right">
                  <el-button
                    type="text"
                    size="small"
                    class="table-button-view"
                    @click="toDetails(row)"
                  >
                    详情
                  </el-button>
                  <el-button
                    v-if="batchPer.interface"
                    size="mini"
                    type="text"
                    @click.native="interfaceAuth(row)"
                  >接口权限
                  </el-button>
                  <el-button
                    v-if="batchPer.bind"
                    size="mini"
                    type="text"
                    @click.native="bindTerm(row)"
                  >绑定组织
                  </el-button>
                  <!-- <el-button
                    v-if="batchPer.warnRules"
                    size="mini"
                    type="text"
                    @click.native="bindRules(row)"
                  >绑定报警规则
                  </el-button> -->
                </template>
              </udOperation>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('account')"
            label="账号"
            :show-overflow-tooltip="true"
            min-width="80"
            prop="account"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('contactName')"
            label="联系人"
            :show-overflow-tooltip="true"
            prop="contactName"
            min-width="120"
            :resizable="false"
          />
          <el-table-column
            v-if="columns.visible('status')"
            label="状态"
            :show-overflow-tooltip="true"
            prop="status"
            min-width="120"
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ getEnumDictLabel('accountStatus', scope.row.status) }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('deptId')"
            label="所属机构"
            prop="deptId"
            min-width="180"
            show-overflow-tooltip
            :resizable="false"
          >
            <template slot-scope="scope">
              {{ scope.row.deptList?.map( item => item.deptName).join(',') }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns.visible('systemId')"
            label="所属平台"
            :show-overflow-tooltip="true"
            min-width="150"
            prop="systemId"
            :resizable="false"
          >
            <template slot-scope="scope">
              <div>
                {{ scope.row.systemInfo?.systemName }}
              </div>
            </template>
          </el-table-column>
          <el-empty
            slot="empty"
            :image="require('@/assets/images/nodata.png')"
          />
        </el-table>
      </div>
      <!--分页组件-->
      <pagination />
    </div>
    <el-dialog
      v-dialog-drag
      title="绑定组织"
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="termVisible"
      width="650px"
      @close="closeDept"
    >
      <div>
        <el-input
          v-model="filterDept"
          placeholder="请输入绑定组织名称"
          size="small"
          style="margin-bottom: 4px"
          @input="(val) => this.$refs.treeTerm.filter(val)"
        />
        <vue-easy-tree
          ref="treeTerm"
          height="calc(60vh - 20px)"
          show-checkbox
          :checkStrictly="true"
          :defaultExpandedKeys="[]"
          :data="orgTreeData"
          node-key="id"
          :props="defaultProps"
          :filter-node-method="filterTermNode"
        />
      </div>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="cancelTerm"
        >取 消</el-button>
        <el-button
          type="primary"
          size="small"
          @click="submitTerm"
        >确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      v-dialog-drag
      title="绑定告警规则"
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="warnVisible"
      width="650px"
      @close="closeRule"
    >
      <div>
        <el-input
          v-model="filterText"
          placeholder="请输入规则搜索"
          size="small"
          style="margin-bottom: 4px"
          @input="(val) => this.$refs.ruleTree.filter(val)"
        />
        <vue-easy-tree
          ref="ruleTree"
          :data="ruleData"
          show-checkbox
          :props="ruleProps"
          node-key="id"
          :filter-node-method="filterRuleNode"
        />
      </div>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="cancelWarn"
        >取 消</el-button>
        <el-button
          type="primary"
          size="small"
          @click="submitWarn"
        >确 定</el-button>
      </span>
    </el-dialog>
    <!--表单渲染-->
    <eForm
      :btnShow="btnShow"
      :sysList="sysList"
      :dict="dict"
      @cancelCU="cancel"
    />
    <interfacePage ref="interfaceRef" :rowInfo="curRows" />
  </basic-container>
</template>

<script>
import crudRealTimeMonitoring, {
  getUser,
  resetPassword,
  freeze,
  unfreeze,
  resetPwd,
  bindDept,
  bindAlarmRule,
} from "@/api/access/platformAccount";
import CRUD, { presenter } from "@/components/Crud/crud";
import crudOperation from "@/components/Crud/CRUD.operation";
import pagination from "@/components/Crud/Pagination";
import HeadCommon from "@/components/formHead/headCommon.vue";
import { listSystem } from "@/api/log/api.js";
import { getDeptPerInit } from '@/api/base/dept';
import { getRuleAllotTree } from "@/api/rule";
import BindVehicle from "../module/bindVehicle.vue";
import { passwordRuleUpdate } from "@/utils/validate";
import VueEasyTree from "@wchbrad/vue-easy-tree";
import { email, phoneNum } from "@/utils/validate";
import udOperation from "@/components/Crud/UD.operation";
import eForm from "./form";
import interfacePage from './interface'

// crud交由presenter持有
const crud = CRUD({
  title: "", // 车辆
  crudMethod: { ...crudRealTimeMonitoring }
});

export default {
  name: "PlatformAccount",
  components: {
    crudOperation,
    pagination,
    HeadCommon,
    BindVehicle,
    VueEasyTree,
    udOperation,
    eForm,
    interfacePage,
  },
  mixins: [presenter(crud)],
  // 数据字典
  dicts: ['accountStatus'],
  data() {
    return {
      form: {},
      search: {},
      roleBox: false,
      initFlag: true,
      query: {},
      loading: true,
      platformLoading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      platformPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      init: {
        roleTree: [],
        deptTree: [],
      },
      props: {
        label: "title",
        value: "key",
      },
      headConfig: {
        initQuery: false,
        item: {
          1: {
            name: "账号",
            type: "input",
            value: "account",
          },
        },
        button: {},
      },
      batchPer: {
        add: ["admin", "platformAccount:add"],
        edit: ["admin", "platformAccount:edit"],
        del: ["admin", "platformAccount:del"],
        bind: ["admin", "platformAccount:bind"],
        interface: ["admin", "platformAccount:interface"],
        resetPassw: ["admin", "platformAccount:resetPassw"],
        lock: ["admin", "platformAccount:lock"],
        unlock: ["admin", "platformAccount:unlock"],
        warnRules: ["admin", "platformAccount:warnRules"],
      },
      btnShow: true, // 显示确认取消按钮
      data: [],
      excelForm: {},
      bindDialogVisible: false,
      bindUser: undefined,
      selectTransfer: [],
      filterMethod(query, item) {
        return item.name.indexOf(query) > -1;
      },
      termVisible: false,
      rulesVisible: true,
      warnVisible: false,
      curRows: {},
      optionTerm: {
        virtualize: true,
        defaultExpandAll: true,
        multiple: true,
        addBtn: false,
        checkStrictly: true,
        props: {
          label: "title",
        },
      },
      orgTreeData: [],
      ruleProps: {
        label: "name",
        // children: "rules",
      },
      ruleData: [],
      filterText: "",
      filterDept: "",
      sysList: [],
      defaultProps: {
        children: 'children',
        label: 'title',
        value: 'id'
      },
    };
  },
  computed: {},
  activated() {
    this.getInitDict();
    this.initData();
  },
  methods: {
    closeDept() {
      this.filterDept = "";
      this.$refs.treeTerm.filter(this.filterDept);
    },
    closeRule() {
      this.filterText = "";
      this.$refs.ruleTree.filter(this.filterText);
    },
    toDetails(param) {
      crud.toEdit(param);
      this.btnShow = false;
    },
    cancel() {
      this.btnShow = true;
    },
    getInitDict() {
      listSystem().then((res) => {
        const list = res.data.data || [];
        const data = list.reduce((map, item) => {
          map[item.id] = item.systemName;
          return map;
        }, {});
        this.sysList = list;
      });
    },
    filterTermNode(value, data) {
      if (!value) return true;
      if (data.title) {
        return data.title.indexOf(value) !== -1;
      } else if(data.label) {
        return data.label.indexOf(value) !== -1;
      }
    },
    filterRuleNode(value, data) {
      if (!value) return true;
      if (data.name) {
        return data.name.indexOf(value) !== -1;
      } else if (data.ruleName) {
        return data.ruleName.indexOf(value) !== -1;
      }
    },
    interfaceAuth(row) {
      this.curRows = row;
      this.$refs.interfaceRef.visible = true;
    },
    bindTerm(row) {
      // 获取已选择数据
      this.curRows = row;
      this.termVisible = true;
      this.$nextTick(() => {
        const arr = row.deptId ? row.deptId.split(",") : [];
        this.$refs.treeTerm.setCheckedKeys(arr);
      });
    },
    submitTerm() {
      const userIds = this.curRows.id;
      const arr = this.$refs.treeTerm.getCheckedKeys();
      if (!arr.length) {
        return this.$message.warning("请选择至少一条数据");
      }
      bindDept(userIds, arr.join(",")).then((res) => {
        this.$message.success("操作成功！");
        this.crud.refresh();
        this.cancelTerm();
      });
    },
    cancelTerm() {
      this.curRows = {};
      this.termVisible = false;
      this.selectTransfer = [];
      this.filterDept = "";
    },
    bindRules(row) {
      this.curRows = row;
      // 获取已选择数据
      this.warnVisible = true;
      this.$nextTick(() => {
        const arr = row.alarmRuleId ? row.alarmRuleId.split(",") : [];
        this.$refs.ruleTree.setCheckedKeys(arr);
      });
    },
    resetPwd() {
      this.$confirm(`是否确认重置密钥，请谨慎操作`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        resetPwd().then(res => {
          this.$message.success("操作成功！");
        });
      }).catch(() => {
      });
    },
    submitWarn() {
      const userIds = this.curRows.id;
      const ruleIds = this.$refs.ruleTree.getCheckedKeys() || [];
      // if (!ruleIds.length) {
      //   return this.$message.warning("请选择至少一条数据");
      // }
      bindAlarmRule(userIds, ruleIds.join(",")).then((res) => {
        this.$message.success("操作成功！");
        this.crud.toQuery();
        this.cancelWarn();
      });
    },
    cancelWarn() {
      this.curRows = {};
      this.warnVisible = false;
      this.filterText = "";
    },

    initData(tenantId) {
      getDeptPerInit({tenantId}).then(({data}) => {
        this.orgTreeData = data;
      });
      // getRuleAllotTree({ parentId: 0 }).then((res) => {
      //   this.ruleData = res.data.content;
      //   this.processing(this.ruleData);
      // });
    },
    processing(list) {
      list &&
        list.forEach((element) => {
          if (element.rules) {
            element.children = element.rules;
          } else {
            this.processing(element.children);
          }
        });
    },
    handlevalidate() {
      if (!this.form.id && !passwordRuleUpdate.test(this.form.password)) {
        this.$message.error("密码必须8到20位, 且包含数字、大小写字母、特殊符号!");
        return true;
      } else if (!email.test(this.form.contactEmail)) {
        this.$message.error("邮箱不正确!");
        return true;
      } else if (!phoneNum.test(this.form.contactPhone)) {
        this.$message.error("联系电话不正确!");
        return true;
      }
    },
    rowSave(row, done, loading) {
      if (this.handlevalidate()) {
        loading();
        return;
      }
      row.deptId = row.deptId.join(",");
      // row.roleId = row.roleId.join(",");
      // row.postId = row.postId.join(",");
      // row.monitDeptId = row.monitDeptId.join(",");
      add(row).then(
        () => {
          this.initFlag = false;
          this.crud.refresh();
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      if (this.handlevalidate()) {
        loading();
        return;
      }
      row.deptId = row.deptId.join(",");
      // row.roleId = row.roleId.join(",");
      // row.postId = row.postId.join(",");
      // row.monitDeptId = row.monitDeptId.join(",");
      update(row).then(
        () => {
          this.initFlag = false;
          this.crud.toQuery();
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          done();
        },
        (error) => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.crud.refresh();
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      // this.onLoad(this.page, params);
      done();
    },
    selectionClear() {
      this.crud.selections = [];
      this.$refs.crud.toggleSelection();
    },
    handleReset() {
      if (this.crud.selections.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$prompt("请输入密码", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern: passwordRuleUpdate,
        inputPlaceholder: "必须8到20位, 且包含数字、大小写字母、特殊符号",
        inputErrorMessage: "必须8到20位, 且包含数字、大小写字母、特殊符号",
      })
        .then(({ value }) => {
          resetPassword({
            userIds: this.crud.selections.map( item => item.id).join(','),
            password: value,
          }).then(() => {
            this.$message.success("操作成功！");
            this.crud.toQuery();
          });
        })
        .catch(() => {});
    },
    handleLock(islock) {
      if (this.crud.selections.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if(islock) {
        const exist = this.crud.selections.find( item => item.status == 'E')
        if(exist) {
          return this.$message.warning("存在禁用账号，请重新勾选！");
        }
      } else {
        const exist = this.crud.selections.find( item => item.status == 'U')
        if(exist) {
          return this.$message.warning("存在正常状态的账号，请重新勾选！");
        }
      }
      this.$confirm(`确定将账号${islock ? "禁用" : "解封"}？`, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const fun = islock ? freeze : unfreeze;
        fun(this.crud.selections.map( item => item.id).join(',')).then((res) => {
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.crud.toQuery();
        });
      });
    },
    // beforeOpen(done, type) {
    //   if (["edit", "view"].includes(type)) {
    //     getUser(this.form.id).then((res) => {
    //       this.form = res.data.data;
    //       if (this.form.sex === -1) this.form.sex = ""; //如果在新建时不选择性别，后台会返回-1
    //       if (this.form.hasOwnProperty("deptId")) {
    //         this.form.deptId = this.form.deptId.split(",");
    //       }
    //       if (this.form.hasOwnProperty("roleId")) {
    //         this.form.roleId = this.form.roleId.split(",");
    //       }
    //       if (this.form.hasOwnProperty("postId")) {
    //         this.form.postId = this.form.postId.split(",");
    //       }
    //       if (this.form.hasOwnProperty("monitDeptId")) {
    //         this.form.monitDeptId = this.form.monitDeptId.split(",");
    //       }
    //     });
    //   }
    //   this.initFlag = true;
    //   done();
    // },
    // currentChange(currentPage) {
    //   this.page.currentPage = currentPage;
    // },
    // sizeChange(pageSize) {
    //   this.page.pageSize = pageSize;
    // },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-input-number .el-input__inner {
  text-align: left;
}

::v-deep
  .el-table--enable-row-hover
  .el-table__body
  tr:hover
  > td.el-table__cell {
  background-color: #fcf0c1;
}

::v-deep .el-table__row {
  td {
    box-sizing: border-box;
    height: 42px;
    padding: 0;
  }
}

/*滚动条中间滑动部分*/
/deep/ ::-webkit-scrollbar-thumb {
  background-color: rgba(125, 125, 125, 0.5);
}

// 覆盖公共样式, 防止alarmType搜索框被隐藏
/deep/ .el-card {
  overflow: inherit;
}
</style>
