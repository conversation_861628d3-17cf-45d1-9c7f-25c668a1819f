<template>
  <el-dialog
    v-dialog-drag
    title="接口权限"
    append-to-body
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="95%"
  >
    <div class="box-container">
      <div class="box-item left">
        <div class="title-text">未授权接口</div>
        <div class="box-item-title">
          <el-input
            v-model="code"
            placeholder="请输入接口编码"
            class="item-title"
            style="width: 200px;"
            clearable
            size="small"
          />
          <el-input
            v-model="name"
            class="item-title"
            placeholder="请输入接口名称"
            style="width: 200px;"
            clearable
            size="small"
          />
          <el-select
            v-model="serviceId"
            placeholder="请选择所属业务服务"
            style="width: 200px;"
            class="item-title"
            @change="changeRule"
            clearable
            size="small"
          >
            <el-option
              v-for="item in serviceList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="small"
            class="item-title"
            @click="refresh"
          >搜 索</el-button>
        </div>
        <div class="table-box">
          <el-table
            ref="multipleTableLeft"
            :data="tableDataLeft"
            tooltip-effect="dark"
            row-key="id"
            current-row-key="id"
            height="50vh"
            border
            resizable
            @selection-change="handleSelectionChangeLeft"
          >
            <el-table-column
              :resizable="false"
              type="selection"
              width="50"
              reserve-selection
            />
            <el-table-column
              :resizable="false"
              label="接口编码"
              prop="code"
              min-width="120"
              show-overflow-tooltip
            />
            <el-table-column
              label="接口名称"
              prop="name"
              min-width="120"
              :resizable="false"
            />
            <el-table-column
              prop="url"
              label="接口url"
              min-width="120"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              prop="serviceName"
              label="所属业务服务"
              min-width="120"
              show-overflow-tooltip
              :resizable="false"
            />
          </el-table>
        </div>
        <div class="bottom">
          <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :page-size="size"
            :page-sizes="[10, 20, 30, 40, 50, 100]"
            :current-page="curPage"
            :total="totalPage"
            @current-change="pageChange"
            @size-change="sizeChangeHandler"
          />
        </div>
      </div>
      <div class="btn">
        <el-button
          type="primary"
          icon="el-icon-arrow-right"
          size="small"
          title="新增"
          style="width: 50px;margin-bottom: 10px;"
          @click="handleAdd"
        />
        <el-button
          icon="el-icon-arrow-left"
          size="small"
          title="删除"
          style="width: 50px;margin-left: 0;"
          @click="handleDel"
        />
      </div>
      <div class="box-item right">
        <div class="title-text">
          已授权接口<span>{{ checkedNum }}</span>
        </div>
        <div class="box-item-title">
          <el-input
            v-model="codeLocal"
            placeholder="请输入接口编码"
            class="item-title"
            style="width: 200px;"
            clearable
            size="small"
          />
          <el-input
            v-model="nameLocal"
            class="item-title"
            placeholder="请输入接口名称"
            style="width: 200px;"
            clearable
            size="small"
          />
          <el-select
            v-model="serviceIdLocal"
            placeholder="请选择所属业务服务"
            style="width: 200px;"
            class="item-title"
            @change="changeRule"
            clearable
            size="small"
          >
            <el-option
              v-for="item in serviceList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="small"
            class="item-title"
            @click="getRightData"
          >搜 索</el-button>
        </div>
        <div class="table-box">
          <el-table
            ref="multipleTableRight"
            v-loading="loading"
            :data="tableDataRight"
            tooltip-effect="dark"
            current-row-key="id"
            row-key="id"
            height="50vh"
            border
            resizable
            @selection-change="handleSelectionChangeRight"
          >
            <el-table-column
              :resizable="false"
              type="selection"
              width="50"
            />
            <el-table-column
              :resizable="false"
              label="接口编码"
              prop="code"
              min-width="120"
              show-overflow-tooltip
            />
            <el-table-column
              label="接口名称"
              prop="name"
              min-width="120"
              :resizable="false"
            />
            <el-table-column
              prop="url"
              label="接口url"
              min-width="120"
              show-overflow-tooltip
              :resizable="false"
            />
            <el-table-column
              prop="serviceName"
              label="所属业务服务"
              min-width="120"
              show-overflow-tooltip
              :resizable="false"
            />
          </el-table>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import pagination from "@/components/Crud/Pagination";
import { serviceListAll } from '@/api/log/api';
import { bindList, bindedList, inAndOut } from "@/api/access/platformAccount";
export default {
  name: "Interface",
  components: {
    pagination,
  },
  props: {
    btnShow: {
      type: Boolean,
      default: true,
    },
    rowInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      tableDataLeft: [],
      tableDataRight: [],
      total: 0,
      totalPage: 0,
      curPage: 1,
      size: 10,
      visible: false,
      loading: false,
      serviceList: [],
      code: '',
      name: '',
      serviceId: '',
      codeLocal: '',
      nameLocal: '',
      serviceIdLocal: '',
      selectedList: []
    };
  },
  watch: {
    visible () {
      if (this.visible) {
        this.getTableList()
        this.getRightData()
        this.getServiceList()
      }
    }
  },
  methods: {
    getServiceList () {
      serviceListAll().then(res => {
        this.serviceList = res.data.data?.map(item => {
          return {
            label: item.name,
            value: item.id
          }
        });
      });
    },
    handleSelectionChangeLeft (val) {
      this.selectedList = val;
    },
    handleSelectionChangeRight(val) {
      this.delList = val
    },
    handleAdd() {
      if(!this.selectedList.length) {
        this.$message.warning('请先选择添加的数据');
      }
      const ids = this.selectedList.map( item => item.id).join(',')
      inAndOut({type: 'add', ids, userId: this.rowInfo.id}).then( res => {
        this.$refs.multipleTableLeft.clearSelection();
        this.$refs.multipleTableRight.clearSelection();
        this.getTableList()
        this.getRightData()
      })
    },
    handleDel() {
      if(!this.delList.length) {
        this.$message.warning('请先选择移除的数据');
      }
      const ids = this.delList.map( item => item.id).join(',')
      inAndOut({type: 'remove', ids, userId: this.rowInfo.id}).then( res => {
        this.$refs.multipleTableLeft.clearSelection();
        this.$refs.multipleTableRight.clearSelection();
        this.getTableList()
        this.getRightData()
      })
    },
    pageChange (val) {
      this.curPage = val;
      this.getTableList();
    },
    sizeChangeHandler (val) {
      this.size = val;
      this.refresh();
    },
    getTableList () {
      bindList({
        name: this.name,
        code: this.code,
        serviceId: this.serviceId,
        current: this.curPage,
        size: this.size,
        userId: this.rowInfo.id
      })
        .then((res) => {
          const obj = res.data.data || {}
          this.tableDataLeft = obj.records || [];
          this.totalPage = obj.total || 0;
        });
    },
    getRightData() {
      bindedList({
        name: this.nameLocal,
        code: this.codeLocal,
        serviceId: this.serviceIdLocal,
        userId: this.rowInfo.id
      })
        .then((res) => {
          this.tableDataRight = res.data.data || []
        });
    },
    refresh () {
      this.curPage = 1;
      this.getTableList();
    }
  },
};
</script>
<style lang="less" scoped>
.box-container {
  display: flex;
  overflow-x: auto;
  overflow-y: hidden;
  .btn {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-content: center;
    padding: 0px 15px;
  }
  .box-item {
    flex: 1;
    min-width: 550px;
    // padding: 15px;
    // border: 1px solid #409eff;
    // margin: 20px;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    &.left, &.right {
      flex: 8;
    }
    .title-text {
      font-size: 16px;
      font-weight: 600;
      span {
        margin-left: 8px;
        color: #ccc;
        font-weight: normal;
        font-size: 12px;
      }
    }
    .box-item-title {
      display: flex;
      flex-wrap: wrap;
      margin-left: -15px;
      margin-bottom: 15px;
      .item-title{
        margin: 15px 0px 0px 15px;
      }
    }
    .table-box {
      flex: 1;
      overflow-y: auto;
    }
    .bottom {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
    }
  }
}
.ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>

