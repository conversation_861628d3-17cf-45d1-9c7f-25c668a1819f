<template>
  <div>
    <div class="assistSeniorLayout">
      <!-- <div class="assistSeniorBtn">
        <div
          :class="['assistSeniorBtnsearch',btnFlag?'white':'']"
          @click="()=>{searchHandle()}"
        >
          <div class="assistSeniorBtnContent">
            查询
          </div>
        </div>
        <div :class="['assistSeniorBtnset',btnFlag?'':'white']">
          <div class="assistSeniorBtnContent">
            设置
          </div>
        </div>
      </div> -->
      <RowForm
        ref="rowForm"
        :data="data"
        :data-type="dataType"
        @submitHandle="submitHandle"
      />
    </div>
    <BtnBottom @sendCommand="sendCommand"/>
  </div>
</template>

<script>
import RowForm from '../module/rowForm.vue';
import { querysuparam, setterminalsuparam } from '@/api/system/terminalConfig.js';
import { typeAdas, adasDefault } from '../module/dataType';
import BtnBottom from '../module/btnBottom.vue';

export default {
  name: 'AssistSeniorConfig',
  components: {
    RowForm,
    BtnBottom
  },
  props: {
    phones: {
      type: [Array],
      default: () => {
        return [];
      }
    },
    changeVehicle: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      data: {},
      dataType: {},
      btnFlag: false
    };
  },
  mounted() {
    this.dataType = typeAdas;
    this.data = adasDefault;
  },
  methods: {
    // 选车错误信息
    selectMsg() {
      let flag = this.phones.length === 1 ? 0 : this.phones.length > 1 ? 2 : 1;
      if (flag) {
        this.$message({
          type: 'error',
          message: flag > 1 ? '仅支持单辆车' : '请先选择车辆'
        });
      }

      return !flag;
    },
    sendCommand(value) {
      if (value) {
        this.$refs.rowForm.onSubmit();
      }
      else {
        this.searchHandle();
      }
    },
    // 查询参数  flag取消提示
    searchHandle(flag) {
      // if (!this.selectMsg()) {
      //   return;
      // }
      if (!this.changeVehicle) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      let parme = {
        // phone: this.changeVehicle.phone,
        paramId: 62308,
        device_type: this.changeVehicle.deviceType,
        device_id: BigInt(this.changeVehicle.deviceId),
      };
      querysuparam(parme).then(res => {
        if (res.code === 200) {
          !flag && this.$message({
            type: 'success',
            message: '操作成功!'
          });
          res.data.eventEnable = this.dealMultiple(res.data.eventEnableStr);
          res.data.alarmEnable = this.dealMultiple(res.data.alarmEnableStr);
          res.data['obligateBytes_4'] = res.data.obligateBytes4;
          delete res.data['obligateBytes4'];
          this.data = res.data;
        }
      }).catch(err => {
        console.log(err);
      });
    },
    // 处理多选框数据
    dealMultiple(val) {
      val = val.split('').reverse().join('');
      let arr = [];
      for (let i in val) {
        if (val[i] === '1') {
          arr.push(i);
        }
      }
      return arr;
    },
    // 单独处理2个特殊数据
    enableCase(value) {
      if (value === 'alarmEnable' || value === 'eventEnable') {
        return '-1';
      }
      else {
        return -1;
      }
    },
    // 单独处理类型数据
    typeCase(key, data) {
      if (key === 'alarmEnable' || key === 'eventEnable') {
        return this.reDealMultiple(data);
        // } else if (key === 'obligate' || key === 'obligateBytes_4') {
        //   return data;
      }
      else {
        return parseInt(data);
      }
    },
    // 处理数据
    dealDataCase(check, data, key) {
      if (check && (data || data === 0)) {
        return this.typeCase(key, data);
      }
      else {
        return this.enableCase(key);
      }
    },
    // 提交表单
    submitHandle(data) {
      if (!this.selectMsg()) {
        return;
      }
      let dataCopy = JSON.parse(JSON.stringify(this.data));
      let checkGroup = this.$refs.rowForm.checkedGrant;
      let obj = {};
      checkGroup.forEach(v => {
        for (let k in dataCopy) {
          if (k === v.key) {
            obj[k] = this.dealDataCase(v.value, dataCopy[k], v.key);
          }
        }
      });
      // obj.phone = this.phones[0];
      obj.device_type = this.phones[0].deviceType,
      obj.device_id = BigInt(this.phones[0].deviceId),
      obj['sbAlarmType'] = 'adas';
      setterminalsuparam(obj).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.searchHandle(true);// 重新获取数据
        }
      }).catch(err => {
        console.log(err);
      });
    },
    // 复原多选框数据格式
    reDealMultiple(val) {
      let str = '00000000000000000000000000000000';
      str = str.split('');
      val.forEach(v => {
        str[v] = '1';
      });
      str = str.reverse().join('');
      return str;
    }
  }
};
</script>

<style lang="less" scoped>
// adas
.assistSeniorLayout {
  max-height: calc(100vh - 220px);
  overflow: scroll;
  display: flex;
  padding-left: 10px;
}

.assistSeniorBtn {
  width: 60px;
  display: flex;
  flex-direction: column;
  padding-top: 26px;
  padding-bottom: 90px;
}

.assistSeniorBtnsearch, .assistSeniorBtnset {
  background: #409eff;
  flex: 1;
  border: 1px solid #4096d1;
  text-align: center;
  color: #ffffff;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.assistSeniorBtnContent {
  width: 100%;
}

.white {
  background: #ffffff;
  color: #000000;
  cursor: default
}
</style>
