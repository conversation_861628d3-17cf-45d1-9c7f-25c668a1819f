<template>
  <div class="v_a_property_layout">
    <el-table
      :data="carPick"
      style="width: 100%"
      height="300"
      empty-text="请选择车辆"
      :header-cell-style="{
        backgroundColor:'#e1e5ee','text-align':'center',
        'border-color':'#aebac5','color':'#000','font-weight': 400,
        'height':'34px','line-height': '34px','padding': 0}"
      :cell-style="{'text-align':'center'}"
    >
      <el-table-column
        type="index"
        width="50"
        label="序号"
      />
      <el-table-column
        prop="targetName"
        label="监控对象"
        :resizable="false"
        >
        <template slot-scope="scope">
            {{ scope.row.targetName || $utils.emptymap.targetName }}
        </template>
      </el-table-column>
<!--      <el-table-column-->
<!--        prop="simId"-->
<!--        label="SIM卡号"-->
<!--      />-->
      <el-table-column
        prop="phone"
        label="序列号"
        :resizable="false"
      />
      <el-table-column label="发送状态" :resizable="false">
        <template slot-scope="scope">
          <span>{{ (scope.row.id && resultData[scope.row.id])?'已发送':'' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="查询结果" :resizable="false">
        <template slot-scope="scope">
          <el-button
            size="mini"
            :disabled="getshowHandle(scope.row)"
            @click="showHandle(scope.row, scope.$index)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="发送指令" :resizable="false">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="searchHandle(scope.row)"
          >
            查询
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div
      class="va_config"
    >
      <div class="config_label">
        监控对象
      </div>
      <div class="config_value">
        {{ resultData.resultCar }}
      </div>
    </div>
    <div
      v-for="(item) in vaConfig"
      :key="item"
      class="va_config"
    >
      <div class="config_label">
        {{ $t(item) }}
      </div>
      <div class="config_value">
        {{ turnType(item,showData[item]) }}
      </div>
    </div>
  </div>
</template>

<script>
import { queryaudiovideoattr } from '@/api/system/terminalConfig.js';
export default {
  name: 'AudioVideoProperty',
  components: {
  },
  props: {
    carPick: {
      type: [Array],
      default: () => {
        return [];
      }
    }
  },
  data () {
    return {
      vaConfig: ['audioCodeType', 'audioChannels', 'audioSample', 'audioBits',
        'audioLength', 'audioOutput', 'videoCodeType',
        'maxAudioChannels', 'maxVideoChannels'
      ],
      showData: {},
      resultData: {
        resultCar: null
      },
      resultType: {
        audioCodeType: {
          6: 'G.711A',
          8: 'G.726'
        },
        audioSample: {
          0: '8kHz',
          1: '22.05kHz',
          2: '44.1kHz',
          3: '48kHz'
        },
        audioBits: {
          0: '8位', 1: '16位', 2: '32位'
        },
        audioOutput: {
          0: '不支持', 1: '支持'
        },
        videoCodeType: {
          26: 'ADPCMA',
          98: 'H.264',
          99: 'H.265'
        }
      }
    };
  },
  mounted () {
  },
  methods: {
    turnType (item, value) {
      if (item && (value || value === 0)) {
        if (this.resultType[item]) {
          return this.resultType[item][value] || '';
        } else {
          return value;
        }
      }
      // 输入音频采样率,0:8kHz,1:22.05kHz,2:44.1kHz,3:48kHz
      // 输入音频采样位数,0:8位，1:16位，2:32位
      // 是否支持音频输出，0:不支持，1:支持
    },
    getshowHandle (value) {
      if (value.id && this.resultData[value.id]) {
        return false;
      } else {
        return true;
      }
    },
    showHandle (value, index) {
      this.resultData.resultCar = this.carPick[index].targetName;
      this.showData = this.resultData[value.id];
    },
    // 查询参数 flag是否提示
    searchHandle (value) {
      let parme = {
        device_type: value.deviceType,
        device_id: BigInt(value.deviceId),
      };
      queryaudiovideoattr(parme).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.resultData[value.id] = res.data;
          this.showData = res.data;
          this.resultData.resultCar = this.carPick[0].targetName;
        }
      }).catch(err => {
        console.log(err);
      });
    }

  }
};
</script>

<style scoped>
.v_a_property_layout{
  /* max-height:calc(100vh - 220px) ; */
  overflow: scroll;
  padding: 10px;
}
.va_config{
  margin-top: 10px;
  display: flex;
  margin-bottom: 4px;
  border: 1px solid #d6d6d6;
}
.car_config{
  display: flex;
  border: 1px solid #d6d6d6;
  max-height: 200px;
  overflow-y: scroll;

}
.config_label,.config_value{
  height: 34px;
  line-height: 34px;
}

.config_label{
  padding-left: 10px;
  flex: 2;
  background-color: #e8eaed;
}
.config_value{
  padding-left: 10px;
  flex: 3;
}
.car_table_head{
  background-color: #e1e5ee;
  text-align: center;
}
</style>
