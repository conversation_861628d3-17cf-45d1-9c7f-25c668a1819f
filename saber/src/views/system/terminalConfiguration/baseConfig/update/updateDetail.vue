<template>
  <div style="display: inline-block">
    <el-dialog
      :title="dialogTitle"
      append-to-body
      width="600px"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
    >
      <div class="work_item_room">
        <!-- <div class="work_item_box">
          <span class="work_item_title">车牌号：</span>
          <span class="work_item_value">{{ updateDetail.licencePlate }}</span>
        </div> -->
        <div class="work_item_box">
          <span class="work_item_title">MCU版本:</span>
          <span
            class="work_item_value"
            :title="updateDetail"
          >{{ updateDetail.mcu }}</span>
        </div>
        <div class="work_item_box">
          <span class="work_item_title">DVR版本:</span>
          <span
            class="work_item_value"
            :title="updateDetail"
          >{{ updateDetail.dvr }}</span>
        </div>
        <!-- <div class="work_item_box">
          <span class="work_item_title">DVR版本：</span>
          <span
            class="work_item_value"
            :title="updateDetail.dvr"
          >{{ updateDetail.dvr }}</span>
        </div>
        <div class="work_item_box">
          <span class="work_item_title">MCU版本：</span>
          <span
            class="work_item_value"
            :title="updateDetail.mcu"
          >{{ updateDetail.mcu }}</span>
        </div> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
      dialogTitle: '查看版本',
      updateDetail: {
        mcu: undefined,
        dvr: undefined
      },
      showDialog: false
    };
  },
  methods: {
    setUpdateDetail (_data) {
      this.updateDetail.mcu = _data.split('&')[0];
      this.updateDetail.dvr = _data.split('&')[1];
      // let hardwareInfo = _data.split('][');
      // this.updateDetail = {
      //   version: hardwareInfo[0].substring(1).split(':')[1],
      //   dvr: hardwareInfo[1],
      //   mcu: hardwareInfo[2]
      // };
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .el-dialog__header {
  background-color: #F8F8FF;
}
.work_item_room{
  display: flex;
  flex-flow: row wrap;
}
.work_item_box{
  margin: 2px;
  width: 500px;
  display: flex;
  align-items: center;
  margin-right: 8px;
  margin-top: 16px;
}
.work_item_title{
  width: 150px;
  font-size: 17px;
  color: #303133;
}
.work_item_value{
  color: #303133;
  font-size: 16px;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
  flex: 1;
}
</style>
