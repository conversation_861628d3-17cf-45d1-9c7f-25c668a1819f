<template>
  <div>
    <div class="stateMonitorLayout">
      <!-- <div class="stateMonitorBtn">
        <div
          :class="['stateMonitorBtnsearch',btnFlag?'white':'']"
          @click="()=>{searchHandle()}"
        >
          <div class="stateMonitorBtnContent">
            查询
          </div>
        </div>
        <div :class="['stateMonitorBtnset',btnFlag?'':'white']">
          <div class="stateMonitorBtnContent">
            设置
          </div>
        </div>
      </div> -->
      <div class="main">
        <div class="item_label">
          物理通道号
        </div>
        <div class="item_label">
          逻辑通道号
        </div>
        <div class="item_label">
          通道类型
        </div>
        <div class="item_label">
          是否连接云台
        </div>
        <div class="item_label">
          <span
            class="add_item"
            @click="addHandle"
          >添加</span>
        </div>
      </div>
      <div
        v-for="(item,index) in form"
        :key="index"
        class="main"
      >
        <div class="main_item">
          <div>
            <el-input
              v-model.number="form[index].physicsChannel"
              placeholder="请输入内容"
            />
          </div>
        </div>
        <div class="main_item">
          <div>
            <el-input
              v-model.number="form[index].logicChannel"
              placeholder="请输入内容"
            />
          </div>
        </div>
        <div class="main_item">
          <div>
            <el-select
              v-model="form[index].channelType"
              placeholder="请选择"
            >
              <el-option
                v-for="(subItem,subIndex) in formOptions.channel"
                :key="subIndex"
                :label="subItem.label"
                :value="subItem.value"
              />
            </el-select>
          </div>
        </div>
        <div class="main_item">
          <div>
            <el-select
              v-model="form[index].connectYun"
              placeholder="请选择"
            >
              <el-option
                v-for="(subItem,subIndex) in formOptions.cloud"
                :key="subIndex"
                :label="subItem.label"
                :value="subItem.value"
              />
            </el-select>
          </div>
        </div>
        <div class="main_item">
          <!-- <div @click="addHandle">
            加
          </div> -->
          <div
            class="reduce_item"
          >
            <i
              class="el-icon-remove-outline reduce_icon"
              @click="reduceHandle(index)"
            />
          </div>
        </div>
      </div>
    </div>
    <BtnBottom @sendCommand="sendCommand" />
  </div>
</template>

<script>
// import RowForm from '../module/rowForm.vue';
// import { querysuparam, setterminalsuparam } from '@/api/system/terminalConfig.js';
import {typeRoadCondition, roadConditionDefault} from '../module/dataType';
import BtnBottom from '../module/btnBottom.vue';
// ==
import { querysuparam, setterminalavparam } from '@/api/system/terminalConfig.js';

export default {
  name: 'RoadCondition',
  components: {
    BtnBottom
  },
  props: {
    phones: {
      type: [Array],
      default: () => {
        return [];
      }
    },
    changeVehicle: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      labelMuster: {
        physicsChannel: '物理通道号',
        logicChannel: '逻辑通道号',
        channelType: '通道类型',
        connectYun: '是否连接云台'
      },
      formOptions: {
        channel: [{
          value: 0,
          label: '音视频'
        }, {
          value: 1,
          label: '音频'
        }, {
          value: 2,
          label: '视频'
        }],
        cloud: [{
          value: 0,
          label: '未连接'
        }, {
          value: 1,
          label: '连接'
        }]
      },

      form: [{
        physicsChannel: '',
        logicChannel: '',
        channelType: '',
        connectYun: ''
      }],
      formItem: {
        physicsChannel: '',
        logicChannel: '',
        channelType: '',
        connectYun: ''
      },

      postConfig: {
        'phone': '22090040012',
        'av_type': 118,
        'audio_video_channel_list': {
          'av_num': 4,
          'audio_num': 0,
          'video_num': 3,
          'audio_video_channels': []
        }
      },

      // ==
      modSet: {// 复用时设置
        key: 'sbAlarmType', // 标识设置模块的字段
        value: 'rs', // 标识设置模块的值
        id: 118, // 标识设置模块的数字值
        unMuKey: [{in: 'checkEnableStr', out: 'checkEnable'}], // 多选框的key（用于转换）unusualMultiKey
        unMuFg: 32, // 多选框‘0000’形式位数 unusualMultiFigure
        shCf: true, // searchConfirm
        delCh: true // 删除勾选框
      },
      data: {},
      dataType: {},
      searchCar: undefined, // 已查询的车 非必须
      btnFlag: false
    };
  },
  mounted () {
    this.dataType = typeRoadCondition;
    this.data = roadConditionDefault;
  },
  methods: {
    addHandle () {
      let obj = JSON.parse(JSON.stringify(this.formItem));
      this.form.push(obj);
    },
    reduceHandle (index) {
      this.form.splice(index, 1);
    },
    // 查询
    searchWay () {
      // if (!this.selectMsg()) {
      //   return;
      // }
      if (!this.changeVehicle) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      let parme = {
        // phone: this.changeVehicle.phone,
        device_type: this.changeVehicle.deviceType,
        device_id: BigInt(this.changeVehicle.deviceId),
        paramId: this.modSet.id
      };
      querysuparam(parme).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.searchCar = this.changeVehicle.deviceId;
          let arr = JSON.parse(JSON.stringify(res.data.audioVideoChannels)) || [];
          this.form = arr;
        }
      }).catch(err => {
        console.log(err);
      });
    },
    // 提交
    submitWay () {
      if (!this.selectMsg()) {
        return;
      }
      if (this.modSet.shCf) {
        if (!this.searchCar || (this.searchCar && (this.searchCar !== this.phones[0].deviceId))) {
          this.$message({
            type: 'error',
            message: '请先查询设置参数'
          });
          return;
        }
      }

      // 提示未填写
      let returnMsg = ['请填写 '];
      this.form.forEach((item, index) => {
        let msg = [`第${index + 1}项`];
        for (let key in item) {
          if (!item[key] && item[key] !== 0) {
            msg.push(this.labelMuster[key] + '、');
          }
        }
        if (msg.length > 1) {
          if (this.form.length - 1 !== index) {
            msg[msg.length - 1] = msg[msg.length - 1].replace('、', '; <br/> <span style="display: inline-block; width: 46px;"></span>');
          } else {
            msg[msg.length - 1] = msg[msg.length - 1].replace('、', ';');
          }
          returnMsg.push(msg.join(''));
        }
      });
      if (returnMsg.length > 1) {
        this.$message({
          type: 'error',
          dangerouslyUseHTMLString: true,
          message: returnMsg.join('')
        });
        return;
      }

      let postObj = JSON.parse(JSON.stringify(this.postConfig));
      delete postObj.phone;
      postObj.device_type = this.phones[0].deviceType;
      postObj.device_id = BigInt(this.phones[0].deviceId);
      postObj['audio_video_channel_list']['audio_video_channels'] = JSON.parse(JSON.stringify(this.form));

      setterminalavparam(postObj).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.searchHandle(true);// 重新获取数据
        }
      }).catch(err => {
        console.log(err);
      });
    },
    sendCommand (value) {
      if (value) {
        this.submitWay();
      } else {
        this.searchWay();
      }
    },

    /*! !!!! */

    // 选车错误信息
    selectMsg () {
      let flag = this.phones.length === 1 ? 0 : this.phones.length > 1 ? 2 : 1;
      if (flag) {
        this.$message({
          type: 'error',
          message: flag > 1 ? '仅支持单辆车' : '请先选择车辆'
        });
      };
      return !flag;
    },
    // sendCommand (value) {
    //   if (value) {
    //     this.$refs.rowForm.onSubmit();
    //   } else {
    //     this.searchHandle();
    //   }
    // },
    // 查询参数 flag是否提示
    searchHandle (flag) {
      // if (!this.selectMsg()) {
      //   return;
      // }
      if (!this.changeVehicle) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      let parme = {
        // phone: this.changeVehicle.phone,
        paramId: this.modSet.id,
        device_type: this.changeVehicle.deviceType,
        device_id: BigInt(this.changeVehicle.deviceId),
      };
      querysuparam(parme).then(res => {
        if (res.code === 200) {
          !flag && this.$message({
            type: 'success',
            message: '操作成功!'
          });
          if (this.modSet.unMuKey && this.modSet.unMuKey.length > 0) {
            this.modSet.unMuKey.forEach(item => {
              res.data[item.out] = this.dealMultiple(res.data[item.in]);
            });
          }
          this.searchCar = this.changeVehicle.deviceId;
          this.data = res.data;
        }
      }).catch(err => {
        console.log(err);
      });
    },
    // 处理多选参数
    dealMultiple (val) {
      val = val.split('').reverse().join('');
      let arr = [];
      for (let i in val) {
        if (val[i] === '1') {
          arr.push(i);
        }
      }
      return arr;
    },
    // 单独处理2个特殊数据
    enableCase (value) {
      if (this.judgeUnusualKey(value)) {
        return '-1';
      } else {
        return -1;
      }
    },

    // 判断是不是要转换特殊key
    judgeUnusualKey (key) {
      let flag = false;
      this.modSet.unMuKey.forEach(k => {
        if (key === k.out) {
          flag = true;
        }
      });
      return flag;
    },

    // 处理数据
    dealDataCase (check, data, key) {
      if (check && (data || data === 0)) {
        if (this.judgeUnusualKey(key)) {
          return this.reDealMultiple(data);
        } else {
          return parseInt(data);
        }
      } else {
        return this.enableCase(key);
      }
    },
    // 提交表单
    // submitHandle (data) {
    //   console.log('this.phones', this.phones, this.searchCar);

    //   if (!this.selectMsg()) {
    //     return;
    //   }
    //   if (this.modSet.shCf) {
    //     if (!this.searchCar || (this.searchCar && (this.searchCar !== this.phones[0].deviceId))) {
    //       this.$message({
    //         type: 'error',
    //         message: '请先查询设置参数'
    //       });
    //       return;
    //     }
    //   }
    //   let dataCopy = JSON.parse(JSON.stringify(this.data));
    //   let checkGroup = this.$refs.rowForm.checkedGrant;
    //   let obj = {};
    //   checkGroup.forEach(v => {
    //     for (let k in dataCopy) {
    //       if (k === v.key) {
    //         obj[k] = this.dealDataCase(v.value, dataCopy[k], v.key);
    //       }
    //     }
    //   });
    //   obj.phone = this.phones[0];
    //   // 音视频参数设置对照表:0x75(117)-音视频参数设置,0x76(118)-音视频通道列表设置,0x77(119)-单独视频通道参数设置
    //   obj[this.modSet.key] = this.modSet.value;
    //   setterminalsuparam(obj).then(res => {
    //     if (res.code === 200) {
    //       this.$message({
    //         type: 'success',
    //         message: '操作成功!'
    //       });
    //       this.searchHandle(true);// 重新获取数据
    //     }
    //   }).catch(err => {
    //     console.log(err);
    //   });
    // },
    // 复原多选框参数
    reDealMultiple (val) {
      let str = '';
      for (let i = 0; i < this.modSet.unMuFg; i++) {
        str += '0';
      }
      str = str.split('');
      val.forEach(v => {
        str[v] = '1';
      });
      str = str.reverse().join('');
      return str;
    }
  }
};
</script>

<style scoped>
.stateMonitorLayout{
  max-height:calc(100vh - 220px) ;
  overflow: scroll;
  /* display: flex; */
  padding-left: 10px;
}
.stateMonitorBtn{
  width: 60px;
  display: flex;
  flex-direction: column;
  padding-top: 26px;
  padding-bottom: 90px;
}
.stateMonitorBtnsearch,.stateMonitorBtnset{
  background: #409eff;
  flex: 1;
  border: 1px solid #4096d1;
  text-align: center;
  color: #fff;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.stateMonitorBtnContent{
  width: 100%;
}
.white{
  background: #fff;
  color: #000;
  cursor: default
}
/* == */
.main{
  display: flex;
  width: 100%;
  text-align: center;
}
.main_item{
  flex: 1;
}
.item_label{
  flex: 1;
  height: 35px;
  line-height: 35px;
  background-color: #e1e5ee;
  border: 1px solid #c1c9da;
}
.main_item ::v-deep.el-select{
  width: 100%;
}
.add_item{
  border: 1px solid #aebac5;
  padding: 3px 5px;
  background-color: #fff;
  margin: 5px;
  cursor: pointer;
}
.reduce_item{
  line-height: 35px;
  font-size: 18px;

  /* border: 1px solid #c1c9da; */
  /* box-sizing: border-box; */
}
.reduce_icon{
  cursor: pointer;
}
</style>
