<template>
  <div class="layout">
    <div class="btn">
      <el-button
        type="primary"
        @click="sendHandle(0)"
      >
        查询
      </el-button>
    </div>
    <div class="btn">
      <el-button
        type="primary"
        @click="sendHandle(1)"
      >
        设置
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    sendHandle (v) {
      this.$emit('sendCommand', v);
    }
  }
};
</script>

<style lang="less" scoped>
.layout{
  display: flex;
  padding: 15px;
  .btn{
    flex: 1;
    text-align: center;
  }
}
</style>
