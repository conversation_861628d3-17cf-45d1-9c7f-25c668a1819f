<template>
  <div class="layout">
    <div class="content">
      <div class="option" />
      <div class="value" />
      <div
        v-show="!rowSet.delCh"
        class="tick"
      >
        <el-checkbox
          v-model="checkTop"
          class="tick-box"
          :indeterminate="isIndeterminate"
          @change="handleCheckAllChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    checkedAll: {
      type: Boolean,
      default: true
    },
    isIndeterminate: {
      type: Boolean,
      default: false
    },
    rowSet: {
      type: [Object],
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
      checkTop: true
    };
  },
  watch: {
    checkedAll (newVal, oldVal) {
      this.checkTop = newVal;
    }
  },
  methods: {
    // 全选触发
    handleCheckAllChange (val) {
      this.$emit('checkedChangeBatch', val);
    }
  }
};
</script>

<style lang="less" scoped>
.layout{
  width: 100%;
  margin: 0 10px;
}
.content{
  display: flex;
  flex-direction: row;
  min-height: 26px;
}
.option{
  flex: 2;
}
.value{
  flex: 3;
}
.tick{
  background-color: #e8eaed;
  width: 41px;
}
.tick-box{
  margin-top: 5px;
  margin-left: 12px;
}
</style>
