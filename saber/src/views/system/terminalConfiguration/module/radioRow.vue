<template>
  <div class="layout">
    <div class="content">
      <div class="option">
        {{ getLabel(label) }}
      </div>
      <div class="value">
        <el-radio
          v-for="(item,index) in options"
          :key="index"
          v-model="radio"
          :label="item.k"
          @change="radioChange"
        >
          {{ item.v }}
        </el-radio>
      </div>
      <div
        v-show="!rowSet.delCh"
        class="tick"
      >
        <el-checkbox
          v-model="checked"
          class="tick-box"
          @change="checkboxChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import getLabel from '@/utils/getLabel';
export default {
  props: {
    label: {
      type: [Number, String],
      default: ''
    },
    initValue: {
      type: [Number, String, Object, Array],
      default: ''
    },
    options: {
      type: [Array],
      default: () => {
        return [];
      }
    },
    checkedFlag: {
      type: [Number, String, Object, Array],
      default: () => {
        return {};
      }
    },
    rowSet: {
      type: [Object],
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
      radio: 1,
      checked: true
    };
  },
  watch: {
    initValue: { // 深度监听，可监听到对象、数组的变化
      handler (newV, oldV) {
        this.getInitValue(newV);
      },
      deep: true,
      immediate: true
    },
    checkedFlag: { // 深度监听，可监听到对象、数组的变化
      handler (newV, oldV) {
        this.checked = newV.value;
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 初始值
    getInitValue (v) {
      this.radio = v;
    },
    /**
     * 获取字段对应的标签
     * @param {String} value
     * @return {String}
     */
    getLabel (value) {
      return getLabel('terminalConfiguration', value);
    },
    // 值改变
    radioChange (v) {
      this.$emit('formChange', {
        key: this.label,
        value: v
      });
    },
    // 勾选改变
    checkboxChange (v) {
      this.$emit('formCheckedChange', {
        index: this.checkedFlag.index,
        key: this.checkedFlag.key,
        value: v
      });
    }
  }
};
</script>

<style src="./rowForm.css" scoped>

</style>
