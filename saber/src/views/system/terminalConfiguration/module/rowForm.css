.layout{
  margin: 0 10px;
  border: 1px solid #aebac5;
}
.content{
  display: flex;
  flex-direction: row;
}
.option{
  background-color: #e8eaed;
  flex: 2;
  /* height: 30px; */
  line-height: 30px;
  padding-left: 10px;
  margin-right: 10px;
}
.value{
  flex: 3;
}
.tick{
  background-color: #e8eaed;
  width: 40px;
}
.tick-box{
  margin-top: 5px;
  margin-left: 12px;
}
.value ::v-deep .el-input__inner{
  height: 30px !important;
  border: none !important;
}
.value ::v-deep .el-input__inner:focus {
  box-shadow: none;
}
.value ::v-deep .el-radio{
  padding-top: 7px;
}
.value ::v-deep .el-select{
  width: 100%;
}
.value ::v-deep .el-select .el-input__inner{
  border: none;
}
.value ::v-deep .el-radio{
  margin-right: 15px;
}
