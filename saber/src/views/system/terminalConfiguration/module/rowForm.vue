<template>
  <div class="layout">
    <HeadRow
      ref="headRow"
      :checked-all="checkedAll"
      :is-indeterminate="isIndeterminate"
      :row-set="{
        delCh:formSet.delCh
      }"
      @checkedChangeBatch="checkedChangeBatch"
    />
    <div
      v-for="(item,index) in dataType"
      :key="index"
      :class="{ 'margin5': item.margin }"
    >
      <InputRow
        v-if="!item.type ||item.type==='i'"
        :set="item"
        :init-value="data[item.field]"
        :checked-flag="checkedGrant[index]"
        :row-set="{
          delCh:formSet.delCh
        }"
        @formChange="formChange"
        @formCheckedChange="formCheckedChange"
      />
      <RadioRow
        v-else-if="item.type ==='r'"
        :label="item.field"
        :init-value="data[item.field]"
        :options="item.options"
        :checked-flag="checkedGrant[index]"
        :row-set="{
          delCh:formSet.delCh
        }"
        @formChange="formChange"
        @formCheckedChange="formCheckedChange"
      />
      <SelectRow
        v-else-if="item.type ==='s'||item.type ==='m'"
        :label="item.field"
        :multiple="item.type==='m'"
        :init-value="data[item.field]"
        :options="item.options"
        :checked-flag="checkedGrant[index]"
        :row-set="{
          delCh:formSet.delCh
        }"
        @formChange="formChange"
        @formCheckedChange="formCheckedChange"
      />
    </div>
    <!-- <div class="end-btn">
      <el-button
        type="primary"
        class="submit"
        @click="onSubmit"
      >
        发送指令
      </el-button>
    </div> -->
  </div>
</template>

<script>
import HeadRow from '../module/headRow.vue';
import InputRow from '../module/inputRow.vue';
import RadioRow from '../module/radioRow.vue';
import SelectRow from '../module/selectRow.vue';
export default {
  name: 'Rowform',
  components: {
    HeadRow,
    InputRow,
    RadioRow,
    SelectRow
  },
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      }
    },
    dataType: {
      type: [Object, Array],
      default: () => {
        return [];
      }
    },
    formSet: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data () {
    return {
      checkedMuster: [], // checked汇总
      checkedGrant: [], // checked发放
      checkedAll: true,
      isIndeterminate: false
    };
  },
  watch: {
    dataType: { // 深度监听，可监听到对象、数组的变化
      handler (newV, oldV) {
        this.init(newV);
      },
      deep: true
    }
  },
  methods: {
    // 初始化表单项
    init (dataType) {
      let arr = [];
      let subArr = [];
      dataType && dataType.forEach((item, index) => {
        arr.push(item.field);
        subArr.push({
          index: index,
          key: item.field,
          value: true
        });
      });
      this.checkedMuster = arr;
      this.checkedGrant = subArr;
    },
    // 表单项改变
    formChange (obj) {
      this.data[obj.key] = obj.value;
    },
    // 提交表单
    onSubmit () {
      this.$emit('submitHandle', this.data);
    },
    // 全选&反选
    checkedChangeBatch (flag) {
      let arr = [];
      arr = this.dataType.map((item, index) => {
        return {
          index: index,
          key: item.field,
          value: flag
        };
      });
      this.checkedGrant = arr;
      this.isIndeterminate = false;
      this.checkedAll = flag;
    },
    // 单选改变
    formCheckedChange (v) {
      this.checkedGrant[v.index].value = v.value;
      let flag = 0;
      this.checkedGrant.forEach(item => {
        if (item.value) {
          flag++;
        }
      });
      if (flag === 0) {
        this.checkedAll = false;
        this.isIndeterminate = false;
      } else if (flag === this.checkedMuster.length) {
        this.checkedAll = true;
        this.isIndeterminate = false;
      } else {
        this.checkedAll = false;
        this.isIndeterminate = true;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.layout{
  width: 95%;
}
.submit{
  display: block;
  margin: 10px auto 40px;
}
.margin5{
  margin-bottom: 5px;
}
</style>
