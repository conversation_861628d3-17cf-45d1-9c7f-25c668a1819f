<template>
  <div>
    <div class="stateMonitorLayout">
      <!-- <div class="stateMonitorBtn">
        <div
          :class="['stateMonitorBtnsearch',btnFlag?'white':'']"
          @click="()=>{searchHandle()}"
        >
          <div class="stateMonitorBtnContent">
            查询
          </div>
        </div>
        <div :class="['stateMonitorBtnset',btnFlag?'':'white']">
          <div class="stateMonitorBtnContent">
            设置
          </div>
        </div>
      </div> -->
      <RowForm
        ref="rowForm"
        :data="data"
        :data-type="dataType"
        :form-set="{
          delCh:modSet.delCh
        }"
        @submitHandle="submitHandle"
      />
    </div>
    <BtnBottom @sendCommand="sendCommand" />
  </div>
</template>

<script>
import RowForm from '../module/rowForm.vue';
import { querysuparam, setterminalsuparam } from '@/api/system/terminalConfig.js';
import {typeRoadCondition, roadConditionDefault} from '../module/dataType';
import BtnBottom from '../module/btnBottom.vue';
export default {
  name: 'RoadCondition',
  components: {
    RowForm, BtnBottom
  },
  props: {
    phones: {
      type: [Array],
      default: () => {
        return [];
      }
    },
    changeVehicle: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      modSet: {// 复用时设置
        key: 'sbAlarmType', // 标识设置模块的字段
        value: 'rs', // 标识设置模块的值
        id: 62433, // 标识设置模块的数字值
        unMuKey: [{in: 'checkEnableStr', out: 'checkEnable'}], // 多选框的key（用于转换）unusualMultiKey
        unMuFg: 32, // 多选框‘0000’形式位数 unusualMultiFigure
        shCf: true, // searchConfirm
        delCh: true // 删除勾选框
      },
      data: {},
      dataType: {},
      searchCar: undefined, // 已查询的车 非必须
      btnFlag: false
    };
  },
  mounted () {
    this.dataType = typeRoadCondition;
    this.data = roadConditionDefault;
  },
  methods: {
    // 选车错误信息
    selectMsg () {
      let flag = this.phones.length === 1 ? 0 : this.phones.length > 1 ? 2 : 1;
      if (flag) {
        this.$message({
          type: 'error',
          message: flag > 1 ? '仅支持单辆车' : '请先选择车辆'
        });
      };
      return !flag;
    },
    sendCommand (value) {
      if (value) {
        this.$refs.rowForm.onSubmit();
      } else {
        this.searchHandle();
      }
    },
    // 查询参数 flag是否提示
    searchHandle (flag) {
      // if (!this.selectMsg()) {
      //   return;
      // }
      if (!this.changeVehicle) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      let parme = {
        // phone: this.changeVehicle.phone,
        paramId: this.modSet.id,
        device_type: this.changeVehicle.deviceType,
        device_id: BigInt(this.changeVehicle.deviceId),
      };
      querysuparam(parme).then(res => {
        if (res.code === 200) {
          !flag && this.$message({
            type: 'success',
            message: '操作成功!'
          });
          if (this.modSet.unMuKey && this.modSet.unMuKey.length > 0) {
            this.modSet.unMuKey.forEach(item => {
              res.data[item.out] = this.dealMultiple(res.data[item.in]);
            });
          }
          this.searchCar = this.changeVehicle.deviceId;
          this.data = res.data;
        }
      }).catch(err => {
        console.log(err);
      });
    },
    // 处理多选参数
    dealMultiple (val) {
      val = val.split('').reverse().join('');
      let arr = [];
      for (let i in val) {
        if (val[i] === '1') {
          arr.push(i);
        }
      }
      return arr;
    },
    // 单独处理2个特殊数据
    enableCase (value) {
      if (this.judgeUnusualKey(value)) {
        return '-1';
      } else {
        return -1;
      }
    },

    // 判断是不是要转换特殊key
    judgeUnusualKey (key) {
      let flag = false;
      this.modSet.unMuKey.forEach(k => {
        if (key === k.out) {
          flag = true;
        }
      });
      return flag;
    },

    // 处理数据
    dealDataCase (check, data, key) {
      if (check && (data || data === 0)) {
        if (this.judgeUnusualKey(key)) {
          return this.reDealMultiple(data);
        } else {
          return parseInt(data);
        }
      } else {
        return this.enableCase(key);
      }
    },
    // 提交表单
    submitHandle (data) {
      console.log('this.phones', this.phones, this.searchCar);

      if (!this.selectMsg()) {
        return;
      }
      if (this.modSet.shCf) {
        if (!this.searchCar || (this.searchCar && (this.searchCar !== this.phones[0].deviceId))) {
          this.$message({
            type: 'error',
            message: '请先查询设置参数'
          });
          return;
        }
      }
      let dataCopy = JSON.parse(JSON.stringify(this.data));
      let checkGroup = this.$refs.rowForm.checkedGrant;
      let obj = {};
      checkGroup.forEach(v => {
        for (let k in dataCopy) {
          if (k === v.key) {
            obj[k] = this.dealDataCase(v.value, dataCopy[k], v.key);
          }
        }
      });
      // obj.phone = this.phones[0];
      obj.device_type = this.phones[0].deviceType;
      obj.device_id = BigInt(this.phones[0].deviceId);
      // 音视频参数设置对照表:0x75(117)-音视频参数设置,0x76(118)-音视频通道列表设置,0x77(119)-单独视频通道参数设置
      obj[this.modSet.key] = this.modSet.value;
      setterminalsuparam(obj).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.searchHandle(true);// 重新获取数据
        }
      }).catch(err => {
        console.log(err);
      });
    },
    // 复原多选框参数
    reDealMultiple (val) {
      let str = '';
      for (let i = 0; i < this.modSet.unMuFg; i++) {
        str += '0';
      }
      str = str.split('');
      val.forEach(v => {
        str[v] = '1';
      });
      str = str.reverse().join('');
      return str;
    }
  }
};
</script>

<style scoped>
.stateMonitorLayout{
  max-height:calc(100vh - 220px) ;
  overflow: scroll;
  display: flex;
  padding-left: 10px;
}
.stateMonitorBtn{
  width: 60px;
  display: flex;
  flex-direction: column;
  padding-top: 26px;
  padding-bottom: 90px;
}
.stateMonitorBtnsearch,.stateMonitorBtnset{
  background: #409eff;
  flex: 1;
  border: 1px solid #4096d1;
  text-align: center;
  color: #fff;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.stateMonitorBtnContent{
  width: 100%;
}
.white{
  background: #fff;
  color: #000;
  cursor: default
}
</style>
