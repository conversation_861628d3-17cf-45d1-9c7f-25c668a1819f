<template>
  <div>
    <div class="stateMonitorLayout">
      <!-- <div class="stateMonitorBtn">
        <div
          :class="['stateMonitorBtnsearch',btnFlag?'white':'']"
          @click="()=>{searchHandle()}"
        >
          <div class="stateMonitorBtnContent">
            查询
          </div>
        </div>
        <div :class="['stateMonitorBtnset',btnFlag?'':'white']">
          <div class="stateMonitorBtnContent">
            设置
          </div>
        </div>
      </div> -->
      <div class="add_item">
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-circle-plus-outline"
          @click="addItemHandle"
        >
          添加
        </el-button>
      </div>
      <div
        v-for="(item,index) in form"
        :key="index"
        class="main"
      >
        <div class="main_item">
          <div class="main_label">
            逻辑通道号
          </div>
          <div class="main_value">
            {{ form[index]['channel'] }}
          </div>
        </div>
        <div class="main_item">
          <div class="main_label">
            实时流编码模式
          </div>
          <div class="main_value">
            {{ turnLabel('rtsBitType',form[index]['rtsBitType']) }}
          </div>
        </div>
        <div class="main_item">
          <div class="main_label">
            实时流分辨率
          </div>
          <div class="main_value">
            {{ turnLabel('rtsResolution',form[index]['rtsResolution']) }}
          </div>
        </div>
        <div class="main_item">
          <div class="main_label">
            实时流关键帧间隔
          </div>
          <div class="main_value">
            {{ form[index]['rtsFrame'] }}
          </div>
        </div>
        <div class="main_item">
          <div class="main_label">
            实时流目标帧率
          </div>
          <div class="main_value">
            {{ form[index]['rtsFps'] }}
          </div>
        </div>
        <div class="main_item">
          <div class="main_label">
            实时流目标码率
          </div>
          <div class="main_value">
            {{ form[index]['rtsBps'] }}
          </div>
        </div>
        <div class="main_item">
          <div class="main_label">
            存储流编码模式
          </div>
          <div class="main_value">
            {{ turnLabel('msBitType',form[index]['msBitType']) }}
          </div>
        </div>
        <div class="main_item">
          <div class="main_label">
            存储流分辨率
          </div>
          <div class="main_value">
            {{ turnLabel('msResolution',form[index]['msResolution']) }}
          </div>
        </div>
        <div class="main_item">
          <div class="main_label">
            存储流关键帧间隔
          </div>
          <div class="main_value">
            {{ form[index]['msFrame'] }}
          </div>
        </div>
        <div class="main_item">
          <div class="main_label">
            存储流目标帧率
          </div>
          <div class="main_value">
            {{ form[index]['msFps'] }}
          </div>
        </div>
        <div class="main_item">
          <div class="main_label">
            存储流目标码率
          </div>
          <div class="main_value">
            {{ form[index]['msBps'] }}
          </div>
        </div>
        <div class="main_item">
          <div class="main_label">
            OSD字幕叠加设置
          </div>
          <div class="main_value text_out">
            <el-tooltip
              class="item"
              effect="dark"
              :content="turnLabelSp('osdCoverStr',form[index]['osdCoverStr'])"
              placement="top"
            >
              <span>{{ turnLabelSp('osdCoverStr',form[index]['osdCoverStr']) }}</span>
            </el-tooltip>
          </div>
        </div>
        <div class="main_item">
          <div
            class="main_label"
          >
            操作
          </div>
          <div
            class="main_value"
          >
            <span
              class="value_btn"
              @click="editHandle(index)"
            >编辑</span>
            <!-- <span
              class="value_btn"
              @click="delHandle(index)"
            >删除</span> -->
          </div>
        </div>
      </div>
    </div>
    <BtnBottom @sendCommand="sendCommand" />

    <el-dialog
      title="单独通道视频参数"
      class="dialogForm"
      :modal-append-to-body="false"
      :visible.sync="dialogFormVisible"
    >
      <el-form
        :inline="true"
        :model="formItem"
        class="demo-form-inline"
        label-width="160px"
      >
        <el-form-item
          label="逻辑通道号"
        >
          <el-input
            v-model.number="formItem.channel"
            placeholder="逻辑通道号"
          />
        </el-form-item>

        <el-form-item label="实时流编码模式">
          <el-select
            v-model="formItem['rtsBitType']"
            placeholder="请选择"
          >
            <el-option
              v-for="(subItem,subIndex) in formOptions.rtsBitType"
              :key="subIndex"
              :label="subItem.label"
              :value="subItem.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="实时流分辨率">
          <el-select
            v-model="formItem['rtsResolution']"
            placeholder="请选择"
          >
            <el-option
              v-for="(subItem,subIndex) in formOptions.rtsResolution"
              :key="subIndex"
              :label="subItem.label"
              :value="subItem.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="实时流关键帧间隔">
          <el-input
            v-model.number="formItem['rtsFrame']"
            placeholder="实时流关键帧间隔"
          />
        </el-form-item>
        <el-form-item label="实时流目标帧率">
          <el-input
            v-model.number="formItem['rtsFps']"
            placeholder="实时流目标帧率"
          />
        </el-form-item>
        <el-form-item label="实时流目标码率">
          <el-input
            v-model.number="formItem['rtsBps']"
            placeholder="实时流目标码率"
          />
        </el-form-item>
        <el-form-item label="存储流编码模式">
          <el-select
            v-model="formItem['msBitType']"
            placeholder="请选择"
          >
            <el-option
              v-for="(subItem,subIndex) in formOptions.msBitType"
              :key="subIndex"
              :label="subItem.label"
              :value="subItem.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="存储流分辨率">
          <el-select
            v-model="formItem['msResolution']"
            placeholder="请选择存储流分辨率"
          >
            <el-option
              v-for="(subItem,subIndex) in formOptions.msResolution"
              :key="subIndex"
              :label="subItem.label"
              :value="subItem.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="存储流关键帧间隔">
          <el-input
            v-model.number="formItem['msFrame']"
            placeholder="存储流关键帧间隔"
          />
        </el-form-item>
        <el-form-item label="存储流目标帧率">
          <el-input
            v-model.number="formItem['msFps']"
            placeholder="存储流目标帧率"
          />
        </el-form-item>
        <el-form-item label="存储流目标码率">
          <el-input
            v-model.number="formItem['msBps']"
            placeholder="存储流目标码率"
          />
        </el-form-item>

        <el-form-item label="OSD字幕叠加设置">
          <el-select
            v-model="formItem['osdfront']"
            multiple
            placeholder="请选择OSD字幕叠加设置"
          >
            <el-option
              v-for="(subItem,subIndex) in formOptions.osdCoverStr"
              :key="subIndex"
              :label="subItem.label"
              :value="subItem.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="dialogFormVisible = false"
        >
          取 消
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="confirmDialog"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import RowForm from '../module/rowForm.vue';
// import { querysuparam, setterminalsuparam } from '@/api/system/terminalConfig.js';
import {typeRoadCondition, roadConditionDefault} from '../module/dataType';
import BtnBottom from '../module/btnBottom.vue';
// ==
import { querysuparam, setterminalavparam } from '@/api/system/terminalConfig.js';

export default {
  name: 'RoadCondition',
  components: {
    BtnBottom
  },
  props: {
    phones: {
      type: [Array],
      default: () => {
        return [];
      }
    },
    changeVehicle: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      dialogFormVisible: false,
      formOptions: {
        msBitType: [{
          value: 0,
          label: 'CBR(固定码率)'
        }, {
          value: 1,
          label: 'VBR(可变码率)'
        }, {
          value: 2,
          label: 'ABR(平均码率)'
        }],
        msResolution: [{
          value: 0,
          label: 'QCIF'
        }, {
          value: 1,
          label: 'CIF'
        }, {
          value: 2,
          label: 'WCIF'
        }, {
          value: 3,
          label: 'D1'
        }, {
          value: 4,
          label: 'WD1'
        }, {
          value: 5,
          label: '720P'
        }, {
          value: 6,
          label: '1080P'
        }],
        rtsBitType: [{
          value: 0,
          label: 'CBR(固定码率)'
        }, {
          value: 1,
          label: 'VBR(可变码率)'
        }, {
          value: 2,
          label: 'ABR(平均码率)'
        }],
        rtsResolution: [{
          value: 0,
          label: 'QCIF'
        }, {
          value: 1,
          label: 'CIF'
        }, {
          value: 2,
          label: 'WCIF'
        }, {
          value: 3,
          label: 'D1'
        }, {
          value: 4,
          label: 'WD1'
        }, {
          value: 5,
          label: '720P'
        }, {
          value: 6,
          label: '1080P'
        }],

        channel: [{
          value: 0,
          label: '音视频'
        }, {
          value: 1,
          label: '音频'
        }, {
          value: 2,
          label: '视频'
        }],
        cloud: [{
          value: 0,
          label: '未连接'
        }, {
          value: 1,
          label: '连接'
        }],
        osdCoverStr: [{
          value: '0',
          label: '日期和时间'
        }, {
          value: '1',
          label: '车牌号码'
        }, {
          value: '2',
          label: '逻辑通道号'
        }, {
          value: '3',
          label: '经纬度'
        }, {
          value: '4',
          label: '行驶记录速度'
        }, {
          value: '5',
          label: '卫星定位速度'
        }, {
          value: '6',
          label: '连续驾驶时间'
        }]
      },

      form: [],
      formEmpty: {
        'channel': '',
        'rtsBitType': '',
        'rtsResolution': '',
        'rtsFrame': '',
        'rtsFps': '',
        'rtsBps': '',
        'msBitType': '',
        'msResolution': '',
        'msFrame': '',
        'msFps': '',
        'msBps': '',
        'osdCover': null,
        'osdCoverStr': ''
      },
      // 表单form
      formItem: {
        'channel': 2,
        'rtsBitType': 0,
        'rtsResolution': 3,
        'rtsFrame': 10,
        'rtsFps': 10,
        'rtsBps': 441,
        'msBitType': 0,
        'msResolution': 5,
        'msFrame': 10,
        'msFps': 10,
        'msBps': 441,
        'osdCover': 127,
        'osdCoverStr': '0000000001111111',
        'osdfront': []
      },

      postConfig: {
        'phone': '22090040012',
        'av_type': 119,
        'separate_video_channel_param': {
          'channel_num': 3,
          'channel_list': []
        }
      },

      // ==
      modSet: {// 复用时设置
        key: 'sbAlarmType', // 标识设置模块的字段
        value: 'rs', // 标识设置模块的值
        id: 119, // 标识设置模块的数字值
        unMuKey: [{in: 'checkEnableStr', out: 'checkEnable'}], // 多选框的key（用于转换）unusualMultiKey
        unMuFg: 32, // 多选框‘0000’形式位数 unusualMultiFigure
        shCf: true, // searchConfirm
        delCh: true // 删除勾选框
      },
      data: {},
      dataType: {},
      searchCar: undefined, // 已查询的车 非必须
      btnFlag: false
    };
  },
  mounted () {
    this.dataType = typeRoadCondition;
    this.data = roadConditionDefault;
    let obj = JSON.parse(JSON.stringify(this.formEmpty));
    this.form.push(obj);
  },
  methods: {
    confirmDialog () {
      this.formItem.osdCoverStr = this.turnArr(this.formItem.osdfront);

      let index = this.formItem.index;
      delete this.formItem.index;
      delete this.formItem.osdfront;

      if (index !== undefined) {
        this.form[index] = JSON.parse(JSON.stringify(this.formItem));
      } else {
        this.form.push(JSON.parse(JSON.stringify(this.formItem)));
      }
      this.dialogFormVisible = false;
    },
    addItemHandle () {
      this.formItem = JSON.parse(JSON.stringify(this.formEmpty));
      this.dialogFormVisible = true;
    },
    editHandle (index) {
      this.formItem = JSON.parse(JSON.stringify(this.form[index]));

      console.log('iii{', index, this.formItem);

      // this.formItem.osdfront = this.turnStr(this.formItem.osdCoverStr);
      this.$set(this.formItem, 'osdfront', this.turnStr(this.formItem.osdCoverStr));
      this.formItem.index = index;
      this.dialogFormVisible = true;
    },
    delHandle (index) {
      this.form.splice(index, 1);
    },
    turnLabel (key, value) {
      let str = '';
      this.formOptions[key].forEach(item => {
        if (item.value === value) {
          str = item.label;
        }
      });
      return str;
    },
    turnLabelSp (key, value) {
      let str = '';
      let arr = value.split('');
      arr.reverse();
      arr.forEach((item, index) => {
        if (item === '1') {
          str += this.formOptions[key][index] ? this.formOptions[key][index].label + ',' : '未定义,';
        }
      });
      if (str[str.length - 1] === ',') {
        str = str.substring(0, str.length - 1);
      }
      return str;
    },
    turnStr (str) {
      let arr = str.slice(-7).split(''); // 只保留用户选择的7个选项
      arr.reverse();
      let sel = [];
      arr.forEach((item, index) => {
        if (item === '1') {
          sel.push('' + index);
        }
      });
      return sel;
    },
    turnArr (arr) {
      let arrAll = [];
      for (let i = 0; i < 16; i++) {
        arrAll.push('0');
      }

      arr.forEach(item => {
        arrAll[+item] = '1';
      });
      arrAll.reverse();

      let str = arrAll.join('');
      return str;
    },
    addHandle () {
      this.dialogFormVisible = true;
      // let obj = JSON.parse(JSON.stringify(this.formItem));
      // this.form.push(obj);
    },
    reduceHandle () {

    },
    // 查询
    searchWay () {
      // if (!this.selectMsg()) {
      //   return;
      // }
      if (!this.changeVehicle) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      let parme = {
        // phone: this.changeVehicle.phone,
        paramId: this.modSet.id,
        device_type: this.changeVehicle.deviceType,
        device_id: BigInt(this.changeVehicle.deviceId),
      };
      querysuparam(parme).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.searchCar = this.changeVehicle.deviceId;
          let arr = JSON.parse(JSON.stringify(res.data.channelList));
          this.form = arr;
        }
      }).catch(err => {
        console.log(err);
      });
    },
    // 提交
    submitWay () {
      if (!this.selectMsg()) {
        return;
      }
      if (this.modSet.shCf) {
        if (!this.searchCar || (this.searchCar && (this.searchCar !== this.phones[0].deviceId))) {
          this.$message({
            type: 'error',
            message: '请先查询设置参数'
          });
          return;
        }
      }
      let postObj = JSON.parse(JSON.stringify(this.postConfig));
      delete postObj.phone;
      postObj.device_type = this.phones[0].deviceType;
      postObj.device_id = BigInt(this.phones[0].deviceId);
      postObj['separate_video_channel_param']['channel_list'] = JSON.parse(JSON.stringify(this.form));
      setterminalavparam(postObj).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.searchHandle(true);// 重新获取数据
        }
      }).catch(err => {
        console.log(err);
      });
    },
    sendCommand (value) {
      if (value) {
        this.submitWay();
      } else {
        this.searchWay();
      }
    },

    /*! !!!! */

    // 选车错误信息
    selectMsg () {
      let flag = this.phones.length === 1 ? 0 : this.phones.length > 1 ? 2 : 1;
      if (flag) {
        this.$message({
          type: 'error',
          message: flag > 1 ? '仅支持单辆车' : '请先选择车辆'
        });
      }
      return !flag;
    },
    // sendCommand (value) {
    //   if (value) {
    //     this.$refs.rowForm.onSubmit();
    //   } else {
    //     this.searchHandle();
    //   }
    // },
    // 查询参数 flag是否提示
    searchHandle (flag) {
      // if (!this.selectMsg()) {
      //   return;
      // }
      if (!this.changeVehicle) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      let parme = {
        // phone: this.changeVehicle.phone,
        paramId: this.modSet.id,
        device_type: this.changeVehicle.deviceType,
        device_id: BigInt(this.changeVehicle.deviceId),
      };
      querysuparam(parme).then(res => {
        if (res.code === 200) {
          !flag && this.$message({
            type: 'success',
            message: '操作成功!'
          });
          if (this.modSet.unMuKey && this.modSet.unMuKey.length > 0) {
            this.modSet.unMuKey.forEach(item => {
              res.data[item.out] = this.dealMultiple(res.data[item.in]);
            });
          }
          this.searchCar = this.changeVehicle.deviceId;
          this.data = res.data;
        }
      }).catch(err => {
        console.log(err);
      });
    },
    // 处理多选参数
    dealMultiple (val) {
      val = val.split('').reverse().join('');
      let arr = [];
      for (let i in val) {
        if (val[i] === '1') {
          arr.push(i);
        }
      }
      return arr;
    },
    // 单独处理2个特殊数据
    enableCase (value) {
      if (this.judgeUnusualKey(value)) {
        return '-1';
      } else {
        return -1;
      }
    },

    // 判断是不是要转换特殊key
    judgeUnusualKey (key) {
      let flag = false;
      this.modSet.unMuKey.forEach(k => {
        if (key === k.out) {
          flag = true;
        }
      });
      return flag;
    },

    // 处理数据
    dealDataCase (check, data, key) {
      if (check && (data || data === 0)) {
        if (this.judgeUnusualKey(key)) {
          return this.reDealMultiple(data);
        } else {
          return parseInt(data);
        }
      } else {
        return this.enableCase(key);
      }
    },
    // 提交表单
    // submitHandle (data) {
    //   console.log('this.phones', this.phones, this.searchCar);

    //   if (!this.selectMsg()) {
    //     return;
    //   }
    //   if (this.modSet.shCf) {
    //     if (!this.searchCar || (this.searchCar && (this.searchCar !== this.phones[0].deviceId))) {
    //       this.$message({
    //         type: 'error',
    //         message: '请先查询设置参数'
    //       });
    //       return;
    //     }
    //   }
    //   let dataCopy = JSON.parse(JSON.stringify(this.data));
    //   let checkGroup = this.$refs.rowForm.checkedGrant;
    //   let obj = {};
    //   checkGroup.forEach(v => {
    //     for (let k in dataCopy) {
    //       if (k === v.key) {
    //         obj[k] = this.dealDataCase(v.value, dataCopy[k], v.key);
    //       }
    //     }
    //   });
    //   obj.phone = this.phones[0];
    //   // 音视频参数设置对照表:0x75(117)-音视频参数设置,0x76(118)-音视频通道列表设置,0x77(119)-单独视频通道参数设置
    //   obj[this.modSet.key] = this.modSet.value;
    //   setterminalsuparam(obj).then(res => {
    //     if (res.code === 200) {
    //       this.$message({
    //         type: 'success',
    //         message: '操作成功!'
    //       });
    //       this.searchHandle(true);// 重新获取数据
    //     }
    //   }).catch(err => {
    //     console.log(err);
    //   });
    // },
    // 复原多选框参数
    reDealMultiple (val) {
      let str = '';
      for (let i = 0; i < this.modSet.unMuFg; i++) {
        str += '0';
      }
      str = str.split('');
      val.forEach(v => {
        str[v] = '1';
      });
      str = str.reverse().join('');
      return str;
    }
  }
};
</script>

<style scoped>
.stateMonitorLayout{
  max-height:calc(100vh - 220px) ;
  /* overflow: scroll; */
  overflow-x: scroll;
  /* display: flex; */
  padding-left: 10px;
}
.stateMonitorBtn{
  width: 60px;
  display: flex;
  flex-direction: column;
  padding-top: 26px;
  padding-bottom: 90px;
}
.stateMonitorBtnsearch,.stateMonitorBtnset{
  background: #409eff;
  flex: 1;
  border: 1px solid #4096d1;
  text-align: center;
  color: #fff;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.stateMonitorBtnContent{
  width: 100%;
}
.white{
  background: #fff;
  color: #000;
  cursor: default
}
/* == */
.main{
  display: flex;
  /* width: 100%; */
  min-width: 1800px;
  text-align: center;
  overflow-x: scroll;
}
.main_item{
  width: 140px;
  /* flex: 1; */
  margin-bottom: 10px;
}
.main_item .main_label{
  background-color:#e1e5ee ;
  height: 30px;
  line-height: 30px;
  border: 1px solid #c1c9da;
  border-bottom: none;
}
.main_item .main_value{
  background-color:#f2f2f2 ;
  height: 35px;
  line-height: 35px;
  border: 1px solid #c1c9da;
}
.value_btn{
  border: 1px solid #aebac5;
  padding: 3px 5px;
  background-color: #fff;
  margin: 5px;
  cursor: pointer;
}
.add_item{
  padding: 5px;
}
.text_out{
  overflow: hidden;
}
.dialogForm ::v-deep.el-input__inner{
  width: 222px;
}
</style>
