<template>
  <div>
    <div class="stateMonitorLayout">
      <div class="work_item_room">
        <div>
          <div
            class="header3_title"
          >
            参数设置
          </div>
          <div
            v-for="(item, index) in configData"
            :key="index"
            class="work_item_box"
          >
            <span class="work_item_title">{{ item.label }}：</span>
            <el-input
              v-model.number="item.value"
              onkeyup="value=value.replace(/[^0-9.-]/g,'')"
              class="work_item_value"
              size="mini"
            />
          </div>
          <div class="work_item_button">
            <el-button
              size="mini"
              type="primary"
              @click="queryTerminal"
            >
              查询
            </el-button>
            <el-button
              size="mini"
              type="primary"
              @click="setTerminal"
            >
              设置
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { setTerminalParam, queryTerminalParam } from '@/api/system/terminalConfig.js';
export default {
  name: 'SpecialThresholdConfig',
  props: {
    phones: {
      type: [Array],
      default: () => {
        return [];
      }
    },
    changeVehicle: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      configData: [
        { label: '终端内部低温阈值', value: '', param: 'dev_internal_low_thresh', id: 65523 },
        { label: '终端内部高温阈值', value: '', param: 'dev_internal_high_thresh', id: 65524 },
        { label: '集装箱内部低温阈值', value: '', param: 'container_internal_low_thresh', id: 65525 },
        { label: '集装箱内部高温阈值', value: '', param: 'container_internal_high_thresh', id: 65526 },
        { label: '人体低温阈值', value: '', param: 'body_low_thresh', id: 65527 },
        { label: '人体高温阈值', value: '', param: 'body_high_thresh', id: 65528 },
        { label: '登高阈值', value: '', param: 'climb_thresh', id: 65529 },
      ]
    };
  },
  methods: {
    // 查询
    queryTerminal() {
      if (!this.changeVehicle) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      let params = {
        device_type: this.changeVehicle.deviceType,
        device_id: BigInt(this.changeVehicle.deviceId),
        param_ids: this.configData.map(item => item.id)
      };
      queryTerminalParam(params).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          // 处理获得的数据
          this.configData.forEach(item => {
            item.value = res.data.Params[item.param];
          });
        }
      });
    },
    // 设置
    setTerminal() {
      if (this.phones.length === 0) {
        this.$message({
          type: 'error',
          message: '请先选择终端!'
        });
        return;
      }
      let parmes = {
        device_id: BigInt(this.phones[0].deviceId),
        device_type: this.phones[0].deviceType
      };
      this.configData.forEach(item => {
        parmes[item.param] = item.value;
      });
      setTerminalParam(parmes).then(res => {
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
        }
      }).catch(err => {
        console.log(err);
      });
    }
  }
};
</script>

<style scoped>
.stateMonitorLayout{
  max-height:calc(100vh - 220px) ;
  overflow: scroll;
  display: flex;
  padding-left: 10px;
}
  .header3_title{
    font-size: 14px;
    font-weight: bold;
    margin-left: 15px;
  }
  .work_item_room{
    width: 100%;
    display: flex;
    flex-flow: row wrap;
    justify-content: center;
  }
  .work_item_box{
    margin: 2px;
    width: 70vh;
    display: flex;
    align-items: center;
    margin-right: 8px;
    margin-left: 30px;
  }
  .work_item_title{
    width: 30%;
    font-size: 14px;
    color: #909399;
  }
  .work_item_value {
    color: #303133;
    font-size: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    }
  .work_item_button{
    margin: 5px;
    float: right;
  }
</style>
