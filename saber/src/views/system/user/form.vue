<template>
  <el-dialog
    v-dialog-drag
    :close-on-click-modal="false"
    :before-close="crud.cancelCU"
    :visible="crud.status.cu > 0"
    :title="btnShow ? crud.status.title : '查看'"
    append-to-body
    width="60%"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      class="rewriting-form-disable"
      :hide-required-asterisk="!btnShow"
      label-width="155px"
    >
      <el-row
        span="24"
        type="flex"
      >
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="登录账号"
            prop="account"
          >
            <el-input
              v-model.trim="form.account"
              placeholder="请输入登录账号"
              maxlength="20"
              style="width: 100%"
              :disabled="!crud.status.title.includes('新增')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="用户平台"
            prop="userType"
          >
            <single-select
              :options="dict.userType"
              v-model="form.userType"
              placeholder="请选择用户平台"
              filterable
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          v-if="crud.status.title.includes('新增')"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="密码"
            prop="password"
          >
            <el-input
              v-model.trim="form.password"
              placeholder="请输入密码"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div
          v-if="crud.status.title.includes('新增')"
          class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12"
        >
          <el-form-item
            label="确认密码"
            prop="password2"
          >
            <el-input
              v-model.trim="form.password2"
              placeholder="请输入确认密码"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="用户昵称"
            prop="name"
          >
            <el-input
              v-model.trim="form.name"
              placeholder="请输入用户昵称"
              style="width: 100%"
              maxlength="20"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="用户姓名"
            prop="realName"
          >
            <el-input
              v-model.trim="form.realName"
              placeholder="请输入用户姓名"
              maxlength="35"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="手机号码"
            prop="phone"
          >
            <el-input
              v-model.trim="form.phone"
              placeholder="请输入手机号码"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="账号状态"
            prop="status"
          >
            <single-select
              v-model="form.status"
              :disabled="!btnShow"
              placeholder="请选择账号状态"
              :options="dict.accountStatus1"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="电子邮箱"
            prop="email"
          >
            <el-input
              v-model.trim="form.email"
              placeholder="请输入电子邮箱"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="用户性别"
            prop="sex"
          >
            <single-select
              v-model="form.sex"
              filterable
              placeholder="请选择用户性别"
              :options="dict.sex"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="用户生日"
            prop="birthday"
          >
            <el-date-picker
              v-model="form.birthday"
              :disabled="!btnShow"
              type="date"
              value-format="yyyy-MM-dd hh:mm:ss"
              size="small"
              placeholder="请选择用户生日"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="用户编号"
            prop="code"
          >
            <el-input
              v-model.trim="form.code"
              placeholder="请输入用户编号"
              maxlength="20"
              style="width: 100%"
              :disabled="!btnShow"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="所属角色"
            prop="roleId"
          >
            <el-cascader
              v-model="roleId"
              :options="roleGrantList"
              :show-all-levels="false"
              :disabled="!btnShow"
              :props="{ multiple: true, checkStrictly: true, label: 'title', value: 'id' }"
              clearable
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="所属机构"
            prop="deptId"
          >
            <DeptFormMultiSelect
              ref="deptIdsRef"
              v-model="deptIds"
              :disabled="!btnShow"
              :no-req="true"
              :loadingP="loadingDept"
              :deptOptionsP="deptOptions"
              :is-show="crud.status.cu > 0"
              placeholder="请选择所属机构"
              size="small"
              checkStrictly
              @input="validateTreeSelect('deptIds')"
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="所属岗位"
            prop="postId"
          >
            <el-cascader
              v-model="postId"
              :options="postList"
              :show-all-levels="false"
              :disabled="!btnShow"
              :props="{ multiple: true, checkStrictly: true, label: 'postName', value: 'id' }"
              clearable
            />
          </el-form-item>
        </div>
        <div class="el-col el-col-12 el-col-offset-0 el-col-xs-24 el-col-sm-12 el-col-md-12">
          <el-form-item
            label="监管机构"
            prop="monitDeptId"
          >
            <DeptFormMultiSelect
              ref="mDeptIdsRef"
              v-model="mDeptIds"
              :disabled="!btnShow"
              :no-req="true"
              :is-show="crud.status.cu > 0"
              :loadingP="loadingDept"
              :deptOptionsP="deptOptions"
              placeholder="请选择所属机构"
              size="small"
              checkStrictly
              @input="validateTreeSelect('mDeptIds')"
            />
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        v-if="btnShow"
        size="small"
        @click="crud.cancelCU"
      >
        取消
      </el-button>
      <el-button
        v-if="btnShow"
        :loading="crud.status.cu === 2"
        type="primary"
        size="small"
        @click="handleSubmit"
      >
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import CRUD, { form } from "@/components/Crud/crud";
import { passwordRuleUpdate } from '@/utils/validate';
import DeptFormMultiSelect from '@/components/select/DeptFormMultiSelect/DeptFormMultiSelect.vue';
import { getDeptPerInit } from '@/api/base/dept';
import website from '@/config/website';
import { getRoleTree } from "@/api/system/role";
import SingleSelect from '@/components/select/DictSelect/DictSelectSingle';

const defaultForm = {
  account: "",
  userType: "",
  password: "",
  password2: "",
  name: "",
  realName: "",
  phone: "",
  email: "",
  sex: "",
  birthday: "",
  code: "",
  roleId: "",
  deptId: "",
  postId: "",
  monitDeptId: "",
  id: null,
  status: 1
};
export default {
  components: {
    DeptFormMultiSelect,
    SingleSelect,
  },
  mixins: [form(defaultForm)],
  props: {
    btnShow: {
      type: Boolean,
      default: true,
    },
    dict: {
      type: Object,
      default: () => ({})
    },
    postList: {
      type: Array,
      required: true,
    },
  },
  data () {
    return {
      rules: {
        account: { required: true, trigger: "blur" },
        userType: { required: true, trigger: "blur" },
        password: { required: true, trigger: "blur" },
        password2: { required: true, trigger: "blur" },
        name: { required: true, trigger: "blur" },
        realName: { required: true, trigger: "blur" },
        roleId: { required: true, trigger: "blur" },
        deptId: { required: true, trigger: "blur" },
        postId: { required: true, trigger: "blur" },
        monitDeptId: { required: true, trigger: "blur" },
      },
      roleId: [],
      postId: [],
      deptIds: [],
      mDeptIds: [],
      loadingDept: false,
      deptOptions: [],
      roleGrantList: [],
      oldMDepts: [],
      // cacheEditDepts: [],
    };
  },
  watch: {
    "crud.status.cu" (val) {
      if (val > 0) {
        if (this.form.id) {
          this.roleId = this.form.roleId.split(',');
          this.postId = this.form.postId.split(',');
          this.mDeptIds = this.form.monitDeptId.split(',') || [];
          this.deptIds = this.form.deptId.split(',') || [];
          console.log(this.mDeptIds, this.deptIds);
          this.oldMDepts = this.form.monitDeptId;
        } else {
          this.roleId = [];
          this.postId = [];
          this.deptIds = [];
          this.mDeptIds = [];
        }
        this.getRoles();
        this.$nextTick(() => {
          if(this.btnShow) {
            this.getDept();
          } else {
            let list = [];
            if(this.form.deptId) {
              const ids = this.form.deptId.split(',');
              const names = this.form.deptName?.split(',');
              ids.forEach((id, i) => {
                list.push({id, title: names[i]});
              });
            }
            if(this.form.monitDeptId) {
              const ids = this.form.monitDeptId.split(',');
              const names = this.form.monitDeptName?.split(',') || [];
              ids.forEach((id, i) => {
                if(!list.find( item => item.id !== id)) {
                  list.push({id, title: names[i]});
                }
              });
            }
            this.deptOptions = list;
          }
        });
      }
    },
  },
  methods: {
    getRoles() {
      getRoleTree(website.tenantId).then(res => {
        this.roleGrantList = res.data || [];
      });
    },
    getDept() {
      this.loadingDept = true;
      getDeptPerInit().then(res => {
        this.deptOptions = res.data;
        this.loadingDept = false;
      }).catch(() => {
        this.loadingDept = false;
      });
    },
    handleSubmit () {
      this.form.deptId = this.deptIds.join(',');
      this.form.monitDeptId = this.mDeptIds.join(',');
      this.form.roleId = this.roleId.join(',');
      this.form.postId = this.postId.join(',');
      this.crud.submitCU();
    },
    /**
     * 提交前的验证
     */
    [CRUD.HOOK.afterValidateCU] () {
      if (!this.form.id && !passwordRuleUpdate.test(this.form.password)) {
        this.$message.error("密码必须8到20位, 且包含数字、大小写字母、特殊符号!");
        return false;
      } else if (this.form.contactEmail && !email.test(this.form.contactEmail)) {
        this.$message.error("邮箱不正确!");
        return false;
      } else if (this.form.contactPhone && !phoneNum.test(this.form.contactPhone)) {
        this.$message.error("联系电话不正确!");
        return false;
      }
      if(this.form.password2 !== this.form.password) {
        this.$message.error("密码与确认密码不一致!");
        return false;
      }
      return true;
    },
    /** 点击取消之前*/
    [CRUD.HOOK.afterEditCancel] () {
      this.$emit("cancelCU");
    },
    // 新增/编辑前
    [CRUD.HOOK.beforeToCU] () {
      this.$refs.form && this.$refs.form.clearValidate();
    },
    // 提交之后
    [CRUD.HOOK.afterSubmit] () {
      if ((this.oldMDepts !== this.mDeptIds.join(',')) && this.oldMDepts.length !== 0) {
        this.$message.warning('修改用户监管机构后, 此用户需要刷新页面重新获取数据权限！');
      }
      this.oldMDepts = [];
    },
    /**
     * 验证treeSelect项
     * @param {String} item
     */
    validateTreeSelect (item) {
      this.$refs.form.validateField(item, (valid) => {
        this.$refs[`${item}Ref`].$refs[`${item}StrRef`].$children[0].$el.style.borderColor = valid !== '' ? '#F56C6C' : '#BFBFBF';
      });
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .el-input__inner {
  text-align: left;
}

/deep/ .el-form-item__label {
  font-weight: 400;
}

/deep/ .el-icon-delete {
  font-size: 30px;
}
</style>
