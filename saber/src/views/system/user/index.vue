<template>
  <el-row class="use-manage-content">
    <el-col
      :span="4"
      class="user-container tree-container"
    >
      <div class="avue-tree-left">
        <div class="avue-tree-left-content" v-loading="treeLoad">
          <el-input
          placeholder="输入关键字进行过滤"
          size="middle"
          v-model="filterDept"
          style="margin-bottom: 4px"
          @input="(val) => this.$refs.treeDom.filter(val)"
        />
        <vue-easy-tree
          class="content"
          ref="treeDom"
          height="calc(100% - 20px)"
          :defaultExpandedKeys="[]"
          :data="orgTreeData"
          node-key="id"
          :item-size="40"
          :props="defaultProps"
          :filter-node-method="filterTermNode"
          @node-click="nodeClick"
        >
          <span
            slot-scope="{ node, data }"
            class="custom-tree-node"
          >
          <span :title="node.label">
              {{ node.label }}
            </span>
          </span>
      </vue-easy-tree>
        </div>
      </div>
    </el-col>
    <div
      class="xh-container"
    >
      <div class="scroll-box">
          <div class="head-container">
            <HeadCommon
              :dict="dict"
              :head-config="headConfig"
              label-width="80px"
              @reloadPage="initData"
            >
            </HeadCommon>
        </div>
        <div class="xh-crud-table-container">
          <crudOperation
            :permission="batchPer"
            :download="false"
          >
            <template slot="right">
              <el-button
                v-if="permission.role"
                size="small"
                icon="el-icon-user"
                @click="handleGrant"
              >角色配置
              </el-button>
              <el-button
                v-if="permission.reset"
                size="small"
                icon="el-icon-refresh"
                @click="handleReset"
              >密码重置
              </el-button>
              <el-button
                v-if="userInfo.role_name.includes('admin')"
                size="small"
                icon="el-icon-coordinate"
                @click="handleLock(true)"
              >账号禁用
              </el-button>
              <el-button
                v-if="userInfo.role_name.includes('admin')"
                size="small"
                icon="el-icon-coordinate"
                @click="handleLock(false)"
              >账号解封
              </el-button>
              <el-button
                v-if="userInfo.role_name.includes('admin')"
                class="filter-item"
                icon="el-icon-upload2"
                size="small"
                @click="batchvisible = true"
              >
                导入
              </el-button>
              <el-button
                v-if="userInfo.role_name.includes('admin')"
                :loading="crud.downloadLoading"
                class="filter-item"
                size="small"
                icon="el-icon-download"
                @click="handleExport"
              >
                导 出
              </el-button>
            </template>
          </crudOperation>
          <el-table
            ref="table"
            v-loading="crud.loading"
            :header-cell-style="{background:'#e1e5ee','text-align':'center'}"
            :data="crud.data"
            :cell-style="{'text-align':'center'}"
            :max-height="tableMaxHeight"
            style="width: 100%;height: calc(100% - 74px);"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column
              type="selection"
              width="50"
            />
            <el-table-column
              v-permission="['admin','expatriate:edit','expatriate:del']"
              width="130"
              label="操作"
              align="center"
              fixed="right"
            >
              <template slot-scope="scope">
                <udOperation
                  :data="scope.row"
                  :permission="permission"
                >
                  <template slot="right">
                    <el-button
                      type="text"
                      size="small"
                      class="table-button-view"
                      @click="toDetails(scope.row)"
                    >
                      详情
                    </el-button>
                  </template>
                </udOperation>

              </template>
            </el-table-column>
            <el-table-column
              v-if="columns.visible('account')"
              label="登录账号"
              :show-overflow-tooltip="true"
              min-width="100"
              prop="account"
            />
            <el-table-column
              v-if="columns.visible('realName')"
              label="用户姓名"
              :show-overflow-tooltip="true"
              prop="realName"
              min-width="120"
            />
            <el-table-column
              v-if="columns.visible('roleName')"
              label="所属角色"
              :show-overflow-tooltip="true"
              min-width="150"
              prop="roleName"
            />
            <el-table-column
              v-if="columns.visible('deptName')"
              :show-overflow-tooltip="true"
              label="所属机构"
              min-width="150"
              prop="deptName"
            />
            <el-table-column
              v-if="columns.visible('userTypeName')"
              label="用户平台"
              :show-overflow-tooltip="true"
              min-width="100"
              prop="userTypeName"
            />
            <el-table-column
              v-if="columns.visible('statusName')"
              label="账号状态"
              :show-overflow-tooltip="true"
              min-width="100"
              prop="statusName"
            >
            </el-table-column>
            <el-table-column
              v-if="columns.visible('postName')"
              label="所属岗位"
              :show-overflow-tooltip="true"
              min-width="150"
              prop="postName"
            />
            <el-table-column
              v-if="columns.visible('monitDeptName')"
              label="监管机构"
              :show-overflow-tooltip="true"
              min-width="150"
              prop="monitDeptName"
            />
            <el-empty
              slot="empty"
              :image="require('@/assets/images/nodata.png')"
            />
          </el-table>
          <!--分页组件-->
          <pagination />
        </div>
      </div>
    </div>
    <el-dialog
      title="用户角色配置"
      append-to-body
      :visible.sync="roleBox"
      width="345px"
    >

      <el-tree
        ref="treeRole"
        :data="roleGrantList"
        show-checkbox
        check-strictly
        default-expand-all
        node-key="id"
        :default-checked-keys="roleTreeObj"
        :props="defaultProps"
      />

      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="small"
          @click="roleBox = false"
        >取 消</el-button>
        <el-button
          type="primary"
          size="small"
          @click="submitRole"
        >确 定</el-button>
      </span>
    </el-dialog>
    <!--表单渲染-->
    <eForm
      :btnShow="btnShow"
      :postList="postList"
      :regTurn="regTurn"
      :regTurnM="regTurnM"
      :dict="dict"
      @cancelCU="cancel"
    />
    <BatchImport
      :visible="batchvisible"
      mod="user"
      @close="batchvisible = false"
      @getBatchData="getBatchData"
    />
    <MsgDialog
      ref="msgDialog"
      :msg-data="msgData"
    />
  </el-row>
</template>

<script>
import CRUD, { presenter } from "@/components/Crud/crud";
import crudOperation from "@/components/Crud/CRUD.operation";
import pagination from "@/components/Crud/Pagination";
import HeadCommon from "@/components/formHead/headCommon.vue";
import udOperation from "@/components/Crud/UD.operation";
import eForm from "./form";
import api, {
  addbatch,
  grant,
  resetPassword, lock, unlock
} from "@/api/system/userNew";
import { getRoleTree } from "@/api/system/role";
import { getPostList } from "@/api/system/post";
import { mapGetters } from "vuex";
import website from '@/config/website';
import 'nprogress/nprogress.css';
import VueEasyTree from "@wchbrad/vue-easy-tree";
import { getDictionary } from '@/api/system/dictNew'
import BatchImport from '@/components/upload/batchImport.vue';
import MsgDialog from '@/components/importErr'
import { email, phoneNum } from "@/utils/validate";
import { getDeptPerInit } from '@/api/base/dept';

// crud交由presenter持有
const crud = CRUD({
  title: "", // 车辆
  crudMethod: { ...api }
});

export default {
  name: "UserIndex",
  components: {
    crudOperation,
    pagination,
    HeadCommon,
    VueEasyTree,
    udOperation,
    eForm,
    BatchImport,
    MsgDialog
  },
  mixins: [presenter(crud)],
  // 数据字典
  dicts: ['accountStatus1', 'sex'],
  data () {
    this.regTurn = {
      contactEmail: email,
      contactPhone: phoneNum,
    }
    this.regTurnM = {
      contactEmail: '邮箱不正确',
      contactPhone: '联系电话不正确',
    }
    return {
      treeLoad: false,
      headConfig: {
        initQuery: false,
        item: {
          1: {
            name: "登录账号",
            type: "input",
            value: "account",
          },
          2: {
            name: "用户姓名",
            type: "input",
            value: "realName",
          },
          3: {
            name: "用户平台",
            type: 'select',
            value: 'userType',
            dictsOptions: 'userType',
          },
        },
        button: {},
      },
      orgTreeData: [],
      filterDept: "",
      defaultProps: {
        children: 'children',
        label: 'title',
        value: 'id'
      },
      batchPer: {
        add: ['admin', 'user_add'],
        edit: ['admin', 'user_edit'],
        del: ['admin', 'user_delete'],
        role: ['admin', 'user_role'],
        reset: ['admin', 'user_reset'],
      },
      permission: {
        add: [
          'admin',
          'user_add'
        ],
        edit: [
          'admin',
          'user_edit'
        ],
        del: [
          'admin',
          'user_delete'
        ],
        role: [
          'admin',
          'user_role'
        ],
        reset: [
          'admin',
          'user_reset'
        ],
      },
      roleBox: false,
      btnShow: true, // 显示确认取消按钮
      postList: [],
      roleGrantList: [],
      // 批量引入相关
      batchvisible: false,
      msgData: [], // 批量导入提示消息
      numKey: undefined, // 递归中继值
      dataType: { // excel里文字对应的key
        '登录账号': 'account',
        '用户平台': 'userType',
        '密码': 'password',
        '确认密码': 'password2',
        '用户昵称': 'name',
        '用户姓名': 'realName',
        '手机号码': 'phone',
        '电子邮箱': 'email',
        '用户性别': 'sex',
        '用户生日': 'birthday',
        '用户编号': 'code',
        '所属角色': 'roleId',
        '所属机构': 'deptId',
        '所属岗位': 'postId',
        '监管机构': 'monitDeptId',
      },
      // 必填项
      typeRequired: ['account', 'userType', 'password', 'password2', 'name', 'realName', 'roleId', 'deptId', 'postId', 'monitDeptId'],
      // 字符串项
      typeString: [],
      // 表单名称对应字典(表单名称与字典名称不一致时)
      typeDictName: {
      },
      tipsKey: [], // 提示点集合
    };
  },
  watch: {
    batchvisible(val) {
      if (val) {
        getDeptPerInit().then(({ data }) => {
          const obj = {}
          const handleDeptMap = (list, obj) => {
            if(list.length) {
              list.map( item => {
                obj[item.title] = item.id
                if(item.children) {
                  handleDeptMap(item.children, obj)
                }
              })
            }
          }
          handleDeptMap(data, obj)
          this.deptIdMap = obj
        });
        this.getRoles(true)
      }
    }
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  created () {
    getDictionary({ code: 'user_type' }).then(res => {
      let list = res.data || []
      const obj = {}
      if (list.length) {
        list = list.map(item => {
          obj[item.dictKey] = item.dictValue
          return { value: item.dictKey, label: item.dictValue }
        })
      }
      this.$set(this.dict, 'userType', list);
      this.$set(this.dict.dict, 'userType', obj);
    })
    this.initData()
  },
  methods: {
    handleExport () {
      crud.toQuery()
      const ids =crud.selections?.length ? crud.selections.map( item => item.id) : null
      crud.doExport(ids);
    },
    nodeClick(data) {
      if (this.treeDeptId === data.id) {
        this.$refs.treeDom.setCurrentKey(null)
        this.treeDeptId = ''
      } else {
        this.treeDeptId = data.id;
      }
      this.$set(crud.query, 'deptId', this.treeDeptId);
      crud.toQuery();
    },
    /** 导出 - 之前 */
    [CRUD.HOOK.beforeExport]() {
      // 获取当前选中的列
      const columnList = Object.keys(this.crud.props.tableColumns);
      let list = [];
      // 获取当前选中的字段名
      this.crud.query.columnNameList = columnList.filter((key) => this.crud.props.tableColumns[key].visible === true);
      // 获取当前选中的中文名称
      for (let index = 0; index < columnList.length; index++) {
        const element = columnList[index];
        if (this.crud.props.tableColumns[element].visible === true) {
          list.push(this.crud.props.tableColumns[element].label);
        }
      }
      this.crud.query.headNameList = list;
    },
    getRoles(needMap) {
      getRoleTree(website.tenantId).then(res => {
        this.roleGrantList = res.data || []
        if(needMap) {
          const obj = {}
          const fun = (list) => {
            list.forEach( item => {
              obj[item.title] = item.id
              if(item.children?.length) {
                fun(item.children)
              }
            })
          }
          if(this.roleGrantList.length) {
            fun(this.roleGrantList)
          }
          this.roleMap = obj
        } else {
          this.roleBox = true
        }
      });
    },
    initData() {
      this.treeLoad = true
      getDeptPerInit({ tenantId: website.tenantId, isRefresh: true }).then(({ data }) => {
        this.treeLoad = false
        this.orgTreeData = data
      }).catch(() => {
        this.treeLoad = false
      })
      getPostList(website.tenantId).then(res => {
        this.postList = res.data.data || []
        const obj = {}
        const fun = (list) => {
          list.forEach( item => {
            obj[item.postName] = item.id
            if(item.children?.length) {
              fun(item.children)
            }
          })
        }
        if(this.postList.length) {
          fun(this.postList)
        }
        this.postMap = obj
      });
    },
    closeDept () {
      this.filterDept = ""
      this.$refs.treeDom.filter(this.filterDept)
    },
    toDetails (param) {
      crud.toEdit(param);
      this.btnShow = false;
    },
    cancel () {
      this.btnShow = true;
    },
    filterTermNode (value, data) {
      if (!value) return true;
      if (data.title) {
        return data.title.indexOf(value) !== -1;
      } else if (data.label) {
        return data.label.indexOf(value) !== -1;
      }
    },
    handleReset () {
      if (crud.selections.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择账号密码重置为123456?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return resetPassword(crud.selections.map(item => item.id).join(','));
        })
        .then(() => {
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          crud.toQuery();
        });
    },
    handleGrant () {
      if (crud.selections.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.roleTreeObj = [];
      if (crud.selections.length === 1) {
        this.roleTreeObj = crud.selections[0].roleId.split(",");
      }
      this.getRoles()
    },

    submitRole () {
      const roleList = this.$refs.treeRole.getCheckedKeys().join(",");
      let str = ''
      if (crud.selections.length) {
        str = crud.selections.map(item => item.id).join(',')
      }
      grant(str, roleList).then(() => {
        this.roleBox = false;
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        crud.toQuery();
      });
    },
    handleLock (islock) {
      if (crud.selections.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if(islock) {
        const exist = crud.selections.find( item => item.status == 0)
        if(exist) {
          return this.$message.warning("存在禁用账号，请重新勾选！");
        }
      } else {
        const exist = crud.selections.find( item => item.status == 1)
        if(exist) {
          return this.$message.warning("存在正常状态的账号，请重新勾选！");
        }
      }
      this.$confirm(`确定将账号${islock ? "禁用" : "解封"}？`, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          const fun = islock ? lock : unlock;
          fun(crud.selections.map(item => item.id).join(',')).then(() => {
            this.$message({
              type: "success",
              message: "操作成功!",
            });
            this.crud.toQuery();
          });
        })
    },
    // 获取excel的数据
    getBatchData (returnValue) {
      let data = returnValue.data;
      if (!this.reversalObj) {
        this.reversalObj = {};
        for (let k in this.dataType) {
          this.reversalObj[this.dataType[k]] = k;
        }
      }
      this.tipsKey = [];
      let arr = data.map((item, index) => {
        let obj = {};
        for (let key in item) {
          for (let k in this.dataType) {
            if (key === k) {
              obj[this.dataType[k]] = item[key];
            }
          }
        }
        this.handleMap(obj, index)
        this.typeTimeTurn(obj, index);
        this.typeTreeTurn(obj);
        this.typeRequiredTurn(obj, index);
        this.typeRegTurn(obj, index, item)
        return obj;
      });
      if (this.tipsKey && this.tipsKey.length > 0) {
        let arr = [];
        this.tipsKey.forEach((item, index) => {
          if (item && item.length > 0) {
            const errList = []
            item.forEach(v => {
              errList.push(v)
            });
            arr.push({
              sort: `第${index + 1}行`,
              details: errList.join(',')
            });
          }
        });
        this.msgData = arr;
        this.$refs.msgDialog.msgVisible = true;
      } else {
        this.addbatchPost(arr);
        returnValue.close();
      }
    },
    transformMap(obj, map, name) {
      const list = obj[name]?.split(",") || []
      const str = list.reduce((arr, val) => {
        if(map[val]) {
          arr.push(map[val])
        }
        return arr
      }, []).join(',')
      obj[name] = str
    },
    handleMap (obj) {
      const list = [{name: 'roleId', map: this.roleMap}, {name: 'postId', map: this.postMap}]
      list.forEach(item => {
        this.transformMap(obj, item.map, item.name)
      })
    },
    handleErrFun (index, v) {
      const pL = this.reversalObj[v]
      if (pL) {
        if (!this.tipsKey[index]) {
          this.tipsKey[index] = [pL];
        } else if (!this.tipsKey[index].includes(pL)) {
          this.tipsKey[index].push(pL);
        }
      }
    },
    typeRegTurn (obj, k, data) {
      Object.keys(this.regTurn).forEach(v => {
        const reg = this.regTurn[v]
        if (obj[v] && !reg.test(obj[v])) {
          this.handleErrFun(k, v)
        }
      })
      if(obj.deptId) {
        const ids = obj.deptId.split(',')
        const list = []
        ids.forEach( key => {
          list.push(this.deptIdMap[key])
        })
        obj.deptId = list.join(',')
      }
      if(obj.password2 !== obj.password) {
        this.handleErrFun(k, 'password2')
      }
      if(obj.monitDeptId) {
        const ids = obj.monitDeptId.split(',')
        const list = []
        ids.forEach( key => {
          list.push(this.deptIdMap[key])
        })
        obj.monitDeptId = list.join(',')
      }
      const rList = ['roleId', 'deptId', 'postId', 'monitDeptId', 'userType', 'sex']
      rList.forEach( v => {
        const pL = this.reversalObj[v];
        let isEquArray = false
        if(Array.isArray(data[pL])) isEquArray = (data[pL].length !== obj[v].length || data[pL].find( item => obj[v].includes(item)) || obj[v].find( item => data[pL].includes(item)))
        if (data[pL] && (data[pL] === obj[v] || !obj[v] || isEquArray)) {
          this.handleErrFun(k, v);
        }
      });
      const max= {
        account: 20,
        name: 20,
        realName: 35,
        code: 20
      }
      Object.keys(max).forEach( key => {
        if(obj[key]?.length > max[key]) {
          this.handleErrFun(k, key);
        }
      })
    },
    // 必填项判断
    typeRequiredTurn (obj, k, dataRequired = this.typeRequired) {
      dataRequired.forEach(v => {
        if (typeof (v) === 'object' && v.mod) {
          this.typeRequiredTurn(obj[v.mod], k, v.required);
        } else if (!obj[v] && obj[v] !== 0) {
          this.handleErrFun(k, v)
        }
      });
    },
    // 字典里的转key/id
    typeTreeTurn (obj) {
      for (let k in this.dict) {
        for (let j in obj) {
          if (k === j) {
            this.treeTurn(this.dict[k], obj[j], j);
            obj[j] = this.numKey;
            this.numKey = undefined;
          } else if (k === this.typeDictName[j]) {
            this.treeTurn(this.dict[this.typeDictName[j]], obj[j], j);
            obj[j] = this.numKey;
            this.numKey = undefined;
          }
        }
      }
    },
    getTreeTurn (str) {
      let obj = {
        value: 'value',
        label: 'label'
      };
      switch (str) {
        case 'monitDeptId':
          obj.value = 'id';
          obj.label = 'title';
          break;
        case 'deptId':
          obj.value = 'id';
          obj.label = 'title';
          break;
        default:
          break;
      }
      return obj;
    },
    // 递归找key/id
    treeTurn (tree, word, str, iValue = this.getTreeTurn(str).value, iLabel = this.getTreeTurn(str).label, iChildren = 'children') {
      tree.forEach(item => {
        if (!item.disabled && item[iLabel] === word) {
          this.numKey = item[iValue];
        } else if (item[iChildren] && item[iChildren].length > 0) {
          this.treeTurn(item[iChildren], word, str, iValue, iLabel, iChildren);
        }
      });
    },
    // 时间转化
    typeTimeTurn (obj, index) {
      if(obj.birthday && !/^(\d+)-(\d+)-(\d+)/.test(obj.birthday)) {
        this.handleErrFun(index, birthday)
      } else if(obj.birthday) {
        obj.birthday = obj.birthday + ' 12:00:00'
      }
    },
    // 提交请求
    addbatchPost (arr) {
      addbatch(arr).then(res => {
        this.$message({
          showClose: true,
          message: res.data.msg,
          type: 'success'
        });
        this.crud.refresh();
      });
    },
    /**
     * 获取某种字典类型的文字
     * @param {String} dictName
     * @param {Int|String} value
     */
    getEnumDictLabel (dictName, value) {
      if (this.dict.dict[dictName] && this.dict.dict[dictName][value]) {
        return this.dict.dict[dictName][value].label;
      } else {
        return '';
      }
    },
  },
};
</script>
<!-- <style lang="less">
.user-container .el-tree-node.is-focusable {
  background-color: transparent;
}
</style> -->
<style lang="less" scoped>
@media screen and (max-width: 1500px) {
    .tree-container {
      width: 240px !important;
    }
}
.user-container {
  height: 100%;
  overflow: hidden;
  .avue-tree-left {
    height: 100%;
    display: flex;
    flex-direction: column;
    // padding: 8px;
    padding-right: 8px;
    .avue-tree-left-content{
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      .content {
      margin-top: 8px;
      flex: 1;
      overflow-y: auto;
      overflow-x: auto;
      /deep/ .el-tree-node {
        &.is-current {
          .el-tree-node__content {
            background: rgba(var(--gn-color-rgb), 0.1);
          }
        }
        .el-tree-node__content {
          &:hover {
            background: rgba(var(--gn-color-rgb), 0.1);
          }
        }
      }
    }
    }
  }
}
.xh-container {
    // height: calc(100% - 10px);
    height: 100%;
    overflow: auto;
    .scroll-box {
      height: 100%;
      min-width: 950px;
      overflow-x: auto;
      display: flex;
      flex-direction: column;
      .xh-crud-table-container {
        flex: 1;
      }
    }
  }
</style>
